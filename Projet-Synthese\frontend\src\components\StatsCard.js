import React from 'react';

const StatsCard = ({ title, value, change, icon, color }) => {
  const getColorClasses = (color) => {
    const colors = {
      blue: 'bg-blue-500',
      green: 'bg-green-500',
      purple: 'bg-purple-500',
      orange: 'bg-orange-500',
      red: 'bg-red-500',
      yellow: 'bg-yellow-500'
    };
    return colors[color] || 'bg-gray-500';
  };

  return (
    <div className="bg-white p-4 lg:p-6 rounded-lg shadow-sm border">
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <p className="text-sm font-medium text-gray-600 mb-1">{title}</p>
          <p className="text-2xl font-bold text-gray-900 mb-2">{value}</p>
          {change && (
            <p className="text-sm text-gray-600">{change}</p>
          )}
        </div>
        <div className={`p-3 rounded-full ${getColorClasses(color)}`}>
          {icon}
        </div>
      </div>
    </div>
  );
};

export default StatsCard;
