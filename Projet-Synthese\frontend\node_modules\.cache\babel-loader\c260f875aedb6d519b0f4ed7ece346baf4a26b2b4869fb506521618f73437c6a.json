{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Projet-Synthese\\\\frontend\\\\src\\\\pages\\\\ProductDetailPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, Link, useNavigate } from 'react-router-dom';\nimport { productAPI } from '../services/apiService';\nimport { useCart } from '../context/CartContext';\nimport RelatedProducts from '../components/products/RelatedProducts';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ProductDetailPage = () => {\n  _s();\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const {\n    dispatch\n  } = useCart();\n  const [product, setProduct] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [quantity, setQuantity] = useState(1);\n  const [selectedGrillingOptions, setSelectedGrillingOptions] = useState({});\n  useEffect(() => {\n    fetchProduct();\n  }, [id]);\n  const fetchProduct = async () => {\n    try {\n      setLoading(true);\n      console.log('🔄 Chargement du produit:', id);\n      const response = await productAPI.getById(id);\n      if (response.success) {\n        setProduct(response.data);\n        console.log('✅ Produit chargé:', response.data.name);\n        setError(null);\n      } else {\n        console.log('⚠️ Produit non trouvé');\n        setError('Produit non trouvé');\n      }\n    } catch (err) {\n      console.error('❌ Erreur lors du chargement du produit:', err);\n      setError('Erreur lors du chargement du produit');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleQuantityChange = value => {\n    if (value < 1) return;\n    if (product && product.stock && value > product.stock) return;\n    setQuantity(value);\n  };\n  const handleGrillingOptionChange = (optionId, value) => {\n    setSelectedGrillingOptions(prev => ({\n      ...prev,\n      [optionId]: value\n    }));\n  };\n  const handleAddToCart = () => {\n    if (!product) return;\n    dispatch({\n      type: 'ADD_ITEM',\n      payload: {\n        id: product.id,\n        name: product.name,\n        price: product.sale_price || product.price,\n        image: product.image_url,\n        quantity: quantity,\n        grillingOptions: selectedGrillingOptions\n      }\n    });\n    alert(`${product.name} ajouté au panier (${quantity})`);\n  };\n  const formatPrice = price => {\n    return new Intl.NumberFormat('fr-MA', {\n      style: 'currency',\n      currency: 'MAD'\n    }).format(price);\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 py-12 flex justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-500\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 7\n    }, this);\n  }\n  if (error || !product) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 py-12\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\",\n        children: error || \"Produit non trouvé\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Vérifier si le produit est en promotion\n  const isOnSale = product.sale_price && product.sale_price < product.price;\n\n  // Calculer le pourcentage de réduction si en promotion\n  const discountPercentage = isOnSale ? Math.round((product.price - product.sale_price) / product.price * 100) : 0;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container mx-auto px-4 py-12\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6 text-sm\",\n      children: [/*#__PURE__*/_jsxDEV(Link, {\n        to: \"/\",\n        className: \"text-gray-500 hover:text-green-600\",\n        children: \"Accueil\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"mx-2 text-gray-400\",\n        children: \"/\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/products\",\n        className: \"text-gray-500 hover:text-green-600\",\n        children: \"Produits\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"mx-2 text-gray-400\",\n        children: \"/\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-gray-700\",\n        children: product.name\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-md overflow-hidden mb-10\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"md:flex\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"md:w-1/2 relative\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: product.image_url || 'https://via.placeholder.com/600x600?text=Pas+d\\'image',\n            alt: product.name,\n            className: \"w-full h-full object-cover\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 13\n          }, this), isOnSale && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute top-4 left-4 bg-red-500 text-white font-bold px-3 py-1 rounded\",\n            children: [\"-\", discountPercentage, \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 15\n          }, this), product.is_organic && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute top-4 right-4 bg-green-500 text-white font-bold px-3 py-1 rounded\",\n            children: \"BIO\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"md:w-1/2 p-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-3xl font-bold mb-2\",\n            children: product.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-baseline\",\n              children: isOnSale ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-2xl font-bold text-red-600 mr-2\",\n                  children: [product.sale_price.toFixed(2), \" \\u20AC\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 153,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-lg text-gray-500 line-through\",\n                  children: [product.price.toFixed(2), \" \\u20AC\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 156,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-2xl font-bold\",\n                children: [product.price.toFixed(2), \" \\u20AC\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 15\n            }, this), product.price_per_unit && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"ml-4 text-sm text-gray-500\",\n              children: [\"(\", product.price_per_unit.toFixed(2), \" \\u20AC / \", product.unit, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-6\",\n            children: product.stock > 0 ? /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-green-600 font-medium\",\n              children: [\"En stock (\", product.stock, \" disponibles)\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-red-600 font-medium\",\n              children: \"Rupture de stock\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-lg font-semibold mb-2\",\n              children: \"Description\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-700\",\n              children: product.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-lg font-semibold mb-2\",\n              children: \"Caract\\xE9ristiques\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"text-gray-700 space-y-1\",\n              children: [product.weight && /*#__PURE__*/_jsxDEV(\"li\", {\n                children: [\"Poids/Volume: \", product.weight, \" \", product.unit]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 19\n              }, this), product.origin && /*#__PURE__*/_jsxDEV(\"li\", {\n                children: [\"Origine: \", product.origin]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 19\n              }, this), product.is_organic && /*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-5 h-5 text-green-500 mr-1\",\n                  fill: \"currentColor\",\n                  viewBox: \"0 0 20 20\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    fillRule: \"evenodd\",\n                    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                    clipRule: \"evenodd\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 207,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 206,\n                  columnNumber: 21\n                }, this), \"Produit biologique\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this), product.grilling_options && product.grilling_options.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-lg font-semibold mb-2\",\n              children: \"Options de cuisson\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: product.grilling_options.map(option => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"radio\",\n                  id: `option-${option.id}`,\n                  name: \"grilling-option\",\n                  className: \"mr-2\",\n                  onChange: () => handleGrillingOptionChange(option.id, true),\n                  checked: selectedGrillingOptions[option.id] === true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 222,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: `option-${option.id}`,\n                  className: \"flex-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-medium\",\n                    children: option.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 231,\n                    columnNumber: 25\n                  }, this), option.additional_cost > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-2 text-sm text-gray-500\",\n                    children: [\"(+\", option.additional_cost.toFixed(2), \" \\u20AC)\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 233,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-600\",\n                    children: option.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 237,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 230,\n                  columnNumber: 23\n                }, this)]\n              }, option.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center border rounded-md\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleQuantityChange(quantity - 1),\n                className: \"px-3 py-1 text-gray-600 hover:bg-gray-100\",\n                disabled: quantity <= 1,\n                children: \"-\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"px-4 py-1\",\n                children: quantity\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleQuantityChange(quantity + 1),\n                className: \"px-3 py-1 text-gray-600 hover:bg-gray-100\",\n                disabled: product.stock && quantity >= product.stock,\n                children: \"+\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleAddToCart,\n              disabled: product.stock <= 0,\n              className: `flex-1 py-2 px-4 rounded-md ${product.stock > 0 ? 'bg-green-600 text-white hover:bg-green-700' : 'bg-gray-300 text-gray-500 cursor-not-allowed'} transition`,\n              children: product.stock > 0 ? 'Ajouter au panier' : 'Indisponible'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-12\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-2xl font-bold mb-6\",\n        children: \"Produits similaires\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 283,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(RelatedProducts, {\n        categoryId: product.category_id,\n        currentProductId: product.id\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 282,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 109,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductDetailPage, \"MBPE6W18NHxaoS9v/S/hSZhfJOI=\", false, function () {\n  return [useParams, useNavigate, useCart];\n});\n_c = ProductDetailPage;\nexport default ProductDetailPage;\nvar _c;\n$RefreshReg$(_c, \"ProductDetailPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "Link", "useNavigate", "productAPI", "useCart", "RelatedProducts", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProductDetailPage", "_s", "id", "navigate", "dispatch", "product", "setProduct", "loading", "setLoading", "error", "setError", "quantity", "setQuantity", "selectedGrillingOptions", "setSelectedGrillingOptions", "fetchProduct", "console", "log", "response", "getById", "success", "data", "name", "err", "handleQuantityChange", "value", "stock", "handleGrillingOptionChange", "optionId", "prev", "handleAddToCart", "type", "payload", "price", "sale_price", "image", "image_url", "grillingOptions", "alert", "formatPrice", "Intl", "NumberFormat", "style", "currency", "format", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "isOnSale", "discountPercentage", "Math", "round", "to", "src", "alt", "is_organic", "toFixed", "price_per_unit", "unit", "description", "weight", "origin", "fill", "viewBox", "fillRule", "d", "clipRule", "grilling_options", "length", "map", "option", "onChange", "checked", "htmlFor", "additional_cost", "onClick", "disabled", "categoryId", "category_id", "currentProductId", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet-Synthese/frontend/src/pages/ProductDetailPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, Link, useNavigate } from 'react-router-dom';\nimport { productAPI } from '../services/apiService';\nimport { useCart } from '../context/CartContext';\nimport RelatedProducts from '../components/products/RelatedProducts';\n\nconst ProductDetailPage = () => {\n  const { id } = useParams();\n  const navigate = useNavigate();\n  const { dispatch } = useCart();\n  const [product, setProduct] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [quantity, setQuantity] = useState(1);\n  const [selectedGrillingOptions, setSelectedGrillingOptions] = useState({});\n\n  useEffect(() => {\n    fetchProduct();\n  }, [id]);\n\n  const fetchProduct = async () => {\n    try {\n      setLoading(true);\n      console.log('🔄 Chargement du produit:', id);\n\n      const response = await productAPI.getById(id);\n\n      if (response.success) {\n        setProduct(response.data);\n        console.log('✅ Produit chargé:', response.data.name);\n        setError(null);\n      } else {\n        console.log('⚠️ Produit non trouvé');\n        setError('Produit non trouvé');\n      }\n    } catch (err) {\n      console.error('❌ Erreur lors du chargement du produit:', err);\n      setError('Erreur lors du chargement du produit');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleQuantityChange = (value) => {\n    if (value < 1) return;\n    if (product && product.stock && value > product.stock) return;\n    setQuantity(value);\n  };\n\n  const handleGrillingOptionChange = (optionId, value) => {\n    setSelectedGrillingOptions(prev => ({\n      ...prev,\n      [optionId]: value\n    }));\n  };\n\n  const handleAddToCart = () => {\n    if (!product) return;\n\n    dispatch({\n      type: 'ADD_ITEM',\n      payload: {\n        id: product.id,\n        name: product.name,\n        price: product.sale_price || product.price,\n        image: product.image_url,\n        quantity: quantity,\n        grillingOptions: selectedGrillingOptions\n      }\n    });\n\n    alert(`${product.name} ajouté au panier (${quantity})`);\n  };\n\n  const formatPrice = (price) => {\n    return new Intl.NumberFormat('fr-MA', {\n      style: 'currency',\n      currency: 'MAD'\n    }).format(price);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"container mx-auto px-4 py-12 flex justify-center\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-500\"></div>\n      </div>\n    );\n  }\n\n  if (error || !product) {\n    return (\n      <div className=\"container mx-auto px-4 py-12\">\n        <div className=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\">\n          {error || \"Produit non trouvé\"}\n        </div>\n      </div>\n    );\n  }\n\n  // Vérifier si le produit est en promotion\n  const isOnSale = product.sale_price && product.sale_price < product.price;\n  \n  // Calculer le pourcentage de réduction si en promotion\n  const discountPercentage = isOnSale \n    ? Math.round(((product.price - product.sale_price) / product.price) * 100) \n    : 0;\n\n  return (\n    <div className=\"container mx-auto px-4 py-12\">\n      {/* Fil d'Ariane */}\n      <div className=\"mb-6 text-sm\">\n        <Link to=\"/\" className=\"text-gray-500 hover:text-green-600\">Accueil</Link>\n        <span className=\"mx-2 text-gray-400\">/</span>\n        <Link to=\"/products\" className=\"text-gray-500 hover:text-green-600\">Produits</Link>\n        <span className=\"mx-2 text-gray-400\">/</span>\n        <span className=\"text-gray-700\">{product.name}</span>\n      </div>\n      \n      <div className=\"bg-white rounded-lg shadow-md overflow-hidden mb-10\">\n        <div className=\"md:flex\">\n          {/* Image du produit */}\n          <div className=\"md:w-1/2 relative\">\n            <img\n              src={product.image_url || 'https://via.placeholder.com/600x600?text=Pas+d\\'image'}\n              alt={product.name}\n              className=\"w-full h-full object-cover\"\n            />\n            \n            {/* Badge de promotion */}\n            {isOnSale && (\n              <div className=\"absolute top-4 left-4 bg-red-500 text-white font-bold px-3 py-1 rounded\">\n                -{discountPercentage}%\n              </div>\n            )}\n            \n            {/* Badge bio si applicable */}\n            {product.is_organic && (\n              <div className=\"absolute top-4 right-4 bg-green-500 text-white font-bold px-3 py-1 rounded\">\n                BIO\n              </div>\n            )}\n          </div>\n          \n          {/* Informations du produit */}\n          <div className=\"md:w-1/2 p-8\">\n            <h1 className=\"text-3xl font-bold mb-2\">{product.name}</h1>\n            \n            <div className=\"flex items-center mb-4\">\n              {/* Affichage du prix */}\n              <div className=\"flex items-baseline\">\n                {isOnSale ? (\n                  <>\n                    <span className=\"text-2xl font-bold text-red-600 mr-2\">\n                      {product.sale_price.toFixed(2)} €\n                    </span>\n                    <span className=\"text-lg text-gray-500 line-through\">\n                      {product.price.toFixed(2)} €\n                    </span>\n                  </>\n                ) : (\n                  <span className=\"text-2xl font-bold\">\n                    {product.price.toFixed(2)} €\n                  </span>\n                )}\n              </div>\n              \n              {/* Affichage du prix au kilo/litre si disponible */}\n              {product.price_per_unit && (\n                <span className=\"ml-4 text-sm text-gray-500\">\n                  ({product.price_per_unit.toFixed(2)} € / {product.unit})\n                </span>\n              )}\n            </div>\n            \n            {/* Disponibilité */}\n            <div className=\"mb-6\">\n              {product.stock > 0 ? (\n                <span className=\"text-green-600 font-medium\">\n                  En stock ({product.stock} disponibles)\n                </span>\n              ) : (\n                <span className=\"text-red-600 font-medium\">\n                  Rupture de stock\n                </span>\n              )}\n            </div>\n            \n            {/* Description */}\n            <div className=\"mb-6\">\n              <h2 className=\"text-lg font-semibold mb-2\">Description</h2>\n              <p className=\"text-gray-700\">{product.description}</p>\n            </div>\n            \n            {/* Caractéristiques */}\n            <div className=\"mb-6\">\n              <h2 className=\"text-lg font-semibold mb-2\">Caractéristiques</h2>\n              <ul className=\"text-gray-700 space-y-1\">\n                {product.weight && (\n                  <li>Poids/Volume: {product.weight} {product.unit}</li>\n                )}\n                {product.origin && (\n                  <li>Origine: {product.origin}</li>\n                )}\n                {product.is_organic && (\n                  <li className=\"flex items-center\">\n                    <svg className=\"w-5 h-5 text-green-500 mr-1\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n                    </svg>\n                    Produit biologique\n                  </li>\n                )}\n              </ul>\n            </div>\n            \n            {/* Options de cuisson pour les viandes */}\n            {product.grilling_options && product.grilling_options.length > 0 && (\n              <div className=\"mb-6\">\n                <h2 className=\"text-lg font-semibold mb-2\">Options de cuisson</h2>\n                <div className=\"space-y-3\">\n                  {product.grilling_options.map(option => (\n                    <div key={option.id} className=\"flex items-center\">\n                      <input\n                        type=\"radio\"\n                        id={`option-${option.id}`}\n                        name=\"grilling-option\"\n                        className=\"mr-2\"\n                        onChange={() => handleGrillingOptionChange(option.id, true)}\n                        checked={selectedGrillingOptions[option.id] === true}\n                      />\n                      <label htmlFor={`option-${option.id}`} className=\"flex-1\">\n                        <span className=\"font-medium\">{option.name}</span>\n                        {option.additional_cost > 0 && (\n                          <span className=\"ml-2 text-sm text-gray-500\">\n                            (+{option.additional_cost.toFixed(2)} €)\n                          </span>\n                        )}\n                        <p className=\"text-sm text-gray-600\">{option.description}</p>\n                      </label>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            )}\n            \n            {/* Quantité et ajout au panier */}\n            <div className=\"flex items-center space-x-4\">\n              <div className=\"flex items-center border rounded-md\">\n                <button\n                  onClick={() => handleQuantityChange(quantity - 1)}\n                  className=\"px-3 py-1 text-gray-600 hover:bg-gray-100\"\n                  disabled={quantity <= 1}\n                >\n                  -\n                </button>\n                <span className=\"px-4 py-1\">{quantity}</span>\n                <button\n                  onClick={() => handleQuantityChange(quantity + 1)}\n                  className=\"px-3 py-1 text-gray-600 hover:bg-gray-100\"\n                  disabled={product.stock && quantity >= product.stock}\n                >\n                  +\n                </button>\n              </div>\n              \n              <button\n                onClick={handleAddToCart}\n                disabled={product.stock <= 0}\n                className={`flex-1 py-2 px-4 rounded-md ${\n                  product.stock > 0\n                    ? 'bg-green-600 text-white hover:bg-green-700'\n                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'\n                } transition`}\n              >\n                {product.stock > 0 ? 'Ajouter au panier' : 'Indisponible'}\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n      \n      {/* Produits similaires */}\n      <div className=\"mt-12\">\n        <h2 className=\"text-2xl font-bold mb-6\">Produits similaires</h2>\n        <RelatedProducts categoryId={product.category_id} currentProductId={product.id} />\n      </div>\n    </div>\n  );\n};\n\nexport default ProductDetailPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AAC/D,SAASC,UAAU,QAAQ,wBAAwB;AACnD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,eAAe,MAAM,wCAAwC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErE,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAM;IAAEC;EAAG,CAAC,GAAGZ,SAAS,CAAC,CAAC;EAC1B,MAAMa,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEY;EAAS,CAAC,GAAGV,OAAO,CAAC,CAAC;EAC9B,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACqB,KAAK,EAAEC,QAAQ,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACuB,QAAQ,EAAEC,WAAW,CAAC,GAAGxB,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACyB,uBAAuB,EAAEC,0BAA0B,CAAC,GAAG1B,QAAQ,CAAC,CAAC,CAAC,CAAC;EAE1EC,SAAS,CAAC,MAAM;IACd0B,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,CAACb,EAAE,CAAC,CAAC;EAER,MAAMa,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACFP,UAAU,CAAC,IAAI,CAAC;MAChBQ,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEf,EAAE,CAAC;MAE5C,MAAMgB,QAAQ,GAAG,MAAMzB,UAAU,CAAC0B,OAAO,CAACjB,EAAE,CAAC;MAE7C,IAAIgB,QAAQ,CAACE,OAAO,EAAE;QACpBd,UAAU,CAACY,QAAQ,CAACG,IAAI,CAAC;QACzBL,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEC,QAAQ,CAACG,IAAI,CAACC,IAAI,CAAC;QACpDZ,QAAQ,CAAC,IAAI,CAAC;MAChB,CAAC,MAAM;QACLM,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;QACpCP,QAAQ,CAAC,oBAAoB,CAAC;MAChC;IACF,CAAC,CAAC,OAAOa,GAAG,EAAE;MACZP,OAAO,CAACP,KAAK,CAAC,yCAAyC,EAAEc,GAAG,CAAC;MAC7Db,QAAQ,CAAC,sCAAsC,CAAC;IAClD,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMgB,oBAAoB,GAAIC,KAAK,IAAK;IACtC,IAAIA,KAAK,GAAG,CAAC,EAAE;IACf,IAAIpB,OAAO,IAAIA,OAAO,CAACqB,KAAK,IAAID,KAAK,GAAGpB,OAAO,CAACqB,KAAK,EAAE;IACvDd,WAAW,CAACa,KAAK,CAAC;EACpB,CAAC;EAED,MAAME,0BAA0B,GAAGA,CAACC,QAAQ,EAAEH,KAAK,KAAK;IACtDX,0BAA0B,CAACe,IAAI,KAAK;MAClC,GAAGA,IAAI;MACP,CAACD,QAAQ,GAAGH;IACd,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMK,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI,CAACzB,OAAO,EAAE;IAEdD,QAAQ,CAAC;MACP2B,IAAI,EAAE,UAAU;MAChBC,OAAO,EAAE;QACP9B,EAAE,EAAEG,OAAO,CAACH,EAAE;QACdoB,IAAI,EAAEjB,OAAO,CAACiB,IAAI;QAClBW,KAAK,EAAE5B,OAAO,CAAC6B,UAAU,IAAI7B,OAAO,CAAC4B,KAAK;QAC1CE,KAAK,EAAE9B,OAAO,CAAC+B,SAAS;QACxBzB,QAAQ,EAAEA,QAAQ;QAClB0B,eAAe,EAAExB;MACnB;IACF,CAAC,CAAC;IAEFyB,KAAK,CAAC,GAAGjC,OAAO,CAACiB,IAAI,sBAAsBX,QAAQ,GAAG,CAAC;EACzD,CAAC;EAED,MAAM4B,WAAW,GAAIN,KAAK,IAAK;IAC7B,OAAO,IAAIO,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAACC,MAAM,CAACX,KAAK,CAAC;EAClB,CAAC;EAED,IAAI1B,OAAO,EAAE;IACX,oBACEV,OAAA;MAAKgD,SAAS,EAAC,kDAAkD;MAAAC,QAAA,eAC/DjD,OAAA;QAAKgD,SAAS,EAAC;MAA4E;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/F,CAAC;EAEV;EAEA,IAAIzC,KAAK,IAAI,CAACJ,OAAO,EAAE;IACrB,oBACER,OAAA;MAAKgD,SAAS,EAAC,8BAA8B;MAAAC,QAAA,eAC3CjD,OAAA;QAAKgD,SAAS,EAAC,iEAAiE;QAAAC,QAAA,EAC7ErC,KAAK,IAAI;MAAoB;QAAAsC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,MAAMC,QAAQ,GAAG9C,OAAO,CAAC6B,UAAU,IAAI7B,OAAO,CAAC6B,UAAU,GAAG7B,OAAO,CAAC4B,KAAK;;EAEzE;EACA,MAAMmB,kBAAkB,GAAGD,QAAQ,GAC/BE,IAAI,CAACC,KAAK,CAAE,CAACjD,OAAO,CAAC4B,KAAK,GAAG5B,OAAO,CAAC6B,UAAU,IAAI7B,OAAO,CAAC4B,KAAK,GAAI,GAAG,CAAC,GACxE,CAAC;EAEL,oBACEpC,OAAA;IAAKgD,SAAS,EAAC,8BAA8B;IAAAC,QAAA,gBAE3CjD,OAAA;MAAKgD,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3BjD,OAAA,CAACN,IAAI;QAACgE,EAAE,EAAC,GAAG;QAACV,SAAS,EAAC,oCAAoC;QAAAC,QAAA,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC1ErD,OAAA;QAAMgD,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAAC;MAAC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC7CrD,OAAA,CAACN,IAAI;QAACgE,EAAE,EAAC,WAAW;QAACV,SAAS,EAAC,oCAAoC;QAAAC,QAAA,EAAC;MAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACnFrD,OAAA;QAAMgD,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAAC;MAAC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC7CrD,OAAA;QAAMgD,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAEzC,OAAO,CAACiB;MAAI;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClD,CAAC,eAENrD,OAAA;MAAKgD,SAAS,EAAC,qDAAqD;MAAAC,QAAA,eAClEjD,OAAA;QAAKgD,SAAS,EAAC,SAAS;QAAAC,QAAA,gBAEtBjD,OAAA;UAAKgD,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCjD,OAAA;YACE2D,GAAG,EAAEnD,OAAO,CAAC+B,SAAS,IAAI,uDAAwD;YAClFqB,GAAG,EAAEpD,OAAO,CAACiB,IAAK;YAClBuB,SAAS,EAAC;UAA4B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,EAGDC,QAAQ,iBACPtD,OAAA;YAAKgD,SAAS,EAAC,yEAAyE;YAAAC,QAAA,GAAC,GACtF,EAACM,kBAAkB,EAAC,GACvB;UAAA;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CACN,EAGA7C,OAAO,CAACqD,UAAU,iBACjB7D,OAAA;YAAKgD,SAAS,EAAC,4EAA4E;YAAAC,QAAA,EAAC;UAE5F;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNrD,OAAA;UAAKgD,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BjD,OAAA;YAAIgD,SAAS,EAAC,yBAAyB;YAAAC,QAAA,EAAEzC,OAAO,CAACiB;UAAI;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAE3DrD,OAAA;YAAKgD,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBAErCjD,OAAA;cAAKgD,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EACjCK,QAAQ,gBACPtD,OAAA,CAAAE,SAAA;gBAAA+C,QAAA,gBACEjD,OAAA;kBAAMgD,SAAS,EAAC,sCAAsC;kBAAAC,QAAA,GACnDzC,OAAO,CAAC6B,UAAU,CAACyB,OAAO,CAAC,CAAC,CAAC,EAAC,SACjC;gBAAA;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACPrD,OAAA;kBAAMgD,SAAS,EAAC,oCAAoC;kBAAAC,QAAA,GACjDzC,OAAO,CAAC4B,KAAK,CAAC0B,OAAO,CAAC,CAAC,CAAC,EAAC,SAC5B;gBAAA;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA,eACP,CAAC,gBAEHrD,OAAA;gBAAMgD,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,GACjCzC,OAAO,CAAC4B,KAAK,CAAC0B,OAAO,CAAC,CAAC,CAAC,EAAC,SAC5B;cAAA;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YACP;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,EAGL7C,OAAO,CAACuD,cAAc,iBACrB/D,OAAA;cAAMgD,SAAS,EAAC,4BAA4B;cAAAC,QAAA,GAAC,GAC1C,EAACzC,OAAO,CAACuD,cAAc,CAACD,OAAO,CAAC,CAAC,CAAC,EAAC,YAAK,EAACtD,OAAO,CAACwD,IAAI,EAAC,GACzD;YAAA;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGNrD,OAAA;YAAKgD,SAAS,EAAC,MAAM;YAAAC,QAAA,EAClBzC,OAAO,CAACqB,KAAK,GAAG,CAAC,gBAChB7B,OAAA;cAAMgD,SAAS,EAAC,4BAA4B;cAAAC,QAAA,GAAC,YACjC,EAACzC,OAAO,CAACqB,KAAK,EAAC,eAC3B;YAAA;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,gBAEPrD,OAAA;cAAMgD,SAAS,EAAC,0BAA0B;cAAAC,QAAA,EAAC;YAE3C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UACP;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGNrD,OAAA;YAAKgD,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBjD,OAAA;cAAIgD,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3DrD,OAAA;cAAGgD,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAEzC,OAAO,CAACyD;YAAW;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC,eAGNrD,OAAA;YAAKgD,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBjD,OAAA;cAAIgD,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChErD,OAAA;cAAIgD,SAAS,EAAC,yBAAyB;cAAAC,QAAA,GACpCzC,OAAO,CAAC0D,MAAM,iBACblE,OAAA;gBAAAiD,QAAA,GAAI,gBAAc,EAACzC,OAAO,CAAC0D,MAAM,EAAC,GAAC,EAAC1D,OAAO,CAACwD,IAAI;cAAA;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACtD,EACA7C,OAAO,CAAC2D,MAAM,iBACbnE,OAAA;gBAAAiD,QAAA,GAAI,WAAS,EAACzC,OAAO,CAAC2D,MAAM;cAAA;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAClC,EACA7C,OAAO,CAACqD,UAAU,iBACjB7D,OAAA;gBAAIgD,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAC/BjD,OAAA;kBAAKgD,SAAS,EAAC,6BAA6B;kBAACoB,IAAI,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAApB,QAAA,eAClFjD,OAAA;oBAAMsE,QAAQ,EAAC,SAAS;oBAACC,CAAC,EAAC,uIAAuI;oBAACC,QAAQ,EAAC;kBAAS;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrL,CAAC,sBAER;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CACL;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,EAGL7C,OAAO,CAACiE,gBAAgB,IAAIjE,OAAO,CAACiE,gBAAgB,CAACC,MAAM,GAAG,CAAC,iBAC9D1E,OAAA;YAAKgD,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBjD,OAAA;cAAIgD,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClErD,OAAA;cAAKgD,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBzC,OAAO,CAACiE,gBAAgB,CAACE,GAAG,CAACC,MAAM,iBAClC5E,OAAA;gBAAqBgD,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChDjD,OAAA;kBACEkC,IAAI,EAAC,OAAO;kBACZ7B,EAAE,EAAE,UAAUuE,MAAM,CAACvE,EAAE,EAAG;kBAC1BoB,IAAI,EAAC,iBAAiB;kBACtBuB,SAAS,EAAC,MAAM;kBAChB6B,QAAQ,EAAEA,CAAA,KAAM/C,0BAA0B,CAAC8C,MAAM,CAACvE,EAAE,EAAE,IAAI,CAAE;kBAC5DyE,OAAO,EAAE9D,uBAAuB,CAAC4D,MAAM,CAACvE,EAAE,CAAC,KAAK;gBAAK;kBAAA6C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC,eACFrD,OAAA;kBAAO+E,OAAO,EAAE,UAAUH,MAAM,CAACvE,EAAE,EAAG;kBAAC2C,SAAS,EAAC,QAAQ;kBAAAC,QAAA,gBACvDjD,OAAA;oBAAMgD,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAE2B,MAAM,CAACnD;kBAAI;oBAAAyB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,EACjDuB,MAAM,CAACI,eAAe,GAAG,CAAC,iBACzBhF,OAAA;oBAAMgD,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,GAAC,IACzC,EAAC2B,MAAM,CAACI,eAAe,CAAClB,OAAO,CAAC,CAAC,CAAC,EAAC,UACvC;kBAAA;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CACP,eACDrD,OAAA;oBAAGgD,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAE2B,MAAM,CAACX;kBAAW;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxD,CAAC;cAAA,GAjBAuB,MAAM,CAACvE,EAAE;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAkBd,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAGDrD,OAAA;YAAKgD,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1CjD,OAAA;cAAKgD,SAAS,EAAC,qCAAqC;cAAAC,QAAA,gBAClDjD,OAAA;gBACEiF,OAAO,EAAEA,CAAA,KAAMtD,oBAAoB,CAACb,QAAQ,GAAG,CAAC,CAAE;gBAClDkC,SAAS,EAAC,2CAA2C;gBACrDkC,QAAQ,EAAEpE,QAAQ,IAAI,CAAE;gBAAAmC,QAAA,EACzB;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTrD,OAAA;gBAAMgD,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAEnC;cAAQ;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC7CrD,OAAA;gBACEiF,OAAO,EAAEA,CAAA,KAAMtD,oBAAoB,CAACb,QAAQ,GAAG,CAAC,CAAE;gBAClDkC,SAAS,EAAC,2CAA2C;gBACrDkC,QAAQ,EAAE1E,OAAO,CAACqB,KAAK,IAAIf,QAAQ,IAAIN,OAAO,CAACqB,KAAM;gBAAAoB,QAAA,EACtD;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENrD,OAAA;cACEiF,OAAO,EAAEhD,eAAgB;cACzBiD,QAAQ,EAAE1E,OAAO,CAACqB,KAAK,IAAI,CAAE;cAC7BmB,SAAS,EAAE,+BACTxC,OAAO,CAACqB,KAAK,GAAG,CAAC,GACb,4CAA4C,GAC5C,8CAA8C,aACtC;cAAAoB,QAAA,EAEbzC,OAAO,CAACqB,KAAK,GAAG,CAAC,GAAG,mBAAmB,GAAG;YAAc;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNrD,OAAA;MAAKgD,SAAS,EAAC,OAAO;MAAAC,QAAA,gBACpBjD,OAAA;QAAIgD,SAAS,EAAC,yBAAyB;QAAAC,QAAA,EAAC;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAChErD,OAAA,CAACF,eAAe;QAACqF,UAAU,EAAE3E,OAAO,CAAC4E,WAAY;QAACC,gBAAgB,EAAE7E,OAAO,CAACH;MAAG;QAAA6C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/E,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjD,EAAA,CAzRID,iBAAiB;EAAA,QACNV,SAAS,EACPE,WAAW,EACPE,OAAO;AAAA;AAAAyF,EAAA,GAHxBnF,iBAAiB;AA2RvB,eAAeA,iBAAiB;AAAC,IAAAmF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}