{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Projet-Synthese\\\\frontend\\\\src\\\\pages\\\\ProductsPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useData } from '../context/DataContext';\nimport { productAPI } from '../services/apiService';\nimport ProductCard from '../components/products/ProductCard';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProductsPage = () => {\n  _s();\n  const {\n    state\n  } = useData();\n  const {\n    products: contextProducts,\n    categories\n  } = state;\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('');\n  useEffect(() => {\n    fetchProducts();\n  }, [contextProducts, selectedCategory]);\n  const fetchProducts = async () => {\n    try {\n      setLoading(true);\n      console.log('🔄 Chargement des produits...');\n\n      // Utiliser d'abord les produits du contexte\n      if (contextProducts && contextProducts.length > 0) {\n        let filteredProducts = contextProducts;\n        if (selectedCategory) {\n          filteredProducts = contextProducts.filter(p => p.category_id == selectedCategory);\n        }\n        setProducts(filteredProducts);\n        console.log(`✅ ${filteredProducts.length} produits chargés depuis le contexte`);\n      } else {\n        // Sinon, charger depuis l'API\n        const response = await productAPI.getAll({\n          is_active: true,\n          category_id: selectedCategory || undefined\n        });\n        if (response.success) {\n          const productData = response.data.data || response.data;\n          setProducts(productData);\n          console.log(`✅ ${productData.length} produits chargés depuis l'API`);\n        } else {\n          console.log('⚠️ Aucun produit trouvé');\n          setProducts([]);\n        }\n      }\n      setError(null);\n    } catch (err) {\n      console.error('❌ Erreur lors du chargement des produits:', err);\n      setError('Erreur lors du chargement des produits');\n      setProducts([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleSearch = e => {\n    e.preventDefault();\n    if (searchTerm.trim()) {\n      const filtered = contextProducts.filter(product => product.name.toLowerCase().includes(searchTerm.toLowerCase()) || product.description.toLowerCase().includes(searchTerm.toLowerCase()));\n      setProducts(filtered);\n    } else {\n      fetchProducts();\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container mx-auto px-4 py-12\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center mb-12\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-4xl font-bold text-gray-900 mb-4\",\n        children: \"Tous nos produits\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600 max-w-2xl mx-auto\",\n        children: \"D\\xE9couvrez notre gamme compl\\xE8te de produits frais et savoureux\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-sm border p-6 mb-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSearch,\n          className: \"relative\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Rechercher un produit...\",\n            value: searchTerm,\n            onChange: e => setSearchTerm(e.target.value),\n            className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"absolute left-3 top-3 h-4 w-4 text-gray-400\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            stroke: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: selectedCategory,\n          onChange: e => setSelectedCategory(e.target.value),\n          className: \"border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"\",\n            children: \"Toutes les cat\\xE9gories\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 13\n          }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n            value: category.id,\n            children: category.name\n          }, category.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 86,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center mb-8\",\n      children: /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-gray-600\",\n        children: [products.length, \" produit\", products.length !== 1 ? 's' : '', \" trouv\\xE9\", products.length !== 1 ? 's' : '']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 7\n    }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n      children: Array(8).fill(0).map((_, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card animate-pulse\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gray-300 aspect-square w-full\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4 space-y-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"h-4 bg-gray-300 rounded w-1/4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"h-6 bg-gray-300 rounded w-3/4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"h-4 bg-gray-300 rounded w-1/2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 15\n        }, this)]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 9\n    }, this) : error ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-12\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-red-50 border border-red-200 rounded-lg p-8 max-w-2xl mx-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-red-400 mb-4\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"mx-auto h-16 w-16\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            stroke: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 1,\n              d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-xl font-semibold text-red-900 mb-2\",\n          children: \"Erreur de chargement\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-red-700 mb-4\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: fetchProducts,\n          className: \"bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors\",\n          children: \"R\\xE9essayer\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 9\n    }, this) : products.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n      children: products.map(product => /*#__PURE__*/_jsxDEV(ProductCard, {\n        product: product\n      }, product.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 158,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-12\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-50 rounded-lg p-8 max-w-2xl mx-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-gray-400 mb-4\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"mx-auto h-16 w-16\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            stroke: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 1,\n              d: \"M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8V4a1 1 0 00-1-1H7a1 1 0 00-1 1v1m8 0V4.5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-xl font-semibold text-gray-900 mb-2\",\n          children: \"Aucun produit trouv\\xE9\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mb-4\",\n          children: searchTerm || selectedCategory ? \"Aucun produit ne correspond à vos critères de recherche.\" : \"L'administrateur n'a pas encore ajouté de produits.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-500\",\n          children: \"\\uD83D\\uDD17 Les produits sont g\\xE9r\\xE9s via l'interface d'administration Laravel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 164,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 76,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductsPage, \"kLQYRXMmj3J9hAA+QUCPKfyDLqo=\", false, function () {\n  return [useData];\n});\n_c = ProductsPage;\nexport default ProductsPage;\nvar _c;\n$RefreshReg$(_c, \"ProductsPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useData", "productAPI", "ProductCard", "jsxDEV", "_jsxDEV", "ProductsPage", "_s", "state", "products", "contextProducts", "categories", "setProducts", "loading", "setLoading", "error", "setError", "searchTerm", "setSearchTerm", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "fetchProducts", "console", "log", "length", "filteredProducts", "filter", "p", "category_id", "response", "getAll", "is_active", "undefined", "success", "productData", "data", "err", "handleSearch", "e", "preventDefault", "trim", "filtered", "product", "name", "toLowerCase", "includes", "description", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "type", "placeholder", "value", "onChange", "target", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "map", "category", "id", "Array", "_", "index", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet-Synthese/frontend/src/pages/ProductsPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useData } from '../context/DataContext';\nimport { productAPI } from '../services/apiService';\nimport ProductCard from '../components/products/ProductCard';\n\nconst ProductsPage = () => {\n  const { state } = useData();\n  const { products: contextProducts, categories } = state;\n\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('');\n\n  useEffect(() => {\n    fetchProducts();\n  }, [contextProducts, selectedCategory]);\n\n  const fetchProducts = async () => {\n    try {\n      setLoading(true);\n      console.log('🔄 Chargement des produits...');\n\n      // Utiliser d'abord les produits du contexte\n      if (contextProducts && contextProducts.length > 0) {\n        let filteredProducts = contextProducts;\n\n        if (selectedCategory) {\n          filteredProducts = contextProducts.filter(p => p.category_id == selectedCategory);\n        }\n\n        setProducts(filteredProducts);\n        console.log(`✅ ${filteredProducts.length} produits chargés depuis le contexte`);\n      } else {\n        // Sinon, charger depuis l'API\n        const response = await productAPI.getAll({\n          is_active: true,\n          category_id: selectedCategory || undefined\n        });\n\n        if (response.success) {\n          const productData = response.data.data || response.data;\n          setProducts(productData);\n          console.log(`✅ ${productData.length} produits chargés depuis l'API`);\n        } else {\n          console.log('⚠️ Aucun produit trouvé');\n          setProducts([]);\n        }\n      }\n\n      setError(null);\n    } catch (err) {\n      console.error('❌ Erreur lors du chargement des produits:', err);\n      setError('Erreur lors du chargement des produits');\n      setProducts([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSearch = (e) => {\n    e.preventDefault();\n    if (searchTerm.trim()) {\n      const filtered = contextProducts.filter(product =>\n        product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        product.description.toLowerCase().includes(searchTerm.toLowerCase())\n      );\n      setProducts(filtered);\n    } else {\n      fetchProducts();\n    }\n  };\n\n  return (\n    <div className=\"container mx-auto px-4 py-12\">\n      {/* En-tête */}\n      <div className=\"text-center mb-12\">\n        <h1 className=\"text-4xl font-bold text-gray-900 mb-4\">Tous nos produits</h1>\n        <p className=\"text-gray-600 max-w-2xl mx-auto\">\n          Découvrez notre gamme complète de produits frais et savoureux\n        </p>\n      </div>\n\n      {/* Barre de recherche et filtres */}\n      <div className=\"bg-white rounded-lg shadow-sm border p-6 mb-8\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          {/* Recherche */}\n          <form onSubmit={handleSearch} className=\"relative\">\n            <input\n              type=\"text\"\n              placeholder=\"Rechercher un produit...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n            />\n            <svg className=\"absolute left-3 top-3 h-4 w-4 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n            </svg>\n          </form>\n\n          {/* Filtre par catégorie */}\n          <select\n            value={selectedCategory}\n            onChange={(e) => setSelectedCategory(e.target.value)}\n            className=\"border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n          >\n            <option value=\"\">Toutes les catégories</option>\n            {categories.map(category => (\n              <option key={category.id} value={category.id}>\n                {category.name}\n              </option>\n            ))}\n          </select>\n        </div>\n      </div>\n\n      {/* Résultats */}\n      <div className=\"flex justify-between items-center mb-8\">\n        <span className=\"text-gray-600\">\n          {products.length} produit{products.length !== 1 ? 's' : ''} trouvé{products.length !== 1 ? 's' : ''}\n        </span>\n      </div>\n\n      {/* Contenu */}\n      {loading ? (\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n          {Array(8).fill(0).map((_, index) => (\n            <div key={index} className=\"card animate-pulse\">\n              <div className=\"bg-gray-300 aspect-square w-full\"></div>\n              <div className=\"p-4 space-y-3\">\n                <div className=\"h-4 bg-gray-300 rounded w-1/4\"></div>\n                <div className=\"h-6 bg-gray-300 rounded w-3/4\"></div>\n                <div className=\"h-4 bg-gray-300 rounded w-1/2\"></div>\n              </div>\n            </div>\n          ))}\n        </div>\n      ) : error ? (\n        <div className=\"text-center py-12\">\n          <div className=\"bg-red-50 border border-red-200 rounded-lg p-8 max-w-2xl mx-auto\">\n            <div className=\"text-red-400 mb-4\">\n              <svg className=\"mx-auto h-16 w-16\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1} d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n              </svg>\n            </div>\n            <h3 className=\"text-xl font-semibold text-red-900 mb-2\">Erreur de chargement</h3>\n            <p className=\"text-red-700 mb-4\">{error}</p>\n            <button\n              onClick={fetchProducts}\n              className=\"bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors\"\n            >\n              Réessayer\n            </button>\n          </div>\n        </div>\n      ) : products.length > 0 ? (\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n          {products.map(product => (\n            <ProductCard key={product.id} product={product} />\n          ))}\n        </div>\n      ) : (\n        <div className=\"text-center py-12\">\n          <div className=\"bg-gray-50 rounded-lg p-8 max-w-2xl mx-auto\">\n            <div className=\"text-gray-400 mb-4\">\n              <svg className=\"mx-auto h-16 w-16\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1} d=\"M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8V4a1 1 0 00-1-1H7a1 1 0 00-1 1v1m8 0V4.5\" />\n              </svg>\n            </div>\n            <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">Aucun produit trouvé</h3>\n            <p className=\"text-gray-600 mb-4\">\n              {searchTerm || selectedCategory\n                ? \"Aucun produit ne correspond à vos critères de recherche.\"\n                : \"L'administrateur n'a pas encore ajouté de produits.\"\n              }\n            </p>\n            <p className=\"text-sm text-gray-500\">\n              🔗 Les produits sont gérés via l'interface d'administration Laravel\n            </p>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default ProductsPage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,UAAU,QAAQ,wBAAwB;AACnD,OAAOC,WAAW,MAAM,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7D,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM;IAAEC;EAAM,CAAC,GAAGP,OAAO,CAAC,CAAC;EAC3B,MAAM;IAAEQ,QAAQ,EAAEC,eAAe;IAAEC;EAAW,CAAC,GAAGH,KAAK;EAEvD,MAAM,CAACC,QAAQ,EAAEG,WAAW,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgB,KAAK,EAAEC,QAAQ,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACkB,UAAU,EAAEC,aAAa,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACoB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAE5DC,SAAS,CAAC,MAAM;IACdqB,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAACX,eAAe,EAAES,gBAAgB,CAAC,CAAC;EAEvC,MAAME,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACFP,UAAU,CAAC,IAAI,CAAC;MAChBQ,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;;MAE5C;MACA,IAAIb,eAAe,IAAIA,eAAe,CAACc,MAAM,GAAG,CAAC,EAAE;QACjD,IAAIC,gBAAgB,GAAGf,eAAe;QAEtC,IAAIS,gBAAgB,EAAE;UACpBM,gBAAgB,GAAGf,eAAe,CAACgB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,WAAW,IAAIT,gBAAgB,CAAC;QACnF;QAEAP,WAAW,CAACa,gBAAgB,CAAC;QAC7BH,OAAO,CAACC,GAAG,CAAC,KAAKE,gBAAgB,CAACD,MAAM,sCAAsC,CAAC;MACjF,CAAC,MAAM;QACL;QACA,MAAMK,QAAQ,GAAG,MAAM3B,UAAU,CAAC4B,MAAM,CAAC;UACvCC,SAAS,EAAE,IAAI;UACfH,WAAW,EAAET,gBAAgB,IAAIa;QACnC,CAAC,CAAC;QAEF,IAAIH,QAAQ,CAACI,OAAO,EAAE;UACpB,MAAMC,WAAW,GAAGL,QAAQ,CAACM,IAAI,CAACA,IAAI,IAAIN,QAAQ,CAACM,IAAI;UACvDvB,WAAW,CAACsB,WAAW,CAAC;UACxBZ,OAAO,CAACC,GAAG,CAAC,KAAKW,WAAW,CAACV,MAAM,gCAAgC,CAAC;QACtE,CAAC,MAAM;UACLF,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;UACtCX,WAAW,CAAC,EAAE,CAAC;QACjB;MACF;MAEAI,QAAQ,CAAC,IAAI,CAAC;IAChB,CAAC,CAAC,OAAOoB,GAAG,EAAE;MACZd,OAAO,CAACP,KAAK,CAAC,2CAA2C,EAAEqB,GAAG,CAAC;MAC/DpB,QAAQ,CAAC,wCAAwC,CAAC;MAClDJ,WAAW,CAAC,EAAE,CAAC;IACjB,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMuB,YAAY,GAAIC,CAAC,IAAK;IAC1BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAItB,UAAU,CAACuB,IAAI,CAAC,CAAC,EAAE;MACrB,MAAMC,QAAQ,GAAG/B,eAAe,CAACgB,MAAM,CAACgB,OAAO,IAC7CA,OAAO,CAACC,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC5B,UAAU,CAAC2B,WAAW,CAAC,CAAC,CAAC,IAC7DF,OAAO,CAACI,WAAW,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC5B,UAAU,CAAC2B,WAAW,CAAC,CAAC,CACrE,CAAC;MACDhC,WAAW,CAAC6B,QAAQ,CAAC;IACvB,CAAC,MAAM;MACLpB,aAAa,CAAC,CAAC;IACjB;EACF,CAAC;EAED,oBACEhB,OAAA;IAAK0C,SAAS,EAAC,8BAA8B;IAAAC,QAAA,gBAE3C3C,OAAA;MAAK0C,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChC3C,OAAA;QAAI0C,SAAS,EAAC,uCAAuC;QAAAC,QAAA,EAAC;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5E/C,OAAA;QAAG0C,SAAS,EAAC,iCAAiC;QAAAC,QAAA,EAAC;MAE/C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGN/C,OAAA;MAAK0C,SAAS,EAAC,+CAA+C;MAAAC,QAAA,eAC5D3C,OAAA;QAAK0C,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAEpD3C,OAAA;UAAMgD,QAAQ,EAAEhB,YAAa;UAACU,SAAS,EAAC,UAAU;UAAAC,QAAA,gBAChD3C,OAAA;YACEiD,IAAI,EAAC,MAAM;YACXC,WAAW,EAAC,0BAA0B;YACtCC,KAAK,EAAEvC,UAAW;YAClBwC,QAAQ,EAAGnB,CAAC,IAAKpB,aAAa,CAACoB,CAAC,CAACoB,MAAM,CAACF,KAAK,CAAE;YAC/CT,SAAS,EAAC;UAAuH;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClI,CAAC,eACF/C,OAAA;YAAK0C,SAAS,EAAC,6CAA6C;YAACY,IAAI,EAAC,MAAM;YAACC,OAAO,EAAC,WAAW;YAACC,MAAM,EAAC,cAAc;YAAAb,QAAA,eAChH3C,OAAA;cAAMyD,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAA6C;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGP/C,OAAA;UACEmD,KAAK,EAAErC,gBAAiB;UACxBsC,QAAQ,EAAGnB,CAAC,IAAKlB,mBAAmB,CAACkB,CAAC,CAACoB,MAAM,CAACF,KAAK,CAAE;UACrDT,SAAS,EAAC,0GAA0G;UAAAC,QAAA,gBAEpH3C,OAAA;YAAQmD,KAAK,EAAC,EAAE;YAAAR,QAAA,EAAC;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EAC9CzC,UAAU,CAACuD,GAAG,CAACC,QAAQ,iBACtB9D,OAAA;YAA0BmD,KAAK,EAAEW,QAAQ,CAACC,EAAG;YAAApB,QAAA,EAC1CmB,QAAQ,CAACxB;UAAI,GADHwB,QAAQ,CAACC,EAAE;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEhB,CACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN/C,OAAA;MAAK0C,SAAS,EAAC,wCAAwC;MAAAC,QAAA,eACrD3C,OAAA;QAAM0C,SAAS,EAAC,eAAe;QAAAC,QAAA,GAC5BvC,QAAQ,CAACe,MAAM,EAAC,UAAQ,EAACf,QAAQ,CAACe,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,EAAC,YAAO,EAACf,QAAQ,CAACe,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE;MAAA;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/F;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,EAGLvC,OAAO,gBACNR,OAAA;MAAK0C,SAAS,EAAC,qEAAqE;MAAAC,QAAA,EACjFqB,KAAK,CAAC,CAAC,CAAC,CAACV,IAAI,CAAC,CAAC,CAAC,CAACO,GAAG,CAAC,CAACI,CAAC,EAAEC,KAAK,kBAC7BlE,OAAA;QAAiB0C,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBAC7C3C,OAAA;UAAK0C,SAAS,EAAC;QAAkC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACxD/C,OAAA;UAAK0C,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B3C,OAAA;YAAK0C,SAAS,EAAC;UAA+B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACrD/C,OAAA;YAAK0C,SAAS,EAAC;UAA+B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACrD/C,OAAA;YAAK0C,SAAS,EAAC;UAA+B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC;MAAA,GANEmB,KAAK;QAAAtB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAOV,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,GACJrC,KAAK,gBACPV,OAAA;MAAK0C,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAChC3C,OAAA;QAAK0C,SAAS,EAAC,kEAAkE;QAAAC,QAAA,gBAC/E3C,OAAA;UAAK0C,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAChC3C,OAAA;YAAK0C,SAAS,EAAC,mBAAmB;YAACY,IAAI,EAAC,MAAM;YAACC,OAAO,EAAC,WAAW;YAACC,MAAM,EAAC,cAAc;YAAAb,QAAA,eACtF3C,OAAA;cAAMyD,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAAmD;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN/C,OAAA;UAAI0C,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjF/C,OAAA;UAAG0C,SAAS,EAAC,mBAAmB;UAAAC,QAAA,EAAEjC;QAAK;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5C/C,OAAA;UACEmE,OAAO,EAAEnD,aAAc;UACvB0B,SAAS,EAAC,+EAA+E;UAAAC,QAAA,EAC1F;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,GACJ3C,QAAQ,CAACe,MAAM,GAAG,CAAC,gBACrBnB,OAAA;MAAK0C,SAAS,EAAC,qEAAqE;MAAAC,QAAA,EACjFvC,QAAQ,CAACyD,GAAG,CAACxB,OAAO,iBACnBrC,OAAA,CAACF,WAAW;QAAkBuC,OAAO,EAAEA;MAAQ,GAA7BA,OAAO,CAAC0B,EAAE;QAAAnB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAqB,CAClD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,gBAEN/C,OAAA;MAAK0C,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAChC3C,OAAA;QAAK0C,SAAS,EAAC,6CAA6C;QAAAC,QAAA,gBAC1D3C,OAAA;UAAK0C,SAAS,EAAC,oBAAoB;UAAAC,QAAA,eACjC3C,OAAA;YAAK0C,SAAS,EAAC,mBAAmB;YAACY,IAAI,EAAC,MAAM;YAACC,OAAO,EAAC,WAAW;YAACC,MAAM,EAAC,cAAc;YAAAb,QAAA,eACtF3C,OAAA;cAAMyD,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAA0I;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/M;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN/C,OAAA;UAAI0C,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClF/C,OAAA;UAAG0C,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAC9B/B,UAAU,IAAIE,gBAAgB,GAC3B,0DAA0D,GAC1D;QAAqD;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAExD,CAAC,eACJ/C,OAAA;UAAG0C,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAErC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC7C,EAAA,CApLID,YAAY;EAAA,QACEL,OAAO;AAAA;AAAAwE,EAAA,GADrBnE,YAAY;AAsLlB,eAAeA,YAAY;AAAC,IAAAmE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}