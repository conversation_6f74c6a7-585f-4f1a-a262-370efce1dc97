<?php

namespace Database\Factories;

use App\Models\User;
use App\Models\Address;
use App\Models\Order;
use Illuminate\Database\Eloquent\Factories\Factory;

class OrderFactory extends Factory
{
    public function definition(): array
    {
        $subtotal = fake()->randomFloat(2, 50, 500);
        $tax = $subtotal * 0.15;
        $shipping = 15.00;
        $discount = fake()->optional(0.3)->randomFloat(2, 5, 50);
        $total = $subtotal + $tax + $shipping - ($discount ?? 0);

        return [
            'user_id' => User::factory(),
            'address_id' => Address::factory(),
            'order_number' => 'ORD-' . fake()->unique()->numerify('######'),
            'subtotal' => $subtotal,
            'tax' => $tax,
            'shipping_cost' => $shipping,
            'discount' => $discount,
            'total' => $total,
            'status' => fake()->randomElement([
                Order::STATUS_PENDING,
                Order::STATUS_PROCESSING,
                Order::STATUS_PREPARING,
                Order::STATUS_SHIPPED,
                Order::STATUS_DELIVERED
            ]),
            'payment_status' => fake()->randomElement([
                Order::PAYMENT_PENDING,
                Order::PAYMENT_PAID
            ]),
            'special_instructions' => fake()->optional()->sentence()
        ];
    }
}