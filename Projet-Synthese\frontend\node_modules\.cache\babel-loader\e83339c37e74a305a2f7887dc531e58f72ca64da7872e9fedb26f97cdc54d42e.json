{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Projet-Synthese\\\\frontend\\\\src\\\\pages\\\\Admin\\\\Products.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Products = () => {\n  _s();\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [currentProduct, setCurrentProduct] = useState(null);\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    price: '',\n    stock: '',\n    category_id: '',\n    is_grillable: false,\n    image: null\n  });\n  const [categories, setCategories] = useState([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n  useEffect(() => {\n    fetchProducts();\n    fetchCategories();\n  }, [currentPage, searchTerm]);\n  const fetchProducts = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get(`/api/admin/products?page=${currentPage}&search=${searchTerm}`);\n      setProducts(response.data.data);\n      setTotalPages(response.data.meta.last_page);\n    } catch (err) {\n      console.log('API non disponible, utilisation des données de test');\n\n      // Données de test pour les produits\n      const testProducts = [{\n        id: 1,\n        name: 'Poulet Grillé Entier',\n        description: 'Poulet entier grillé aux herbes de Provence',\n        price: 25.99,\n        stock: 50,\n        category_id: 1,\n        category: {\n          id: 1,\n          name: 'Produits Grillés'\n        },\n        is_grillable: true,\n        image_url: 'https://images.unsplash.com/photo-1598103442097-8b74394b95c6?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80'\n      }, {\n        id: 2,\n        name: 'Brochettes de Bœuf',\n        description: 'Brochettes de bœuf marinées et grillées',\n        price: 18.50,\n        stock: 30,\n        category_id: 1,\n        category: {\n          id: 1,\n          name: 'Produits Grillés'\n        },\n        is_grillable: true,\n        image_url: 'https://images.unsplash.com/photo-1529692236671-f1f6cf9683ba?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80'\n      }, {\n        id: 3,\n        name: 'Salade César',\n        description: 'Salade César classique avec croûtons',\n        price: 12.50,\n        stock: 40,\n        category_id: 2,\n        category: {\n          id: 2,\n          name: 'Salades'\n        },\n        is_grillable: false,\n        image_url: 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80'\n      }, {\n        id: 4,\n        name: 'Plateau de Fromages',\n        description: 'Sélection de fromages artisanaux',\n        price: 19.90,\n        stock: 15,\n        category_id: 3,\n        category: {\n          id: 3,\n          name: 'Fromages'\n        },\n        is_grillable: false,\n        image_url: 'https://images.unsplash.com/photo-1486297678162-eb2a19b0a32d?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80'\n      }, {\n        id: 5,\n        name: 'Jus d\\'Orange Frais',\n        description: 'Jus d\\'orange fraîchement pressé',\n        price: 4.50,\n        stock: 100,\n        category_id: 4,\n        category: {\n          id: 4,\n          name: 'Boissons'\n        },\n        is_grillable: false,\n        image_url: 'https://images.unsplash.com/photo-1613478223719-2ab802602423?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80'\n      }, {\n        id: 6,\n        name: 'Tiramisu Maison',\n        description: 'Tiramisu traditionnel fait maison',\n        price: 8.50,\n        stock: 25,\n        category_id: 5,\n        category: {\n          id: 5,\n          name: 'Desserts'\n        },\n        is_grillable: false,\n        image_url: 'https://images.unsplash.com/photo-1571877227200-a0d98ea607e9?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80'\n      }, {\n        id: 7,\n        name: 'Saumon Grillé',\n        description: 'Filet de saumon grillé au citron',\n        price: 22.00,\n        stock: 8,\n        category_id: 1,\n        category: {\n          id: 1,\n          name: 'Produits Grillés'\n        },\n        is_grillable: true,\n        image_url: 'https://images.unsplash.com/photo-1467003909585-2f8a72700288?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80'\n      }, {\n        id: 8,\n        name: 'Carpaccio de Bœuf',\n        description: 'Fines tranches de bœuf cru',\n        price: 16.00,\n        stock: 5,\n        category_id: 2,\n        category: {\n          id: 2,\n          name: 'Salades'\n        },\n        is_grillable: false,\n        image_url: 'https://images.unsplash.com/photo-1546833999-b9f581a1996d?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80'\n      }];\n\n      // Filtrer par terme de recherche si nécessaire\n      const filteredProducts = searchTerm ? testProducts.filter(p => p.name.toLowerCase().includes(searchTerm.toLowerCase())) : testProducts;\n      setProducts(filteredProducts);\n      setTotalPages(1);\n      setError(null);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchCategories = async () => {\n    try {\n      const response = await axios.get('/api/categories');\n      setCategories(response.data);\n    } catch (err) {\n      console.error('Erreur lors du chargement des catégories', err);\n    }\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value,\n      type,\n      checked,\n      files\n    } = e.target;\n    if (type === 'file') {\n      setFormData({\n        ...formData,\n        [name]: files[0]\n      });\n    } else if (type === 'checkbox') {\n      setFormData({\n        ...formData,\n        [name]: checked\n      });\n    } else {\n      setFormData({\n        ...formData,\n        [name]: value\n      });\n    }\n  };\n  const openModal = (product = null) => {\n    if (product) {\n      setCurrentProduct(product);\n      setFormData({\n        name: product.name,\n        description: product.description,\n        price: product.price,\n        stock: product.stock,\n        category_id: product.category_id,\n        is_grillable: product.is_grillable,\n        image: null\n      });\n    } else {\n      setCurrentProduct(null);\n      setFormData({\n        name: '',\n        description: '',\n        price: '',\n        stock: '',\n        category_id: '',\n        is_grillable: false,\n        image: null\n      });\n    }\n    setIsModalOpen(true);\n  };\n  const closeModal = () => {\n    setIsModalOpen(false);\n    setCurrentProduct(null);\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    const formDataToSend = new FormData();\n    Object.keys(formData).forEach(key => {\n      if (formData[key] !== null) {\n        formDataToSend.append(key, formData[key]);\n      }\n    });\n    try {\n      if (currentProduct) {\n        // Mise à jour d'un produit existant\n        await axios.post(`/api/admin/products/${currentProduct.id}`, formDataToSend, {\n          headers: {\n            'Content-Type': 'multipart/form-data'\n          }\n        });\n      } else {\n        // Création d'un nouveau produit\n        await axios.post('/api/admin/products', formDataToSend, {\n          headers: {\n            'Content-Type': 'multipart/form-data'\n          }\n        });\n      }\n      closeModal();\n      fetchProducts();\n    } catch (err) {\n      console.error('Erreur lors de l\\'enregistrement du produit', err);\n      alert('Une erreur est survenue lors de l\\'enregistrement du produit');\n    }\n  };\n  const handleDelete = async id => {\n    if (window.confirm('Êtes-vous sûr de vouloir supprimer ce produit ?')) {\n      try {\n        await axios.delete(`/api/admin/products/${id}`);\n        fetchProducts();\n      } catch (err) {\n        console.error('Erreur lors de la suppression du produit', err);\n        alert('Une erreur est survenue lors de la suppression du produit');\n      }\n    }\n  };\n  const handleSearch = e => {\n    setSearchTerm(e.target.value);\n    setCurrentPage(1);\n  };\n  if (loading && products.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-center items-center h-64\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-500\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 260,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 259,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container mx-auto px-4 py-8\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-2xl font-semibold\",\n        children: \"Gestion des produits\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => openModal(),\n        className: \"bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition\",\n        children: \"Ajouter un produit\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 267,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6\",\n      children: /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        placeholder: \"Rechercher un produit...\",\n        className: \"w-full md:w-1/3 px-4 py-2 border rounded-lg\",\n        value: searchTerm,\n        onChange: handleSearch\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 278,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 277,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4\",\n      role: \"alert\",\n      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n        className: \"font-bold\",\n        children: \"Erreur!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 289,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"block sm:inline\",\n        children: [\" \", error]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 290,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 288,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"overflow-x-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"min-w-full divide-y divide-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            className: \"bg-gray-50\",\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Image\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Nom\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Prix\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Stock\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Cat\\xE9gorie\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Grillable\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            className: \"bg-white divide-y divide-gray-200\",\n            children: products.map(product => /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"h-10 w-10 rounded-full overflow-hidden bg-gray-100\",\n                  children: product.image_url ? /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: product.image_url,\n                    alt: product.name,\n                    className: \"h-full w-full object-cover\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 314,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"h-full w-full flex items-center justify-center text-gray-400\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      className: \"h-6 w-6\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      stroke: \"currentColor\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 322,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 321,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 320,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 312,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                children: product.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 328,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                children: [product.price, \" \\u20AC\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 329,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${product.stock > 10 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`,\n                  children: product.stock\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 331,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 330,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                children: product.category ? product.category.name : 'Non catégorisé'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 337,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                children: product.is_grillable ? /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-green-600\",\n                  children: \"Oui\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 342,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-600\",\n                  children: \"Non\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 344,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 340,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => openModal(product),\n                  className: \"text-indigo-600 hover:text-indigo-900 mr-3\",\n                  children: \"Modifier\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 348,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleDelete(product.id),\n                  className: \"text-red-600 hover:text-red-900\",\n                  children: \"Supprimer\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 354,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 347,\n                columnNumber: 19\n              }, this)]\n            }, product.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 295,\n        columnNumber: 9\n      }, this), totalPages > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-6 py-4 bg-gray-50 border-t border-gray-200 flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setCurrentPage(prev => Math.max(prev - 1, 1)),\n          disabled: currentPage === 1,\n          className: `px-3 py-1 rounded ${currentPage === 1 ? 'bg-gray-200 text-gray-500 cursor-not-allowed' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`,\n          children: \"Pr\\xE9c\\xE9dent\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 370,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-sm text-gray-700\",\n          children: [\"Page \", currentPage, \" sur \", totalPages]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 381,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setCurrentPage(prev => Math.min(prev + 1, totalPages)),\n          disabled: currentPage === totalPages,\n          className: `px-3 py-1 rounded ${currentPage === totalPages ? 'bg-gray-200 text-gray-500 cursor-not-allowed' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`,\n          children: \"Suivant\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 384,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 369,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 294,\n      columnNumber: 7\n    }, this), isModalOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-lg w-full max-w-md mx-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-6 py-4 border-b\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold\",\n            children: currentProduct ? 'Modifier le produit' : 'Ajouter un produit'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 404,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 403,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"name\",\n                className: \"block text-gray-700 mb-2\",\n                children: \"Nom\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 411,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"name\",\n                name: \"name\",\n                value: formData.name,\n                onChange: handleInputChange,\n                className: \"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 412,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 410,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"description\",\n                className: \"block text-gray-700 mb-2\",\n                children: \"Description\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 424,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                id: \"description\",\n                name: \"description\",\n                value: formData.description,\n                onChange: handleInputChange,\n                className: \"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\",\n                rows: \"3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 425,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 423,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-2 gap-4 mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"price\",\n                  className: \"block text-gray-700 mb-2\",\n                  children: \"Prix (\\u20AC)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 437,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  id: \"price\",\n                  name: \"price\",\n                  value: formData.price,\n                  onChange: handleInputChange,\n                  className: \"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\",\n                  step: \"0.01\",\n                  min: \"0\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 438,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 436,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"stock\",\n                  className: \"block text-gray-700 mb-2\",\n                  children: \"Stock\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 452,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  id: \"stock\",\n                  name: \"stock\",\n                  value: formData.stock,\n                  onChange: handleInputChange,\n                  className: \"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\",\n                  min: \"0\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 453,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 451,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 435,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"category_id\",\n                className: \"block text-gray-700 mb-2\",\n                children: \"Cat\\xE9gorie\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 467,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                id: \"category_id\",\n                name: \"category_id\",\n                value: formData.category_id,\n                onChange: handleInputChange,\n                className: \"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\",\n                required: true,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"S\\xE9lectionner une cat\\xE9gorie\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 476,\n                  columnNumber: 21\n                }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: category.id,\n                  children: category.name\n                }, category.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 478,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 468,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 466,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"image\",\n                className: \"block text-gray-700 mb-2\",\n                children: \"Image\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 486,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"file\",\n                id: \"image\",\n                name: \"image\",\n                onChange: handleInputChange,\n                className: \"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\",\n                accept: \"image/*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 487,\n                columnNumber: 19\n              }, this), currentProduct && currentProduct.image_url && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-500\",\n                  children: \"Image actuelle:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 497,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"h-20 w-20 mt-1 rounded overflow-hidden bg-gray-100\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: currentProduct.image_url,\n                    alt: currentProduct.name,\n                    className: \"h-full w-full object-cover\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 499,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 498,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 496,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 485,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  id: \"is_grillable\",\n                  name: \"is_grillable\",\n                  checked: formData.is_grillable,\n                  onChange: handleInputChange,\n                  className: \"h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 511,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"is_grillable\",\n                  className: \"ml-2 block text-gray-700\",\n                  children: \"Produit grillable\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 519,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 510,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 509,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 409,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"px-6 py-4 bg-gray-50 border-t flex justify-end\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: closeModal,\n              className: \"px-4 py-2 border rounded-lg text-gray-700 mr-2 hover:bg-gray-100\",\n              children: \"Annuler\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 527,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700\",\n              children: currentProduct ? 'Mettre à jour' : 'Ajouter'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 534,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 526,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 408,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 402,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 401,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 266,\n    columnNumber: 5\n  }, this);\n};\n_s(Products, \"GBKTPNhTLcZKGYnXUR1YPAm0ijA=\");\n_c = Products;\nexport default Products;\nvar _c;\n$RefreshReg$(_c, \"Products\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "jsxDEV", "_jsxDEV", "Products", "_s", "products", "setProducts", "loading", "setLoading", "error", "setError", "currentProduct", "setCurrentProduct", "isModalOpen", "setIsModalOpen", "formData", "setFormData", "name", "description", "price", "stock", "category_id", "is_grillable", "image", "categories", "setCategories", "searchTerm", "setSearchTerm", "currentPage", "setCurrentPage", "totalPages", "setTotalPages", "fetchProducts", "fetchCategories", "response", "get", "data", "meta", "last_page", "err", "console", "log", "testProducts", "id", "category", "image_url", "filteredProducts", "filter", "p", "toLowerCase", "includes", "handleInputChange", "e", "value", "type", "checked", "files", "target", "openModal", "product", "closeModal", "handleSubmit", "preventDefault", "formDataToSend", "FormData", "Object", "keys", "for<PERSON>ach", "key", "append", "post", "headers", "alert", "handleDelete", "window", "confirm", "delete", "handleSearch", "length", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "placeholder", "onChange", "role", "map", "src", "alt", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "prev", "Math", "max", "disabled", "min", "onSubmit", "htmlFor", "required", "rows", "step", "accept", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet-Synthese/frontend/src/pages/Admin/Products.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\n\nconst Products = () => {\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [currentProduct, setCurrentProduct] = useState(null);\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    price: '',\n    stock: '',\n    category_id: '',\n    is_grillable: false,\n    image: null\n  });\n  const [categories, setCategories] = useState([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n\n  useEffect(() => {\n    fetchProducts();\n    fetchCategories();\n  }, [currentPage, searchTerm]);\n\n  const fetchProducts = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get(`/api/admin/products?page=${currentPage}&search=${searchTerm}`);\n      setProducts(response.data.data);\n      setTotalPages(response.data.meta.last_page);\n    } catch (err) {\n      console.log('API non disponible, utilisation des données de test');\n\n      // Données de test pour les produits\n      const testProducts = [\n        {\n          id: 1,\n          name: 'Poulet Grillé Entier',\n          description: 'Poulet entier grillé aux herbes de Provence',\n          price: 25.99,\n          stock: 50,\n          category_id: 1,\n          category: { id: 1, name: 'Produits Grillés' },\n          is_grillable: true,\n          image_url: 'https://images.unsplash.com/photo-1598103442097-8b74394b95c6?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80'\n        },\n        {\n          id: 2,\n          name: 'Brochettes de Bœuf',\n          description: 'Brochettes de bœuf marinées et grillées',\n          price: 18.50,\n          stock: 30,\n          category_id: 1,\n          category: { id: 1, name: 'Produits Grillés' },\n          is_grillable: true,\n          image_url: 'https://images.unsplash.com/photo-1529692236671-f1f6cf9683ba?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80'\n        },\n        {\n          id: 3,\n          name: 'Salade César',\n          description: 'Salade César classique avec croûtons',\n          price: 12.50,\n          stock: 40,\n          category_id: 2,\n          category: { id: 2, name: 'Salades' },\n          is_grillable: false,\n          image_url: 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80'\n        },\n        {\n          id: 4,\n          name: 'Plateau de Fromages',\n          description: 'Sélection de fromages artisanaux',\n          price: 19.90,\n          stock: 15,\n          category_id: 3,\n          category: { id: 3, name: 'Fromages' },\n          is_grillable: false,\n          image_url: 'https://images.unsplash.com/photo-1486297678162-eb2a19b0a32d?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80'\n        },\n        {\n          id: 5,\n          name: 'Jus d\\'Orange Frais',\n          description: 'Jus d\\'orange fraîchement pressé',\n          price: 4.50,\n          stock: 100,\n          category_id: 4,\n          category: { id: 4, name: 'Boissons' },\n          is_grillable: false,\n          image_url: 'https://images.unsplash.com/photo-1613478223719-2ab802602423?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80'\n        },\n        {\n          id: 6,\n          name: 'Tiramisu Maison',\n          description: 'Tiramisu traditionnel fait maison',\n          price: 8.50,\n          stock: 25,\n          category_id: 5,\n          category: { id: 5, name: 'Desserts' },\n          is_grillable: false,\n          image_url: 'https://images.unsplash.com/photo-1571877227200-a0d98ea607e9?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80'\n        },\n        {\n          id: 7,\n          name: 'Saumon Grillé',\n          description: 'Filet de saumon grillé au citron',\n          price: 22.00,\n          stock: 8,\n          category_id: 1,\n          category: { id: 1, name: 'Produits Grillés' },\n          is_grillable: true,\n          image_url: 'https://images.unsplash.com/photo-1467003909585-2f8a72700288?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80'\n        },\n        {\n          id: 8,\n          name: 'Carpaccio de Bœuf',\n          description: 'Fines tranches de bœuf cru',\n          price: 16.00,\n          stock: 5,\n          category_id: 2,\n          category: { id: 2, name: 'Salades' },\n          is_grillable: false,\n          image_url: 'https://images.unsplash.com/photo-1546833999-b9f581a1996d?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80'\n        }\n      ];\n\n      // Filtrer par terme de recherche si nécessaire\n      const filteredProducts = searchTerm\n        ? testProducts.filter(p => p.name.toLowerCase().includes(searchTerm.toLowerCase()))\n        : testProducts;\n\n      setProducts(filteredProducts);\n      setTotalPages(1);\n      setError(null);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchCategories = async () => {\n    try {\n      const response = await axios.get('/api/categories');\n      setCategories(response.data);\n    } catch (err) {\n      console.error('Erreur lors du chargement des catégories', err);\n    }\n  };\n\n  const handleInputChange = (e) => {\n    const { name, value, type, checked, files } = e.target;\n    \n    if (type === 'file') {\n      setFormData({\n        ...formData,\n        [name]: files[0]\n      });\n    } else if (type === 'checkbox') {\n      setFormData({\n        ...formData,\n        [name]: checked\n      });\n    } else {\n      setFormData({\n        ...formData,\n        [name]: value\n      });\n    }\n  };\n\n  const openModal = (product = null) => {\n    if (product) {\n      setCurrentProduct(product);\n      setFormData({\n        name: product.name,\n        description: product.description,\n        price: product.price,\n        stock: product.stock,\n        category_id: product.category_id,\n        is_grillable: product.is_grillable,\n        image: null\n      });\n    } else {\n      setCurrentProduct(null);\n      setFormData({\n        name: '',\n        description: '',\n        price: '',\n        stock: '',\n        category_id: '',\n        is_grillable: false,\n        image: null\n      });\n    }\n    setIsModalOpen(true);\n  };\n\n  const closeModal = () => {\n    setIsModalOpen(false);\n    setCurrentProduct(null);\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    const formDataToSend = new FormData();\n    Object.keys(formData).forEach(key => {\n      if (formData[key] !== null) {\n        formDataToSend.append(key, formData[key]);\n      }\n    });\n    \n    try {\n      if (currentProduct) {\n        // Mise à jour d'un produit existant\n        await axios.post(`/api/admin/products/${currentProduct.id}`, formDataToSend, {\n          headers: {\n            'Content-Type': 'multipart/form-data'\n          }\n        });\n      } else {\n        // Création d'un nouveau produit\n        await axios.post('/api/admin/products', formDataToSend, {\n          headers: {\n            'Content-Type': 'multipart/form-data'\n          }\n        });\n      }\n      \n      closeModal();\n      fetchProducts();\n    } catch (err) {\n      console.error('Erreur lors de l\\'enregistrement du produit', err);\n      alert('Une erreur est survenue lors de l\\'enregistrement du produit');\n    }\n  };\n\n  const handleDelete = async (id) => {\n    if (window.confirm('Êtes-vous sûr de vouloir supprimer ce produit ?')) {\n      try {\n        await axios.delete(`/api/admin/products/${id}`);\n        fetchProducts();\n      } catch (err) {\n        console.error('Erreur lors de la suppression du produit', err);\n        alert('Une erreur est survenue lors de la suppression du produit');\n      }\n    }\n  };\n\n  const handleSearch = (e) => {\n    setSearchTerm(e.target.value);\n    setCurrentPage(1);\n  };\n\n  if (loading && products.length === 0) {\n    return (\n      <div className=\"flex justify-center items-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-500\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"container mx-auto px-4 py-8\">\n      <div className=\"flex justify-between items-center mb-6\">\n        <h1 className=\"text-2xl font-semibold\">Gestion des produits</h1>\n        <button\n          onClick={() => openModal()}\n          className=\"bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition\"\n        >\n          Ajouter un produit\n        </button>\n      </div>\n\n      <div className=\"mb-6\">\n        <input\n          type=\"text\"\n          placeholder=\"Rechercher un produit...\"\n          className=\"w-full md:w-1/3 px-4 py-2 border rounded-lg\"\n          value={searchTerm}\n          onChange={handleSearch}\n        />\n      </div>\n\n      {error && (\n        <div className=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4\" role=\"alert\">\n          <strong className=\"font-bold\">Erreur!</strong>\n          <span className=\"block sm:inline\"> {error}</span>\n        </div>\n      )}\n\n      <div className=\"bg-white rounded-lg shadow overflow-hidden\">\n        <div className=\"overflow-x-auto\">\n          <table className=\"min-w-full divide-y divide-gray-200\">\n            <thead className=\"bg-gray-50\">\n              <tr>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Image</th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Nom</th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Prix</th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Stock</th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Catégorie</th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Grillable</th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Actions</th>\n              </tr>\n            </thead>\n            <tbody className=\"bg-white divide-y divide-gray-200\">\n              {products.map((product) => (\n                <tr key={product.id}>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"h-10 w-10 rounded-full overflow-hidden bg-gray-100\">\n                      {product.image_url ? (\n                        <img\n                          src={product.image_url}\n                          alt={product.name}\n                          className=\"h-full w-full object-cover\"\n                        />\n                      ) : (\n                        <div className=\"h-full w-full flex items-center justify-center text-gray-400\">\n                          <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n                          </svg>\n                        </div>\n                      )}\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">{product.name}</td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">{product.price} €</td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${\n                      product.stock > 10 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'\n                    }`}>\n                      {product.stock}\n                    </span>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                    {product.category ? product.category.name : 'Non catégorisé'}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                    {product.is_grillable ? (\n                      <span className=\"text-green-600\">Oui</span>\n                    ) : (\n                      <span className=\"text-red-600\">Non</span>\n                    )}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                    <button\n                      onClick={() => openModal(product)}\n                      className=\"text-indigo-600 hover:text-indigo-900 mr-3\"\n                    >\n                      Modifier\n                    </button>\n                    <button\n                      onClick={() => handleDelete(product.id)}\n                      className=\"text-red-600 hover:text-red-900\"\n                    >\n                      Supprimer\n                    </button>\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n        \n        {/* Pagination */}\n        {totalPages > 1 && (\n          <div className=\"px-6 py-4 bg-gray-50 border-t border-gray-200 flex items-center justify-between\">\n            <button\n              onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}\n              disabled={currentPage === 1}\n              className={`px-3 py-1 rounded ${\n                currentPage === 1\n                  ? 'bg-gray-200 text-gray-500 cursor-not-allowed'\n                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'\n              }`}\n            >\n              Précédent\n            </button>\n            <span className=\"text-sm text-gray-700\">\n              Page {currentPage} sur {totalPages}\n            </span>\n            <button\n              onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}\n              disabled={currentPage === totalPages}\n              className={`px-3 py-1 rounded ${\n                currentPage === totalPages\n                  ? 'bg-gray-200 text-gray-500 cursor-not-allowed'\n                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'\n              }`}\n            >\n              Suivant\n            </button>\n          </div>\n        )}\n      </div>\n\n      {/* Modal pour ajouter/modifier un produit */}\n      {isModalOpen && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n          <div className=\"bg-white rounded-lg shadow-lg w-full max-w-md mx-4\">\n            <div className=\"px-6 py-4 border-b\">\n              <h3 className=\"text-lg font-semibold\">\n                {currentProduct ? 'Modifier le produit' : 'Ajouter un produit'}\n              </h3>\n            </div>\n            <form onSubmit={handleSubmit}>\n              <div className=\"p-6\">\n                <div className=\"mb-4\">\n                  <label htmlFor=\"name\" className=\"block text-gray-700 mb-2\">Nom</label>\n                  <input\n                    type=\"text\"\n                    id=\"name\"\n                    name=\"name\"\n                    value={formData.name}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n                    required\n                  />\n                </div>\n                \n                <div className=\"mb-4\">\n                  <label htmlFor=\"description\" className=\"block text-gray-700 mb-2\">Description</label>\n                  <textarea\n                    id=\"description\"\n                    name=\"description\"\n                    value={formData.description}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n                    rows=\"3\"\n                  ></textarea>\n                </div>\n                \n                <div className=\"grid grid-cols-2 gap-4 mb-4\">\n                  <div>\n                    <label htmlFor=\"price\" className=\"block text-gray-700 mb-2\">Prix (€)</label>\n                    <input\n                      type=\"number\"\n                      id=\"price\"\n                      name=\"price\"\n                      value={formData.price}\n                      onChange={handleInputChange}\n                      className=\"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n                      step=\"0.01\"\n                      min=\"0\"\n                      required\n                    />\n                  </div>\n                  \n                  <div>\n                    <label htmlFor=\"stock\" className=\"block text-gray-700 mb-2\">Stock</label>\n                    <input\n                      type=\"number\"\n                      id=\"stock\"\n                      name=\"stock\"\n                      value={formData.stock}\n                      onChange={handleInputChange}\n                      className=\"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n                      min=\"0\"\n                      required\n                    />\n                  </div>\n                </div>\n                \n                <div className=\"mb-4\">\n                  <label htmlFor=\"category_id\" className=\"block text-gray-700 mb-2\">Catégorie</label>\n                  <select\n                    id=\"category_id\"\n                    name=\"category_id\"\n                    value={formData.category_id}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n                    required\n                  >\n                    <option value=\"\">Sélectionner une catégorie</option>\n                    {categories.map(category => (\n                      <option key={category.id} value={category.id}>\n                        {category.name}\n                      </option>\n                    ))}\n                  </select>\n                </div>\n                \n                <div className=\"mb-4\">\n                  <label htmlFor=\"image\" className=\"block text-gray-700 mb-2\">Image</label>\n                  <input\n                    type=\"file\"\n                    id=\"image\"\n                    name=\"image\"\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n                    accept=\"image/*\"\n                  />\n                  {currentProduct && currentProduct.image_url && (\n                    <div className=\"mt-2\">\n                      <p className=\"text-sm text-gray-500\">Image actuelle:</p>\n                      <div className=\"h-20 w-20 mt-1 rounded overflow-hidden bg-gray-100\">\n                        <img\n                          src={currentProduct.image_url}\n                          alt={currentProduct.name}\n                          className=\"h-full w-full object-cover\"\n                        />\n                      </div>\n                    </div>\n                  )}\n                </div>\n                \n                <div className=\"mb-4\">\n                  <div className=\"flex items-center\">\n                    <input\n                      type=\"checkbox\"\n                      id=\"is_grillable\"\n                      name=\"is_grillable\"\n                      checked={formData.is_grillable}\n                      onChange={handleInputChange}\n                      className=\"h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded\"\n                    />\n                    <label htmlFor=\"is_grillable\" className=\"ml-2 block text-gray-700\">\n                      Produit grillable\n                    </label>\n                  </div>\n                </div>\n              </div>\n              \n              <div className=\"px-6 py-4 bg-gray-50 border-t flex justify-end\">\n                <button\n                  type=\"button\"\n                  onClick={closeModal}\n                  className=\"px-4 py-2 border rounded-lg text-gray-700 mr-2 hover:bg-gray-100\"\n                >\n                  Annuler\n                </button>\n                <button\n                  type=\"submit\"\n                  className=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700\"\n                >\n                  {currentProduct ? 'Mettre à jour' : 'Ajouter'}\n                </button>\n              </div>\n            </form>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default Products;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACS,OAAO,EAAEC,UAAU,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACW,KAAK,EAAEC,QAAQ,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACa,cAAc,EAAEC,iBAAiB,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACe,WAAW,EAAEC,cAAc,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACiB,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CAAC;IACvCmB,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,YAAY,EAAE,KAAK;IACnBC,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC4B,UAAU,EAAEC,aAAa,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC8B,WAAW,EAAEC,cAAc,CAAC,GAAG/B,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACgC,UAAU,EAAEC,aAAa,CAAC,GAAGjC,QAAQ,CAAC,CAAC,CAAC;EAE/CC,SAAS,CAAC,MAAM;IACdiC,aAAa,CAAC,CAAC;IACfC,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAACL,WAAW,EAAEF,UAAU,CAAC,CAAC;EAE7B,MAAMM,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACFxB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM0B,QAAQ,GAAG,MAAMlC,KAAK,CAACmC,GAAG,CAAC,4BAA4BP,WAAW,WAAWF,UAAU,EAAE,CAAC;MAChGpB,WAAW,CAAC4B,QAAQ,CAACE,IAAI,CAACA,IAAI,CAAC;MAC/BL,aAAa,CAACG,QAAQ,CAACE,IAAI,CAACC,IAAI,CAACC,SAAS,CAAC;IAC7C,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZC,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;;MAElE;MACA,MAAMC,YAAY,GAAG,CACnB;QACEC,EAAE,EAAE,CAAC;QACL1B,IAAI,EAAE,sBAAsB;QAC5BC,WAAW,EAAE,6CAA6C;QAC1DC,KAAK,EAAE,KAAK;QACZC,KAAK,EAAE,EAAE;QACTC,WAAW,EAAE,CAAC;QACduB,QAAQ,EAAE;UAAED,EAAE,EAAE,CAAC;UAAE1B,IAAI,EAAE;QAAmB,CAAC;QAC7CK,YAAY,EAAE,IAAI;QAClBuB,SAAS,EAAE;MACb,CAAC,EACD;QACEF,EAAE,EAAE,CAAC;QACL1B,IAAI,EAAE,oBAAoB;QAC1BC,WAAW,EAAE,yCAAyC;QACtDC,KAAK,EAAE,KAAK;QACZC,KAAK,EAAE,EAAE;QACTC,WAAW,EAAE,CAAC;QACduB,QAAQ,EAAE;UAAED,EAAE,EAAE,CAAC;UAAE1B,IAAI,EAAE;QAAmB,CAAC;QAC7CK,YAAY,EAAE,IAAI;QAClBuB,SAAS,EAAE;MACb,CAAC,EACD;QACEF,EAAE,EAAE,CAAC;QACL1B,IAAI,EAAE,cAAc;QACpBC,WAAW,EAAE,sCAAsC;QACnDC,KAAK,EAAE,KAAK;QACZC,KAAK,EAAE,EAAE;QACTC,WAAW,EAAE,CAAC;QACduB,QAAQ,EAAE;UAAED,EAAE,EAAE,CAAC;UAAE1B,IAAI,EAAE;QAAU,CAAC;QACpCK,YAAY,EAAE,KAAK;QACnBuB,SAAS,EAAE;MACb,CAAC,EACD;QACEF,EAAE,EAAE,CAAC;QACL1B,IAAI,EAAE,qBAAqB;QAC3BC,WAAW,EAAE,kCAAkC;QAC/CC,KAAK,EAAE,KAAK;QACZC,KAAK,EAAE,EAAE;QACTC,WAAW,EAAE,CAAC;QACduB,QAAQ,EAAE;UAAED,EAAE,EAAE,CAAC;UAAE1B,IAAI,EAAE;QAAW,CAAC;QACrCK,YAAY,EAAE,KAAK;QACnBuB,SAAS,EAAE;MACb,CAAC,EACD;QACEF,EAAE,EAAE,CAAC;QACL1B,IAAI,EAAE,qBAAqB;QAC3BC,WAAW,EAAE,kCAAkC;QAC/CC,KAAK,EAAE,IAAI;QACXC,KAAK,EAAE,GAAG;QACVC,WAAW,EAAE,CAAC;QACduB,QAAQ,EAAE;UAAED,EAAE,EAAE,CAAC;UAAE1B,IAAI,EAAE;QAAW,CAAC;QACrCK,YAAY,EAAE,KAAK;QACnBuB,SAAS,EAAE;MACb,CAAC,EACD;QACEF,EAAE,EAAE,CAAC;QACL1B,IAAI,EAAE,iBAAiB;QACvBC,WAAW,EAAE,mCAAmC;QAChDC,KAAK,EAAE,IAAI;QACXC,KAAK,EAAE,EAAE;QACTC,WAAW,EAAE,CAAC;QACduB,QAAQ,EAAE;UAAED,EAAE,EAAE,CAAC;UAAE1B,IAAI,EAAE;QAAW,CAAC;QACrCK,YAAY,EAAE,KAAK;QACnBuB,SAAS,EAAE;MACb,CAAC,EACD;QACEF,EAAE,EAAE,CAAC;QACL1B,IAAI,EAAE,eAAe;QACrBC,WAAW,EAAE,kCAAkC;QAC/CC,KAAK,EAAE,KAAK;QACZC,KAAK,EAAE,CAAC;QACRC,WAAW,EAAE,CAAC;QACduB,QAAQ,EAAE;UAAED,EAAE,EAAE,CAAC;UAAE1B,IAAI,EAAE;QAAmB,CAAC;QAC7CK,YAAY,EAAE,IAAI;QAClBuB,SAAS,EAAE;MACb,CAAC,EACD;QACEF,EAAE,EAAE,CAAC;QACL1B,IAAI,EAAE,mBAAmB;QACzBC,WAAW,EAAE,4BAA4B;QACzCC,KAAK,EAAE,KAAK;QACZC,KAAK,EAAE,CAAC;QACRC,WAAW,EAAE,CAAC;QACduB,QAAQ,EAAE;UAAED,EAAE,EAAE,CAAC;UAAE1B,IAAI,EAAE;QAAU,CAAC;QACpCK,YAAY,EAAE,KAAK;QACnBuB,SAAS,EAAE;MACb,CAAC,CACF;;MAED;MACA,MAAMC,gBAAgB,GAAGpB,UAAU,GAC/BgB,YAAY,CAACK,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC/B,IAAI,CAACgC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACxB,UAAU,CAACuB,WAAW,CAAC,CAAC,CAAC,CAAC,GACjFP,YAAY;MAEhBpC,WAAW,CAACwC,gBAAgB,CAAC;MAC7Bf,aAAa,CAAC,CAAC,CAAC;MAChBrB,QAAQ,CAAC,IAAI,CAAC;IAChB,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMyB,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMlC,KAAK,CAACmC,GAAG,CAAC,iBAAiB,CAAC;MACnDV,aAAa,CAACS,QAAQ,CAACE,IAAI,CAAC;IAC9B,CAAC,CAAC,OAAOG,GAAG,EAAE;MACZC,OAAO,CAAC/B,KAAK,CAAC,0CAA0C,EAAE8B,GAAG,CAAC;IAChE;EACF,CAAC;EAED,MAAMY,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEnC,IAAI;MAAEoC,KAAK;MAAEC,IAAI;MAAEC,OAAO;MAAEC;IAAM,CAAC,GAAGJ,CAAC,CAACK,MAAM;IAEtD,IAAIH,IAAI,KAAK,MAAM,EAAE;MACnBtC,WAAW,CAAC;QACV,GAAGD,QAAQ;QACX,CAACE,IAAI,GAAGuC,KAAK,CAAC,CAAC;MACjB,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIF,IAAI,KAAK,UAAU,EAAE;MAC9BtC,WAAW,CAAC;QACV,GAAGD,QAAQ;QACX,CAACE,IAAI,GAAGsC;MACV,CAAC,CAAC;IACJ,CAAC,MAAM;MACLvC,WAAW,CAAC;QACV,GAAGD,QAAQ;QACX,CAACE,IAAI,GAAGoC;MACV,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMK,SAAS,GAAGA,CAACC,OAAO,GAAG,IAAI,KAAK;IACpC,IAAIA,OAAO,EAAE;MACX/C,iBAAiB,CAAC+C,OAAO,CAAC;MAC1B3C,WAAW,CAAC;QACVC,IAAI,EAAE0C,OAAO,CAAC1C,IAAI;QAClBC,WAAW,EAAEyC,OAAO,CAACzC,WAAW;QAChCC,KAAK,EAAEwC,OAAO,CAACxC,KAAK;QACpBC,KAAK,EAAEuC,OAAO,CAACvC,KAAK;QACpBC,WAAW,EAAEsC,OAAO,CAACtC,WAAW;QAChCC,YAAY,EAAEqC,OAAO,CAACrC,YAAY;QAClCC,KAAK,EAAE;MACT,CAAC,CAAC;IACJ,CAAC,MAAM;MACLX,iBAAiB,CAAC,IAAI,CAAC;MACvBI,WAAW,CAAC;QACVC,IAAI,EAAE,EAAE;QACRC,WAAW,EAAE,EAAE;QACfC,KAAK,EAAE,EAAE;QACTC,KAAK,EAAE,EAAE;QACTC,WAAW,EAAE,EAAE;QACfC,YAAY,EAAE,KAAK;QACnBC,KAAK,EAAE;MACT,CAAC,CAAC;IACJ;IACAT,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EAED,MAAM8C,UAAU,GAAGA,CAAA,KAAM;IACvB9C,cAAc,CAAC,KAAK,CAAC;IACrBF,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMiD,YAAY,GAAG,MAAOT,CAAC,IAAK;IAChCA,CAAC,CAACU,cAAc,CAAC,CAAC;IAElB,MAAMC,cAAc,GAAG,IAAIC,QAAQ,CAAC,CAAC;IACrCC,MAAM,CAACC,IAAI,CAACnD,QAAQ,CAAC,CAACoD,OAAO,CAACC,GAAG,IAAI;MACnC,IAAIrD,QAAQ,CAACqD,GAAG,CAAC,KAAK,IAAI,EAAE;QAC1BL,cAAc,CAACM,MAAM,CAACD,GAAG,EAAErD,QAAQ,CAACqD,GAAG,CAAC,CAAC;MAC3C;IACF,CAAC,CAAC;IAEF,IAAI;MACF,IAAIzD,cAAc,EAAE;QAClB;QACA,MAAMX,KAAK,CAACsE,IAAI,CAAC,uBAAuB3D,cAAc,CAACgC,EAAE,EAAE,EAAEoB,cAAc,EAAE;UAC3EQ,OAAO,EAAE;YACP,cAAc,EAAE;UAClB;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA,MAAMvE,KAAK,CAACsE,IAAI,CAAC,qBAAqB,EAAEP,cAAc,EAAE;UACtDQ,OAAO,EAAE;YACP,cAAc,EAAE;UAClB;QACF,CAAC,CAAC;MACJ;MAEAX,UAAU,CAAC,CAAC;MACZ5B,aAAa,CAAC,CAAC;IACjB,CAAC,CAAC,OAAOO,GAAG,EAAE;MACZC,OAAO,CAAC/B,KAAK,CAAC,6CAA6C,EAAE8B,GAAG,CAAC;MACjEiC,KAAK,CAAC,8DAA8D,CAAC;IACvE;EACF,CAAC;EAED,MAAMC,YAAY,GAAG,MAAO9B,EAAE,IAAK;IACjC,IAAI+B,MAAM,CAACC,OAAO,CAAC,iDAAiD,CAAC,EAAE;MACrE,IAAI;QACF,MAAM3E,KAAK,CAAC4E,MAAM,CAAC,uBAAuBjC,EAAE,EAAE,CAAC;QAC/CX,aAAa,CAAC,CAAC;MACjB,CAAC,CAAC,OAAOO,GAAG,EAAE;QACZC,OAAO,CAAC/B,KAAK,CAAC,0CAA0C,EAAE8B,GAAG,CAAC;QAC9DiC,KAAK,CAAC,2DAA2D,CAAC;MACpE;IACF;EACF,CAAC;EAED,MAAMK,YAAY,GAAIzB,CAAC,IAAK;IAC1BzB,aAAa,CAACyB,CAAC,CAACK,MAAM,CAACJ,KAAK,CAAC;IAC7BxB,cAAc,CAAC,CAAC,CAAC;EACnB,CAAC;EAED,IAAItB,OAAO,IAAIF,QAAQ,CAACyE,MAAM,KAAK,CAAC,EAAE;IACpC,oBACE5E,OAAA;MAAK6E,SAAS,EAAC,uCAAuC;MAAAC,QAAA,eACpD9E,OAAA;QAAK6E,SAAS,EAAC;MAA4E;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/F,CAAC;EAEV;EAEA,oBACElF,OAAA;IAAK6E,SAAS,EAAC,6BAA6B;IAAAC,QAAA,gBAC1C9E,OAAA;MAAK6E,SAAS,EAAC,wCAAwC;MAAAC,QAAA,gBACrD9E,OAAA;QAAI6E,SAAS,EAAC,wBAAwB;QAAAC,QAAA,EAAC;MAAoB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAChElF,OAAA;QACEmF,OAAO,EAAEA,CAAA,KAAM3B,SAAS,CAAC,CAAE;QAC3BqB,SAAS,EAAC,yEAAyE;QAAAC,QAAA,EACpF;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENlF,OAAA;MAAK6E,SAAS,EAAC,MAAM;MAAAC,QAAA,eACnB9E,OAAA;QACEoD,IAAI,EAAC,MAAM;QACXgC,WAAW,EAAC,0BAA0B;QACtCP,SAAS,EAAC,6CAA6C;QACvD1B,KAAK,EAAE3B,UAAW;QAClB6D,QAAQ,EAAEV;MAAa;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAEL3E,KAAK,iBACJP,OAAA;MAAK6E,SAAS,EAAC,+EAA+E;MAACS,IAAI,EAAC,OAAO;MAAAR,QAAA,gBACzG9E,OAAA;QAAQ6E,SAAS,EAAC,WAAW;QAAAC,QAAA,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC9ClF,OAAA;QAAM6E,SAAS,EAAC,iBAAiB;QAAAC,QAAA,GAAC,GAAC,EAACvE,KAAK;MAAA;QAAAwE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9C,CACN,eAEDlF,OAAA;MAAK6E,SAAS,EAAC,4CAA4C;MAAAC,QAAA,gBACzD9E,OAAA;QAAK6E,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9B9E,OAAA;UAAO6E,SAAS,EAAC,qCAAqC;UAAAC,QAAA,gBACpD9E,OAAA;YAAO6E,SAAS,EAAC,YAAY;YAAAC,QAAA,eAC3B9E,OAAA;cAAA8E,QAAA,gBACE9E,OAAA;gBAAI6E,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzGlF,OAAA;gBAAI6E,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvGlF,OAAA;gBAAI6E,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxGlF,OAAA;gBAAI6E,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzGlF,OAAA;gBAAI6E,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7GlF,OAAA;gBAAI6E,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7GlF,OAAA;gBAAI6E,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRlF,OAAA;YAAO6E,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EACjD3E,QAAQ,CAACoF,GAAG,CAAE9B,OAAO,iBACpBzD,OAAA;cAAA8E,QAAA,gBACE9E,OAAA;gBAAI6E,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,eACzC9E,OAAA;kBAAK6E,SAAS,EAAC,oDAAoD;kBAAAC,QAAA,EAChErB,OAAO,CAACd,SAAS,gBAChB3C,OAAA;oBACEwF,GAAG,EAAE/B,OAAO,CAACd,SAAU;oBACvB8C,GAAG,EAAEhC,OAAO,CAAC1C,IAAK;oBAClB8D,SAAS,EAAC;kBAA4B;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC,CAAC,gBAEFlF,OAAA;oBAAK6E,SAAS,EAAC,8DAA8D;oBAAAC,QAAA,eAC3E9E,OAAA;sBAAK0F,KAAK,EAAC,4BAA4B;sBAACb,SAAS,EAAC,SAAS;sBAACc,IAAI,EAAC,MAAM;sBAACC,OAAO,EAAC,WAAW;sBAACC,MAAM,EAAC,cAAc;sBAAAf,QAAA,eAC/G9E,OAAA;wBAAM8F,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,WAAW,EAAE,CAAE;wBAACC,CAAC,EAAC;sBAA2J;wBAAAlB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLlF,OAAA;gBAAI6E,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,EAAErB,OAAO,CAAC1C;cAAI;gBAAAgE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACrFlF,OAAA;gBAAI6E,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,GAAErB,OAAO,CAACxC,KAAK,EAAC,SAAE;cAAA;gBAAA8D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxFlF,OAAA;gBAAI6E,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,eAC/D9E,OAAA;kBAAM6E,SAAS,EAAE,iEACfpB,OAAO,CAACvC,KAAK,GAAG,EAAE,GAAG,6BAA6B,GAAG,yBAAyB,EAC7E;kBAAA4D,QAAA,EACArB,OAAO,CAACvC;gBAAK;kBAAA6D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACLlF,OAAA;gBAAI6E,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,EAC9DrB,OAAO,CAACf,QAAQ,GAAGe,OAAO,CAACf,QAAQ,CAAC3B,IAAI,GAAG;cAAgB;gBAAAgE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC,eACLlF,OAAA;gBAAI6E,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,EAC9DrB,OAAO,CAACrC,YAAY,gBACnBpB,OAAA;kBAAM6E,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,gBAE3ClF,OAAA;kBAAM6E,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cACzC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACLlF,OAAA;gBAAI6E,SAAS,EAAC,iDAAiD;gBAAAC,QAAA,gBAC7D9E,OAAA;kBACEmF,OAAO,EAAEA,CAAA,KAAM3B,SAAS,CAACC,OAAO,CAAE;kBAClCoB,SAAS,EAAC,4CAA4C;kBAAAC,QAAA,EACvD;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTlF,OAAA;kBACEmF,OAAO,EAAEA,CAAA,KAAMZ,YAAY,CAACd,OAAO,CAAChB,EAAE,CAAE;kBACxCoC,SAAS,EAAC,iCAAiC;kBAAAC,QAAA,EAC5C;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC;YAAA,GAlDEzB,OAAO,CAAChB,EAAE;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAmDf,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,EAGLtD,UAAU,GAAG,CAAC,iBACb5B,OAAA;QAAK6E,SAAS,EAAC,iFAAiF;QAAAC,QAAA,gBAC9F9E,OAAA;UACEmF,OAAO,EAAEA,CAAA,KAAMxD,cAAc,CAACuE,IAAI,IAAIC,IAAI,CAACC,GAAG,CAACF,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC,CAAE;UAC7DG,QAAQ,EAAE3E,WAAW,KAAK,CAAE;UAC5BmD,SAAS,EAAE,qBACTnD,WAAW,KAAK,CAAC,GACb,8CAA8C,GAC9C,6CAA6C,EAChD;UAAAoD,QAAA,EACJ;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTlF,OAAA;UAAM6E,SAAS,EAAC,uBAAuB;UAAAC,QAAA,GAAC,OACjC,EAACpD,WAAW,EAAC,OAAK,EAACE,UAAU;QAAA;UAAAmD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eACPlF,OAAA;UACEmF,OAAO,EAAEA,CAAA,KAAMxD,cAAc,CAACuE,IAAI,IAAIC,IAAI,CAACG,GAAG,CAACJ,IAAI,GAAG,CAAC,EAAEtE,UAAU,CAAC,CAAE;UACtEyE,QAAQ,EAAE3E,WAAW,KAAKE,UAAW;UACrCiD,SAAS,EAAE,qBACTnD,WAAW,KAAKE,UAAU,GACtB,8CAA8C,GAC9C,6CAA6C,EAChD;UAAAkD,QAAA,EACJ;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLvE,WAAW,iBACVX,OAAA;MAAK6E,SAAS,EAAC,4EAA4E;MAAAC,QAAA,eACzF9E,OAAA;QAAK6E,SAAS,EAAC,oDAAoD;QAAAC,QAAA,gBACjE9E,OAAA;UAAK6E,SAAS,EAAC,oBAAoB;UAAAC,QAAA,eACjC9E,OAAA;YAAI6E,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAClCrE,cAAc,GAAG,qBAAqB,GAAG;UAAoB;YAAAsE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACNlF,OAAA;UAAMuG,QAAQ,EAAE5C,YAAa;UAAAmB,QAAA,gBAC3B9E,OAAA;YAAK6E,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAClB9E,OAAA;cAAK6E,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB9E,OAAA;gBAAOwG,OAAO,EAAC,MAAM;gBAAC3B,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtElF,OAAA;gBACEoD,IAAI,EAAC,MAAM;gBACXX,EAAE,EAAC,MAAM;gBACT1B,IAAI,EAAC,MAAM;gBACXoC,KAAK,EAAEtC,QAAQ,CAACE,IAAK;gBACrBsE,QAAQ,EAAEpC,iBAAkB;gBAC5B4B,SAAS,EAAC,yFAAyF;gBACnG4B,QAAQ;cAAA;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENlF,OAAA;cAAK6E,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB9E,OAAA;gBAAOwG,OAAO,EAAC,aAAa;gBAAC3B,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrFlF,OAAA;gBACEyC,EAAE,EAAC,aAAa;gBAChB1B,IAAI,EAAC,aAAa;gBAClBoC,KAAK,EAAEtC,QAAQ,CAACG,WAAY;gBAC5BqE,QAAQ,EAAEpC,iBAAkB;gBAC5B4B,SAAS,EAAC,yFAAyF;gBACnG6B,IAAI,EAAC;cAAG;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eAENlF,OAAA;cAAK6E,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1C9E,OAAA;gBAAA8E,QAAA,gBACE9E,OAAA;kBAAOwG,OAAO,EAAC,OAAO;kBAAC3B,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC5ElF,OAAA;kBACEoD,IAAI,EAAC,QAAQ;kBACbX,EAAE,EAAC,OAAO;kBACV1B,IAAI,EAAC,OAAO;kBACZoC,KAAK,EAAEtC,QAAQ,CAACI,KAAM;kBACtBoE,QAAQ,EAAEpC,iBAAkB;kBAC5B4B,SAAS,EAAC,yFAAyF;kBACnG8B,IAAI,EAAC,MAAM;kBACXL,GAAG,EAAC,GAAG;kBACPG,QAAQ;gBAAA;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENlF,OAAA;gBAAA8E,QAAA,gBACE9E,OAAA;kBAAOwG,OAAO,EAAC,OAAO;kBAAC3B,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACzElF,OAAA;kBACEoD,IAAI,EAAC,QAAQ;kBACbX,EAAE,EAAC,OAAO;kBACV1B,IAAI,EAAC,OAAO;kBACZoC,KAAK,EAAEtC,QAAQ,CAACK,KAAM;kBACtBmE,QAAQ,EAAEpC,iBAAkB;kBAC5B4B,SAAS,EAAC,yFAAyF;kBACnGyB,GAAG,EAAC,GAAG;kBACPG,QAAQ;gBAAA;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENlF,OAAA;cAAK6E,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB9E,OAAA;gBAAOwG,OAAO,EAAC,aAAa;gBAAC3B,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACnFlF,OAAA;gBACEyC,EAAE,EAAC,aAAa;gBAChB1B,IAAI,EAAC,aAAa;gBAClBoC,KAAK,EAAEtC,QAAQ,CAACM,WAAY;gBAC5BkE,QAAQ,EAAEpC,iBAAkB;gBAC5B4B,SAAS,EAAC,yFAAyF;gBACnG4B,QAAQ;gBAAA3B,QAAA,gBAER9E,OAAA;kBAAQmD,KAAK,EAAC,EAAE;kBAAA2B,QAAA,EAAC;gBAA0B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EACnD5D,UAAU,CAACiE,GAAG,CAAC7C,QAAQ,iBACtB1C,OAAA;kBAA0BmD,KAAK,EAAET,QAAQ,CAACD,EAAG;kBAAAqC,QAAA,EAC1CpC,QAAQ,CAAC3B;gBAAI,GADH2B,QAAQ,CAACD,EAAE;kBAAAsC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEhB,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENlF,OAAA;cAAK6E,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB9E,OAAA;gBAAOwG,OAAO,EAAC,OAAO;gBAAC3B,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACzElF,OAAA;gBACEoD,IAAI,EAAC,MAAM;gBACXX,EAAE,EAAC,OAAO;gBACV1B,IAAI,EAAC,OAAO;gBACZsE,QAAQ,EAAEpC,iBAAkB;gBAC5B4B,SAAS,EAAC,yFAAyF;gBACnG+B,MAAM,EAAC;cAAS;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC,EACDzE,cAAc,IAAIA,cAAc,CAACkC,SAAS,iBACzC3C,OAAA;gBAAK6E,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnB9E,OAAA;kBAAG6E,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACxDlF,OAAA;kBAAK6E,SAAS,EAAC,oDAAoD;kBAAAC,QAAA,eACjE9E,OAAA;oBACEwF,GAAG,EAAE/E,cAAc,CAACkC,SAAU;oBAC9B8C,GAAG,EAAEhF,cAAc,CAACM,IAAK;oBACzB8D,SAAS,EAAC;kBAA4B;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAENlF,OAAA;cAAK6E,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnB9E,OAAA;gBAAK6E,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChC9E,OAAA;kBACEoD,IAAI,EAAC,UAAU;kBACfX,EAAE,EAAC,cAAc;kBACjB1B,IAAI,EAAC,cAAc;kBACnBsC,OAAO,EAAExC,QAAQ,CAACO,YAAa;kBAC/BiE,QAAQ,EAAEpC,iBAAkB;kBAC5B4B,SAAS,EAAC;gBAAqE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChF,CAAC,eACFlF,OAAA;kBAAOwG,OAAO,EAAC,cAAc;kBAAC3B,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,EAAC;gBAEnE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENlF,OAAA;YAAK6E,SAAS,EAAC,gDAAgD;YAAAC,QAAA,gBAC7D9E,OAAA;cACEoD,IAAI,EAAC,QAAQ;cACb+B,OAAO,EAAEzB,UAAW;cACpBmB,SAAS,EAAC,kEAAkE;cAAAC,QAAA,EAC7E;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTlF,OAAA;cACEoD,IAAI,EAAC,QAAQ;cACbyB,SAAS,EAAC,iEAAiE;cAAAC,QAAA,EAE1ErE,cAAc,GAAG,eAAe,GAAG;YAAS;cAAAsE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAChF,EAAA,CA/hBID,QAAQ;AAAA4G,EAAA,GAAR5G,QAAQ;AAiiBd,eAAeA,QAAQ;AAAC,IAAA4G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}