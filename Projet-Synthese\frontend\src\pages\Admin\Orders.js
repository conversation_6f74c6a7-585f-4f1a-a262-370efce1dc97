import React, { useState, useEffect } from 'react';
import axios from 'axios';

const Orders = () => {
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currentOrder, setCurrentOrder] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [statusFilter, setStatusFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    fetchOrders();
  }, [currentPage, statusFilter, searchTerm]);

  const fetchOrders = async () => {
    try {
      setLoading(true);
      const response = await axios.get(`/api/admin/orders?page=${currentPage}&status=${statusFilter}&search=${searchTerm}`);
      setOrders(response.data.data);
      setTotalPages(response.data.meta.last_page);
    } catch (err) {
      console.log('API non disponible, utilisation des données de test');

      // Données de test pour les commandes
      const testOrders = [
        {
          id: 1001,
          user: {
            id: 1,
            name: 'Marie Dubois',
            email: '<EMAIL>',
            phone: '06 12 34 56 78'
          },
          created_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
          status: 'completed',
          subtotal: 42.50,
          shipping_fee: 5.00,
          discount: 0,
          total: 47.50,
          notes: 'Livraison rapide demandée',
          order_items: [
            {
              id: 1,
              product: {
                id: 1,
                name: 'Poulet Grillé Entier',
                image_url: 'https://images.unsplash.com/photo-1598103442097-8b74394b95c6?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80'
              },
              quantity: 1,
              unit_price: 25.99,
              grilling_option: { name: 'Bien cuit' },
              notes: 'Avec sauce barbecue'
            },
            {
              id: 2,
              product: {
                id: 3,
                name: 'Salade César',
                image_url: 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80'
              },
              quantity: 1,
              unit_price: 12.50,
              grilling_option: null,
              notes: null
            },
            {
              id: 3,
              product: {
                id: 5,
                name: 'Jus d\'Orange Frais',
                image_url: 'https://images.unsplash.com/photo-1613478223719-2ab802602423?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80'
              },
              quantity: 2,
              unit_price: 4.50,
              grilling_option: null,
              notes: null
            }
          ]
        },
        {
          id: 1002,
          user: {
            id: 2,
            name: 'Ahmed Hassan',
            email: '<EMAIL>',
            phone: '07 98 76 54 32'
          },
          created_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
          status: 'processing',
          subtotal: 35.40,
          shipping_fee: 5.00,
          discount: 5.00,
          total: 35.40,
          notes: null,
          order_items: [
            {
              id: 4,
              product: {
                id: 2,
                name: 'Brochettes de Bœuf',
                image_url: 'https://images.unsplash.com/photo-1529692236671-f1f6cf9683ba?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80'
              },
              quantity: 2,
              unit_price: 16.99,
              grilling_option: { name: 'À point' },
              notes: 'Avec légumes grillés'
            }
          ]
        },
        {
          id: 1003,
          user: {
            id: 3,
            name: 'Sophie Martin',
            email: '<EMAIL>',
            phone: '06 55 44 33 22'
          },
          created_at: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(),
          status: 'pending',
          subtotal: 28.40,
          shipping_fee: 5.00,
          discount: 0,
          total: 33.40,
          notes: 'Allergique aux noix',
          order_items: [
            {
              id: 5,
              product: {
                id: 4,
                name: 'Plateau de Fromages',
                image_url: 'https://images.unsplash.com/photo-1486297678162-eb2a19b0a32d?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80'
              },
              quantity: 1,
              unit_price: 19.90,
              grilling_option: null,
              notes: 'Sans noix'
            },
            {
              id: 6,
              product: {
                id: 6,
                name: 'Tiramisu Maison',
                image_url: 'https://images.unsplash.com/photo-1571877227200-a0d98ea607e9?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80'
              },
              quantity: 1,
              unit_price: 8.50,
              grilling_option: null,
              notes: null
            }
          ]
        },
        {
          id: 1004,
          user: {
            id: 4,
            name: 'Jean Dupont',
            email: '<EMAIL>',
            phone: '06 11 22 33 44'
          },
          created_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
          status: 'cancelled',
          subtotal: 22.00,
          shipping_fee: 5.00,
          discount: 0,
          total: 27.00,
          notes: 'Annulé par le client',
          order_items: [
            {
              id: 7,
              product: {
                id: 7,
                name: 'Saumon Grillé',
                image_url: 'https://images.unsplash.com/photo-1467003909585-2f8a72700288?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80'
              },
              quantity: 1,
              unit_price: 22.00,
              grilling_option: { name: 'Mi-cuit' },
              notes: 'Avec citron'
            }
          ]
        }
      ];

      // Filtrer par statut si nécessaire
      let filteredOrders = statusFilter === 'all'
        ? testOrders
        : testOrders.filter(order => order.status === statusFilter);

      // Filtrer par terme de recherche si nécessaire
      if (searchTerm) {
        filteredOrders = filteredOrders.filter(order =>
          order.id.toString().includes(searchTerm) ||
          order.user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          order.user.email.toLowerCase().includes(searchTerm.toLowerCase())
        );
      }

      setOrders(filteredOrders);
      setTotalPages(1);
      setError(null);
    } finally {
      setLoading(false);
    }
  };

  const handleStatusChange = async (orderId, newStatus) => {
    try {
      await axios.patch(`/api/admin/orders/${orderId}/status`, {
        status: newStatus
      });
      
      // Mettre à jour l'état local
      setOrders(orders.map(order => 
        order.id === orderId ? { ...order, status: newStatus } : order
      ));
      
      if (currentOrder && currentOrder.id === orderId) {
        setCurrentOrder({ ...currentOrder, status: newStatus });
      }
    } catch (err) {
      console.error('Erreur lors de la mise à jour du statut', err);
      alert('Une erreur est survenue lors de la mise à jour du statut');
    }
  };

  const openModal = (order) => {
    setCurrentOrder(order);
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setCurrentOrder(null);
  };

  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1);
  };

  const handleFilterChange = (e) => {
    setStatusFilter(e.target.value);
    setCurrentPage(1);
  };

  const getStatusBadgeClass = (status) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'processing':
        return 'bg-blue-100 text-blue-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'long', day: 'numeric', hour: '2-digit', minute: '2-digit' };
    return new Date(dateString).toLocaleDateString('fr-FR', options);
  };

  if (loading && orders.length === 0) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-500"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-semibold mb-6">Gestion des commandes</h1>

      <div className="mb-6 flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div className="flex flex-col md:flex-row gap-4 md:items-center">
          <div>
            <label htmlFor="statusFilter" className="block text-sm font-medium text-gray-700 mb-1">Filtrer par statut</label>
            <select
              id="statusFilter"
              value={statusFilter}
              onChange={handleFilterChange}
              className="px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
            >
              <option value="all">Tous les statuts</option>
              <option value="pending">En attente</option>
              <option value="processing">En cours</option>
              <option value="completed">Terminée</option>
              <option value="cancelled">Annulée</option>
            </select>
          </div>
          
          <div className="flex-1">
            <label htmlFor="searchTerm" className="block text-sm font-medium text-gray-700 mb-1">Rechercher</label>
            <input
              type="text"
              id="searchTerm"
              placeholder="Rechercher par ID, client..."
              value={searchTerm}
              onChange={handleSearch}
              className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
            />
          </div>
        </div>
        
        <button
          onClick={fetchOrders}
          className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 self-end md:self-auto"
        >
          Actualiser
        </button>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
          <strong className="font-bold">Erreur!</strong>
          <span className="block sm:inline"> {error}</span>
        </div>
      )}

      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Client</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {orders.map((order) => (
                <tr key={order.id}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">#{order.id}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{order.user.name}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{formatDate(order.created_at)}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{order.total} DH</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass(order.status)}`}>
                      {order.status === 'pending' && 'En attente'}
                      {order.status === 'processing' && 'En cours'}
                      {order.status === 'completed' && 'Terminée'}
                      {order.status === 'cancelled' && 'Annulée'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button
                      onClick={() => openModal(order)}
                      className="text-indigo-600 hover:text-indigo-900 mr-3"
                    >
                      Détails
                    </button>
                    <select
                      value={order.status}
                      onChange={(e) => handleStatusChange(order.id, e.target.value)}
                      className="text-sm border rounded p-1"
                    >
                      <option value="pending">En attente</option>
                      <option value="processing">En cours</option>
                      <option value="completed">Terminée</option>
                      <option value="cancelled">Annulée</option>
                    </select>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        {/* Pagination */}
        {totalPages > 1 && (
          <div className="px-6 py-4 bg-gray-50 border-t border-gray-200 flex items-center justify-between">
            <button
              onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
              className={`px-3 py-1 rounded ${
                currentPage === 1
                  ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              Précédent
            </button>
            <span className="text-sm text-gray-700">
              Page {currentPage} sur {totalPages}
            </span>
            <button
              onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
              className={`px-3 py-1 rounded ${
                currentPage === totalPages
                  ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              Suivant
            </button>
          </div>
        )}
      </div>

      {/* Modal pour les détails de la commande */}
      {isModalOpen && currentOrder && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto">
            <div className="px-6 py-4 border-b flex justify-between items-center">
              <h3 className="text-lg font-semibold">
                Détails de la commande #{currentOrder.id}
              </h3>
              <button
                onClick={closeModal}
                className="text-gray-500 hover:text-gray-700"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                  <h4 className="text-sm font-medium text-gray-500 uppercase mb-2">Informations client</h4>
                  <div className="bg-gray-50 p-4 rounded">
                    <p className="mb-1"><span className="font-medium">Nom:</span> {currentOrder.user.name}</p>
                    <p className="mb-1"><span className="font-medium">Email:</span> {currentOrder.user.email}</p>
                    <p><span className="font-medium">Téléphone:</span> {currentOrder.user.phone || 'Non spécifié'}</p>
                  </div>
                </div>
                
                <div>
                  <h4 className="text-sm font-medium text-gray-500 uppercase mb-2">Informations commande</h4>
                  <div className="bg-gray-50 p-4 rounded">
                    <p className="mb-1">
                      <span className="font-medium">Date:</span> {formatDate(currentOrder.created_at)}
                    </p>
                    <p className="mb-1">
                      <span className="font-medium">Statut:</span> 
                      <span className={`ml-2 px-2 py-0.5 rounded-full text-xs font-semibold ${getStatusBadgeClass(currentOrder.status)}`}>
                        {currentOrder.status === 'pending' && 'En attente'}
                        {currentOrder.status === 'processing' && 'En cours'}
                        {currentOrder.status === 'completed' && 'Terminée'}
                        {currentOrder.status === 'cancelled' && 'Annulée'}
                      </span>
                    </p>
                    <div className="mt-2">
                      <label htmlFor="orderStatus" className="block text-sm font-medium text-gray-700 mb-1">Changer le statut:</label>
                      <select
                        id="orderStatus"
                        value={currentOrder.status}
                        onChange={(e) => handleStatusChange(currentOrder.id, e.target.value)}
                        className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                      >
                        <option value="pending">En attente</option>
                        <option value="processing">En cours</option>
                        <option value="completed">Terminée</option>
                        <option value="cancelled">Annulée</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>
              
              <h4 className="text-sm font-medium text-gray-500 uppercase mb-2">Produits commandés</h4>
              <div className="bg-gray-50 rounded overflow-hidden mb-6">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-100">
                    <tr>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Produit</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Option de cuisson</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Prix unitaire</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Quantité</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Total</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {currentOrder.order_items.map((item) => (
                      <tr key={item.id}>
                        <td className="px-4 py-3 text-sm text-gray-900">
                          <div className="flex items-center">
                            {item.product.image_url && (
                              <div className="h-10 w-10 flex-shrink-0 mr-3">
                                <img
                                  src={item.product.image_url}
                                  alt={item.product.name}
                                  className="h-10 w-10 rounded-full object-cover"
                                />
                              </div>
                            )}
                            <div>
                              <p className="font-medium">{item.product.name}</p>
                              {item.notes && (
                                <p className="text-xs text-gray-500">Note: {item.notes}</p>
                              )}
                            </div>
                          </div>
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-500">
                          {item.grilling_option ? item.grilling_option.name : 'N/A'}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-500">
                          {item.unit_price} DH
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-500">
                          {item.quantity}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900 font-medium">
                          {(item.unit_price * item.quantity).toFixed(2)} DH
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              
              <div className="flex justify-end">
                <div className="w-full max-w-xs">
                  <div className="border-t pt-4">
                    <div className="flex justify-between mb-2">
                      <span className="text-gray-600">Sous-total:</span>
                      <span className="font-medium">{currentOrder.subtotal} DH</span>
                    </div>
                    <div className="flex justify-between mb-2">
                      <span className="text-gray-600">Frais de livraison:</span>
                      <span className="font-medium">{currentOrder.shipping_fee} DH</span>
                    </div>
                    {currentOrder.discount > 0 && (
                      <div className="flex justify-between mb-2 text-green-600">
                        <span>Réduction:</span>
                        <span>-{currentOrder.discount} DH</span>
                      </div>
                    )}
                    <div className="flex justify-between border-t border-gray-300 pt-2 mt-2">
                      <span className="font-semibold">Total:</span>
                      <span className="font-bold text-lg">{currentOrder.total} DH</span>
                    </div>
                  </div>
                </div>
              </div>
              
              {currentOrder.notes && (
                <div className="mt-6">
                  <h4 className="text-sm font-medium text-gray-500 uppercase mb-2">Notes de commande</h4>
                  <div className="bg-gray-50 p-4 rounded">
                    <p className="text-gray-700">{currentOrder.notes}</p>
                  </div>
                </div>
              )}
            </div>
            
            <div className="px-6 py-4 bg-gray-50 border-t flex justify-end">
              <button
                onClick={closeModal}
                className="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300"
              >
                Fermer
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Orders;

