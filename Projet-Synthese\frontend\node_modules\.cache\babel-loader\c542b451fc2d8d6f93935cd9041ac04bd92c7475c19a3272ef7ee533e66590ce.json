{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Projet-Synthese\\\\frontend\\\\src\\\\pages\\\\Admin\\\\Dashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { toast } from 'react-toastify';\nimport { FaUsers, FaShoppingCart, FaDollarSign, FaBox, FaExclamationTriangle, FaSync } from 'react-icons/fa';\nimport { dashboardService } from '../../services/api/dashboardService';\nimport StatsCard from '../../components/admin/StatsCard';\nimport DashboardCharts from '../../components/admin/DashboardCharts';\nimport RecentActivity from '../../components/admin/RecentActivity';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  var _stats$overview, _stats$overview2, _stats$overview3, _stats$overview4, _stats$overview5, _stats$overview6, _stats$overview7, _stats$overview8, _stats$overview9, _stats$overview10, _stats$charts, _stats$charts$monthly, _stats$charts2, _stats$charts2$monthl, _stats$charts3, _stats$charts3$popula, _stats$charts4, _stats$charts4$popula, _stats$overview11, _stats$overview12, _stats$overview13;\n  // Mode démonstration - pas besoin d'authentification\n  const [stats, setStats] = useState(null);\n  const [recentActivity, setRecentActivity] = useState([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [lastUpdated, setLastUpdated] = useState(new Date());\n  useEffect(() => {\n    fetchDashboardData();\n  }, []);\n  const fetchDashboardData = async () => {\n    try {\n      setIsLoading(true);\n\n      // Simuler des données si l'API n'est pas disponible\n      const mockStats = {\n        overview: {\n          total_orders: 125,\n          total_revenue: 15420.50,\n          total_users: 89,\n          total_products: 45,\n          orders_last_30_days: 32,\n          revenue_last_30_days: 4250.00,\n          new_users_last_30_days: 15,\n          active_promotions: 3,\n          low_stock_products: 2\n        },\n        charts: {\n          monthly_revenue: [{\n            month: 'Jan',\n            revenue: 2100\n          }, {\n            month: 'Fév',\n            revenue: 2800\n          }, {\n            month: 'Mar',\n            revenue: 3200\n          }, {\n            month: 'Avr',\n            revenue: 2900\n          }, {\n            month: 'Mai',\n            revenue: 3800\n          }, {\n            month: 'Jun',\n            revenue: 4250\n          }],\n          popular_categories: [{\n            name: 'Produits Grillés',\n            total_sold: 145\n          }, {\n            name: 'Non Grillés',\n            total_sold: 98\n          }, {\n            name: 'Fromages',\n            total_sold: 67\n          }, {\n            name: 'Boissons',\n            total_sold: 89\n          }]\n        },\n        top_products: [{\n          name: 'Poulet Grillé Entier',\n          total_sold: 45\n        }, {\n          name: 'Salade César',\n          total_sold: 32\n        }, {\n          name: 'Plateau de Fromages',\n          total_sold: 28\n        }, {\n          name: 'Brochettes de Bœuf',\n          total_sold: 25\n        }, {\n          name: 'Jus d\\'Orange Frais',\n          total_sold: 22\n        }]\n      };\n\n      // En mode démonstration, utiliser directement les données mockées\n      // L'API sera automatiquement utilisée quand le backend sera disponible\n      setStats(mockStats);\n      setLastUpdated(new Date());\n    } catch (error) {\n      toast.error('Erreur lors du chargement des données du dashboard');\n      console.error('Erreur dashboard:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('fr-FR', {\n      style: 'currency',\n      currency: 'EUR'\n    }).format(amount);\n  };\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-center items-center h-64\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-gray-900\",\n          children: \"Tableau de bord\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-500\",\n          children: [\"Derni\\xE8re mise \\xE0 jour: \", lastUpdated.toLocaleString('fr-FR')]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: fetchDashboardData,\n        className: \"flex items-center space-x-2 px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors\",\n        disabled: isLoading,\n        children: [/*#__PURE__*/_jsxDEV(FaSync, {\n          className: `${isLoading ? 'animate-spin' : ''}`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Actualiser\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(StatsCard, {\n        title: \"Total Commandes\",\n        value: (stats === null || stats === void 0 ? void 0 : (_stats$overview = stats.overview) === null || _stats$overview === void 0 ? void 0 : _stats$overview.total_orders) || 0,\n        change: `+${(stats === null || stats === void 0 ? void 0 : (_stats$overview2 = stats.overview) === null || _stats$overview2 === void 0 ? void 0 : _stats$overview2.orders_last_30_days) || 0} ce mois`,\n        icon: /*#__PURE__*/_jsxDEV(FaShoppingCart, {\n          className: \"text-xl\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 17\n        }, this),\n        color: \"blue\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatsCard, {\n        title: \"Revenus Total\",\n        value: formatCurrency((stats === null || stats === void 0 ? void 0 : (_stats$overview3 = stats.overview) === null || _stats$overview3 === void 0 ? void 0 : _stats$overview3.total_revenue) || 0),\n        change: `+${formatCurrency((stats === null || stats === void 0 ? void 0 : (_stats$overview4 = stats.overview) === null || _stats$overview4 === void 0 ? void 0 : _stats$overview4.revenue_last_30_days) || 0)} ce mois`,\n        icon: /*#__PURE__*/_jsxDEV(FaDollarSign, {\n          className: \"text-xl\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 17\n        }, this),\n        color: \"green\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatsCard, {\n        title: \"Clients\",\n        value: (stats === null || stats === void 0 ? void 0 : (_stats$overview5 = stats.overview) === null || _stats$overview5 === void 0 ? void 0 : _stats$overview5.total_users) || 0,\n        change: `+${(stats === null || stats === void 0 ? void 0 : (_stats$overview6 = stats.overview) === null || _stats$overview6 === void 0 ? void 0 : _stats$overview6.new_users_last_30_days) || 0} nouveaux`,\n        icon: /*#__PURE__*/_jsxDEV(FaUsers, {\n          className: \"text-xl\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 17\n        }, this),\n        color: \"purple\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatsCard, {\n        title: \"Produits\",\n        value: (stats === null || stats === void 0 ? void 0 : (_stats$overview7 = stats.overview) === null || _stats$overview7 === void 0 ? void 0 : _stats$overview7.total_products) || 0,\n        change: (stats === null || stats === void 0 ? void 0 : (_stats$overview8 = stats.overview) === null || _stats$overview8 === void 0 ? void 0 : _stats$overview8.low_stock_products) > 0 ? `${stats.overview.low_stock_products} en rupture` : 'Stock suffisant',\n        icon: /*#__PURE__*/_jsxDEV(FaBox, {\n          className: \"text-xl\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 17\n        }, this),\n        color: (stats === null || stats === void 0 ? void 0 : (_stats$overview9 = stats.overview) === null || _stats$overview9 === void 0 ? void 0 : _stats$overview9.low_stock_products) > 0 ? 'red' : 'orange'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 7\n    }, this), (stats === null || stats === void 0 ? void 0 : (_stats$overview10 = stats.overview) === null || _stats$overview10 === void 0 ? void 0 : _stats$overview10.low_stock_products) > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(FaExclamationTriangle, {\n          className: \"text-red-500 mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-red-800\",\n          children: [\"Attention: \", stats.overview.low_stock_products, \" produit(s) en rupture de stock\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 158,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(DashboardCharts, {\n      salesData: {\n        labels: (stats === null || stats === void 0 ? void 0 : (_stats$charts = stats.charts) === null || _stats$charts === void 0 ? void 0 : (_stats$charts$monthly = _stats$charts.monthly_revenue) === null || _stats$charts$monthly === void 0 ? void 0 : _stats$charts$monthly.map(item => item.month)) || [],\n        data: (stats === null || stats === void 0 ? void 0 : (_stats$charts2 = stats.charts) === null || _stats$charts2 === void 0 ? void 0 : (_stats$charts2$monthl = _stats$charts2.monthly_revenue) === null || _stats$charts2$monthl === void 0 ? void 0 : _stats$charts2$monthl.map(item => item.revenue)) || []\n      },\n      categoryData: {\n        labels: (stats === null || stats === void 0 ? void 0 : (_stats$charts3 = stats.charts) === null || _stats$charts3 === void 0 ? void 0 : (_stats$charts3$popula = _stats$charts3.popular_categories) === null || _stats$charts3$popula === void 0 ? void 0 : _stats$charts3$popula.map(item => item.name)) || [],\n        data: (stats === null || stats === void 0 ? void 0 : (_stats$charts4 = stats.charts) === null || _stats$charts4 === void 0 ? void 0 : (_stats$charts4$popula = _stats$charts4.popular_categories) === null || _stats$charts4$popula === void 0 ? void 0 : _stats$charts4$popula.map(item => item.total_sold)) || []\n      },\n      topProducts: (stats === null || stats === void 0 ? void 0 : stats.top_products) || []\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 169,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"lg:col-span-2\",\n        children: /*#__PURE__*/_jsxDEV(RecentActivity, {\n          activities: recentActivity\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white p-6 rounded-lg shadow-sm border\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-900 mb-4\",\n            children: \"Promotions actives\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-3xl font-bold text-primary-600\",\n              children: (stats === null || stats === void 0 ? void 0 : (_stats$overview11 = stats.overview) === null || _stats$overview11 === void 0 ? void 0 : _stats$overview11.active_promotions) || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-500\",\n              children: \"promotions en cours\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white p-6 rounded-lg shadow-sm border\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-900 mb-4\",\n            children: \"Performance\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Taux de conversion\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium\",\n                children: \"12.5%\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Panier moyen\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium\",\n                children: formatCurrency(((stats === null || stats === void 0 ? void 0 : (_stats$overview12 = stats.overview) === null || _stats$overview12 === void 0 ? void 0 : _stats$overview12.total_revenue) || 0) / ((stats === null || stats === void 0 ? void 0 : (_stats$overview13 = stats.overview) === null || _stats$overview13 === void 0 ? void 0 : _stats$overview13.total_orders) || 1))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Satisfaction client\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium\",\n                children: \"4.2/5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 182,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 99,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"sCLgKbbQlIxD2D8bNKKJiJdxDQk=\");\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "toast", "FaUsers", "FaShoppingCart", "FaDollarSign", "FaBox", "FaExclamationTriangle", "FaSync", "dashboardService", "StatsCard", "DashboardCharts", "RecentActivity", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "_stats$overview", "_stats$overview2", "_stats$overview3", "_stats$overview4", "_stats$overview5", "_stats$overview6", "_stats$overview7", "_stats$overview8", "_stats$overview9", "_stats$overview10", "_stats$charts", "_stats$charts$monthly", "_stats$charts2", "_stats$charts2$monthl", "_stats$charts3", "_stats$charts3$popula", "_stats$charts4", "_stats$charts4$popula", "_stats$overview11", "_stats$overview12", "_stats$overview13", "stats", "setStats", "recentActivity", "setRecentActivity", "isLoading", "setIsLoading", "lastUpdated", "setLastUpdated", "Date", "fetchDashboardData", "mockStats", "overview", "total_orders", "total_revenue", "total_users", "total_products", "orders_last_30_days", "revenue_last_30_days", "new_users_last_30_days", "active_promotions", "low_stock_products", "charts", "monthly_revenue", "month", "revenue", "popular_categories", "name", "total_sold", "top_products", "error", "console", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "format", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "toLocaleString", "onClick", "disabled", "title", "value", "change", "icon", "color", "salesData", "labels", "map", "item", "data", "categoryData", "topProducts", "activities", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet-Synthese/frontend/src/pages/Admin/Dashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { toast } from 'react-toastify';\nimport {\n  FaUsers,\n  FaShoppingCart,\n  FaDollarSign,\n  FaBox,\n  FaExclamationTriangle,\n  FaSync\n} from 'react-icons/fa';\nimport { dashboardService } from '../../services/api/dashboardService';\nimport StatsCard from '../../components/admin/StatsCard';\nimport DashboardCharts from '../../components/admin/DashboardCharts';\nimport RecentActivity from '../../components/admin/RecentActivity';\n\n\nconst Dashboard = () => {\n  // Mode démonstration - pas besoin d'authentification\n  const [stats, setStats] = useState(null);\n  const [recentActivity, setRecentActivity] = useState([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [lastUpdated, setLastUpdated] = useState(new Date());\n\n  useEffect(() => {\n    fetchDashboardData();\n  }, []);\n\n  const fetchDashboardData = async () => {\n    try {\n      setIsLoading(true);\n\n      // Simuler des données si l'API n'est pas disponible\n      const mockStats = {\n        overview: {\n          total_orders: 125,\n          total_revenue: 15420.50,\n          total_users: 89,\n          total_products: 45,\n          orders_last_30_days: 32,\n          revenue_last_30_days: 4250.00,\n          new_users_last_30_days: 15,\n          active_promotions: 3,\n          low_stock_products: 2\n        },\n        charts: {\n          monthly_revenue: [\n            { month: 'Jan', revenue: 2100 },\n            { month: 'Fév', revenue: 2800 },\n            { month: 'Mar', revenue: 3200 },\n            { month: 'Avr', revenue: 2900 },\n            { month: 'Mai', revenue: 3800 },\n            { month: 'Jun', revenue: 4250 }\n          ],\n          popular_categories: [\n            { name: 'Produits Grillés', total_sold: 145 },\n            { name: 'Non Grillés', total_sold: 98 },\n            { name: 'Fromages', total_sold: 67 },\n            { name: 'Boissons', total_sold: 89 }\n          ]\n        },\n        top_products: [\n          { name: 'Poulet Grillé Entier', total_sold: 45 },\n          { name: 'Salade César', total_sold: 32 },\n          { name: 'Plateau de Fromages', total_sold: 28 },\n          { name: 'Brochettes de Bœuf', total_sold: 25 },\n          { name: 'Jus d\\'Orange Frais', total_sold: 22 }\n        ]\n      };\n\n      // En mode démonstration, utiliser directement les données mockées\n      // L'API sera automatiquement utilisée quand le backend sera disponible\n      setStats(mockStats);\n\n      setLastUpdated(new Date());\n    } catch (error) {\n      toast.error('Erreur lors du chargement des données du dashboard');\n      console.error('Erreur dashboard:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const formatCurrency = (amount) => {\n    return new Intl.NumberFormat('fr-FR', {\n      style: 'currency',\n      currency: 'EUR'\n    }).format(amount);\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"flex justify-center items-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* En-tête */}\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">Tableau de bord</h1>\n          <p className=\"text-sm text-gray-500\">\n            Dernière mise à jour: {lastUpdated.toLocaleString('fr-FR')}\n          </p>\n        </div>\n        <button\n          onClick={fetchDashboardData}\n          className=\"flex items-center space-x-2 px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors\"\n          disabled={isLoading}\n        >\n          <FaSync className={`${isLoading ? 'animate-spin' : ''}`} />\n          <span>Actualiser</span>\n        </button>\n      </div>\n\n      {/* Statistiques principales */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        <StatsCard\n          title=\"Total Commandes\"\n          value={stats?.overview?.total_orders || 0}\n          change={`+${stats?.overview?.orders_last_30_days || 0} ce mois`}\n          icon={<FaShoppingCart className=\"text-xl\" />}\n          color=\"blue\"\n        />\n\n        <StatsCard\n          title=\"Revenus Total\"\n          value={formatCurrency(stats?.overview?.total_revenue || 0)}\n          change={`+${formatCurrency(stats?.overview?.revenue_last_30_days || 0)} ce mois`}\n          icon={<FaDollarSign className=\"text-xl\" />}\n          color=\"green\"\n        />\n\n        <StatsCard\n          title=\"Clients\"\n          value={stats?.overview?.total_users || 0}\n          change={`+${stats?.overview?.new_users_last_30_days || 0} nouveaux`}\n          icon={<FaUsers className=\"text-xl\" />}\n          color=\"purple\"\n        />\n\n        <StatsCard\n          title=\"Produits\"\n          value={stats?.overview?.total_products || 0}\n          change={stats?.overview?.low_stock_products > 0 ?\n            `${stats.overview.low_stock_products} en rupture` :\n            'Stock suffisant'\n          }\n          icon={<FaBox className=\"text-xl\" />}\n          color={stats?.overview?.low_stock_products > 0 ? 'red' : 'orange'}\n        />\n      </div>\n\n      {/* Alertes */}\n      {stats?.overview?.low_stock_products > 0 && (\n        <div className=\"bg-red-50 border border-red-200 rounded-lg p-4\">\n          <div className=\"flex items-center\">\n            <FaExclamationTriangle className=\"text-red-500 mr-2\" />\n            <span className=\"text-red-800\">\n              Attention: {stats.overview.low_stock_products} produit(s) en rupture de stock\n            </span>\n          </div>\n        </div>\n      )}\n\n      {/* Graphiques */}\n      <DashboardCharts\n        salesData={{\n          labels: stats?.charts?.monthly_revenue?.map(item => item.month) || [],\n          data: stats?.charts?.monthly_revenue?.map(item => item.revenue) || []\n        }}\n        categoryData={{\n          labels: stats?.charts?.popular_categories?.map(item => item.name) || [],\n          data: stats?.charts?.popular_categories?.map(item => item.total_sold) || []\n        }}\n        topProducts={stats?.top_products || []}\n      />\n\n      {/* Section inférieure */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n        {/* Activités récentes */}\n        <div className=\"lg:col-span-2\">\n          <RecentActivity activities={recentActivity} />\n        </div>\n\n        {/* Statistiques supplémentaires */}\n        <div className=\"space-y-6\">\n          <div className=\"bg-white p-6 rounded-lg shadow-sm border\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Promotions actives</h3>\n            <div className=\"text-center\">\n              <p className=\"text-3xl font-bold text-primary-600\">\n                {stats?.overview?.active_promotions || 0}\n              </p>\n              <p className=\"text-sm text-gray-500\">promotions en cours</p>\n            </div>\n          </div>\n\n          <div className=\"bg-white p-6 rounded-lg shadow-sm border\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Performance</h3>\n            <div className=\"space-y-3\">\n              <div className=\"flex justify-between\">\n                <span className=\"text-sm text-gray-600\">Taux de conversion</span>\n                <span className=\"text-sm font-medium\">12.5%</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-sm text-gray-600\">Panier moyen</span>\n                <span className=\"text-sm font-medium\">\n                  {formatCurrency((stats?.overview?.total_revenue || 0) / (stats?.overview?.total_orders || 1))}\n                </span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-sm text-gray-600\">Satisfaction client</span>\n                <span className=\"text-sm font-medium\">4.2/5</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Dashboard;\n\n\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SACEC,OAAO,EACPC,cAAc,EACdC,YAAY,EACZC,KAAK,EACLC,qBAAqB,EACrBC,MAAM,QACD,gBAAgB;AACvB,SAASC,gBAAgB,QAAQ,qCAAqC;AACtE,OAAOC,SAAS,MAAM,kCAAkC;AACxD,OAAOC,eAAe,MAAM,wCAAwC;AACpE,OAAOC,cAAc,MAAM,uCAAuC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGnE,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,eAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,iBAAA,EAAAC,aAAA,EAAAC,qBAAA,EAAAC,cAAA,EAAAC,qBAAA,EAAAC,cAAA,EAAAC,qBAAA,EAAAC,cAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,iBAAA,EAAAC,iBAAA;EACtB;EACA,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACwC,cAAc,EAAEC,iBAAiB,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC0C,SAAS,EAAEC,YAAY,CAAC,GAAG3C,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC4C,WAAW,EAAEC,cAAc,CAAC,GAAG7C,QAAQ,CAAC,IAAI8C,IAAI,CAAC,CAAC,CAAC;EAE1D7C,SAAS,CAAC,MAAM;IACd8C,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACFJ,YAAY,CAAC,IAAI,CAAC;;MAElB;MACA,MAAMK,SAAS,GAAG;QAChBC,QAAQ,EAAE;UACRC,YAAY,EAAE,GAAG;UACjBC,aAAa,EAAE,QAAQ;UACvBC,WAAW,EAAE,EAAE;UACfC,cAAc,EAAE,EAAE;UAClBC,mBAAmB,EAAE,EAAE;UACvBC,oBAAoB,EAAE,OAAO;UAC7BC,sBAAsB,EAAE,EAAE;UAC1BC,iBAAiB,EAAE,CAAC;UACpBC,kBAAkB,EAAE;QACtB,CAAC;QACDC,MAAM,EAAE;UACNC,eAAe,EAAE,CACf;YAAEC,KAAK,EAAE,KAAK;YAAEC,OAAO,EAAE;UAAK,CAAC,EAC/B;YAAED,KAAK,EAAE,KAAK;YAAEC,OAAO,EAAE;UAAK,CAAC,EAC/B;YAAED,KAAK,EAAE,KAAK;YAAEC,OAAO,EAAE;UAAK,CAAC,EAC/B;YAAED,KAAK,EAAE,KAAK;YAAEC,OAAO,EAAE;UAAK,CAAC,EAC/B;YAAED,KAAK,EAAE,KAAK;YAAEC,OAAO,EAAE;UAAK,CAAC,EAC/B;YAAED,KAAK,EAAE,KAAK;YAAEC,OAAO,EAAE;UAAK,CAAC,CAChC;UACDC,kBAAkB,EAAE,CAClB;YAAEC,IAAI,EAAE,kBAAkB;YAAEC,UAAU,EAAE;UAAI,CAAC,EAC7C;YAAED,IAAI,EAAE,aAAa;YAAEC,UAAU,EAAE;UAAG,CAAC,EACvC;YAAED,IAAI,EAAE,UAAU;YAAEC,UAAU,EAAE;UAAG,CAAC,EACpC;YAAED,IAAI,EAAE,UAAU;YAAEC,UAAU,EAAE;UAAG,CAAC;QAExC,CAAC;QACDC,YAAY,EAAE,CACZ;UAAEF,IAAI,EAAE,sBAAsB;UAAEC,UAAU,EAAE;QAAG,CAAC,EAChD;UAAED,IAAI,EAAE,cAAc;UAAEC,UAAU,EAAE;QAAG,CAAC,EACxC;UAAED,IAAI,EAAE,qBAAqB;UAAEC,UAAU,EAAE;QAAG,CAAC,EAC/C;UAAED,IAAI,EAAE,oBAAoB;UAAEC,UAAU,EAAE;QAAG,CAAC,EAC9C;UAAED,IAAI,EAAE,qBAAqB;UAAEC,UAAU,EAAE;QAAG,CAAC;MAEnD,CAAC;;MAED;MACA;MACA1B,QAAQ,CAACS,SAAS,CAAC;MAEnBH,cAAc,CAAC,IAAIC,IAAI,CAAC,CAAC,CAAC;IAC5B,CAAC,CAAC,OAAOqB,KAAK,EAAE;MACdjE,KAAK,CAACiE,KAAK,CAAC,oDAAoD,CAAC;MACjEC,OAAO,CAACD,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;IAC3C,CAAC,SAAS;MACRxB,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAM0B,cAAc,GAAIC,MAAM,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAACC,MAAM,CAACL,MAAM,CAAC;EACnB,CAAC;EAED,IAAI5B,SAAS,EAAE;IACb,oBACE5B,OAAA;MAAK8D,SAAS,EAAC,uCAAuC;MAAAC,QAAA,eACpD/D,OAAA;QAAK8D,SAAS,EAAC;MAAmE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtF,CAAC;EAEV;EAEA,oBACEnE,OAAA;IAAK8D,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExB/D,OAAA;MAAK8D,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChD/D,OAAA;QAAA+D,QAAA,gBACE/D,OAAA;UAAI8D,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrEnE,OAAA;UAAG8D,SAAS,EAAC,uBAAuB;UAAAC,QAAA,GAAC,8BACb,EAACjC,WAAW,CAACsC,cAAc,CAAC,OAAO,CAAC;QAAA;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACNnE,OAAA;QACEqE,OAAO,EAAEpC,kBAAmB;QAC5B6B,SAAS,EAAC,mHAAmH;QAC7HQ,QAAQ,EAAE1C,SAAU;QAAAmC,QAAA,gBAEpB/D,OAAA,CAACN,MAAM;UAACoE,SAAS,EAAE,GAAGlC,SAAS,GAAG,cAAc,GAAG,EAAE;QAAG;UAAAoC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3DnE,OAAA;UAAA+D,QAAA,EAAM;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNnE,OAAA;MAAK8D,SAAS,EAAC,sDAAsD;MAAAC,QAAA,gBACnE/D,OAAA,CAACJ,SAAS;QACR2E,KAAK,EAAC,iBAAiB;QACvBC,KAAK,EAAE,CAAAhD,KAAK,aAALA,KAAK,wBAAArB,eAAA,GAALqB,KAAK,CAAEW,QAAQ,cAAAhC,eAAA,uBAAfA,eAAA,CAAiBiC,YAAY,KAAI,CAAE;QAC1CqC,MAAM,EAAE,IAAI,CAAAjD,KAAK,aAALA,KAAK,wBAAApB,gBAAA,GAALoB,KAAK,CAAEW,QAAQ,cAAA/B,gBAAA,uBAAfA,gBAAA,CAAiBoC,mBAAmB,KAAI,CAAC,UAAW;QAChEkC,IAAI,eAAE1E,OAAA,CAACV,cAAc;UAACwE,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC7CQ,KAAK,EAAC;MAAM;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC,eAEFnE,OAAA,CAACJ,SAAS;QACR2E,KAAK,EAAC,eAAe;QACrBC,KAAK,EAAEjB,cAAc,CAAC,CAAA/B,KAAK,aAALA,KAAK,wBAAAnB,gBAAA,GAALmB,KAAK,CAAEW,QAAQ,cAAA9B,gBAAA,uBAAfA,gBAAA,CAAiBgC,aAAa,KAAI,CAAC,CAAE;QAC3DoC,MAAM,EAAE,IAAIlB,cAAc,CAAC,CAAA/B,KAAK,aAALA,KAAK,wBAAAlB,gBAAA,GAALkB,KAAK,CAAEW,QAAQ,cAAA7B,gBAAA,uBAAfA,gBAAA,CAAiBmC,oBAAoB,KAAI,CAAC,CAAC,UAAW;QACjFiC,IAAI,eAAE1E,OAAA,CAACT,YAAY;UAACuE,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC3CQ,KAAK,EAAC;MAAO;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC,eAEFnE,OAAA,CAACJ,SAAS;QACR2E,KAAK,EAAC,SAAS;QACfC,KAAK,EAAE,CAAAhD,KAAK,aAALA,KAAK,wBAAAjB,gBAAA,GAALiB,KAAK,CAAEW,QAAQ,cAAA5B,gBAAA,uBAAfA,gBAAA,CAAiB+B,WAAW,KAAI,CAAE;QACzCmC,MAAM,EAAE,IAAI,CAAAjD,KAAK,aAALA,KAAK,wBAAAhB,gBAAA,GAALgB,KAAK,CAAEW,QAAQ,cAAA3B,gBAAA,uBAAfA,gBAAA,CAAiBkC,sBAAsB,KAAI,CAAC,WAAY;QACpEgC,IAAI,eAAE1E,OAAA,CAACX,OAAO;UAACyE,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACtCQ,KAAK,EAAC;MAAQ;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC,eAEFnE,OAAA,CAACJ,SAAS;QACR2E,KAAK,EAAC,UAAU;QAChBC,KAAK,EAAE,CAAAhD,KAAK,aAALA,KAAK,wBAAAf,gBAAA,GAALe,KAAK,CAAEW,QAAQ,cAAA1B,gBAAA,uBAAfA,gBAAA,CAAiB8B,cAAc,KAAI,CAAE;QAC5CkC,MAAM,EAAE,CAAAjD,KAAK,aAALA,KAAK,wBAAAd,gBAAA,GAALc,KAAK,CAAEW,QAAQ,cAAAzB,gBAAA,uBAAfA,gBAAA,CAAiBkC,kBAAkB,IAAG,CAAC,GAC7C,GAAGpB,KAAK,CAACW,QAAQ,CAACS,kBAAkB,aAAa,GACjD,iBACD;QACD8B,IAAI,eAAE1E,OAAA,CAACR,KAAK;UAACsE,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACpCQ,KAAK,EAAE,CAAAnD,KAAK,aAALA,KAAK,wBAAAb,gBAAA,GAALa,KAAK,CAAEW,QAAQ,cAAAxB,gBAAA,uBAAfA,gBAAA,CAAiBiC,kBAAkB,IAAG,CAAC,GAAG,KAAK,GAAG;MAAS;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAGL,CAAA3C,KAAK,aAALA,KAAK,wBAAAZ,iBAAA,GAALY,KAAK,CAAEW,QAAQ,cAAAvB,iBAAA,uBAAfA,iBAAA,CAAiBgC,kBAAkB,IAAG,CAAC,iBACtC5C,OAAA;MAAK8D,SAAS,EAAC,gDAAgD;MAAAC,QAAA,eAC7D/D,OAAA;QAAK8D,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC/D,OAAA,CAACP,qBAAqB;UAACqE,SAAS,EAAC;QAAmB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACvDnE,OAAA;UAAM8D,SAAS,EAAC,cAAc;UAAAC,QAAA,GAAC,aAClB,EAACvC,KAAK,CAACW,QAAQ,CAACS,kBAAkB,EAAC,iCAChD;QAAA;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDnE,OAAA,CAACH,eAAe;MACd+E,SAAS,EAAE;QACTC,MAAM,EAAE,CAAArD,KAAK,aAALA,KAAK,wBAAAX,aAAA,GAALW,KAAK,CAAEqB,MAAM,cAAAhC,aAAA,wBAAAC,qBAAA,GAAbD,aAAA,CAAeiC,eAAe,cAAAhC,qBAAA,uBAA9BA,qBAAA,CAAgCgE,GAAG,CAACC,IAAI,IAAIA,IAAI,CAAChC,KAAK,CAAC,KAAI,EAAE;QACrEiC,IAAI,EAAE,CAAAxD,KAAK,aAALA,KAAK,wBAAAT,cAAA,GAALS,KAAK,CAAEqB,MAAM,cAAA9B,cAAA,wBAAAC,qBAAA,GAAbD,cAAA,CAAe+B,eAAe,cAAA9B,qBAAA,uBAA9BA,qBAAA,CAAgC8D,GAAG,CAACC,IAAI,IAAIA,IAAI,CAAC/B,OAAO,CAAC,KAAI;MACrE,CAAE;MACFiC,YAAY,EAAE;QACZJ,MAAM,EAAE,CAAArD,KAAK,aAALA,KAAK,wBAAAP,cAAA,GAALO,KAAK,CAAEqB,MAAM,cAAA5B,cAAA,wBAAAC,qBAAA,GAAbD,cAAA,CAAegC,kBAAkB,cAAA/B,qBAAA,uBAAjCA,qBAAA,CAAmC4D,GAAG,CAACC,IAAI,IAAIA,IAAI,CAAC7B,IAAI,CAAC,KAAI,EAAE;QACvE8B,IAAI,EAAE,CAAAxD,KAAK,aAALA,KAAK,wBAAAL,cAAA,GAALK,KAAK,CAAEqB,MAAM,cAAA1B,cAAA,wBAAAC,qBAAA,GAAbD,cAAA,CAAe8B,kBAAkB,cAAA7B,qBAAA,uBAAjCA,qBAAA,CAAmC0D,GAAG,CAACC,IAAI,IAAIA,IAAI,CAAC5B,UAAU,CAAC,KAAI;MAC3E,CAAE;MACF+B,WAAW,EAAE,CAAA1D,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE4B,YAAY,KAAI;IAAG;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxC,CAAC,eAGFnE,OAAA;MAAK8D,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBAEpD/D,OAAA;QAAK8D,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC5B/D,OAAA,CAACF,cAAc;UAACqF,UAAU,EAAEzD;QAAe;UAAAsC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC,eAGNnE,OAAA;QAAK8D,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB/D,OAAA;UAAK8D,SAAS,EAAC,0CAA0C;UAAAC,QAAA,gBACvD/D,OAAA;YAAI8D,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChFnE,OAAA;YAAK8D,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B/D,OAAA;cAAG8D,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAC/C,CAAAvC,KAAK,aAALA,KAAK,wBAAAH,iBAAA,GAALG,KAAK,CAAEW,QAAQ,cAAAd,iBAAA,uBAAfA,iBAAA,CAAiBsB,iBAAiB,KAAI;YAAC;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC,eACJnE,OAAA;cAAG8D,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnE,OAAA;UAAK8D,SAAS,EAAC,0CAA0C;UAAAC,QAAA,gBACvD/D,OAAA;YAAI8D,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzEnE,OAAA;YAAK8D,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB/D,OAAA;cAAK8D,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACnC/D,OAAA;gBAAM8D,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACjEnE,OAAA;gBAAM8D,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC,eACNnE,OAAA;cAAK8D,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACnC/D,OAAA;gBAAM8D,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC3DnE,OAAA;gBAAM8D,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAClCR,cAAc,CAAC,CAAC,CAAA/B,KAAK,aAALA,KAAK,wBAAAF,iBAAA,GAALE,KAAK,CAAEW,QAAQ,cAAAb,iBAAA,uBAAfA,iBAAA,CAAiBe,aAAa,KAAI,CAAC,KAAK,CAAAb,KAAK,aAALA,KAAK,wBAAAD,iBAAA,GAALC,KAAK,CAAEW,QAAQ,cAAAZ,iBAAA,uBAAfA,iBAAA,CAAiBa,YAAY,KAAI,CAAC,CAAC;cAAC;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNnE,OAAA;cAAK8D,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACnC/D,OAAA;gBAAM8D,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAClEnE,OAAA;gBAAM8D,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjE,EAAA,CA9MID,SAAS;AAAAmF,EAAA,GAATnF,SAAS;AAgNf,eAAeA,SAAS;AAAC,IAAAmF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}