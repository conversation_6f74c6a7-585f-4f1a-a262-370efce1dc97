import { useState } from 'react';
import { useData } from '../context/DataContext';
import { toast } from 'react-toastify';

// Hook personnalisé pour les opérations CRUD
const useCRUD = (service, entityType) => {
  const { dispatch, DataActions } = useData();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Mapping des types d'entités vers les actions Redux
  const actionMap = {
    products: {
      SET: DataActions.SET_PRODUCTS,
      ADD: DataActions.ADD_PRODUCT,
      UPDATE: DataActions.UPDATE_PRODUCT,
      DELETE: DataActions.DELETE_PRODUCT
    },
    categories: {
      SET: DataActions.SET_CATEGORIES,
      ADD: DataActions.ADD_CATEGORY,
      UPDATE: DataActions.UPDATE_CATEGORY,
      DELETE: DataActions.DELETE_CATEGORY
    },
    orders: {
      SET: DataActions.SET_ORDERS,
      ADD: DataActions.ADD_ORDER,
      UPDATE: DataActions.UPDATE_ORDER,
      DELETE: DataActions.DELETE_ORDER
    },
    users: {
      SET: DataActions.SET_USERS,
      ADD: DataActions.ADD_USER,
      UPDATE: DataActions.UPDATE_USER,
      DELETE: DataActions.DELETE_USER
    },
    promotions: {
      SET: DataActions.SET_PROMOTIONS,
      ADD: DataActions.ADD_PROMOTION,
      UPDATE: DataActions.UPDATE_PROMOTION,
      DELETE: DataActions.DELETE_PROMOTION
    }
  };

  const actions = actionMap[entityType];

  // Récupérer tous les éléments
  const getAll = async (params = {}) => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await service.getAll(params);
      
      if (result.success) {
        dispatch({ type: actions.SET, payload: result.data });
        return { success: true, data: result.data, meta: result.meta };
      } else {
        setError(result.error);
        toast.error(`Erreur lors du chargement des ${entityType}`);
        return { success: false, error: result.error };
      }
    } catch (err) {
      const errorMsg = `Erreur de connexion pour ${entityType}`;
      setError(errorMsg);
      toast.error(errorMsg);
      return { success: false, error: errorMsg };
    } finally {
      setLoading(false);
    }
  };

  // Récupérer un élément par ID
  const getById = async (id) => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await service.getById(id);
      
      if (result.success) {
        return { success: true, data: result.data };
      } else {
        setError(result.error);
        toast.error(`Erreur lors du chargement de l'élément`);
        return { success: false, error: result.error };
      }
    } catch (err) {
      const errorMsg = 'Erreur de connexion';
      setError(errorMsg);
      toast.error(errorMsg);
      return { success: false, error: errorMsg };
    } finally {
      setLoading(false);
    }
  };

  // Créer un nouvel élément
  const create = async (data, successMessage = null) => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await service.create(data);
      
      if (result.success) {
        dispatch({ type: actions.ADD, payload: result.data });
        toast.success(successMessage || `${entityType.slice(0, -1)} créé avec succès`);
        return { success: true, data: result.data };
      } else {
        // Fallback: ajout local avec ID temporaire
        const newItem = {
          ...data,
          id: Date.now(),
          created_at: new Date().toISOString()
        };
        dispatch({ type: actions.ADD, payload: newItem });
        toast.success(`${entityType.slice(0, -1)} créé (mode démonstration)`);
        return { success: true, data: newItem };
      }
    } catch (err) {
      // Fallback: ajout local en cas d'erreur
      const newItem = {
        ...data,
        id: Date.now(),
        created_at: new Date().toISOString()
      };
      dispatch({ type: actions.ADD, payload: newItem });
      toast.success(`${entityType.slice(0, -1)} créé (mode démonstration)`);
      return { success: true, data: newItem };
    } finally {
      setLoading(false);
    }
  };

  // Mettre à jour un élément
  const update = async (id, data, successMessage = null) => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await service.update(id, data);
      
      if (result.success) {
        dispatch({ 
          type: actions.UPDATE, 
          payload: { ...result.data, id }
        });
        toast.success(successMessage || `${entityType.slice(0, -1)} mis à jour avec succès`);
        return { success: true, data: result.data };
      } else {
        // Fallback: mise à jour locale
        dispatch({ 
          type: actions.UPDATE, 
          payload: { ...data, id }
        });
        toast.success(`${entityType.slice(0, -1)} mis à jour (mode démonstration)`);
        return { success: true, data: { ...data, id } };
      }
    } catch (err) {
      // Fallback: mise à jour locale en cas d'erreur
      dispatch({ 
        type: actions.UPDATE, 
        payload: { ...data, id }
      });
      toast.success(`${entityType.slice(0, -1)} mis à jour (mode démonstration)`);
      return { success: true, data: { ...data, id } };
    } finally {
      setLoading(false);
    }
  };

  // Supprimer un élément
  const remove = async (id, successMessage = null) => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await service.delete(id);
      
      if (result.success) {
        dispatch({ type: actions.DELETE, payload: id });
        toast.success(successMessage || `${entityType.slice(0, -1)} supprimé avec succès`);
        return { success: true };
      } else {
        // Fallback: suppression locale
        dispatch({ type: actions.DELETE, payload: id });
        toast.success(`${entityType.slice(0, -1)} supprimé (mode démonstration)`);
        return { success: true };
      }
    } catch (err) {
      // Fallback: suppression locale en cas d'erreur
      dispatch({ type: actions.DELETE, payload: id });
      toast.success(`${entityType.slice(0, -1)} supprimé (mode démonstration)`);
      return { success: true };
    } finally {
      setLoading(false);
    }
  };

  // Rechercher des éléments
  const search = async (query, filters = {}) => {
    if (!service.search) {
      // Fallback: recherche locale si le service ne supporte pas la recherche
      return { success: false, error: 'Recherche non supportée par ce service' };
    }
    
    setLoading(true);
    setError(null);
    
    try {
      const result = await service.search(query, filters);
      
      if (result.success) {
        return { success: true, data: result.data, meta: result.meta };
      } else {
        setError(result.error);
        toast.error('Erreur lors de la recherche');
        return { success: false, error: result.error };
      }
    } catch (err) {
      const errorMsg = 'Erreur de connexion lors de la recherche';
      setError(errorMsg);
      toast.error(errorMsg);
      return { success: false, error: errorMsg };
    } finally {
      setLoading(false);
    }
  };

  return {
    loading,
    error,
    getAll,
    getById,
    create,
    update,
    remove,
    search,
    setError,
    setLoading
  };
};

export default useCRUD;
