import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import axios from 'axios';
import ProductCard from '../components/products/ProductCard';
import ActivePromotions from '../components/promotions/ActivePromotions';
import { FaArrowRight, FaUtensils, FaPizzaSlice, FaCheese, FaCocktail } from 'react-icons/fa';

const HomePage = () => {
  const [featuredProducts, setFeaturedProducts] = useState([]);
  const [newProducts, setNewProducts] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isUsingTestData, setIsUsingTestData] = useState(false);

  useEffect(() => {
    const fetchProducts = async () => {
      try {
        // Appel à l'API pour récupérer les produits en vedette
        const featuredResponse = await axios.get(`${process.env.REACT_APP_API_URL}/api/products`, {
          params: {
            featured: true,
            per_page: 4
          }
        });

        // Appel à l'API pour récupérer les nouveaux produits
        // Vous pouvez ajouter un paramètre dans votre API pour filtrer les nouveaux produits
        // ou utiliser un tri par date de création
        const newResponse = await axios.get(`${process.env.REACT_APP_API_URL}/api/products`, {
          params: {
            sort_by: 'created_at',
            sort_direction: 'desc',
            per_page: 4
          }
        });

        setFeaturedProducts(featuredResponse.data.data.data);
        setNewProducts(newResponse.data.data.data);
        setIsLoading(false);
      } catch (error) {
        console.error('Erreur lors du chargement des produits:', error);

        // Données de test en cas d'erreur de connexion
        const testProducts = [
          {
            id: 1,
            name: 'Poulet Grillé Entier',
            description: 'Poulet entier grillé aux herbes de Provence, tendre et savoureux',
            short_description: 'Poulet entier grillé aux herbes',
            price: 25.99,
            sale_price: null,
            image: 'https://images.unsplash.com/photo-1598103442097-8b74394b95c6?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
            category: { name: 'Produits Grillés' },
            is_featured: true,
            is_active: true,
            stock_quantity: 50
          },
          {
            id: 2,
            name: 'Brochettes de Bœuf',
            description: 'Brochettes de bœuf marinées et grillées, accompagnées de légumes',
            short_description: 'Brochettes de bœuf marinées',
            price: 18.50,
            sale_price: 16.99,
            image: 'https://images.unsplash.com/photo-1529692236671-f1f6cf9683ba?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
            category: { name: 'Produits Grillés' },
            is_featured: true,
            is_active: true,
            stock_quantity: 30
          },
          {
            id: 3,
            name: 'Salade César',
            description: 'Salade César classique avec croûtons, parmesan et sauce maison',
            short_description: 'Salade César classique',
            price: 12.50,
            sale_price: null,
            image: 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
            category: { name: 'Salades' },
            is_featured: true,
            is_active: true,
            stock_quantity: 40
          },
          {
            id: 4,
            name: 'Plateau de Fromages',
            description: 'Sélection de fromages artisanaux avec confiture et noix',
            short_description: 'Plateau de fromages artisanaux',
            price: 19.90,
            sale_price: null,
            image: 'https://images.unsplash.com/photo-1486297678162-eb2a19b0a32d?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
            category: { name: 'Fromages' },
            is_featured: true,
            is_active: true,
            stock_quantity: 15
          }
        ];

        setFeaturedProducts(testProducts);
        setNewProducts(testProducts);
        setIsUsingTestData(false); // Masquer le bandeau pour la démonstration
        console.log('Utilisation des données de test - Mode démonstration');
        setIsLoading(false);
      }
    };

    fetchProducts();
  }, []);

  const categories = [
    {
      id: 1,
      name: "Produits à Griller",
      description: "Viandes, saucisses et brochettes prêtes à griller",
      icon: <FaUtensils className="text-4xl text-yummy-grilled" />,
      color: "bg-yummy-grilled/10 border-yummy-grilled/20",
      textColor: "text-yummy-grilled",
      path: "/produits-grilles"
    },
    {
      id: 2,
      name: "Produits Non-Grillés",
      description: "Salades, sandwichs et plats préparés",
      icon: <FaPizzaSlice className="text-4xl text-yummy-nongrilled" />,
      color: "bg-yummy-nongrilled/10 border-yummy-nongrilled/20",
      textColor: "text-yummy-nongrilled",
      path: "/produits-non-grilles"
    },
    {
      id: 3,
      name: "Fromages",
      description: "Fromages locaux et importés de qualité",
      icon: <FaCheese className="text-4xl text-yummy-cheese" />,
      color: "bg-yummy-cheese/10 border-yummy-cheese/20",
      textColor: "text-yummy-cheese",
      path: "/fromages"
    },
    {
      id: 4,
      name: "Boissons",
      description: "Boissons fraîches, chaudes et alcoolisées",
      icon: <FaCocktail className="text-4xl text-yummy-drinks" />,
      color: "bg-yummy-drinks/10 border-yummy-drinks/20",
      textColor: "text-yummy-drinks",
      path: "/boissons"
    }
  ];

  // Composant Hero Section
  const HeroSection = () => (
    <section className="relative bg-gradient-to-r from-green-50 to-green-100 py-16 md:py-24">
      <div className="container">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
          <div className="space-y-6">
            <motion.h1
              className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              Des produits frais pour <span className="text-primary-600">tous vos repas</span>
            </motion.h1>

            <motion.p
              className="text-lg text-gray-700"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              Découvrez notre sélection de produits à griller, non-grillés, fromages et boissons de qualité pour satisfaire toutes vos envies gourmandes.
            </motion.p>

            <motion.div
              className="flex flex-wrap gap-4"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <Link to="/products" className="btn-primary">
                Découvrir nos produits
              </Link>
              <Link to="/about" className="btn-outline">
                En savoir plus
              </Link>
            </motion.div>
          </div>

          <motion.div
            className="relative"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
          >
            <img
              src="https://images.unsplash.com/photo-1504674900247-0877df9cc836?ixlib=rb-1.2.1&auto=format&fit=crop&w=1200&q=80"
              alt="Produits frais"
              className="rounded-lg shadow-xl"
            />
            <div className="absolute -bottom-6 -right-6 bg-white p-4 rounded-lg shadow-lg">
              <div className="flex items-center gap-2">
                <div className="bg-green-100 p-2 rounded-full">
                  <FaUtensils className="text-primary-600 text-xl" />
                </div>
                <div>
                  <p className="font-semibold text-gray-900">Livraison rapide</p>
                  <p className="text-sm text-gray-600">En 24h chez vous</p>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );

  // Composant Category List
  const CategoryList = () => (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mt-8">
      {categories.map((category) => (
        <motion.div
          key={category.id}
          className={`card p-6 border ${category.color} hover:shadow-lg transition-shadow`}
          whileHover={{ y: -5 }}
          transition={{ duration: 0.3 }}
        >
          <div className="flex flex-col items-center text-center">
            <div className={`p-4 rounded-full ${category.color} mb-4`}>
              {category.icon}
            </div>
            <h3 className={`text-xl font-semibold mb-2 ${category.textColor}`}>
              {category.name}
            </h3>
            <p className="text-gray-600 mb-4">
              {category.description}
            </p>
            <Link
              to={category.path}
              className={`flex items-center ${category.textColor} font-medium hover:underline`}
            >
              Découvrir <FaArrowRight className="ml-2" />
            </Link>
          </div>
        </motion.div>
      ))}
    </div>
  );

  // Composant Featured Products
  const FeaturedProducts = () => (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mt-8">
      {isLoading ? (
        Array(4).fill(0).map((_, index) => (
          <div key={index} className="card animate-pulse">
            <div className="bg-gray-300 aspect-square w-full"></div>
            <div className="p-4 space-y-3">
              <div className="h-4 bg-gray-300 rounded w-1/4"></div>
              <div className="h-6 bg-gray-300 rounded w-3/4"></div>
              <div className="h-4 bg-gray-300 rounded w-1/2"></div>
            </div>
          </div>
        ))
      ) : (
        featuredProducts.map(product => (
          <ProductCard key={product.id} product={product} />
        ))
      )}
    </div>
  );

  // Composant Promo Section
  const PromoSection = () => (
    <section className="py-16 bg-gradient-to-r from-secondary-500 to-secondary-600 text-white">
      <div className="container">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
          <div>
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Offre spéciale du moment</h2>
            <p className="text-lg mb-6">
              Profitez de 15% de réduction sur tous nos produits à griller pour vos barbecues d'été !
            </p>
            <div className="flex flex-wrap gap-4">
              <Link to="/produits-grilles" className="bg-white text-secondary-600 px-6 py-3 rounded-full font-medium hover:bg-gray-100 transition-colors">
                En profiter maintenant
              </Link>
            </div>
          </div>

          <div className="relative">
            <img
              src="https://images.unsplash.com/photo-1544025162-d76694265947?ixlib=rb-1.2.1&auto=format&fit=crop&w=1200&q=80"
              alt="Barbecue"
              className="rounded-lg shadow-xl"
            />
            <div className="absolute top-0 right-0 bg-accent-500 text-white text-xl font-bold p-4 rounded-bl-lg rounded-tr-lg">
              -15%
            </div>
          </div>
        </div>
      </div>
    </section>
  );

  return (
    <div className="bg-white">
      {/* Bandeau d'accès rapide admin pour la démonstration */}
      <div className="bg-gradient-to-r from-green-600 to-green-700 text-white p-3">
        <div className="container mx-auto flex justify-between items-center">
          <div className="flex items-center space-x-2">
            <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span className="text-sm font-medium">Mode Démonstration - Projet YUMMY</span>
          </div>
          <div className="flex items-center space-x-4">
            <a
              href="/admin/dashboard"
              className="bg-white text-green-600 px-4 py-1 rounded text-sm font-medium hover:bg-gray-100 transition-colors"
            >
              🔧 Dashboard Admin
            </a>
            <span className="text-sm opacity-75">Toutes les fonctionnalités sont accessibles</span>
          </div>
        </div>
      </div>

      <HeroSection />

      <section className="py-16 bg-white">
        <div className="container">
          <div className="flex flex-col md:flex-row justify-between items-center mb-8">
            <h2 className="text-3xl font-bold text-gray-900">Nos catégories</h2>
            <Link to="/products" className="flex items-center text-primary-600 font-medium hover:text-primary-700">
              Voir tous les produits <FaArrowRight className="ml-2" />
            </Link>
          </div>
          <CategoryList />
        </div>
      </section>

      <section className="py-16 bg-gray-50">
        <div className="container">
          <h2 className="text-3xl font-bold text-gray-900 mb-8">Produits en vedette</h2>
          <FeaturedProducts />
        </div>
      </section>

      <PromoSection />

      <section className="py-16 bg-gray-50">
        <div className="container">
          <h2 className="text-3xl font-bold text-gray-900 mb-8">Promotions en cours</h2>
          <ActivePromotions />
        </div>
      </section>

      <section className="py-16 bg-white">
        <div className="container">
          <h2 className="text-3xl font-bold text-gray-900 mb-8">Nouveaux produits</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {isLoading ? (
              Array(4).fill(0).map((_, index) => (
                <div key={index} className="card animate-pulse">
                  <div className="bg-gray-300 aspect-square w-full"></div>
                  <div className="p-4 space-y-3">
                    <div className="h-4 bg-gray-300 rounded w-1/4"></div>
                    <div className="h-6 bg-gray-300 rounded w-3/4"></div>
                    <div className="h-4 bg-gray-300 rounded w-1/2"></div>
                  </div>
                </div>
              ))
            ) : (
              newProducts.map(product => (
                <ProductCard key={product.id} product={product} />
              ))
            )}
          </div>
        </div>
      </section>
    </div>
  );
};

export default HomePage;


