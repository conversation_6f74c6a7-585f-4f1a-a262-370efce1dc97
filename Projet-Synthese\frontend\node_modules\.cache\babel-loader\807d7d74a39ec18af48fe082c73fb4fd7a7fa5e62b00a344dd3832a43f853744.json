{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Projet-Synthese\\\\frontend\\\\src\\\\pages\\\\ProductDetailPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, Link, useNavigate } from 'react-router-dom';\nimport { productAPI } from '../services/apiService';\nimport { useCart } from '../context/CartContext';\nimport RelatedProducts from '../components/products/RelatedProducts';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ProductDetailPage = () => {\n  _s();\n  const {\n    id\n  } = useParams();\n  const {\n    dispatch\n  } = useCart();\n  const [product, setProduct] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [quantity, setQuantity] = useState(1);\n  const [selectedGrillingOptions, setSelectedGrillingOptions] = useState({});\n  useEffect(() => {\n    const fetchProduct = async () => {\n      try {\n        const response = await axios.get(`/api/products/${id}`);\n        setProduct(response.data);\n        setLoading(false);\n      } catch (err) {\n        setError('Erreur lors du chargement du produit');\n        setLoading(false);\n        console.error(err);\n      }\n    };\n    fetchProduct();\n  }, [id]);\n  const handleQuantityChange = value => {\n    if (value < 1) return;\n    if (product && product.stock && value > product.stock) return;\n    setQuantity(value);\n  };\n  const handleGrillingOptionChange = (optionId, value) => {\n    setSelectedGrillingOptions(prev => ({\n      ...prev,\n      [optionId]: value\n    }));\n  };\n  const handleAddToCart = () => {\n    if (!product) return;\n    dispatch({\n      type: 'ADD_ITEM',\n      payload: {\n        id: product.id,\n        name: product.name,\n        price: product.sale_price || product.price,\n        image: product.image,\n        quantity: quantity,\n        grillingOptions: selectedGrillingOptions\n      }\n    });\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 py-12 flex justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-500\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 7\n    }, this);\n  }\n  if (error || !product) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 py-12\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\",\n        children: error || \"Produit non trouvé\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Vérifier si le produit est en promotion\n  const isOnSale = product.sale_price && product.sale_price < product.price;\n\n  // Calculer le pourcentage de réduction si en promotion\n  const discountPercentage = isOnSale ? Math.round((product.price - product.sale_price) / product.price * 100) : 0;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container mx-auto px-4 py-12\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6 text-sm\",\n      children: [/*#__PURE__*/_jsxDEV(Link, {\n        to: \"/\",\n        className: \"text-gray-500 hover:text-green-600\",\n        children: \"Accueil\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"mx-2 text-gray-400\",\n        children: \"/\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/products\",\n        className: \"text-gray-500 hover:text-green-600\",\n        children: \"Produits\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"mx-2 text-gray-400\",\n        children: \"/\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-gray-700\",\n        children: product.name\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-md overflow-hidden mb-10\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"md:flex\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"md:w-1/2 relative\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: product.image || '/placeholder.png',\n            alt: product.name,\n            className: \"w-full h-full object-cover\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 13\n          }, this), isOnSale && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute top-4 left-4 bg-red-500 text-white font-bold px-3 py-1 rounded\",\n            children: [\"-\", discountPercentage, \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 15\n          }, this), product.is_organic && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute top-4 right-4 bg-green-500 text-white font-bold px-3 py-1 rounded\",\n            children: \"BIO\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"md:w-1/2 p-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-3xl font-bold mb-2\",\n            children: product.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-baseline\",\n              children: isOnSale ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-2xl font-bold text-red-600 mr-2\",\n                  children: [product.sale_price.toFixed(2), \" \\u20AC\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 132,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-lg text-gray-500 line-through\",\n                  children: [product.price.toFixed(2), \" \\u20AC\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 135,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-2xl font-bold\",\n                children: [product.price.toFixed(2), \" \\u20AC\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 15\n            }, this), product.price_per_unit && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"ml-4 text-sm text-gray-500\",\n              children: [\"(\", product.price_per_unit.toFixed(2), \" \\u20AC / \", product.unit, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-6\",\n            children: product.stock > 0 ? /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-green-600 font-medium\",\n              children: [\"En stock (\", product.stock, \" disponibles)\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-red-600 font-medium\",\n              children: \"Rupture de stock\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-lg font-semibold mb-2\",\n              children: \"Description\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-700\",\n              children: product.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-lg font-semibold mb-2\",\n              children: \"Caract\\xE9ristiques\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"text-gray-700 space-y-1\",\n              children: [product.weight && /*#__PURE__*/_jsxDEV(\"li\", {\n                children: [\"Poids/Volume: \", product.weight, \" \", product.unit]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 178,\n                columnNumber: 19\n              }, this), product.origin && /*#__PURE__*/_jsxDEV(\"li\", {\n                children: [\"Origine: \", product.origin]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 19\n              }, this), product.is_organic && /*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-5 h-5 text-green-500 mr-1\",\n                  fill: \"currentColor\",\n                  viewBox: \"0 0 20 20\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    fillRule: \"evenodd\",\n                    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                    clipRule: \"evenodd\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 186,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 185,\n                  columnNumber: 21\n                }, this), \"Produit biologique\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this), product.grilling_options && product.grilling_options.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-lg font-semibold mb-2\",\n              children: \"Options de cuisson\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: product.grilling_options.map(option => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"radio\",\n                  id: `option-${option.id}`,\n                  name: \"grilling-option\",\n                  className: \"mr-2\",\n                  onChange: () => handleGrillingOptionChange(option.id, true),\n                  checked: selectedGrillingOptions[option.id] === true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 201,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: `option-${option.id}`,\n                  className: \"flex-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-medium\",\n                    children: option.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 210,\n                    columnNumber: 25\n                  }, this), option.additional_cost > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-2 text-sm text-gray-500\",\n                    children: [\"(+\", option.additional_cost.toFixed(2), \" \\u20AC)\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 212,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-600\",\n                    children: option.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 216,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 209,\n                  columnNumber: 23\n                }, this)]\n              }, option.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center border rounded-md\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleQuantityChange(quantity - 1),\n                className: \"px-3 py-1 text-gray-600 hover:bg-gray-100\",\n                disabled: quantity <= 1,\n                children: \"-\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"px-4 py-1\",\n                children: quantity\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleQuantityChange(quantity + 1),\n                className: \"px-3 py-1 text-gray-600 hover:bg-gray-100\",\n                disabled: product.stock && quantity >= product.stock,\n                children: \"+\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleAddToCart,\n              disabled: product.stock <= 0,\n              className: `flex-1 py-2 px-4 rounded-md ${product.stock > 0 ? 'bg-green-600 text-white hover:bg-green-700' : 'bg-gray-300 text-gray-500 cursor-not-allowed'} transition`,\n              children: product.stock > 0 ? 'Ajouter au panier' : 'Indisponible'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-12\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-2xl font-bold mb-6\",\n        children: \"Produits similaires\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 262,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(RelatedProducts, {\n        categoryId: product.category_id,\n        currentProductId: product.id\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 261,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 88,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductDetailPage, \"wkgfrrtoBKHbTqw+zV7yhRdR5sU=\", false, function () {\n  return [useParams, useCart];\n});\n_c = ProductDetailPage;\nexport default ProductDetailPage;\nvar _c;\n$RefreshReg$(_c, \"ProductDetailPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "Link", "useNavigate", "productAPI", "useCart", "RelatedProducts", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProductDetailPage", "_s", "id", "dispatch", "product", "setProduct", "loading", "setLoading", "error", "setError", "quantity", "setQuantity", "selectedGrillingOptions", "setSelectedGrillingOptions", "fetchProduct", "response", "axios", "get", "data", "err", "console", "handleQuantityChange", "value", "stock", "handleGrillingOptionChange", "optionId", "prev", "handleAddToCart", "type", "payload", "name", "price", "sale_price", "image", "grillingOptions", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "isOnSale", "discountPercentage", "Math", "round", "to", "src", "alt", "is_organic", "toFixed", "price_per_unit", "unit", "description", "weight", "origin", "fill", "viewBox", "fillRule", "d", "clipRule", "grilling_options", "length", "map", "option", "onChange", "checked", "htmlFor", "additional_cost", "onClick", "disabled", "categoryId", "category_id", "currentProductId", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet-Synthese/frontend/src/pages/ProductDetailPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { usePara<PERSON>, Link, useNavigate } from 'react-router-dom';\nimport { productAPI } from '../services/apiService';\nimport { useCart } from '../context/CartContext';\nimport RelatedProducts from '../components/products/RelatedProducts';\n\nconst ProductDetailPage = () => {\n  const { id } = useParams();\n  const { dispatch } = useCart();\n  const [product, setProduct] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [quantity, setQuantity] = useState(1);\n  const [selectedGrillingOptions, setSelectedGrillingOptions] = useState({});\n  \n  useEffect(() => {\n    const fetchProduct = async () => {\n      try {\n        const response = await axios.get(`/api/products/${id}`);\n        setProduct(response.data);\n        setLoading(false);\n      } catch (err) {\n        setError('Erreur lors du chargement du produit');\n        setLoading(false);\n        console.error(err);\n      }\n    };\n\n    fetchProduct();\n  }, [id]);\n\n  const handleQuantityChange = (value) => {\n    if (value < 1) return;\n    if (product && product.stock && value > product.stock) return;\n    setQuantity(value);\n  };\n\n  const handleGrillingOptionChange = (optionId, value) => {\n    setSelectedGrillingOptions(prev => ({\n      ...prev,\n      [optionId]: value\n    }));\n  };\n\n  const handleAddToCart = () => {\n    if (!product) return;\n    \n    dispatch({\n      type: 'ADD_ITEM',\n      payload: {\n        id: product.id,\n        name: product.name,\n        price: product.sale_price || product.price,\n        image: product.image,\n        quantity: quantity,\n        grillingOptions: selectedGrillingOptions\n      }\n    });\n  };\n\n  if (loading) {\n    return (\n      <div className=\"container mx-auto px-4 py-12 flex justify-center\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-500\"></div>\n      </div>\n    );\n  }\n\n  if (error || !product) {\n    return (\n      <div className=\"container mx-auto px-4 py-12\">\n        <div className=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\">\n          {error || \"Produit non trouvé\"}\n        </div>\n      </div>\n    );\n  }\n\n  // Vérifier si le produit est en promotion\n  const isOnSale = product.sale_price && product.sale_price < product.price;\n  \n  // Calculer le pourcentage de réduction si en promotion\n  const discountPercentage = isOnSale \n    ? Math.round(((product.price - product.sale_price) / product.price) * 100) \n    : 0;\n\n  return (\n    <div className=\"container mx-auto px-4 py-12\">\n      {/* Fil d'Ariane */}\n      <div className=\"mb-6 text-sm\">\n        <Link to=\"/\" className=\"text-gray-500 hover:text-green-600\">Accueil</Link>\n        <span className=\"mx-2 text-gray-400\">/</span>\n        <Link to=\"/products\" className=\"text-gray-500 hover:text-green-600\">Produits</Link>\n        <span className=\"mx-2 text-gray-400\">/</span>\n        <span className=\"text-gray-700\">{product.name}</span>\n      </div>\n      \n      <div className=\"bg-white rounded-lg shadow-md overflow-hidden mb-10\">\n        <div className=\"md:flex\">\n          {/* Image du produit */}\n          <div className=\"md:w-1/2 relative\">\n            <img \n              src={product.image || '/placeholder.png'} \n              alt={product.name} \n              className=\"w-full h-full object-cover\"\n            />\n            \n            {/* Badge de promotion */}\n            {isOnSale && (\n              <div className=\"absolute top-4 left-4 bg-red-500 text-white font-bold px-3 py-1 rounded\">\n                -{discountPercentage}%\n              </div>\n            )}\n            \n            {/* Badge bio si applicable */}\n            {product.is_organic && (\n              <div className=\"absolute top-4 right-4 bg-green-500 text-white font-bold px-3 py-1 rounded\">\n                BIO\n              </div>\n            )}\n          </div>\n          \n          {/* Informations du produit */}\n          <div className=\"md:w-1/2 p-8\">\n            <h1 className=\"text-3xl font-bold mb-2\">{product.name}</h1>\n            \n            <div className=\"flex items-center mb-4\">\n              {/* Affichage du prix */}\n              <div className=\"flex items-baseline\">\n                {isOnSale ? (\n                  <>\n                    <span className=\"text-2xl font-bold text-red-600 mr-2\">\n                      {product.sale_price.toFixed(2)} €\n                    </span>\n                    <span className=\"text-lg text-gray-500 line-through\">\n                      {product.price.toFixed(2)} €\n                    </span>\n                  </>\n                ) : (\n                  <span className=\"text-2xl font-bold\">\n                    {product.price.toFixed(2)} €\n                  </span>\n                )}\n              </div>\n              \n              {/* Affichage du prix au kilo/litre si disponible */}\n              {product.price_per_unit && (\n                <span className=\"ml-4 text-sm text-gray-500\">\n                  ({product.price_per_unit.toFixed(2)} € / {product.unit})\n                </span>\n              )}\n            </div>\n            \n            {/* Disponibilité */}\n            <div className=\"mb-6\">\n              {product.stock > 0 ? (\n                <span className=\"text-green-600 font-medium\">\n                  En stock ({product.stock} disponibles)\n                </span>\n              ) : (\n                <span className=\"text-red-600 font-medium\">\n                  Rupture de stock\n                </span>\n              )}\n            </div>\n            \n            {/* Description */}\n            <div className=\"mb-6\">\n              <h2 className=\"text-lg font-semibold mb-2\">Description</h2>\n              <p className=\"text-gray-700\">{product.description}</p>\n            </div>\n            \n            {/* Caractéristiques */}\n            <div className=\"mb-6\">\n              <h2 className=\"text-lg font-semibold mb-2\">Caractéristiques</h2>\n              <ul className=\"text-gray-700 space-y-1\">\n                {product.weight && (\n                  <li>Poids/Volume: {product.weight} {product.unit}</li>\n                )}\n                {product.origin && (\n                  <li>Origine: {product.origin}</li>\n                )}\n                {product.is_organic && (\n                  <li className=\"flex items-center\">\n                    <svg className=\"w-5 h-5 text-green-500 mr-1\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n                    </svg>\n                    Produit biologique\n                  </li>\n                )}\n              </ul>\n            </div>\n            \n            {/* Options de cuisson pour les viandes */}\n            {product.grilling_options && product.grilling_options.length > 0 && (\n              <div className=\"mb-6\">\n                <h2 className=\"text-lg font-semibold mb-2\">Options de cuisson</h2>\n                <div className=\"space-y-3\">\n                  {product.grilling_options.map(option => (\n                    <div key={option.id} className=\"flex items-center\">\n                      <input\n                        type=\"radio\"\n                        id={`option-${option.id}`}\n                        name=\"grilling-option\"\n                        className=\"mr-2\"\n                        onChange={() => handleGrillingOptionChange(option.id, true)}\n                        checked={selectedGrillingOptions[option.id] === true}\n                      />\n                      <label htmlFor={`option-${option.id}`} className=\"flex-1\">\n                        <span className=\"font-medium\">{option.name}</span>\n                        {option.additional_cost > 0 && (\n                          <span className=\"ml-2 text-sm text-gray-500\">\n                            (+{option.additional_cost.toFixed(2)} €)\n                          </span>\n                        )}\n                        <p className=\"text-sm text-gray-600\">{option.description}</p>\n                      </label>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            )}\n            \n            {/* Quantité et ajout au panier */}\n            <div className=\"flex items-center space-x-4\">\n              <div className=\"flex items-center border rounded-md\">\n                <button\n                  onClick={() => handleQuantityChange(quantity - 1)}\n                  className=\"px-3 py-1 text-gray-600 hover:bg-gray-100\"\n                  disabled={quantity <= 1}\n                >\n                  -\n                </button>\n                <span className=\"px-4 py-1\">{quantity}</span>\n                <button\n                  onClick={() => handleQuantityChange(quantity + 1)}\n                  className=\"px-3 py-1 text-gray-600 hover:bg-gray-100\"\n                  disabled={product.stock && quantity >= product.stock}\n                >\n                  +\n                </button>\n              </div>\n              \n              <button\n                onClick={handleAddToCart}\n                disabled={product.stock <= 0}\n                className={`flex-1 py-2 px-4 rounded-md ${\n                  product.stock > 0\n                    ? 'bg-green-600 text-white hover:bg-green-700'\n                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'\n                } transition`}\n              >\n                {product.stock > 0 ? 'Ajouter au panier' : 'Indisponible'}\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n      \n      {/* Produits similaires */}\n      <div className=\"mt-12\">\n        <h2 className=\"text-2xl font-bold mb-6\">Produits similaires</h2>\n        <RelatedProducts categoryId={product.category_id} currentProductId={product.id} />\n      </div>\n    </div>\n  );\n};\n\nexport default ProductDetailPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AAC/D,SAASC,UAAU,QAAQ,wBAAwB;AACnD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,eAAe,MAAM,wCAAwC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErE,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAM;IAAEC;EAAG,CAAC,GAAGZ,SAAS,CAAC,CAAC;EAC1B,MAAM;IAAEa;EAAS,CAAC,GAAGT,OAAO,CAAC,CAAC;EAC9B,MAAM,CAACU,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACoB,KAAK,EAAEC,QAAQ,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACsB,QAAQ,EAAEC,WAAW,CAAC,GAAGvB,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACwB,uBAAuB,EAAEC,0BAA0B,CAAC,GAAGzB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAE1EC,SAAS,CAAC,MAAM;IACd,MAAMyB,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAACC,GAAG,CAAC,iBAAiBf,EAAE,EAAE,CAAC;QACvDG,UAAU,CAACU,QAAQ,CAACG,IAAI,CAAC;QACzBX,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC,CAAC,OAAOY,GAAG,EAAE;QACZV,QAAQ,CAAC,sCAAsC,CAAC;QAChDF,UAAU,CAAC,KAAK,CAAC;QACjBa,OAAO,CAACZ,KAAK,CAACW,GAAG,CAAC;MACpB;IACF,CAAC;IAEDL,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,CAACZ,EAAE,CAAC,CAAC;EAER,MAAMmB,oBAAoB,GAAIC,KAAK,IAAK;IACtC,IAAIA,KAAK,GAAG,CAAC,EAAE;IACf,IAAIlB,OAAO,IAAIA,OAAO,CAACmB,KAAK,IAAID,KAAK,GAAGlB,OAAO,CAACmB,KAAK,EAAE;IACvDZ,WAAW,CAACW,KAAK,CAAC;EACpB,CAAC;EAED,MAAME,0BAA0B,GAAGA,CAACC,QAAQ,EAAEH,KAAK,KAAK;IACtDT,0BAA0B,CAACa,IAAI,KAAK;MAClC,GAAGA,IAAI;MACP,CAACD,QAAQ,GAAGH;IACd,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMK,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI,CAACvB,OAAO,EAAE;IAEdD,QAAQ,CAAC;MACPyB,IAAI,EAAE,UAAU;MAChBC,OAAO,EAAE;QACP3B,EAAE,EAAEE,OAAO,CAACF,EAAE;QACd4B,IAAI,EAAE1B,OAAO,CAAC0B,IAAI;QAClBC,KAAK,EAAE3B,OAAO,CAAC4B,UAAU,IAAI5B,OAAO,CAAC2B,KAAK;QAC1CE,KAAK,EAAE7B,OAAO,CAAC6B,KAAK;QACpBvB,QAAQ,EAAEA,QAAQ;QAClBwB,eAAe,EAAEtB;MACnB;IACF,CAAC,CAAC;EACJ,CAAC;EAED,IAAIN,OAAO,EAAE;IACX,oBACET,OAAA;MAAKsC,SAAS,EAAC,kDAAkD;MAAAC,QAAA,eAC/DvC,OAAA;QAAKsC,SAAS,EAAC;MAA4E;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/F,CAAC;EAEV;EAEA,IAAIhC,KAAK,IAAI,CAACJ,OAAO,EAAE;IACrB,oBACEP,OAAA;MAAKsC,SAAS,EAAC,8BAA8B;MAAAC,QAAA,eAC3CvC,OAAA;QAAKsC,SAAS,EAAC,iEAAiE;QAAAC,QAAA,EAC7E5B,KAAK,IAAI;MAAoB;QAAA6B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,MAAMC,QAAQ,GAAGrC,OAAO,CAAC4B,UAAU,IAAI5B,OAAO,CAAC4B,UAAU,GAAG5B,OAAO,CAAC2B,KAAK;;EAEzE;EACA,MAAMW,kBAAkB,GAAGD,QAAQ,GAC/BE,IAAI,CAACC,KAAK,CAAE,CAACxC,OAAO,CAAC2B,KAAK,GAAG3B,OAAO,CAAC4B,UAAU,IAAI5B,OAAO,CAAC2B,KAAK,GAAI,GAAG,CAAC,GACxE,CAAC;EAEL,oBACElC,OAAA;IAAKsC,SAAS,EAAC,8BAA8B;IAAAC,QAAA,gBAE3CvC,OAAA;MAAKsC,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3BvC,OAAA,CAACN,IAAI;QAACsD,EAAE,EAAC,GAAG;QAACV,SAAS,EAAC,oCAAoC;QAAAC,QAAA,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC1E3C,OAAA;QAAMsC,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAAC;MAAC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC7C3C,OAAA,CAACN,IAAI;QAACsD,EAAE,EAAC,WAAW;QAACV,SAAS,EAAC,oCAAoC;QAAAC,QAAA,EAAC;MAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACnF3C,OAAA;QAAMsC,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAAC;MAAC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC7C3C,OAAA;QAAMsC,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAEhC,OAAO,CAAC0B;MAAI;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClD,CAAC,eAEN3C,OAAA;MAAKsC,SAAS,EAAC,qDAAqD;MAAAC,QAAA,eAClEvC,OAAA;QAAKsC,SAAS,EAAC,SAAS;QAAAC,QAAA,gBAEtBvC,OAAA;UAAKsC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCvC,OAAA;YACEiD,GAAG,EAAE1C,OAAO,CAAC6B,KAAK,IAAI,kBAAmB;YACzCc,GAAG,EAAE3C,OAAO,CAAC0B,IAAK;YAClBK,SAAS,EAAC;UAA4B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,EAGDC,QAAQ,iBACP5C,OAAA;YAAKsC,SAAS,EAAC,yEAAyE;YAAAC,QAAA,GAAC,GACtF,EAACM,kBAAkB,EAAC,GACvB;UAAA;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CACN,EAGApC,OAAO,CAAC4C,UAAU,iBACjBnD,OAAA;YAAKsC,SAAS,EAAC,4EAA4E;YAAAC,QAAA,EAAC;UAE5F;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGN3C,OAAA;UAAKsC,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BvC,OAAA;YAAIsC,SAAS,EAAC,yBAAyB;YAAAC,QAAA,EAAEhC,OAAO,CAAC0B;UAAI;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAE3D3C,OAAA;YAAKsC,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBAErCvC,OAAA;cAAKsC,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EACjCK,QAAQ,gBACP5C,OAAA,CAAAE,SAAA;gBAAAqC,QAAA,gBACEvC,OAAA;kBAAMsC,SAAS,EAAC,sCAAsC;kBAAAC,QAAA,GACnDhC,OAAO,CAAC4B,UAAU,CAACiB,OAAO,CAAC,CAAC,CAAC,EAAC,SACjC;gBAAA;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACP3C,OAAA;kBAAMsC,SAAS,EAAC,oCAAoC;kBAAAC,QAAA,GACjDhC,OAAO,CAAC2B,KAAK,CAACkB,OAAO,CAAC,CAAC,CAAC,EAAC,SAC5B;gBAAA;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA,eACP,CAAC,gBAEH3C,OAAA;gBAAMsC,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,GACjChC,OAAO,CAAC2B,KAAK,CAACkB,OAAO,CAAC,CAAC,CAAC,EAAC,SAC5B;cAAA;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YACP;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,EAGLpC,OAAO,CAAC8C,cAAc,iBACrBrD,OAAA;cAAMsC,SAAS,EAAC,4BAA4B;cAAAC,QAAA,GAAC,GAC1C,EAAChC,OAAO,CAAC8C,cAAc,CAACD,OAAO,CAAC,CAAC,CAAC,EAAC,YAAK,EAAC7C,OAAO,CAAC+C,IAAI,EAAC,GACzD;YAAA;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGN3C,OAAA;YAAKsC,SAAS,EAAC,MAAM;YAAAC,QAAA,EAClBhC,OAAO,CAACmB,KAAK,GAAG,CAAC,gBAChB1B,OAAA;cAAMsC,SAAS,EAAC,4BAA4B;cAAAC,QAAA,GAAC,YACjC,EAAChC,OAAO,CAACmB,KAAK,EAAC,eAC3B;YAAA;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,gBAEP3C,OAAA;cAAMsC,SAAS,EAAC,0BAA0B;cAAAC,QAAA,EAAC;YAE3C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UACP;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGN3C,OAAA;YAAKsC,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBvC,OAAA;cAAIsC,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3D3C,OAAA;cAAGsC,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAEhC,OAAO,CAACgD;YAAW;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC,eAGN3C,OAAA;YAAKsC,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBvC,OAAA;cAAIsC,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChE3C,OAAA;cAAIsC,SAAS,EAAC,yBAAyB;cAAAC,QAAA,GACpChC,OAAO,CAACiD,MAAM,iBACbxD,OAAA;gBAAAuC,QAAA,GAAI,gBAAc,EAAChC,OAAO,CAACiD,MAAM,EAAC,GAAC,EAACjD,OAAO,CAAC+C,IAAI;cAAA;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACtD,EACApC,OAAO,CAACkD,MAAM,iBACbzD,OAAA;gBAAAuC,QAAA,GAAI,WAAS,EAAChC,OAAO,CAACkD,MAAM;cAAA;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAClC,EACApC,OAAO,CAAC4C,UAAU,iBACjBnD,OAAA;gBAAIsC,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAC/BvC,OAAA;kBAAKsC,SAAS,EAAC,6BAA6B;kBAACoB,IAAI,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAApB,QAAA,eAClFvC,OAAA;oBAAM4D,QAAQ,EAAC,SAAS;oBAACC,CAAC,EAAC,uIAAuI;oBAACC,QAAQ,EAAC;kBAAS;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrL,CAAC,sBAER;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CACL;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,EAGLpC,OAAO,CAACwD,gBAAgB,IAAIxD,OAAO,CAACwD,gBAAgB,CAACC,MAAM,GAAG,CAAC,iBAC9DhE,OAAA;YAAKsC,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBvC,OAAA;cAAIsC,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClE3C,OAAA;cAAKsC,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBhC,OAAO,CAACwD,gBAAgB,CAACE,GAAG,CAACC,MAAM,iBAClClE,OAAA;gBAAqBsC,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChDvC,OAAA;kBACE+B,IAAI,EAAC,OAAO;kBACZ1B,EAAE,EAAE,UAAU6D,MAAM,CAAC7D,EAAE,EAAG;kBAC1B4B,IAAI,EAAC,iBAAiB;kBACtBK,SAAS,EAAC,MAAM;kBAChB6B,QAAQ,EAAEA,CAAA,KAAMxC,0BAA0B,CAACuC,MAAM,CAAC7D,EAAE,EAAE,IAAI,CAAE;kBAC5D+D,OAAO,EAAErD,uBAAuB,CAACmD,MAAM,CAAC7D,EAAE,CAAC,KAAK;gBAAK;kBAAAmC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC,eACF3C,OAAA;kBAAOqE,OAAO,EAAE,UAAUH,MAAM,CAAC7D,EAAE,EAAG;kBAACiC,SAAS,EAAC,QAAQ;kBAAAC,QAAA,gBACvDvC,OAAA;oBAAMsC,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAE2B,MAAM,CAACjC;kBAAI;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,EACjDuB,MAAM,CAACI,eAAe,GAAG,CAAC,iBACzBtE,OAAA;oBAAMsC,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,GAAC,IACzC,EAAC2B,MAAM,CAACI,eAAe,CAAClB,OAAO,CAAC,CAAC,CAAC,EAAC,UACvC;kBAAA;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CACP,eACD3C,OAAA;oBAAGsC,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAE2B,MAAM,CAACX;kBAAW;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxD,CAAC;cAAA,GAjBAuB,MAAM,CAAC7D,EAAE;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAkBd,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAGD3C,OAAA;YAAKsC,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1CvC,OAAA;cAAKsC,SAAS,EAAC,qCAAqC;cAAAC,QAAA,gBAClDvC,OAAA;gBACEuE,OAAO,EAAEA,CAAA,KAAM/C,oBAAoB,CAACX,QAAQ,GAAG,CAAC,CAAE;gBAClDyB,SAAS,EAAC,2CAA2C;gBACrDkC,QAAQ,EAAE3D,QAAQ,IAAI,CAAE;gBAAA0B,QAAA,EACzB;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT3C,OAAA;gBAAMsC,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAE1B;cAAQ;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC7C3C,OAAA;gBACEuE,OAAO,EAAEA,CAAA,KAAM/C,oBAAoB,CAACX,QAAQ,GAAG,CAAC,CAAE;gBAClDyB,SAAS,EAAC,2CAA2C;gBACrDkC,QAAQ,EAAEjE,OAAO,CAACmB,KAAK,IAAIb,QAAQ,IAAIN,OAAO,CAACmB,KAAM;gBAAAa,QAAA,EACtD;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAEN3C,OAAA;cACEuE,OAAO,EAAEzC,eAAgB;cACzB0C,QAAQ,EAAEjE,OAAO,CAACmB,KAAK,IAAI,CAAE;cAC7BY,SAAS,EAAE,+BACT/B,OAAO,CAACmB,KAAK,GAAG,CAAC,GACb,4CAA4C,GAC5C,8CAA8C,aACtC;cAAAa,QAAA,EAEbhC,OAAO,CAACmB,KAAK,GAAG,CAAC,GAAG,mBAAmB,GAAG;YAAc;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN3C,OAAA;MAAKsC,SAAS,EAAC,OAAO;MAAAC,QAAA,gBACpBvC,OAAA;QAAIsC,SAAS,EAAC,yBAAyB;QAAAC,QAAA,EAAC;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAChE3C,OAAA,CAACF,eAAe;QAAC2E,UAAU,EAAElE,OAAO,CAACmE,WAAY;QAACC,gBAAgB,EAAEpE,OAAO,CAACF;MAAG;QAAAmC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/E,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvC,EAAA,CApQID,iBAAiB;EAAA,QACNV,SAAS,EACHI,OAAO;AAAA;AAAA+E,EAAA,GAFxBzE,iBAAiB;AAsQvB,eAAeA,iBAAiB;AAAC,IAAAyE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}