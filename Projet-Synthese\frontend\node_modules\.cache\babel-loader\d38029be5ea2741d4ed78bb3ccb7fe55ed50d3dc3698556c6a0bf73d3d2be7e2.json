{"ast": null, "code": "import axios from 'axios';\n\n// Configuration de base pour les appels API\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';\n\n// Instance axios configurée\nconst apiClient = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json',\n    'Accept': 'application/json'\n  }\n});\n\n// Intercepteur pour ajouter le token d'authentification\napiClient.interceptors.request.use(config => {\n  const token = localStorage.getItem('auth_token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// Service Dashboard API\nexport const dashboardService = {\n  // Récupérer les statistiques générales\n  getStats: async () => {\n    try {\n      const response = await apiClient.get('/api/admin/dashboard/stats');\n      return {\n        success: true,\n        data: response.data.data || response.data\n      };\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Erreur API stats:', error);\n      return {\n        success: false,\n        error: ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Erreur de connexion',\n        // Pas de données de fallback - forcer l'utilisation de Laravel\n        data: {\n          totalProducts: 0,\n          totalOrders: 0,\n          totalRevenue: 0,\n          totalUsers: 0,\n          lowStockProducts: 0,\n          pendingOrders: 0,\n          monthlyRevenue: 0,\n          weeklyOrders: 0\n        }\n      };\n    }\n  },\n  // Récupérer les données pour les graphiques\n  getChartData: async (period = '7days') => {\n    try {\n      const response = await apiClient.get(`/api/admin/dashboard/charts?period=${period}`);\n      return {\n        success: true,\n        data: response.data.data || response.data\n      };\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      console.error('Erreur API charts:', error);\n      return {\n        success: false,\n        error: ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || 'Erreur de connexion',\n        // Données de fallback pour la démonstration\n        data: {\n          salesChart: {\n            labels: ['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'],\n            datasets: [{\n              label: 'Ventes (MAD)',\n              data: [1200, 1900, 800, 1500, 2000, 2400, 1800],\n              borderColor: 'rgb(34, 197, 94)',\n              backgroundColor: 'rgba(34, 197, 94, 0.1)',\n              tension: 0.4\n            }]\n          },\n          ordersChart: {\n            labels: ['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'],\n            datasets: [{\n              label: 'Commandes',\n              data: [12, 19, 8, 15, 20, 24, 18],\n              backgroundColor: ['#ef4444', '#f97316', '#eab308', '#22c55e', '#3b82f6', '#8b5cf6', '#ec4899']\n            }]\n          },\n          categoriesChart: {\n            labels: ['Produits Grillés', 'Salades', 'Fromages', 'Boissons', 'Desserts'],\n            datasets: [{\n              data: [35, 25, 20, 15, 5],\n              backgroundColor: ['#ef4444', '#22c55e', '#eab308', '#3b82f6', '#8b5cf6']\n            }]\n          }\n        }\n      };\n    }\n  },\n  // Récupérer les activités récentes\n  getRecentActivity: async (limit = 10) => {\n    try {\n      const response = await apiClient.get(`/api/admin/dashboard/activity?limit=${limit}`);\n      return {\n        success: true,\n        data: response.data.data || response.data\n      };\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      console.error('Erreur API activity:', error);\n      return {\n        success: false,\n        error: ((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.message) || 'Erreur de connexion',\n        // Pas de données de fallback - forcer l'utilisation de Laravel\n        data: []\n      };\n    }\n  },\n  // Récupérer les produits en stock faible\n  getLowStockProducts: async () => {\n    try {\n      const response = await apiClient.get('/api/admin/dashboard/low-stock');\n      return {\n        success: true,\n        data: response.data.data || response.data\n      };\n    } catch (error) {\n      var _error$response4, _error$response4$data;\n      console.error('Erreur API low-stock:', error);\n      return {\n        success: false,\n        error: ((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.message) || 'Erreur de connexion',\n        // Pas de données de fallback - forcer l'utilisation de Laravel\n        data: []\n      };\n    }\n  },\n  // Récupérer les commandes récentes\n  getRecentOrders: async (limit = 5) => {\n    try {\n      const response = await apiClient.get(`/api/admin/dashboard/recent-orders?limit=${limit}`);\n      return {\n        success: true,\n        data: response.data.data || response.data\n      };\n    } catch (error) {\n      var _error$response5, _error$response5$data;\n      console.error('Erreur API recent-orders:', error);\n      return {\n        success: false,\n        error: ((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : (_error$response5$data = _error$response5.data) === null || _error$response5$data === void 0 ? void 0 : _error$response5$data.message) || 'Erreur de connexion',\n        // Données de fallback pour la démonstration\n        data: [{\n          id: 1234,\n          user: {\n            name: 'Marie Dubois',\n            email: '<EMAIL>'\n          },\n          total: 89.50,\n          status: 'pending',\n          created_at: '2024-01-15T10:30:00Z',\n          items_count: 3\n        }, {\n          id: 1233,\n          user: {\n            name: 'Ahmed Benali',\n            email: '<EMAIL>'\n          },\n          total: 156.75,\n          status: 'completed',\n          created_at: '2024-01-15T09:15:00Z',\n          items_count: 5\n        }, {\n          id: 1232,\n          user: {\n            name: 'Fatima Zahra',\n            email: '<EMAIL>'\n          },\n          total: 234.25,\n          status: 'processing',\n          created_at: '2024-01-15T08:45:00Z',\n          items_count: 7\n        }]\n      };\n    }\n  }\n};\nexport default dashboardService;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "apiClient", "create", "baseURL", "timeout", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "dashboardService", "getStats", "response", "get", "success", "data", "_error$response", "_error$response$data", "console", "message", "totalProducts", "totalOrders", "totalRevenue", "totalUsers", "lowStockProducts", "pendingOrders", "monthlyRevenue", "weeklyOrders", "getChartData", "period", "_error$response2", "_error$response2$data", "salesChart", "labels", "datasets", "label", "borderColor", "backgroundColor", "tension", "ordersChart", "categoriesChart", "getRecentActivity", "limit", "_error$response3", "_error$response3$data", "getLowStockProducts", "_error$response4", "_error$response4$data", "getRecentOrders", "_error$response5", "_error$response5$data", "id", "user", "name", "email", "total", "status", "created_at", "items_count"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet-Synthese/frontend/src/services/dashboardService.js"], "sourcesContent": ["import axios from 'axios';\n\n// Configuration de base pour les appels API\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';\n\n// Instance axios configurée\nconst apiClient = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json',\n    'Accept': 'application/json',\n  },\n});\n\n// Intercepteur pour ajouter le token d'authentification\napiClient.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('auth_token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Service Dashboard API\nexport const dashboardService = {\n  // Récupérer les statistiques générales\n  getStats: async () => {\n    try {\n      const response = await apiClient.get('/api/admin/dashboard/stats');\n      return {\n        success: true,\n        data: response.data.data || response.data\n      };\n    } catch (error) {\n      console.error('Erreur API stats:', error);\n      return {\n        success: false,\n        error: error.response?.data?.message || 'Erreur de connexion',\n        // Pas de données de fallback - forcer l'utilisation de Laravel\n        data: {\n          totalProducts: 0,\n          totalOrders: 0,\n          totalRevenue: 0,\n          totalUsers: 0,\n          lowStockProducts: 0,\n          pendingOrders: 0,\n          monthlyRevenue: 0,\n          weeklyOrders: 0\n        }\n      };\n    }\n  },\n\n  // Récupérer les données pour les graphiques\n  getChartData: async (period = '7days') => {\n    try {\n      const response = await apiClient.get(`/api/admin/dashboard/charts?period=${period}`);\n      return {\n        success: true,\n        data: response.data.data || response.data\n      };\n    } catch (error) {\n      console.error('Erreur API charts:', error);\n      return {\n        success: false,\n        error: error.response?.data?.message || 'Erreur de connexion',\n        // Données de fallback pour la démonstration\n        data: {\n          salesChart: {\n            labels: ['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'],\n            datasets: [{\n              label: 'Ventes (MAD)',\n              data: [1200, 1900, 800, 1500, 2000, 2400, 1800],\n              borderColor: 'rgb(34, 197, 94)',\n              backgroundColor: 'rgba(34, 197, 94, 0.1)',\n              tension: 0.4\n            }]\n          },\n          ordersChart: {\n            labels: ['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'],\n            datasets: [{\n              label: 'Commandes',\n              data: [12, 19, 8, 15, 20, 24, 18],\n              backgroundColor: [\n                '#ef4444', '#f97316', '#eab308', \n                '#22c55e', '#3b82f6', '#8b5cf6', '#ec4899'\n              ]\n            }]\n          },\n          categoriesChart: {\n            labels: ['Produits Grillés', 'Salades', 'Fromages', 'Boissons', 'Desserts'],\n            datasets: [{\n              data: [35, 25, 20, 15, 5],\n              backgroundColor: [\n                '#ef4444', '#22c55e', '#eab308', '#3b82f6', '#8b5cf6'\n              ]\n            }]\n          }\n        }\n      };\n    }\n  },\n\n  // Récupérer les activités récentes\n  getRecentActivity: async (limit = 10) => {\n    try {\n      const response = await apiClient.get(`/api/admin/dashboard/activity?limit=${limit}`);\n      return {\n        success: true,\n        data: response.data.data || response.data\n      };\n    } catch (error) {\n      console.error('Erreur API activity:', error);\n      return {\n        success: false,\n        error: error.response?.data?.message || 'Erreur de connexion',\n        // Pas de données de fallback - forcer l'utilisation de Laravel\n        data: []\n      };\n    }\n  },\n\n  // Récupérer les produits en stock faible\n  getLowStockProducts: async () => {\n    try {\n      const response = await apiClient.get('/api/admin/dashboard/low-stock');\n      return {\n        success: true,\n        data: response.data.data || response.data\n      };\n    } catch (error) {\n      console.error('Erreur API low-stock:', error);\n      return {\n        success: false,\n        error: error.response?.data?.message || 'Erreur de connexion',\n        // Pas de données de fallback - forcer l'utilisation de Laravel\n        data: []\n      };\n    }\n  },\n\n  // Récupérer les commandes récentes\n  getRecentOrders: async (limit = 5) => {\n    try {\n      const response = await apiClient.get(`/api/admin/dashboard/recent-orders?limit=${limit}`);\n      return {\n        success: true,\n        data: response.data.data || response.data\n      };\n    } catch (error) {\n      console.error('Erreur API recent-orders:', error);\n      return {\n        success: false,\n        error: error.response?.data?.message || 'Erreur de connexion',\n        // Données de fallback pour la démonstration\n        data: [\n          {\n            id: 1234,\n            user: {\n              name: 'Marie Dubois',\n              email: '<EMAIL>'\n            },\n            total: 89.50,\n            status: 'pending',\n            created_at: '2024-01-15T10:30:00Z',\n            items_count: 3\n          },\n          {\n            id: 1233,\n            user: {\n              name: 'Ahmed Benali',\n              email: '<EMAIL>'\n            },\n            total: 156.75,\n            status: 'completed',\n            created_at: '2024-01-15T09:15:00Z',\n            items_count: 5\n          },\n          {\n            id: 1232,\n            user: {\n              name: 'Fatima Zahra',\n              email: '<EMAIL>'\n            },\n            total: 234.25,\n            status: 'processing',\n            created_at: '2024-01-15T08:45:00Z',\n            items_count: 7\n          }\n        ]\n      };\n    }\n  }\n};\n\nexport default dashboardService;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;;AAE7E;AACA,MAAMC,SAAS,GAAGL,KAAK,CAACM,MAAM,CAAC;EAC7BC,OAAO,EAAEN,YAAY;EACrBO,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE;IACP,cAAc,EAAE,kBAAkB;IAClC,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC;;AAEF;AACAJ,SAAS,CAACK,YAAY,CAACC,OAAO,CAACC,GAAG,CAC/BC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;EAChD,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACAK,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACA,OAAO,MAAMG,gBAAgB,GAAG;EAC9B;EACAC,QAAQ,EAAE,MAAAA,CAAA,KAAY;IACpB,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMlB,SAAS,CAACmB,GAAG,CAAC,4BAA4B,CAAC;MAClE,OAAO;QACLC,OAAO,EAAE,IAAI;QACbC,IAAI,EAAEH,QAAQ,CAACG,IAAI,CAACA,IAAI,IAAIH,QAAQ,CAACG;MACvC,CAAC;IACH,CAAC,CAAC,OAAOR,KAAK,EAAE;MAAA,IAAAS,eAAA,EAAAC,oBAAA;MACdC,OAAO,CAACX,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MACzC,OAAO;QACLO,OAAO,EAAE,KAAK;QACdP,KAAK,EAAE,EAAAS,eAAA,GAAAT,KAAK,CAACK,QAAQ,cAAAI,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBD,IAAI,cAAAE,oBAAA,uBAApBA,oBAAA,CAAsBE,OAAO,KAAI,qBAAqB;QAC7D;QACAJ,IAAI,EAAE;UACJK,aAAa,EAAE,CAAC;UAChBC,WAAW,EAAE,CAAC;UACdC,YAAY,EAAE,CAAC;UACfC,UAAU,EAAE,CAAC;UACbC,gBAAgB,EAAE,CAAC;UACnBC,aAAa,EAAE,CAAC;UAChBC,cAAc,EAAE,CAAC;UACjBC,YAAY,EAAE;QAChB;MACF,CAAC;IACH;EACF,CAAC;EAED;EACAC,YAAY,EAAE,MAAAA,CAAOC,MAAM,GAAG,OAAO,KAAK;IACxC,IAAI;MACF,MAAMjB,QAAQ,GAAG,MAAMlB,SAAS,CAACmB,GAAG,CAAC,sCAAsCgB,MAAM,EAAE,CAAC;MACpF,OAAO;QACLf,OAAO,EAAE,IAAI;QACbC,IAAI,EAAEH,QAAQ,CAACG,IAAI,CAACA,IAAI,IAAIH,QAAQ,CAACG;MACvC,CAAC;IACH,CAAC,CAAC,OAAOR,KAAK,EAAE;MAAA,IAAAuB,gBAAA,EAAAC,qBAAA;MACdb,OAAO,CAACX,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,OAAO;QACLO,OAAO,EAAE,KAAK;QACdP,KAAK,EAAE,EAAAuB,gBAAA,GAAAvB,KAAK,CAACK,QAAQ,cAAAkB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBf,IAAI,cAAAgB,qBAAA,uBAApBA,qBAAA,CAAsBZ,OAAO,KAAI,qBAAqB;QAC7D;QACAJ,IAAI,EAAE;UACJiB,UAAU,EAAE;YACVC,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;YACzDC,QAAQ,EAAE,CAAC;cACTC,KAAK,EAAE,cAAc;cACrBpB,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;cAC/CqB,WAAW,EAAE,kBAAkB;cAC/BC,eAAe,EAAE,wBAAwB;cACzCC,OAAO,EAAE;YACX,CAAC;UACH,CAAC;UACDC,WAAW,EAAE;YACXN,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;YACzDC,QAAQ,EAAE,CAAC;cACTC,KAAK,EAAE,WAAW;cAClBpB,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;cACjCsB,eAAe,EAAE,CACf,SAAS,EAAE,SAAS,EAAE,SAAS,EAC/B,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS;YAE9C,CAAC;UACH,CAAC;UACDG,eAAe,EAAE;YACfP,MAAM,EAAE,CAAC,kBAAkB,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC;YAC3EC,QAAQ,EAAE,CAAC;cACTnB,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;cACzBsB,eAAe,EAAE,CACf,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS;YAEzD,CAAC;UACH;QACF;MACF,CAAC;IACH;EACF,CAAC;EAED;EACAI,iBAAiB,EAAE,MAAAA,CAAOC,KAAK,GAAG,EAAE,KAAK;IACvC,IAAI;MACF,MAAM9B,QAAQ,GAAG,MAAMlB,SAAS,CAACmB,GAAG,CAAC,uCAAuC6B,KAAK,EAAE,CAAC;MACpF,OAAO;QACL5B,OAAO,EAAE,IAAI;QACbC,IAAI,EAAEH,QAAQ,CAACG,IAAI,CAACA,IAAI,IAAIH,QAAQ,CAACG;MACvC,CAAC;IACH,CAAC,CAAC,OAAOR,KAAK,EAAE;MAAA,IAAAoC,gBAAA,EAAAC,qBAAA;MACd1B,OAAO,CAACX,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,OAAO;QACLO,OAAO,EAAE,KAAK;QACdP,KAAK,EAAE,EAAAoC,gBAAA,GAAApC,KAAK,CAACK,QAAQ,cAAA+B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB5B,IAAI,cAAA6B,qBAAA,uBAApBA,qBAAA,CAAsBzB,OAAO,KAAI,qBAAqB;QAC7D;QACAJ,IAAI,EAAE;MACR,CAAC;IACH;EACF,CAAC;EAED;EACA8B,mBAAmB,EAAE,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMjC,QAAQ,GAAG,MAAMlB,SAAS,CAACmB,GAAG,CAAC,gCAAgC,CAAC;MACtE,OAAO;QACLC,OAAO,EAAE,IAAI;QACbC,IAAI,EAAEH,QAAQ,CAACG,IAAI,CAACA,IAAI,IAAIH,QAAQ,CAACG;MACvC,CAAC;IACH,CAAC,CAAC,OAAOR,KAAK,EAAE;MAAA,IAAAuC,gBAAA,EAAAC,qBAAA;MACd7B,OAAO,CAACX,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,OAAO;QACLO,OAAO,EAAE,KAAK;QACdP,KAAK,EAAE,EAAAuC,gBAAA,GAAAvC,KAAK,CAACK,QAAQ,cAAAkC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB/B,IAAI,cAAAgC,qBAAA,uBAApBA,qBAAA,CAAsB5B,OAAO,KAAI,qBAAqB;QAC7D;QACAJ,IAAI,EAAE;MACR,CAAC;IACH;EACF,CAAC;EAED;EACAiC,eAAe,EAAE,MAAAA,CAAON,KAAK,GAAG,CAAC,KAAK;IACpC,IAAI;MACF,MAAM9B,QAAQ,GAAG,MAAMlB,SAAS,CAACmB,GAAG,CAAC,4CAA4C6B,KAAK,EAAE,CAAC;MACzF,OAAO;QACL5B,OAAO,EAAE,IAAI;QACbC,IAAI,EAAEH,QAAQ,CAACG,IAAI,CAACA,IAAI,IAAIH,QAAQ,CAACG;MACvC,CAAC;IACH,CAAC,CAAC,OAAOR,KAAK,EAAE;MAAA,IAAA0C,gBAAA,EAAAC,qBAAA;MACdhC,OAAO,CAACX,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,OAAO;QACLO,OAAO,EAAE,KAAK;QACdP,KAAK,EAAE,EAAA0C,gBAAA,GAAA1C,KAAK,CAACK,QAAQ,cAAAqC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBlC,IAAI,cAAAmC,qBAAA,uBAApBA,qBAAA,CAAsB/B,OAAO,KAAI,qBAAqB;QAC7D;QACAJ,IAAI,EAAE,CACJ;UACEoC,EAAE,EAAE,IAAI;UACRC,IAAI,EAAE;YACJC,IAAI,EAAE,cAAc;YACpBC,KAAK,EAAE;UACT,CAAC;UACDC,KAAK,EAAE,KAAK;UACZC,MAAM,EAAE,SAAS;UACjBC,UAAU,EAAE,sBAAsB;UAClCC,WAAW,EAAE;QACf,CAAC,EACD;UACEP,EAAE,EAAE,IAAI;UACRC,IAAI,EAAE;YACJC,IAAI,EAAE,cAAc;YACpBC,KAAK,EAAE;UACT,CAAC;UACDC,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,WAAW;UACnBC,UAAU,EAAE,sBAAsB;UAClCC,WAAW,EAAE;QACf,CAAC,EACD;UACEP,EAAE,EAAE,IAAI;UACRC,IAAI,EAAE;YACJC,IAAI,EAAE,cAAc;YACpBC,KAAK,EAAE;UACT,CAAC;UACDC,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,YAAY;UACpBC,UAAU,EAAE,sBAAsB;UAClCC,WAAW,EAAE;QACf,CAAC;MAEL,CAAC;IACH;EACF;AACF,CAAC;AAED,eAAehD,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}