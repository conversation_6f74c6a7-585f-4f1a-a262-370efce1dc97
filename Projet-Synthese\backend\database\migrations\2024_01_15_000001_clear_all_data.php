<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Supprimer toutes les données existantes
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        
        // Vider toutes les tables
        DB::table('products')->truncate();
        DB::table('categories')->truncate();
        DB::table('orders')->truncate();
        DB::table('order_items')->truncate();
        DB::table('users')->where('email', '!=', '<EMAIL>')->delete();
        DB::table('promotions')->truncate();
        
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');
        
        echo "✅ Toutes les données ont été supprimées\n";
        echo "🔧 L'admin doit maintenant gérer l'application\n";
        echo "📝 Seul l'utilisateur <EMAIL> est conservé\n";
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Ne rien faire - on ne peut pas restaurer les données supprimées
        echo "⚠️ Impossible de restaurer les données supprimées\n";
    }
};
