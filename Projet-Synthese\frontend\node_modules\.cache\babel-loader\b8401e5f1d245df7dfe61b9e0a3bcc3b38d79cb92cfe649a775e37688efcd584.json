{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Projet-Synthese\\\\frontend\\\\src\\\\pages\\\\Admin\\\\ProductsCRUD.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useData } from '../../context/DataContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProductsCRUD = () => {\n  _s();\n  const {\n    state,\n    dispatch,\n    DataActions\n  } = useData();\n  const {\n    products,\n    categories,\n    loading\n  } = state;\n  const [error, setError] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const [currentProduct, setCurrentProduct] = useState(null);\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    price: '',\n    stock: '',\n    category_id: '',\n    is_grillable: false,\n    image: null\n  });\n  const [filteredProducts, setFilteredProducts] = useState([]);\n\n  // Filtrer les produits selon le terme de recherche\n  useEffect(() => {\n    if (searchTerm) {\n      const filtered = products.filter(product => product.name.toLowerCase().includes(searchTerm.toLowerCase()) || product.description.toLowerCase().includes(searchTerm.toLowerCase()));\n      setFilteredProducts(filtered);\n    } else {\n      setFilteredProducts(products);\n    }\n  }, [products, searchTerm]);\n\n  // Fonction pour rafraîchir les données\n  const refreshData = () => {\n    dispatch({\n      type: DataActions.SET_LOADING,\n      payload: true\n    });\n    try {\n      setError(null);\n      console.log('Données actualisées avec succès');\n    } catch (err) {\n      console.log('Mode démonstration - Données de test utilisées');\n      setError('Mode démonstration - Données de test utilisées');\n    } finally {\n      dispatch({\n        type: DataActions.SET_LOADING,\n        payload: false\n      });\n    }\n  };\n\n  // Gestion des changements dans le formulaire\n  const handleInputChange = e => {\n    const {\n      name,\n      value,\n      type,\n      checked,\n      files\n    } = e.target;\n    if (type === 'checkbox') {\n      setFormData(prev => ({\n        ...prev,\n        [name]: checked\n      }));\n    } else if (type === 'file') {\n      setFormData(prev => ({\n        ...prev,\n        [name]: files[0]\n      }));\n    } else {\n      setFormData(prev => ({\n        ...prev,\n        [name]: value\n      }));\n    }\n  };\n\n  // Ouvrir le modal pour ajouter/modifier un produit\n  const openModal = (product = null) => {\n    if (product) {\n      setCurrentProduct(product);\n      setFormData({\n        name: product.name,\n        description: product.description,\n        price: product.price,\n        stock: product.stock,\n        category_id: product.category_id,\n        is_grillable: product.is_grillable,\n        image: null\n      });\n    } else {\n      setCurrentProduct(null);\n      setFormData({\n        name: '',\n        description: '',\n        price: '',\n        stock: '',\n        category_id: '',\n        is_grillable: false,\n        image: null\n      });\n    }\n    setIsModalOpen(true);\n  };\n\n  // Fermer le modal\n  const closeModal = () => {\n    setIsModalOpen(false);\n    setCurrentProduct(null);\n    setFormData({\n      name: '',\n      description: '',\n      price: '',\n      stock: '',\n      category_id: '',\n      is_grillable: false,\n      image: null\n    });\n  };\n\n  // Soumettre le formulaire\n  const handleSubmit = e => {\n    e.preventDefault();\n    dispatch({\n      type: DataActions.SET_LOADING,\n      payload: true\n    });\n    try {\n      const productData = {\n        ...formData,\n        price: parseFloat(formData.price),\n        stock: parseInt(formData.stock),\n        category_id: parseInt(formData.category_id)\n      };\n      if (currentProduct) {\n        // Mise à jour d'un produit existant\n        dispatch({\n          type: DataActions.UPDATE_PRODUCT,\n          payload: {\n            ...productData,\n            id: currentProduct.id,\n            category: categories.find(cat => cat.id === productData.category_id)\n          }\n        });\n        console.log('Produit mis à jour avec succès');\n      } else {\n        // Création d'un nouveau produit\n        const newProduct = {\n          ...productData,\n          id: Date.now(),\n          category: categories.find(cat => cat.id === productData.category_id),\n          image_url: 'https://images.unsplash.com/photo-1546069901-ba9599a7e63c?w=300'\n        };\n        dispatch({\n          type: DataActions.ADD_PRODUCT,\n          payload: newProduct\n        });\n        console.log('Produit créé avec succès');\n      }\n      closeModal();\n    } catch (err) {\n      console.error('Erreur lors de l\\'enregistrement du produit', err);\n    } finally {\n      dispatch({\n        type: DataActions.SET_LOADING,\n        payload: false\n      });\n    }\n  };\n\n  // Supprimer un produit\n  const handleDelete = id => {\n    if (window.confirm('Êtes-vous sûr de vouloir supprimer ce produit ?')) {\n      dispatch({\n        type: DataActions.SET_LOADING,\n        payload: true\n      });\n      try {\n        dispatch({\n          type: DataActions.DELETE_PRODUCT,\n          payload: id\n        });\n        console.log('Produit supprimé avec succès');\n      } catch (err) {\n        console.error('Erreur lors de la suppression du produit', err);\n      } finally {\n        dispatch({\n          type: DataActions.SET_LOADING,\n          payload: false\n        });\n      }\n    }\n  };\n\n  // Gestion de la recherche\n  const handleSearch = e => {\n    setSearchTerm(e.target.value);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-gray-900\",\n          children: \"Gestion des Produits\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-500\",\n          children: [filteredProducts.length, \" produit(s) \\u2022 CRUD complet activ\\xE9\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex space-x-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: refreshData,\n          disabled: loading,\n          className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50\",\n          children: loading ? 'Actualisation...' : 'Actualiser'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => openModal(),\n          className: \"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700\",\n          children: \"Ajouter un produit\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 171,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white p-4 rounded-lg shadow\",\n      children: /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        placeholder: \"Rechercher un produit...\",\n        value: searchTerm,\n        onChange: handleSearch,\n        className: \"w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative\",\n      role: \"alert\",\n      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n        className: \"font-bold\",\n        children: \"Info!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"block sm:inline\",\n        children: [\" \", error]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 207,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow overflow-hidden\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"overflow-x-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"min-w-full divide-y divide-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            className: \"bg-gray-50\",\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Image\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Nom\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Prix\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Stock\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Cat\\xE9gorie\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Grillable\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 224,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            className: \"bg-white divide-y divide-gray-200\",\n            children: filteredProducts.map(product => /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"h-10 w-10 rounded-full overflow-hidden bg-gray-100\",\n                  children: product.image_url ? /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: product.image_url,\n                    alt: product.name,\n                    className: \"h-full w-full object-cover\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 234,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"h-full w-full flex items-center justify-center text-gray-400\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      className: \"h-6 w-6\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      stroke: \"currentColor\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 242,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 241,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 240,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 232,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                children: product.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                children: [product.price, \" DH\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${product.stock > 10 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`,\n                  children: product.stock\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 251,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                children: product.category ? product.category.name : 'Non catégorisé'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                children: product.is_grillable ? /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-green-600\",\n                  children: \"Oui\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 262,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-600\",\n                  children: \"Non\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 264,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => openModal(product),\n                  className: \"text-indigo-600 hover:text-indigo-900 mr-3\",\n                  children: \"Modifier\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 268,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleDelete(product.id),\n                  className: \"text-red-600 hover:text-red-900\",\n                  children: \"Supprimer\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 274,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 19\n              }, this)]\n            }, product.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 214,\n      columnNumber: 7\n    }, this), isModalOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-lg w-full max-w-md mx-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-6 py-4 border-b\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold\",\n            children: currentProduct ? 'Modifier le produit' : 'Ajouter un produit'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"name\",\n                className: \"block text-gray-700 mb-2\",\n                children: \"Nom\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"name\",\n                name: \"name\",\n                value: formData.name,\n                onChange: handleInputChange,\n                className: \"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"description\",\n                className: \"block text-gray-700 mb-2\",\n                children: \"Description\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 313,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                id: \"description\",\n                name: \"description\",\n                value: formData.description,\n                onChange: handleInputChange,\n                className: \"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\",\n                rows: \"3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-2 gap-4 mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"price\",\n                  className: \"block text-gray-700 mb-2\",\n                  children: \"Prix (DH)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 326,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  id: \"price\",\n                  name: \"price\",\n                  value: formData.price,\n                  onChange: handleInputChange,\n                  className: \"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\",\n                  step: \"0.01\",\n                  min: \"0\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 327,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 325,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"stock\",\n                  className: \"block text-gray-700 mb-2\",\n                  children: \"Stock\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 341,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  id: \"stock\",\n                  name: \"stock\",\n                  value: formData.stock,\n                  onChange: handleInputChange,\n                  className: \"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\",\n                  min: \"0\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 342,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 340,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"category_id\",\n                className: \"block text-gray-700 mb-2\",\n                children: \"Cat\\xE9gorie\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 356,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                id: \"category_id\",\n                name: \"category_id\",\n                value: formData.category_id,\n                onChange: handleInputChange,\n                className: \"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\",\n                required: true,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"S\\xE9lectionner une cat\\xE9gorie\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 365,\n                  columnNumber: 21\n                }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: category.id,\n                  children: category.name\n                }, category.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 367,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 357,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"image\",\n                className: \"block text-gray-700 mb-2\",\n                children: \"Image\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 375,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"file\",\n                id: \"image\",\n                name: \"image\",\n                onChange: handleInputChange,\n                className: \"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\",\n                accept: \"image/*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 376,\n                columnNumber: 19\n              }, this), currentProduct && currentProduct.image_url && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-500\",\n                  children: \"Image actuelle:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 386,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"h-20 w-20 mt-1 rounded overflow-hidden bg-gray-100\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: currentProduct.image_url,\n                    alt: currentProduct.name,\n                    className: \"h-full w-full object-cover\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 388,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 387,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 385,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 374,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  id: \"is_grillable\",\n                  name: \"is_grillable\",\n                  checked: formData.is_grillable,\n                  onChange: handleInputChange,\n                  className: \"h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 400,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"is_grillable\",\n                  className: \"ml-2 block text-gray-700\",\n                  children: \"Produit grillable\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 408,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 399,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 398,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"px-6 py-4 bg-gray-50 border-t flex justify-end\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: closeModal,\n              className: \"px-4 py-2 border rounded-lg text-gray-700 mr-2 hover:bg-gray-100\",\n              children: \"Annuler\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 416,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              disabled: loading,\n              className: \"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50\",\n              children: loading ? 'Enregistrement...' : currentProduct ? 'Mettre à jour' : 'Ajouter'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 423,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 415,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 291,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 290,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 169,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductsCRUD, \"Cjuq0AMGyBtILmIzTnVAtgC6QTE=\", false, function () {\n  return [useData];\n});\n_c = ProductsCRUD;\nexport default ProductsCRUD;\nvar _c;\n$RefreshReg$(_c, \"ProductsCRUD\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useData", "jsxDEV", "_jsxDEV", "ProductsCRUD", "_s", "state", "dispatch", "DataActions", "products", "categories", "loading", "error", "setError", "searchTerm", "setSearchTerm", "isModalOpen", "setIsModalOpen", "currentProduct", "setCurrentProduct", "formData", "setFormData", "name", "description", "price", "stock", "category_id", "is_grillable", "image", "filteredProducts", "setFilteredProducts", "filtered", "filter", "product", "toLowerCase", "includes", "refreshData", "type", "SET_LOADING", "payload", "console", "log", "err", "handleInputChange", "e", "value", "checked", "files", "target", "prev", "openModal", "closeModal", "handleSubmit", "preventDefault", "productData", "parseFloat", "parseInt", "UPDATE_PRODUCT", "id", "category", "find", "cat", "newProduct", "Date", "now", "image_url", "ADD_PRODUCT", "handleDelete", "window", "confirm", "DELETE_PRODUCT", "handleSearch", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "onClick", "disabled", "placeholder", "onChange", "role", "map", "src", "alt", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "onSubmit", "htmlFor", "required", "rows", "step", "min", "accept", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet-Synthese/frontend/src/pages/Admin/ProductsCRUD.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useData } from '../../context/DataContext';\n\nconst ProductsCRUD = () => {\n  const { state, dispatch, DataActions } = useData();\n  const { products, categories, loading } = state;\n  \n  const [error, setError] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const [currentProduct, setCurrentProduct] = useState(null);\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    price: '',\n    stock: '',\n    category_id: '',\n    is_grillable: false,\n    image: null\n  });\n  const [filteredProducts, setFilteredProducts] = useState([]);\n\n  // Filtrer les produits selon le terme de recherche\n  useEffect(() => {\n    if (searchTerm) {\n      const filtered = products.filter(product =>\n        product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        product.description.toLowerCase().includes(searchTerm.toLowerCase())\n      );\n      setFilteredProducts(filtered);\n    } else {\n      setFilteredProducts(products);\n    }\n  }, [products, searchTerm]);\n\n  // Fonction pour rafraîchir les données\n  const refreshData = () => {\n    dispatch({ type: DataActions.SET_LOADING, payload: true });\n\n    try {\n      setError(null);\n      console.log('Données actualisées avec succès');\n    } catch (err) {\n      console.log('Mode démonstration - Données de test utilisées');\n      setError('Mode démonstration - Données de test utilisées');\n    } finally {\n      dispatch({ type: DataActions.SET_LOADING, payload: false });\n    }\n  };\n\n  // Gestion des changements dans le formulaire\n  const handleInputChange = (e) => {\n    const { name, value, type, checked, files } = e.target;\n    \n    if (type === 'checkbox') {\n      setFormData(prev => ({ ...prev, [name]: checked }));\n    } else if (type === 'file') {\n      setFormData(prev => ({ ...prev, [name]: files[0] }));\n    } else {\n      setFormData(prev => ({ ...prev, [name]: value }));\n    }\n  };\n\n  // Ouvrir le modal pour ajouter/modifier un produit\n  const openModal = (product = null) => {\n    if (product) {\n      setCurrentProduct(product);\n      setFormData({\n        name: product.name,\n        description: product.description,\n        price: product.price,\n        stock: product.stock,\n        category_id: product.category_id,\n        is_grillable: product.is_grillable,\n        image: null\n      });\n    } else {\n      setCurrentProduct(null);\n      setFormData({\n        name: '',\n        description: '',\n        price: '',\n        stock: '',\n        category_id: '',\n        is_grillable: false,\n        image: null\n      });\n    }\n    setIsModalOpen(true);\n  };\n\n  // Fermer le modal\n  const closeModal = () => {\n    setIsModalOpen(false);\n    setCurrentProduct(null);\n    setFormData({\n      name: '',\n      description: '',\n      price: '',\n      stock: '',\n      category_id: '',\n      is_grillable: false,\n      image: null\n    });\n  };\n\n  // Soumettre le formulaire\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    dispatch({ type: DataActions.SET_LOADING, payload: true });\n\n    try {\n      const productData = {\n        ...formData,\n        price: parseFloat(formData.price),\n        stock: parseInt(formData.stock),\n        category_id: parseInt(formData.category_id)\n      };\n\n      if (currentProduct) {\n        // Mise à jour d'un produit existant\n        dispatch({\n          type: DataActions.UPDATE_PRODUCT,\n          payload: { ...productData, id: currentProduct.id, category: categories.find(cat => cat.id === productData.category_id) }\n        });\n        console.log('Produit mis à jour avec succès');\n      } else {\n        // Création d'un nouveau produit\n        const newProduct = {\n          ...productData,\n          id: Date.now(),\n          category: categories.find(cat => cat.id === productData.category_id),\n          image_url: 'https://images.unsplash.com/photo-1546069901-ba9599a7e63c?w=300'\n        };\n        dispatch({ type: DataActions.ADD_PRODUCT, payload: newProduct });\n        console.log('Produit créé avec succès');\n      }\n\n      closeModal();\n    } catch (err) {\n      console.error('Erreur lors de l\\'enregistrement du produit', err);\n    } finally {\n      dispatch({ type: DataActions.SET_LOADING, payload: false });\n    }\n  };\n\n  // Supprimer un produit\n  const handleDelete = (id) => {\n    if (window.confirm('Êtes-vous sûr de vouloir supprimer ce produit ?')) {\n      dispatch({ type: DataActions.SET_LOADING, payload: true });\n\n      try {\n        dispatch({ type: DataActions.DELETE_PRODUCT, payload: id });\n        console.log('Produit supprimé avec succès');\n      } catch (err) {\n        console.error('Erreur lors de la suppression du produit', err);\n      } finally {\n        dispatch({ type: DataActions.SET_LOADING, payload: false });\n      }\n    }\n  };\n\n  // Gestion de la recherche\n  const handleSearch = (e) => {\n    setSearchTerm(e.target.value);\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* En-tête */}\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">Gestion des Produits</h1>\n          <p className=\"text-sm text-gray-500\">\n            {filteredProducts.length} produit(s) • CRUD complet activé\n          </p>\n        </div>\n        <div className=\"flex space-x-3\">\n          <button\n            onClick={refreshData}\n            disabled={loading}\n            className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50\"\n          >\n            {loading ? 'Actualisation...' : 'Actualiser'}\n          </button>\n          <button\n            onClick={() => openModal()}\n            className=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700\"\n          >\n            Ajouter un produit\n          </button>\n        </div>\n      </div>\n\n      {/* Barre de recherche */}\n      <div className=\"bg-white p-4 rounded-lg shadow\">\n        <input\n          type=\"text\"\n          placeholder=\"Rechercher un produit...\"\n          value={searchTerm}\n          onChange={handleSearch}\n          className=\"w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n        />\n      </div>\n\n      {error && (\n        <div className=\"bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative\" role=\"alert\">\n          <strong className=\"font-bold\">Info!</strong>\n          <span className=\"block sm:inline\"> {error}</span>\n        </div>\n      )}\n\n      {/* Tableau des produits */}\n      <div className=\"bg-white rounded-lg shadow overflow-hidden\">\n        <div className=\"overflow-x-auto\">\n          <table className=\"min-w-full divide-y divide-gray-200\">\n            <thead className=\"bg-gray-50\">\n              <tr>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Image</th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Nom</th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Prix</th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Stock</th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Catégorie</th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Grillable</th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Actions</th>\n              </tr>\n            </thead>\n            <tbody className=\"bg-white divide-y divide-gray-200\">\n              {filteredProducts.map((product) => (\n                <tr key={product.id}>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"h-10 w-10 rounded-full overflow-hidden bg-gray-100\">\n                      {product.image_url ? (\n                        <img\n                          src={product.image_url}\n                          alt={product.name}\n                          className=\"h-full w-full object-cover\"\n                        />\n                      ) : (\n                        <div className=\"h-full w-full flex items-center justify-center text-gray-400\">\n                          <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n                          </svg>\n                        </div>\n                      )}\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">{product.name}</td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">{product.price} DH</td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${\n                      product.stock > 10 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'\n                    }`}>\n                      {product.stock}\n                    </span>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                    {product.category ? product.category.name : 'Non catégorisé'}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                    {product.is_grillable ? (\n                      <span className=\"text-green-600\">Oui</span>\n                    ) : (\n                      <span className=\"text-red-600\">Non</span>\n                    )}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                    <button\n                      onClick={() => openModal(product)}\n                      className=\"text-indigo-600 hover:text-indigo-900 mr-3\"\n                    >\n                      Modifier\n                    </button>\n                    <button\n                      onClick={() => handleDelete(product.id)}\n                      className=\"text-red-600 hover:text-red-900\"\n                    >\n                      Supprimer\n                    </button>\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      </div>\n\n      {/* Modal pour ajouter/modifier un produit */}\n      {isModalOpen && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n          <div className=\"bg-white rounded-lg shadow-lg w-full max-w-md mx-4\">\n            <div className=\"px-6 py-4 border-b\">\n              <h3 className=\"text-lg font-semibold\">\n                {currentProduct ? 'Modifier le produit' : 'Ajouter un produit'}\n              </h3>\n            </div>\n            <form onSubmit={handleSubmit}>\n              <div className=\"p-6\">\n                <div className=\"mb-4\">\n                  <label htmlFor=\"name\" className=\"block text-gray-700 mb-2\">Nom</label>\n                  <input\n                    type=\"text\"\n                    id=\"name\"\n                    name=\"name\"\n                    value={formData.name}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n                    required\n                  />\n                </div>\n\n                <div className=\"mb-4\">\n                  <label htmlFor=\"description\" className=\"block text-gray-700 mb-2\">Description</label>\n                  <textarea\n                    id=\"description\"\n                    name=\"description\"\n                    value={formData.description}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n                    rows=\"3\"\n                  ></textarea>\n                </div>\n\n                <div className=\"grid grid-cols-2 gap-4 mb-4\">\n                  <div>\n                    <label htmlFor=\"price\" className=\"block text-gray-700 mb-2\">Prix (DH)</label>\n                    <input\n                      type=\"number\"\n                      id=\"price\"\n                      name=\"price\"\n                      value={formData.price}\n                      onChange={handleInputChange}\n                      className=\"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n                      step=\"0.01\"\n                      min=\"0\"\n                      required\n                    />\n                  </div>\n\n                  <div>\n                    <label htmlFor=\"stock\" className=\"block text-gray-700 mb-2\">Stock</label>\n                    <input\n                      type=\"number\"\n                      id=\"stock\"\n                      name=\"stock\"\n                      value={formData.stock}\n                      onChange={handleInputChange}\n                      className=\"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n                      min=\"0\"\n                      required\n                    />\n                  </div>\n                </div>\n\n                <div className=\"mb-4\">\n                  <label htmlFor=\"category_id\" className=\"block text-gray-700 mb-2\">Catégorie</label>\n                  <select\n                    id=\"category_id\"\n                    name=\"category_id\"\n                    value={formData.category_id}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n                    required\n                  >\n                    <option value=\"\">Sélectionner une catégorie</option>\n                    {categories.map(category => (\n                      <option key={category.id} value={category.id}>\n                        {category.name}\n                      </option>\n                    ))}\n                  </select>\n                </div>\n\n                <div className=\"mb-4\">\n                  <label htmlFor=\"image\" className=\"block text-gray-700 mb-2\">Image</label>\n                  <input\n                    type=\"file\"\n                    id=\"image\"\n                    name=\"image\"\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n                    accept=\"image/*\"\n                  />\n                  {currentProduct && currentProduct.image_url && (\n                    <div className=\"mt-2\">\n                      <p className=\"text-sm text-gray-500\">Image actuelle:</p>\n                      <div className=\"h-20 w-20 mt-1 rounded overflow-hidden bg-gray-100\">\n                        <img\n                          src={currentProduct.image_url}\n                          alt={currentProduct.name}\n                          className=\"h-full w-full object-cover\"\n                        />\n                      </div>\n                    </div>\n                  )}\n                </div>\n\n                <div className=\"mb-4\">\n                  <div className=\"flex items-center\">\n                    <input\n                      type=\"checkbox\"\n                      id=\"is_grillable\"\n                      name=\"is_grillable\"\n                      checked={formData.is_grillable}\n                      onChange={handleInputChange}\n                      className=\"h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded\"\n                    />\n                    <label htmlFor=\"is_grillable\" className=\"ml-2 block text-gray-700\">\n                      Produit grillable\n                    </label>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"px-6 py-4 bg-gray-50 border-t flex justify-end\">\n                <button\n                  type=\"button\"\n                  onClick={closeModal}\n                  className=\"px-4 py-2 border rounded-lg text-gray-700 mr-2 hover:bg-gray-100\"\n                >\n                  Annuler\n                </button>\n                <button\n                  type=\"submit\"\n                  disabled={loading}\n                  className=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50\"\n                >\n                  {loading ? 'Enregistrement...' : (currentProduct ? 'Mettre à jour' : 'Ajouter')}\n                </button>\n              </div>\n            </form>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default ProductsCRUD;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM;IAAEC,KAAK;IAAEC,QAAQ;IAAEC;EAAY,CAAC,GAAGP,OAAO,CAAC,CAAC;EAClD,MAAM;IAAEQ,QAAQ;IAAEC,UAAU;IAAEC;EAAQ,CAAC,GAAGL,KAAK;EAE/C,MAAM,CAACM,KAAK,EAAEC,QAAQ,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACe,UAAU,EAAEC,aAAa,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACiB,WAAW,EAAEC,cAAc,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACmB,cAAc,EAAEC,iBAAiB,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACqB,QAAQ,EAAEC,WAAW,CAAC,GAAGtB,QAAQ,CAAC;IACvCuB,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,YAAY,EAAE,KAAK;IACnBC,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;;EAE5D;EACAC,SAAS,CAAC,MAAM;IACd,IAAIc,UAAU,EAAE;MACd,MAAMiB,QAAQ,GAAGtB,QAAQ,CAACuB,MAAM,CAACC,OAAO,IACtCA,OAAO,CAACX,IAAI,CAACY,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACrB,UAAU,CAACoB,WAAW,CAAC,CAAC,CAAC,IAC7DD,OAAO,CAACV,WAAW,CAACW,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACrB,UAAU,CAACoB,WAAW,CAAC,CAAC,CACrE,CAAC;MACDJ,mBAAmB,CAACC,QAAQ,CAAC;IAC/B,CAAC,MAAM;MACLD,mBAAmB,CAACrB,QAAQ,CAAC;IAC/B;EACF,CAAC,EAAE,CAACA,QAAQ,EAAEK,UAAU,CAAC,CAAC;;EAE1B;EACA,MAAMsB,WAAW,GAAGA,CAAA,KAAM;IACxB7B,QAAQ,CAAC;MAAE8B,IAAI,EAAE7B,WAAW,CAAC8B,WAAW;MAAEC,OAAO,EAAE;IAAK,CAAC,CAAC;IAE1D,IAAI;MACF1B,QAAQ,CAAC,IAAI,CAAC;MACd2B,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAChD,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZF,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;MAC7D5B,QAAQ,CAAC,gDAAgD,CAAC;IAC5D,CAAC,SAAS;MACRN,QAAQ,CAAC;QAAE8B,IAAI,EAAE7B,WAAW,CAAC8B,WAAW;QAAEC,OAAO,EAAE;MAAM,CAAC,CAAC;IAC7D;EACF,CAAC;;EAED;EACA,MAAMI,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEtB,IAAI;MAAEuB,KAAK;MAAER,IAAI;MAAES,OAAO;MAAEC;IAAM,CAAC,GAAGH,CAAC,CAACI,MAAM;IAEtD,IAAIX,IAAI,KAAK,UAAU,EAAE;MACvBhB,WAAW,CAAC4B,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAAC3B,IAAI,GAAGwB;MAAQ,CAAC,CAAC,CAAC;IACrD,CAAC,MAAM,IAAIT,IAAI,KAAK,MAAM,EAAE;MAC1BhB,WAAW,CAAC4B,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAAC3B,IAAI,GAAGyB,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,CAAC;IACtD,CAAC,MAAM;MACL1B,WAAW,CAAC4B,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAAC3B,IAAI,GAAGuB;MAAM,CAAC,CAAC,CAAC;IACnD;EACF,CAAC;;EAED;EACA,MAAMK,SAAS,GAAGA,CAACjB,OAAO,GAAG,IAAI,KAAK;IACpC,IAAIA,OAAO,EAAE;MACXd,iBAAiB,CAACc,OAAO,CAAC;MAC1BZ,WAAW,CAAC;QACVC,IAAI,EAAEW,OAAO,CAACX,IAAI;QAClBC,WAAW,EAAEU,OAAO,CAACV,WAAW;QAChCC,KAAK,EAAES,OAAO,CAACT,KAAK;QACpBC,KAAK,EAAEQ,OAAO,CAACR,KAAK;QACpBC,WAAW,EAAEO,OAAO,CAACP,WAAW;QAChCC,YAAY,EAAEM,OAAO,CAACN,YAAY;QAClCC,KAAK,EAAE;MACT,CAAC,CAAC;IACJ,CAAC,MAAM;MACLT,iBAAiB,CAAC,IAAI,CAAC;MACvBE,WAAW,CAAC;QACVC,IAAI,EAAE,EAAE;QACRC,WAAW,EAAE,EAAE;QACfC,KAAK,EAAE,EAAE;QACTC,KAAK,EAAE,EAAE;QACTC,WAAW,EAAE,EAAE;QACfC,YAAY,EAAE,KAAK;QACnBC,KAAK,EAAE;MACT,CAAC,CAAC;IACJ;IACAX,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;;EAED;EACA,MAAMkC,UAAU,GAAGA,CAAA,KAAM;IACvBlC,cAAc,CAAC,KAAK,CAAC;IACrBE,iBAAiB,CAAC,IAAI,CAAC;IACvBE,WAAW,CAAC;MACVC,IAAI,EAAE,EAAE;MACRC,WAAW,EAAE,EAAE;MACfC,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE,EAAE;MACTC,WAAW,EAAE,EAAE;MACfC,YAAY,EAAE,KAAK;MACnBC,KAAK,EAAE;IACT,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMwB,YAAY,GAAIR,CAAC,IAAK;IAC1BA,CAAC,CAACS,cAAc,CAAC,CAAC;IAClB9C,QAAQ,CAAC;MAAE8B,IAAI,EAAE7B,WAAW,CAAC8B,WAAW;MAAEC,OAAO,EAAE;IAAK,CAAC,CAAC;IAE1D,IAAI;MACF,MAAMe,WAAW,GAAG;QAClB,GAAGlC,QAAQ;QACXI,KAAK,EAAE+B,UAAU,CAACnC,QAAQ,CAACI,KAAK,CAAC;QACjCC,KAAK,EAAE+B,QAAQ,CAACpC,QAAQ,CAACK,KAAK,CAAC;QAC/BC,WAAW,EAAE8B,QAAQ,CAACpC,QAAQ,CAACM,WAAW;MAC5C,CAAC;MAED,IAAIR,cAAc,EAAE;QAClB;QACAX,QAAQ,CAAC;UACP8B,IAAI,EAAE7B,WAAW,CAACiD,cAAc;UAChClB,OAAO,EAAE;YAAE,GAAGe,WAAW;YAAEI,EAAE,EAAExC,cAAc,CAACwC,EAAE;YAAEC,QAAQ,EAAEjD,UAAU,CAACkD,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACH,EAAE,KAAKJ,WAAW,CAAC5B,WAAW;UAAE;QACzH,CAAC,CAAC;QACFc,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;MAC/C,CAAC,MAAM;QACL;QACA,MAAMqB,UAAU,GAAG;UACjB,GAAGR,WAAW;UACdI,EAAE,EAAEK,IAAI,CAACC,GAAG,CAAC,CAAC;UACdL,QAAQ,EAAEjD,UAAU,CAACkD,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACH,EAAE,KAAKJ,WAAW,CAAC5B,WAAW,CAAC;UACpEuC,SAAS,EAAE;QACb,CAAC;QACD1D,QAAQ,CAAC;UAAE8B,IAAI,EAAE7B,WAAW,CAAC0D,WAAW;UAAE3B,OAAO,EAAEuB;QAAW,CAAC,CAAC;QAChEtB,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;MACzC;MAEAU,UAAU,CAAC,CAAC;IACd,CAAC,CAAC,OAAOT,GAAG,EAAE;MACZF,OAAO,CAAC5B,KAAK,CAAC,6CAA6C,EAAE8B,GAAG,CAAC;IACnE,CAAC,SAAS;MACRnC,QAAQ,CAAC;QAAE8B,IAAI,EAAE7B,WAAW,CAAC8B,WAAW;QAAEC,OAAO,EAAE;MAAM,CAAC,CAAC;IAC7D;EACF,CAAC;;EAED;EACA,MAAM4B,YAAY,GAAIT,EAAE,IAAK;IAC3B,IAAIU,MAAM,CAACC,OAAO,CAAC,iDAAiD,CAAC,EAAE;MACrE9D,QAAQ,CAAC;QAAE8B,IAAI,EAAE7B,WAAW,CAAC8B,WAAW;QAAEC,OAAO,EAAE;MAAK,CAAC,CAAC;MAE1D,IAAI;QACFhC,QAAQ,CAAC;UAAE8B,IAAI,EAAE7B,WAAW,CAAC8D,cAAc;UAAE/B,OAAO,EAAEmB;QAAG,CAAC,CAAC;QAC3DlB,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;MAC7C,CAAC,CAAC,OAAOC,GAAG,EAAE;QACZF,OAAO,CAAC5B,KAAK,CAAC,0CAA0C,EAAE8B,GAAG,CAAC;MAChE,CAAC,SAAS;QACRnC,QAAQ,CAAC;UAAE8B,IAAI,EAAE7B,WAAW,CAAC8B,WAAW;UAAEC,OAAO,EAAE;QAAM,CAAC,CAAC;MAC7D;IACF;EACF,CAAC;;EAED;EACA,MAAMgC,YAAY,GAAI3B,CAAC,IAAK;IAC1B7B,aAAa,CAAC6B,CAAC,CAACI,MAAM,CAACH,KAAK,CAAC;EAC/B,CAAC;EAED,oBACE1C,OAAA;IAAKqE,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBtE,OAAA;MAAKqE,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChDtE,OAAA;QAAAsE,QAAA,gBACEtE,OAAA;UAAIqE,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1E1E,OAAA;UAAGqE,SAAS,EAAC,uBAAuB;UAAAC,QAAA,GACjC5C,gBAAgB,CAACiD,MAAM,EAAC,2CAC3B;QAAA;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACN1E,OAAA;QAAKqE,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BtE,OAAA;UACE4E,OAAO,EAAE3C,WAAY;UACrB4C,QAAQ,EAAErE,OAAQ;UAClB6D,SAAS,EAAC,mFAAmF;UAAAC,QAAA,EAE5F9D,OAAO,GAAG,kBAAkB,GAAG;QAAY;UAAA+D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC,eACT1E,OAAA;UACE4E,OAAO,EAAEA,CAAA,KAAM7B,SAAS,CAAC,CAAE;UAC3BsB,SAAS,EAAC,iEAAiE;UAAAC,QAAA,EAC5E;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN1E,OAAA;MAAKqE,SAAS,EAAC,gCAAgC;MAAAC,QAAA,eAC7CtE,OAAA;QACEkC,IAAI,EAAC,MAAM;QACX4C,WAAW,EAAC,0BAA0B;QACtCpC,KAAK,EAAE/B,UAAW;QAClBoE,QAAQ,EAAEX,YAAa;QACvBC,SAAS,EAAC;MAAyF;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAELjE,KAAK,iBACJT,OAAA;MAAKqE,SAAS,EAAC,mFAAmF;MAACW,IAAI,EAAC,OAAO;MAAAV,QAAA,gBAC7GtE,OAAA;QAAQqE,SAAS,EAAC,WAAW;QAAAC,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC5C1E,OAAA;QAAMqE,SAAS,EAAC,iBAAiB;QAAAC,QAAA,GAAC,GAAC,EAAC7D,KAAK;MAAA;QAAA8D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9C,CACN,eAGD1E,OAAA;MAAKqE,SAAS,EAAC,4CAA4C;MAAAC,QAAA,eACzDtE,OAAA;QAAKqE,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9BtE,OAAA;UAAOqE,SAAS,EAAC,qCAAqC;UAAAC,QAAA,gBACpDtE,OAAA;YAAOqE,SAAS,EAAC,YAAY;YAAAC,QAAA,eAC3BtE,OAAA;cAAAsE,QAAA,gBACEtE,OAAA;gBAAIqE,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzG1E,OAAA;gBAAIqE,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvG1E,OAAA;gBAAIqE,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxG1E,OAAA;gBAAIqE,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzG1E,OAAA;gBAAIqE,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7G1E,OAAA;gBAAIqE,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7G1E,OAAA;gBAAIqE,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACR1E,OAAA;YAAOqE,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EACjD5C,gBAAgB,CAACuD,GAAG,CAAEnD,OAAO,iBAC5B9B,OAAA;cAAAsE,QAAA,gBACEtE,OAAA;gBAAIqE,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,eACzCtE,OAAA;kBAAKqE,SAAS,EAAC,oDAAoD;kBAAAC,QAAA,EAChExC,OAAO,CAACgC,SAAS,gBAChB9D,OAAA;oBACEkF,GAAG,EAAEpD,OAAO,CAACgC,SAAU;oBACvBqB,GAAG,EAAErD,OAAO,CAACX,IAAK;oBAClBkD,SAAS,EAAC;kBAA4B;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC,CAAC,gBAEF1E,OAAA;oBAAKqE,SAAS,EAAC,8DAA8D;oBAAAC,QAAA,eAC3EtE,OAAA;sBAAKoF,KAAK,EAAC,4BAA4B;sBAACf,SAAS,EAAC,SAAS;sBAACgB,IAAI,EAAC,MAAM;sBAACC,OAAO,EAAC,WAAW;sBAACC,MAAM,EAAC,cAAc;sBAAAjB,QAAA,eAC/GtE,OAAA;wBAAMwF,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,WAAW,EAAE,CAAE;wBAACC,CAAC,EAAC;sBAA2J;wBAAApB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACL1E,OAAA;gBAAIqE,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,EAAExC,OAAO,CAACX;cAAI;gBAAAoD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACrF1E,OAAA;gBAAIqE,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,GAAExC,OAAO,CAACT,KAAK,EAAC,KAAG;cAAA;gBAAAkD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzF1E,OAAA;gBAAIqE,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,eAC/DtE,OAAA;kBAAMqE,SAAS,EAAE,iEACfvC,OAAO,CAACR,KAAK,GAAG,EAAE,GAAG,6BAA6B,GAAG,yBAAyB,EAC7E;kBAAAgD,QAAA,EACAxC,OAAO,CAACR;gBAAK;kBAAAiD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACL1E,OAAA;gBAAIqE,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,EAC9DxC,OAAO,CAAC0B,QAAQ,GAAG1B,OAAO,CAAC0B,QAAQ,CAACrC,IAAI,GAAG;cAAgB;gBAAAoD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC,eACL1E,OAAA;gBAAIqE,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,EAC9DxC,OAAO,CAACN,YAAY,gBACnBxB,OAAA;kBAAMqE,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,gBAE3C1E,OAAA;kBAAMqE,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cACzC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACL1E,OAAA;gBAAIqE,SAAS,EAAC,iDAAiD;gBAAAC,QAAA,gBAC7DtE,OAAA;kBACE4E,OAAO,EAAEA,CAAA,KAAM7B,SAAS,CAACjB,OAAO,CAAE;kBAClCuC,SAAS,EAAC,4CAA4C;kBAAAC,QAAA,EACvD;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT1E,OAAA;kBACE4E,OAAO,EAAEA,CAAA,KAAMZ,YAAY,CAAClC,OAAO,CAACyB,EAAE,CAAE;kBACxCc,SAAS,EAAC,iCAAiC;kBAAAC,QAAA,EAC5C;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC;YAAA,GAlDE5C,OAAO,CAACyB,EAAE;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAmDf,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL7D,WAAW,iBACVb,OAAA;MAAKqE,SAAS,EAAC,4EAA4E;MAAAC,QAAA,eACzFtE,OAAA;QAAKqE,SAAS,EAAC,oDAAoD;QAAAC,QAAA,gBACjEtE,OAAA;UAAKqE,SAAS,EAAC,oBAAoB;UAAAC,QAAA,eACjCtE,OAAA;YAAIqE,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAClCvD,cAAc,GAAG,qBAAqB,GAAG;UAAoB;YAAAwD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACN1E,OAAA;UAAM4F,QAAQ,EAAE3C,YAAa;UAAAqB,QAAA,gBAC3BtE,OAAA;YAAKqE,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAClBtE,OAAA;cAAKqE,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBtE,OAAA;gBAAO6F,OAAO,EAAC,MAAM;gBAACxB,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtE1E,OAAA;gBACEkC,IAAI,EAAC,MAAM;gBACXqB,EAAE,EAAC,MAAM;gBACTpC,IAAI,EAAC,MAAM;gBACXuB,KAAK,EAAEzB,QAAQ,CAACE,IAAK;gBACrB4D,QAAQ,EAAEvC,iBAAkB;gBAC5B6B,SAAS,EAAC,yFAAyF;gBACnGyB,QAAQ;cAAA;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN1E,OAAA;cAAKqE,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBtE,OAAA;gBAAO6F,OAAO,EAAC,aAAa;gBAACxB,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrF1E,OAAA;gBACEuD,EAAE,EAAC,aAAa;gBAChBpC,IAAI,EAAC,aAAa;gBAClBuB,KAAK,EAAEzB,QAAQ,CAACG,WAAY;gBAC5B2D,QAAQ,EAAEvC,iBAAkB;gBAC5B6B,SAAS,EAAC,yFAAyF;gBACnG0B,IAAI,EAAC;cAAG;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eAEN1E,OAAA;cAAKqE,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1CtE,OAAA;gBAAAsE,QAAA,gBACEtE,OAAA;kBAAO6F,OAAO,EAAC,OAAO;kBAACxB,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC7E1E,OAAA;kBACEkC,IAAI,EAAC,QAAQ;kBACbqB,EAAE,EAAC,OAAO;kBACVpC,IAAI,EAAC,OAAO;kBACZuB,KAAK,EAAEzB,QAAQ,CAACI,KAAM;kBACtB0D,QAAQ,EAAEvC,iBAAkB;kBAC5B6B,SAAS,EAAC,yFAAyF;kBACnG2B,IAAI,EAAC,MAAM;kBACXC,GAAG,EAAC,GAAG;kBACPH,QAAQ;gBAAA;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN1E,OAAA;gBAAAsE,QAAA,gBACEtE,OAAA;kBAAO6F,OAAO,EAAC,OAAO;kBAACxB,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACzE1E,OAAA;kBACEkC,IAAI,EAAC,QAAQ;kBACbqB,EAAE,EAAC,OAAO;kBACVpC,IAAI,EAAC,OAAO;kBACZuB,KAAK,EAAEzB,QAAQ,CAACK,KAAM;kBACtByD,QAAQ,EAAEvC,iBAAkB;kBAC5B6B,SAAS,EAAC,yFAAyF;kBACnG4B,GAAG,EAAC,GAAG;kBACPH,QAAQ;gBAAA;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN1E,OAAA;cAAKqE,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBtE,OAAA;gBAAO6F,OAAO,EAAC,aAAa;gBAACxB,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACnF1E,OAAA;gBACEuD,EAAE,EAAC,aAAa;gBAChBpC,IAAI,EAAC,aAAa;gBAClBuB,KAAK,EAAEzB,QAAQ,CAACM,WAAY;gBAC5BwD,QAAQ,EAAEvC,iBAAkB;gBAC5B6B,SAAS,EAAC,yFAAyF;gBACnGyB,QAAQ;gBAAAxB,QAAA,gBAERtE,OAAA;kBAAQ0C,KAAK,EAAC,EAAE;kBAAA4B,QAAA,EAAC;gBAA0B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EACnDnE,UAAU,CAAC0E,GAAG,CAACzB,QAAQ,iBACtBxD,OAAA;kBAA0B0C,KAAK,EAAEc,QAAQ,CAACD,EAAG;kBAAAe,QAAA,EAC1Cd,QAAQ,CAACrC;gBAAI,GADHqC,QAAQ,CAACD,EAAE;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEhB,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAEN1E,OAAA;cAAKqE,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBtE,OAAA;gBAAO6F,OAAO,EAAC,OAAO;gBAACxB,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACzE1E,OAAA;gBACEkC,IAAI,EAAC,MAAM;gBACXqB,EAAE,EAAC,OAAO;gBACVpC,IAAI,EAAC,OAAO;gBACZ4D,QAAQ,EAAEvC,iBAAkB;gBAC5B6B,SAAS,EAAC,yFAAyF;gBACnG6B,MAAM,EAAC;cAAS;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC,EACD3D,cAAc,IAAIA,cAAc,CAAC+C,SAAS,iBACzC9D,OAAA;gBAAKqE,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBtE,OAAA;kBAAGqE,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACxD1E,OAAA;kBAAKqE,SAAS,EAAC,oDAAoD;kBAAAC,QAAA,eACjEtE,OAAA;oBACEkF,GAAG,EAAEnE,cAAc,CAAC+C,SAAU;oBAC9BqB,GAAG,EAAEpE,cAAc,CAACI,IAAK;oBACzBkD,SAAS,EAAC;kBAA4B;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEN1E,OAAA;cAAKqE,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnBtE,OAAA;gBAAKqE,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChCtE,OAAA;kBACEkC,IAAI,EAAC,UAAU;kBACfqB,EAAE,EAAC,cAAc;kBACjBpC,IAAI,EAAC,cAAc;kBACnBwB,OAAO,EAAE1B,QAAQ,CAACO,YAAa;kBAC/BuD,QAAQ,EAAEvC,iBAAkB;kBAC5B6B,SAAS,EAAC;gBAAqE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChF,CAAC,eACF1E,OAAA;kBAAO6F,OAAO,EAAC,cAAc;kBAACxB,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,EAAC;gBAEnE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN1E,OAAA;YAAKqE,SAAS,EAAC,gDAAgD;YAAAC,QAAA,gBAC7DtE,OAAA;cACEkC,IAAI,EAAC,QAAQ;cACb0C,OAAO,EAAE5B,UAAW;cACpBqB,SAAS,EAAC,kEAAkE;cAAAC,QAAA,EAC7E;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT1E,OAAA;cACEkC,IAAI,EAAC,QAAQ;cACb2C,QAAQ,EAAErE,OAAQ;cAClB6D,SAAS,EAAC,qFAAqF;cAAAC,QAAA,EAE9F9D,OAAO,GAAG,mBAAmB,GAAIO,cAAc,GAAG,eAAe,GAAG;YAAU;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACxE,EAAA,CAjbID,YAAY;EAAA,QACyBH,OAAO;AAAA;AAAAqG,EAAA,GAD5ClG,YAAY;AAmblB,eAAeA,YAAY;AAAC,IAAAkG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}