import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FaTag, FaPercent, FaDollarSign, FaShippingFast, FaClock } from 'react-icons/fa';
import { promotionService } from '../../services/api/promotionService';

const ActivePromotions = () => {
  const [promotions, setPromotions] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchPromotions = async () => {
      try {
        const data = await promotionService.getActivePromotions();
        setPromotions(data);
      } catch (error) {
        console.error('Erreur lors du chargement des promotions:', error);

        // Données de test en cas d'erreur de connexion
        const testPromotions = [
          {
            id: 1,
            name: 'Bienvenue 10%',
            code: 'BIENVENUE10',
            description: 'Réduction de 10% pour les nouveaux clients sur leur première commande',
            type: 'percentage',
            value: 10,
            minimum_amount: 50,
            end_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 jours
            is_active: true
          },
          {
            id: 2,
            name: 'Livraison Gratuite',
            code: 'LIVRAISON',
            description: 'Livraison gratuite pour toute commande supérieure à 30€',
            type: 'free_shipping',
            value: 0,
            minimum_amount: 30,
            end_date: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString(), // 15 jours
            is_active: true
          },
          {
            id: 3,
            name: 'Réduction 5€',
            code: 'REDUCTION5',
            description: '5€ de réduction immédiate sur votre commande',
            type: 'fixed',
            value: 5,
            minimum_amount: 25,
            end_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 jours
            is_active: true
          }
        ];

        setPromotions(testPromotions);
        console.log('Utilisation des données de test pour les promotions - Backend non disponible');
      } finally {
        setIsLoading(false);
      }
    };

    fetchPromotions();
  }, []);

  const getPromotionIcon = (type) => {
    switch (type) {
      case 'percentage':
        return <FaPercent className="text-xl" />;
      case 'fixed':
        return <FaDollarSign className="text-xl" />;
      case 'free_shipping':
        return <FaShippingFast className="text-xl" />;
      default:
        return <FaTag className="text-xl" />;
    }
  };

  const getPromotionColor = (type) => {
    switch (type) {
      case 'percentage':
        return 'from-blue-500 to-blue-600';
      case 'fixed':
        return 'from-green-500 to-green-600';
      case 'free_shipping':
        return 'from-purple-500 to-purple-600';
      default:
        return 'from-gray-500 to-gray-600';
    }
  };

  const formatValue = (promotion) => {
    switch (promotion.type) {
      case 'percentage':
        return `${promotion.value}%`;
      case 'fixed':
        return `${promotion.value}$`;
      case 'free_shipping':
        return 'Gratuite';
      default:
        return promotion.value;
    }
  };

  const formatEndDate = (endDate) => {
    const date = new Date(endDate);
    const now = new Date();
    const diffTime = date - now;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays <= 0) {
      return 'Expire aujourd\'hui';
    } else if (diffDays === 1) {
      return 'Expire demain';
    } else if (diffDays <= 7) {
      return `Expire dans ${diffDays} jours`;
    } else {
      return `Valide jusqu'au ${date.toLocaleDateString('fr-FR')}`;
    }
  };

  const copyToClipboard = (code) => {
    navigator.clipboard.writeText(code);
    // Vous pouvez ajouter une notification toast ici
  };

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {Array(3).fill(0).map((_, index) => (
          <div key={index} className="bg-gray-200 animate-pulse rounded-lg h-32"></div>
        ))}
      </div>
    );
  }

  if (promotions.length === 0) {
    return (
      <div className="text-center py-8">
        <FaTag className="mx-auto text-4xl text-gray-400 mb-4" />
        <p className="text-gray-600">Aucune promotion active pour le moment</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {promotions.map((promotion, index) => (
        <motion.div
          key={promotion.id}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: index * 0.1 }}
          className={`relative overflow-hidden rounded-lg bg-gradient-to-r ${getPromotionColor(promotion.type)} text-white shadow-lg hover:shadow-xl transition-shadow cursor-pointer`}
          onClick={() => copyToClipboard(promotion.code)}
        >
          <div className="p-6">
            <div className="flex items-start justify-between mb-4">
              <div className="bg-white bg-opacity-20 p-3 rounded-full">
                {getPromotionIcon(promotion.type)}
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold">
                  {formatValue(promotion)}
                </div>
                <div className="text-sm opacity-90">
                  {promotion.type === 'percentage' && 'de réduction'}
                  {promotion.type === 'fixed' && 'de réduction'}
                  {promotion.type === 'free_shipping' && 'livraison'}
                </div>
              </div>
            </div>

            <div className="mb-4">
              <h3 className="font-semibold text-lg mb-1">{promotion.name}</h3>
              <p className="text-sm opacity-90 line-clamp-2">{promotion.description}</p>
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm opacity-90">Code:</span>
                <span className="font-mono font-bold bg-white bg-opacity-20 px-2 py-1 rounded">
                  {promotion.code}
                </span>
              </div>

              {promotion.minimum_amount && (
                <div className="flex items-center justify-between">
                  <span className="text-sm opacity-90">Minimum:</span>
                  <span className="text-sm font-medium">{promotion.minimum_amount}$</span>
                </div>
              )}

              <div className="flex items-center justify-between">
                <FaClock className="text-sm opacity-75" />
                <span className="text-xs opacity-90">
                  {formatEndDate(promotion.end_date)}
                </span>
              </div>
            </div>
          </div>

          {/* Effet de brillance au survol */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-0 hover:opacity-10 transform -skew-x-12 -translate-x-full hover:translate-x-full transition-all duration-700"></div>
        </motion.div>
      ))}
    </div>
  );
};

export default ActivePromotions;
