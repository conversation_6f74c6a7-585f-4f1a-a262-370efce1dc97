{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Projet-Synthese\\\\frontend\\\\src\\\\context\\\\DataContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useReducer, useEffect } from 'react';\nimport { productAPI, categoryAPI } from '../services/apiService';\n\n// État initial\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst initialState = {\n  products: [],\n  categories: [],\n  orders: [],\n  users: [],\n  promotions: [],\n  loading: false,\n  error: null\n};\n\n// Actions\nconst DataActions = {\n  SET_LOADING: 'SET_LOADING',\n  SET_ERROR: 'SET_ERROR',\n  SET_PRODUCTS: 'SET_PRODUCTS',\n  ADD_PRODUCT: 'ADD_PRODUCT',\n  UPDATE_PRODUCT: 'UPDATE_PRODUCT',\n  DELETE_PRODUCT: 'DELETE_PRODUCT',\n  SET_CATEGORIES: 'SET_CATEGORIES',\n  ADD_CATEGORY: 'ADD_CATEGORY',\n  UPDATE_CATEGORY: 'UPDATE_CATEGORY',\n  DELETE_CATEGORY: 'DELETE_CATEGORY',\n  SET_ORDERS: 'SET_ORDERS',\n  ADD_ORDER: 'ADD_ORDER',\n  UPDATE_ORDER: 'UPDATE_ORDER',\n  DELETE_ORDER: 'DELETE_ORDER',\n  SET_USERS: 'SET_USERS',\n  ADD_USER: 'ADD_USER',\n  UPDATE_USER: 'UPDATE_USER',\n  DELETE_USER: 'DELETE_USER',\n  SET_PROMOTIONS: 'SET_PROMOTIONS',\n  ADD_PROMOTION: 'ADD_PROMOTION',\n  UPDATE_PROMOTION: 'UPDATE_PROMOTION',\n  DELETE_PROMOTION: 'DELETE_PROMOTION'\n};\n\n// Reducer\nconst dataReducer = (state, action) => {\n  switch (action.type) {\n    case DataActions.SET_LOADING:\n      return {\n        ...state,\n        loading: action.payload\n      };\n    case DataActions.SET_ERROR:\n      return {\n        ...state,\n        error: action.payload,\n        loading: false\n      };\n\n    // Products\n    case DataActions.SET_PRODUCTS:\n      return {\n        ...state,\n        products: action.payload,\n        loading: false\n      };\n    case DataActions.ADD_PRODUCT:\n      return {\n        ...state,\n        products: [...state.products, {\n          ...action.payload,\n          id: Date.now()\n        }],\n        loading: false\n      };\n    case DataActions.UPDATE_PRODUCT:\n      return {\n        ...state,\n        products: state.products.map(product => product.id === action.payload.id ? {\n          ...product,\n          ...action.payload\n        } : product),\n        loading: false\n      };\n    case DataActions.DELETE_PRODUCT:\n      return {\n        ...state,\n        products: state.products.filter(product => product.id !== action.payload),\n        loading: false\n      };\n\n    // Categories\n    case DataActions.SET_CATEGORIES:\n      return {\n        ...state,\n        categories: action.payload,\n        loading: false\n      };\n    case DataActions.ADD_CATEGORY:\n      return {\n        ...state,\n        categories: [...state.categories, {\n          ...action.payload,\n          id: Date.now()\n        }],\n        loading: false\n      };\n    case DataActions.UPDATE_CATEGORY:\n      return {\n        ...state,\n        categories: state.categories.map(category => category.id === action.payload.id ? {\n          ...category,\n          ...action.payload\n        } : category),\n        loading: false\n      };\n    case DataActions.DELETE_CATEGORY:\n      return {\n        ...state,\n        categories: state.categories.filter(category => category.id !== action.payload),\n        loading: false\n      };\n\n    // Orders\n    case DataActions.SET_ORDERS:\n      return {\n        ...state,\n        orders: action.payload,\n        loading: false\n      };\n    case DataActions.ADD_ORDER:\n      return {\n        ...state,\n        orders: [...state.orders, {\n          ...action.payload,\n          id: Date.now()\n        }],\n        loading: false\n      };\n    case DataActions.UPDATE_ORDER:\n      return {\n        ...state,\n        orders: state.orders.map(order => order.id === action.payload.id ? {\n          ...order,\n          ...action.payload\n        } : order),\n        loading: false\n      };\n    case DataActions.DELETE_ORDER:\n      return {\n        ...state,\n        orders: state.orders.filter(order => order.id !== action.payload),\n        loading: false\n      };\n\n    // Users\n    case DataActions.SET_USERS:\n      return {\n        ...state,\n        users: action.payload,\n        loading: false\n      };\n    case DataActions.ADD_USER:\n      return {\n        ...state,\n        users: [...state.users, {\n          ...action.payload,\n          id: Date.now()\n        }],\n        loading: false\n      };\n    case DataActions.UPDATE_USER:\n      return {\n        ...state,\n        users: state.users.map(user => user.id === action.payload.id ? {\n          ...user,\n          ...action.payload\n        } : user),\n        loading: false\n      };\n    case DataActions.DELETE_USER:\n      return {\n        ...state,\n        users: state.users.filter(user => user.id !== action.payload),\n        loading: false\n      };\n\n    // Promotions\n    case DataActions.SET_PROMOTIONS:\n      return {\n        ...state,\n        promotions: action.payload,\n        loading: false\n      };\n    case DataActions.ADD_PROMOTION:\n      return {\n        ...state,\n        promotions: [...state.promotions, {\n          ...action.payload,\n          id: Date.now()\n        }],\n        loading: false\n      };\n    case DataActions.UPDATE_PROMOTION:\n      return {\n        ...state,\n        promotions: state.promotions.map(promotion => promotion.id === action.payload.id ? {\n          ...promotion,\n          ...action.payload\n        } : promotion),\n        loading: false\n      };\n    case DataActions.DELETE_PROMOTION:\n      return {\n        ...state,\n        promotions: state.promotions.filter(promotion => promotion.id !== action.payload),\n        loading: false\n      };\n    default:\n      return state;\n  }\n};\n\n// Context\nconst DataContext = /*#__PURE__*/createContext();\n\n// Provider\nexport const DataProvider = ({\n  children\n}) => {\n  _s();\n  const [state, dispatch] = useReducer(dataReducer, initialState);\n\n  // Charger les données depuis l'API\n  const loadDataFromAPI = async () => {\n    dispatch({\n      type: DataActions.SET_LOADING,\n      payload: true\n    });\n    try {\n      // Charger les catégories\n      const categoriesResult = await categoryAPI.getAll();\n      if (categoriesResult.success && categoriesResult.data.length > 0) {\n        dispatch({\n          type: DataActions.SET_CATEGORIES,\n          payload: categoriesResult.data\n        });\n      } else {\n        // Fallback: données de test pour les catégories\n        initializeTestCategories();\n      }\n\n      // Charger les produits\n      const productsResult = await productAPI.getAll();\n      if (productsResult.success && productsResult.data.length > 0) {\n        dispatch({\n          type: DataActions.SET_PRODUCTS,\n          payload: productsResult.data\n        });\n      } else {\n        // Fallback: données de test pour les produits\n        initializeTestProducts();\n      }\n    } catch (error) {\n      console.log('API non disponible, utilisation des données de test');\n      initializeTestData();\n    } finally {\n      dispatch({\n        type: DataActions.SET_LOADING,\n        payload: false\n      });\n    }\n  };\n\n  // Données de test pour les catégories\n  const initializeTestCategories = () => {\n    const testCategories = [{\n      id: 1,\n      name: 'Produits Grillés',\n      description: 'Viandes et légumes grillés',\n      color: 'bg-red-100',\n      textColor: 'text-red-800',\n      status: 'active',\n      slug: 'produits-grilles'\n    }, {\n      id: 2,\n      name: 'Salades',\n      description: 'Salades fraîches et variées',\n      color: 'bg-green-100',\n      textColor: 'text-green-800',\n      status: 'active',\n      slug: 'salades'\n    }, {\n      id: 3,\n      name: 'Fromages',\n      description: 'Sélection de fromages artisanaux',\n      color: 'bg-yellow-100',\n      textColor: 'text-yellow-800',\n      status: 'active',\n      slug: 'fromages'\n    }, {\n      id: 4,\n      name: 'Boissons',\n      description: 'Boissons fraîches et chaudes',\n      color: 'bg-blue-100',\n      textColor: 'text-blue-800',\n      status: 'active',\n      slug: 'boissons'\n    }, {\n      id: 5,\n      name: 'Desserts',\n      description: 'Desserts maison délicieux',\n      color: 'bg-purple-100',\n      textColor: 'text-purple-800',\n      status: 'active',\n      slug: 'desserts'\n    }, {\n      id: 6,\n      name: 'Produits Non Grillés',\n      description: 'Plats cuisinés traditionnels',\n      color: 'bg-gray-100',\n      textColor: 'text-gray-800',\n      status: 'active',\n      slug: 'produits-non-grilles'\n    }];\n    dispatch({\n      type: DataActions.SET_CATEGORIES,\n      payload: testCategories\n    });\n  };\n\n  // Données de test pour les produits\n  const initializeTestProducts = () => {\n    const testProducts = [{\n      id: 1,\n      name: 'Poulet Grillé Entier',\n      description: 'Poulet entier grillé aux herbes',\n      price: 89.99,\n      stock: 15,\n      category_id: 1,\n      is_grillable: true,\n      image_url: 'https://images.unsplash.com/photo-1598103442097-8b74394b95c6?w=300',\n      slug: 'poulet-grille-entier'\n    }, {\n      id: 2,\n      name: 'Brochettes de Bœuf',\n      description: 'Brochettes de bœuf marinées',\n      price: 65.50,\n      stock: 8,\n      category_id: 1,\n      is_grillable: true,\n      image_url: 'https://images.unsplash.com/photo-1529692236671-f1f6cf9683ba?w=300',\n      slug: 'brochettes-boeuf'\n    }, {\n      id: 3,\n      name: 'Salade César',\n      description: 'Salade César avec croûtons',\n      price: 35.00,\n      stock: 20,\n      category_id: 2,\n      is_grillable: false,\n      image_url: 'https://images.unsplash.com/photo-1546793665-c74683f339c1?w=300',\n      slug: 'salade-cesar'\n    }, {\n      id: 4,\n      name: 'Plateau de Fromages',\n      description: 'Assortiment de fromages locaux',\n      price: 75.00,\n      stock: 12,\n      category_id: 3,\n      is_grillable: false,\n      image_url: 'https://images.unsplash.com/photo-1486297678162-eb2a19b0a32d?w=300',\n      slug: 'plateau-fromages'\n    }, {\n      id: 5,\n      name: 'Jus d\\'Orange Frais',\n      description: 'Jus d\\'orange pressé maison',\n      price: 15.00,\n      stock: 25,\n      category_id: 4,\n      is_grillable: false,\n      image_url: 'https://images.unsplash.com/photo-1621506289937-a8e4df240d0b?w=300',\n      slug: 'jus-orange-frais'\n    }, {\n      id: 6,\n      name: 'Tiramisu Maison',\n      description: 'Tiramisu traditionnel italien',\n      price: 25.00,\n      stock: 10,\n      category_id: 5,\n      is_grillable: false,\n      image_url: 'https://images.unsplash.com/photo-1571877227200-a0d98ea607e9?w=300',\n      slug: 'tiramisu-maison'\n    }, {\n      id: 7,\n      name: 'Saumon Grillé',\n      description: 'Filet de saumon grillé aux légumes',\n      price: 95.00,\n      stock: 6,\n      category_id: 1,\n      is_grillable: true,\n      image_url: 'https://images.unsplash.com/photo-1467003909585-2f8a72700288?w=300',\n      slug: 'saumon-grille'\n    }, {\n      id: 8,\n      name: 'Carpaccio de Bœuf',\n      description: 'Carpaccio de bœuf aux copeaux de parmesan',\n      price: 55.00,\n      stock: 14,\n      category_id: 6,\n      is_grillable: false,\n      image_url: 'https://images.unsplash.com/photo-**********-d76694265947?w=300',\n      slug: 'carpaccio-boeuf'\n    }];\n    dispatch({\n      type: DataActions.SET_PRODUCTS,\n      payload: testProducts\n    });\n  };\n\n  // Fallback complet\n  const initializeTestData = () => {\n    initializeTestCategories();\n    initializeTestProducts();\n  };\n  useEffect(() => {\n    loadDataFromAPI();\n  }, []);\n  return /*#__PURE__*/_jsxDEV(DataContext.Provider, {\n    value: {\n      state,\n      dispatch,\n      DataActions\n    },\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 266,\n    columnNumber: 5\n  }, this);\n};\n\n// Hook personnalisé\n_s(DataProvider, \"bgCdjuTOmPdSBRwTap80EFd9Y3U=\");\n_c = DataProvider;\nexport const useData = () => {\n  _s2();\n  const context = useContext(DataContext);\n  if (!context) {\n    throw new Error('useData must be used within a DataProvider');\n  }\n  return context;\n};\n_s2(useData, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport default DataContext;\nvar _c;\n$RefreshReg$(_c, \"DataProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useReducer", "useEffect", "productAPI", "categoryAPI", "jsxDEV", "_jsxDEV", "initialState", "products", "categories", "orders", "users", "promotions", "loading", "error", "DataActions", "SET_LOADING", "SET_ERROR", "SET_PRODUCTS", "ADD_PRODUCT", "UPDATE_PRODUCT", "DELETE_PRODUCT", "SET_CATEGORIES", "ADD_CATEGORY", "UPDATE_CATEGORY", "DELETE_CATEGORY", "SET_ORDERS", "ADD_ORDER", "UPDATE_ORDER", "DELETE_ORDER", "SET_USERS", "ADD_USER", "UPDATE_USER", "DELETE_USER", "SET_PROMOTIONS", "ADD_PROMOTION", "UPDATE_PROMOTION", "DELETE_PROMOTION", "dataReducer", "state", "action", "type", "payload", "id", "Date", "now", "map", "product", "filter", "category", "order", "user", "promotion", "DataContext", "DataProvider", "children", "_s", "dispatch", "loadDataFromAPI", "categoriesResult", "getAll", "success", "data", "length", "initializeTestCategories", "productsResult", "initializeTestProducts", "console", "log", "initializeTestData", "testCategories", "name", "description", "color", "textColor", "status", "slug", "testProducts", "price", "stock", "category_id", "is_grillable", "image_url", "Provider", "value", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useData", "_s2", "context", "Error", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet-Synthese/frontend/src/context/DataContext.js"], "sourcesContent": ["import React, { createContext, useContext, useReducer, useEffect } from 'react';\nimport { productAPI, categoryAPI } from '../services/apiService';\n\n// État initial\nconst initialState = {\n  products: [],\n  categories: [],\n  orders: [],\n  users: [],\n  promotions: [],\n  loading: false,\n  error: null\n};\n\n// Actions\nconst DataActions = {\n  SET_LOADING: 'SET_LOADING',\n  SET_ERROR: 'SET_ERROR',\n  SET_PRODUCTS: 'SET_PRODUCTS',\n  ADD_PRODUCT: 'ADD_PRODUCT',\n  UPDATE_PRODUCT: 'UPDATE_PRODUCT',\n  DELETE_PRODUCT: 'DELETE_PRODUCT',\n  SET_CATEGORIES: 'SET_CATEGORIES',\n  ADD_CATEGORY: 'ADD_CATEGORY',\n  UPDATE_CATEGORY: 'UPDATE_CATEGORY',\n  DELETE_CATEGORY: 'DELETE_CATEGORY',\n  SET_ORDERS: 'SET_ORDERS',\n  ADD_ORDER: 'ADD_ORDER',\n  UPDATE_ORDER: 'UPDATE_ORDER',\n  DELETE_ORDER: 'DELETE_ORDER',\n  SET_USERS: 'SET_USERS',\n  ADD_USER: 'ADD_USER',\n  UPDATE_USER: 'UPDATE_USER',\n  DELETE_USER: 'DELETE_USER',\n  SET_PROMOTIONS: 'SET_PROMOTIONS',\n  ADD_PROMOTION: 'ADD_PROMOTION',\n  UPDATE_PROMOTION: 'UPDATE_PROMOTION',\n  DELETE_PROMOTION: 'DELETE_PROMOTION'\n};\n\n// Reducer\nconst dataReducer = (state, action) => {\n  switch (action.type) {\n    case DataActions.SET_LOADING:\n      return { ...state, loading: action.payload };\n    \n    case DataActions.SET_ERROR:\n      return { ...state, error: action.payload, loading: false };\n    \n    // Products\n    case DataActions.SET_PRODUCTS:\n      return { ...state, products: action.payload, loading: false };\n    \n    case DataActions.ADD_PRODUCT:\n      return { \n        ...state, \n        products: [...state.products, { ...action.payload, id: Date.now() }],\n        loading: false \n      };\n    \n    case DataActions.UPDATE_PRODUCT:\n      return {\n        ...state,\n        products: state.products.map(product =>\n          product.id === action.payload.id ? { ...product, ...action.payload } : product\n        ),\n        loading: false\n      };\n    \n    case DataActions.DELETE_PRODUCT:\n      return {\n        ...state,\n        products: state.products.filter(product => product.id !== action.payload),\n        loading: false\n      };\n    \n    // Categories\n    case DataActions.SET_CATEGORIES:\n      return { ...state, categories: action.payload, loading: false };\n    \n    case DataActions.ADD_CATEGORY:\n      return { \n        ...state, \n        categories: [...state.categories, { ...action.payload, id: Date.now() }],\n        loading: false \n      };\n    \n    case DataActions.UPDATE_CATEGORY:\n      return {\n        ...state,\n        categories: state.categories.map(category =>\n          category.id === action.payload.id ? { ...category, ...action.payload } : category\n        ),\n        loading: false\n      };\n    \n    case DataActions.DELETE_CATEGORY:\n      return {\n        ...state,\n        categories: state.categories.filter(category => category.id !== action.payload),\n        loading: false\n      };\n    \n    // Orders\n    case DataActions.SET_ORDERS:\n      return { ...state, orders: action.payload, loading: false };\n    \n    case DataActions.ADD_ORDER:\n      return { \n        ...state, \n        orders: [...state.orders, { ...action.payload, id: Date.now() }],\n        loading: false \n      };\n    \n    case DataActions.UPDATE_ORDER:\n      return {\n        ...state,\n        orders: state.orders.map(order =>\n          order.id === action.payload.id ? { ...order, ...action.payload } : order\n        ),\n        loading: false\n      };\n    \n    case DataActions.DELETE_ORDER:\n      return {\n        ...state,\n        orders: state.orders.filter(order => order.id !== action.payload),\n        loading: false\n      };\n    \n    // Users\n    case DataActions.SET_USERS:\n      return { ...state, users: action.payload, loading: false };\n    \n    case DataActions.ADD_USER:\n      return { \n        ...state, \n        users: [...state.users, { ...action.payload, id: Date.now() }],\n        loading: false \n      };\n    \n    case DataActions.UPDATE_USER:\n      return {\n        ...state,\n        users: state.users.map(user =>\n          user.id === action.payload.id ? { ...user, ...action.payload } : user\n        ),\n        loading: false\n      };\n    \n    case DataActions.DELETE_USER:\n      return {\n        ...state,\n        users: state.users.filter(user => user.id !== action.payload),\n        loading: false\n      };\n    \n    // Promotions\n    case DataActions.SET_PROMOTIONS:\n      return { ...state, promotions: action.payload, loading: false };\n    \n    case DataActions.ADD_PROMOTION:\n      return { \n        ...state, \n        promotions: [...state.promotions, { ...action.payload, id: Date.now() }],\n        loading: false \n      };\n    \n    case DataActions.UPDATE_PROMOTION:\n      return {\n        ...state,\n        promotions: state.promotions.map(promotion =>\n          promotion.id === action.payload.id ? { ...promotion, ...action.payload } : promotion\n        ),\n        loading: false\n      };\n    \n    case DataActions.DELETE_PROMOTION:\n      return {\n        ...state,\n        promotions: state.promotions.filter(promotion => promotion.id !== action.payload),\n        loading: false\n      };\n    \n    default:\n      return state;\n  }\n};\n\n// Context\nconst DataContext = createContext();\n\n// Provider\nexport const DataProvider = ({ children }) => {\n  const [state, dispatch] = useReducer(dataReducer, initialState);\n\n  // Charger les données depuis l'API\n  const loadDataFromAPI = async () => {\n    dispatch({ type: DataActions.SET_LOADING, payload: true });\n\n    try {\n      // Charger les catégories\n      const categoriesResult = await categoryAPI.getAll();\n      if (categoriesResult.success && categoriesResult.data.length > 0) {\n        dispatch({ type: DataActions.SET_CATEGORIES, payload: categoriesResult.data });\n      } else {\n        // Fallback: données de test pour les catégories\n        initializeTestCategories();\n      }\n\n      // Charger les produits\n      const productsResult = await productAPI.getAll();\n      if (productsResult.success && productsResult.data.length > 0) {\n        dispatch({ type: DataActions.SET_PRODUCTS, payload: productsResult.data });\n      } else {\n        // Fallback: données de test pour les produits\n        initializeTestProducts();\n      }\n    } catch (error) {\n      console.log('API non disponible, utilisation des données de test');\n      initializeTestData();\n    } finally {\n      dispatch({ type: DataActions.SET_LOADING, payload: false });\n    }\n  };\n\n  // Données de test pour les catégories\n  const initializeTestCategories = () => {\n    const testCategories = [\n      { id: 1, name: 'Produits Grillés', description: 'Viandes et légumes grillés', color: 'bg-red-100', textColor: 'text-red-800', status: 'active', slug: 'produits-grilles' },\n      { id: 2, name: 'Salades', description: 'Salades fraîches et variées', color: 'bg-green-100', textColor: 'text-green-800', status: 'active', slug: 'salades' },\n      { id: 3, name: 'Fromages', description: 'Sélection de fromages artisanaux', color: 'bg-yellow-100', textColor: 'text-yellow-800', status: 'active', slug: 'fromages' },\n      { id: 4, name: 'Boissons', description: 'Boissons fraîches et chaudes', color: 'bg-blue-100', textColor: 'text-blue-800', status: 'active', slug: 'boissons' },\n      { id: 5, name: 'Desserts', description: 'Desserts maison délicieux', color: 'bg-purple-100', textColor: 'text-purple-800', status: 'active', slug: 'desserts' },\n      { id: 6, name: 'Produits Non Grillés', description: 'Plats cuisinés traditionnels', color: 'bg-gray-100', textColor: 'text-gray-800', status: 'active', slug: 'produits-non-grilles' }\n    ];\n    dispatch({ type: DataActions.SET_CATEGORIES, payload: testCategories });\n  };\n\n  // Données de test pour les produits\n  const initializeTestProducts = () => {\n    const testProducts = [\n      { id: 1, name: 'Poulet Grillé Entier', description: 'Poulet entier grillé aux herbes', price: 89.99, stock: 15, category_id: 1, is_grillable: true, image_url: 'https://images.unsplash.com/photo-1598103442097-8b74394b95c6?w=300', slug: 'poulet-grille-entier' },\n      { id: 2, name: 'Brochettes de Bœuf', description: 'Brochettes de bœuf marinées', price: 65.50, stock: 8, category_id: 1, is_grillable: true, image_url: 'https://images.unsplash.com/photo-1529692236671-f1f6cf9683ba?w=300', slug: 'brochettes-boeuf' },\n      { id: 3, name: 'Salade César', description: 'Salade César avec croûtons', price: 35.00, stock: 20, category_id: 2, is_grillable: false, image_url: 'https://images.unsplash.com/photo-1546793665-c74683f339c1?w=300', slug: 'salade-cesar' },\n      { id: 4, name: 'Plateau de Fromages', description: 'Assortiment de fromages locaux', price: 75.00, stock: 12, category_id: 3, is_grillable: false, image_url: 'https://images.unsplash.com/photo-1486297678162-eb2a19b0a32d?w=300', slug: 'plateau-fromages' },\n      { id: 5, name: 'Jus d\\'Orange Frais', description: 'Jus d\\'orange pressé maison', price: 15.00, stock: 25, category_id: 4, is_grillable: false, image_url: 'https://images.unsplash.com/photo-1621506289937-a8e4df240d0b?w=300', slug: 'jus-orange-frais' },\n      { id: 6, name: 'Tiramisu Maison', description: 'Tiramisu traditionnel italien', price: 25.00, stock: 10, category_id: 5, is_grillable: false, image_url: 'https://images.unsplash.com/photo-1571877227200-a0d98ea607e9?w=300', slug: 'tiramisu-maison' },\n      { id: 7, name: 'Saumon Grillé', description: 'Filet de saumon grillé aux légumes', price: 95.00, stock: 6, category_id: 1, is_grillable: true, image_url: 'https://images.unsplash.com/photo-1467003909585-2f8a72700288?w=300', slug: 'saumon-grille' },\n      { id: 8, name: 'Carpaccio de Bœuf', description: 'Carpaccio de bœuf aux copeaux de parmesan', price: 55.00, stock: 14, category_id: 6, is_grillable: false, image_url: 'https://images.unsplash.com/photo-**********-d76694265947?w=300', slug: 'carpaccio-boeuf' }\n    ];\n    dispatch({ type: DataActions.SET_PRODUCTS, payload: testProducts });\n  };\n\n  // Fallback complet\n  const initializeTestData = () => {\n    initializeTestCategories();\n    initializeTestProducts();\n  };\n\n  useEffect(() => {\n    loadDataFromAPI();\n  }, []);\n\n  return (\n    <DataContext.Provider value={{ state, dispatch, DataActions }}>\n      {children}\n    </DataContext.Provider>\n  );\n};\n\n// Hook personnalisé\nexport const useData = () => {\n  const context = useContext(DataContext);\n  if (!context) {\n    throw new Error('useData must be used within a DataProvider');\n  }\n  return context;\n};\n\nexport default DataContext;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,UAAU,EAAEC,SAAS,QAAQ,OAAO;AAC/E,SAASC,UAAU,EAAEC,WAAW,QAAQ,wBAAwB;;AAEhE;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,YAAY,GAAG;EACnBC,QAAQ,EAAE,EAAE;EACZC,UAAU,EAAE,EAAE;EACdC,MAAM,EAAE,EAAE;EACVC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,EAAE;EACdC,OAAO,EAAE,KAAK;EACdC,KAAK,EAAE;AACT,CAAC;;AAED;AACA,MAAMC,WAAW,GAAG;EAClBC,WAAW,EAAE,aAAa;EAC1BC,SAAS,EAAE,WAAW;EACtBC,YAAY,EAAE,cAAc;EAC5BC,WAAW,EAAE,aAAa;EAC1BC,cAAc,EAAE,gBAAgB;EAChCC,cAAc,EAAE,gBAAgB;EAChCC,cAAc,EAAE,gBAAgB;EAChCC,YAAY,EAAE,cAAc;EAC5BC,eAAe,EAAE,iBAAiB;EAClCC,eAAe,EAAE,iBAAiB;EAClCC,UAAU,EAAE,YAAY;EACxBC,SAAS,EAAE,WAAW;EACtBC,YAAY,EAAE,cAAc;EAC5BC,YAAY,EAAE,cAAc;EAC5BC,SAAS,EAAE,WAAW;EACtBC,QAAQ,EAAE,UAAU;EACpBC,WAAW,EAAE,aAAa;EAC1BC,WAAW,EAAE,aAAa;EAC1BC,cAAc,EAAE,gBAAgB;EAChCC,aAAa,EAAE,eAAe;EAC9BC,gBAAgB,EAAE,kBAAkB;EACpCC,gBAAgB,EAAE;AACpB,CAAC;;AAED;AACA,MAAMC,WAAW,GAAGA,CAACC,KAAK,EAAEC,MAAM,KAAK;EACrC,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAK1B,WAAW,CAACC,WAAW;MAC1B,OAAO;QAAE,GAAGuB,KAAK;QAAE1B,OAAO,EAAE2B,MAAM,CAACE;MAAQ,CAAC;IAE9C,KAAK3B,WAAW,CAACE,SAAS;MACxB,OAAO;QAAE,GAAGsB,KAAK;QAAEzB,KAAK,EAAE0B,MAAM,CAACE,OAAO;QAAE7B,OAAO,EAAE;MAAM,CAAC;;IAE5D;IACA,KAAKE,WAAW,CAACG,YAAY;MAC3B,OAAO;QAAE,GAAGqB,KAAK;QAAE/B,QAAQ,EAAEgC,MAAM,CAACE,OAAO;QAAE7B,OAAO,EAAE;MAAM,CAAC;IAE/D,KAAKE,WAAW,CAACI,WAAW;MAC1B,OAAO;QACL,GAAGoB,KAAK;QACR/B,QAAQ,EAAE,CAAC,GAAG+B,KAAK,CAAC/B,QAAQ,EAAE;UAAE,GAAGgC,MAAM,CAACE,OAAO;UAAEC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC;QAAE,CAAC,CAAC;QACpEhC,OAAO,EAAE;MACX,CAAC;IAEH,KAAKE,WAAW,CAACK,cAAc;MAC7B,OAAO;QACL,GAAGmB,KAAK;QACR/B,QAAQ,EAAE+B,KAAK,CAAC/B,QAAQ,CAACsC,GAAG,CAACC,OAAO,IAClCA,OAAO,CAACJ,EAAE,KAAKH,MAAM,CAACE,OAAO,CAACC,EAAE,GAAG;UAAE,GAAGI,OAAO;UAAE,GAAGP,MAAM,CAACE;QAAQ,CAAC,GAAGK,OACzE,CAAC;QACDlC,OAAO,EAAE;MACX,CAAC;IAEH,KAAKE,WAAW,CAACM,cAAc;MAC7B,OAAO;QACL,GAAGkB,KAAK;QACR/B,QAAQ,EAAE+B,KAAK,CAAC/B,QAAQ,CAACwC,MAAM,CAACD,OAAO,IAAIA,OAAO,CAACJ,EAAE,KAAKH,MAAM,CAACE,OAAO,CAAC;QACzE7B,OAAO,EAAE;MACX,CAAC;;IAEH;IACA,KAAKE,WAAW,CAACO,cAAc;MAC7B,OAAO;QAAE,GAAGiB,KAAK;QAAE9B,UAAU,EAAE+B,MAAM,CAACE,OAAO;QAAE7B,OAAO,EAAE;MAAM,CAAC;IAEjE,KAAKE,WAAW,CAACQ,YAAY;MAC3B,OAAO;QACL,GAAGgB,KAAK;QACR9B,UAAU,EAAE,CAAC,GAAG8B,KAAK,CAAC9B,UAAU,EAAE;UAAE,GAAG+B,MAAM,CAACE,OAAO;UAAEC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC;QAAE,CAAC,CAAC;QACxEhC,OAAO,EAAE;MACX,CAAC;IAEH,KAAKE,WAAW,CAACS,eAAe;MAC9B,OAAO;QACL,GAAGe,KAAK;QACR9B,UAAU,EAAE8B,KAAK,CAAC9B,UAAU,CAACqC,GAAG,CAACG,QAAQ,IACvCA,QAAQ,CAACN,EAAE,KAAKH,MAAM,CAACE,OAAO,CAACC,EAAE,GAAG;UAAE,GAAGM,QAAQ;UAAE,GAAGT,MAAM,CAACE;QAAQ,CAAC,GAAGO,QAC3E,CAAC;QACDpC,OAAO,EAAE;MACX,CAAC;IAEH,KAAKE,WAAW,CAACU,eAAe;MAC9B,OAAO;QACL,GAAGc,KAAK;QACR9B,UAAU,EAAE8B,KAAK,CAAC9B,UAAU,CAACuC,MAAM,CAACC,QAAQ,IAAIA,QAAQ,CAACN,EAAE,KAAKH,MAAM,CAACE,OAAO,CAAC;QAC/E7B,OAAO,EAAE;MACX,CAAC;;IAEH;IACA,KAAKE,WAAW,CAACW,UAAU;MACzB,OAAO;QAAE,GAAGa,KAAK;QAAE7B,MAAM,EAAE8B,MAAM,CAACE,OAAO;QAAE7B,OAAO,EAAE;MAAM,CAAC;IAE7D,KAAKE,WAAW,CAACY,SAAS;MACxB,OAAO;QACL,GAAGY,KAAK;QACR7B,MAAM,EAAE,CAAC,GAAG6B,KAAK,CAAC7B,MAAM,EAAE;UAAE,GAAG8B,MAAM,CAACE,OAAO;UAAEC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC;QAAE,CAAC,CAAC;QAChEhC,OAAO,EAAE;MACX,CAAC;IAEH,KAAKE,WAAW,CAACa,YAAY;MAC3B,OAAO;QACL,GAAGW,KAAK;QACR7B,MAAM,EAAE6B,KAAK,CAAC7B,MAAM,CAACoC,GAAG,CAACI,KAAK,IAC5BA,KAAK,CAACP,EAAE,KAAKH,MAAM,CAACE,OAAO,CAACC,EAAE,GAAG;UAAE,GAAGO,KAAK;UAAE,GAAGV,MAAM,CAACE;QAAQ,CAAC,GAAGQ,KACrE,CAAC;QACDrC,OAAO,EAAE;MACX,CAAC;IAEH,KAAKE,WAAW,CAACc,YAAY;MAC3B,OAAO;QACL,GAAGU,KAAK;QACR7B,MAAM,EAAE6B,KAAK,CAAC7B,MAAM,CAACsC,MAAM,CAACE,KAAK,IAAIA,KAAK,CAACP,EAAE,KAAKH,MAAM,CAACE,OAAO,CAAC;QACjE7B,OAAO,EAAE;MACX,CAAC;;IAEH;IACA,KAAKE,WAAW,CAACe,SAAS;MACxB,OAAO;QAAE,GAAGS,KAAK;QAAE5B,KAAK,EAAE6B,MAAM,CAACE,OAAO;QAAE7B,OAAO,EAAE;MAAM,CAAC;IAE5D,KAAKE,WAAW,CAACgB,QAAQ;MACvB,OAAO;QACL,GAAGQ,KAAK;QACR5B,KAAK,EAAE,CAAC,GAAG4B,KAAK,CAAC5B,KAAK,EAAE;UAAE,GAAG6B,MAAM,CAACE,OAAO;UAAEC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC;QAAE,CAAC,CAAC;QAC9DhC,OAAO,EAAE;MACX,CAAC;IAEH,KAAKE,WAAW,CAACiB,WAAW;MAC1B,OAAO;QACL,GAAGO,KAAK;QACR5B,KAAK,EAAE4B,KAAK,CAAC5B,KAAK,CAACmC,GAAG,CAACK,IAAI,IACzBA,IAAI,CAACR,EAAE,KAAKH,MAAM,CAACE,OAAO,CAACC,EAAE,GAAG;UAAE,GAAGQ,IAAI;UAAE,GAAGX,MAAM,CAACE;QAAQ,CAAC,GAAGS,IACnE,CAAC;QACDtC,OAAO,EAAE;MACX,CAAC;IAEH,KAAKE,WAAW,CAACkB,WAAW;MAC1B,OAAO;QACL,GAAGM,KAAK;QACR5B,KAAK,EAAE4B,KAAK,CAAC5B,KAAK,CAACqC,MAAM,CAACG,IAAI,IAAIA,IAAI,CAACR,EAAE,KAAKH,MAAM,CAACE,OAAO,CAAC;QAC7D7B,OAAO,EAAE;MACX,CAAC;;IAEH;IACA,KAAKE,WAAW,CAACmB,cAAc;MAC7B,OAAO;QAAE,GAAGK,KAAK;QAAE3B,UAAU,EAAE4B,MAAM,CAACE,OAAO;QAAE7B,OAAO,EAAE;MAAM,CAAC;IAEjE,KAAKE,WAAW,CAACoB,aAAa;MAC5B,OAAO;QACL,GAAGI,KAAK;QACR3B,UAAU,EAAE,CAAC,GAAG2B,KAAK,CAAC3B,UAAU,EAAE;UAAE,GAAG4B,MAAM,CAACE,OAAO;UAAEC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC;QAAE,CAAC,CAAC;QACxEhC,OAAO,EAAE;MACX,CAAC;IAEH,KAAKE,WAAW,CAACqB,gBAAgB;MAC/B,OAAO;QACL,GAAGG,KAAK;QACR3B,UAAU,EAAE2B,KAAK,CAAC3B,UAAU,CAACkC,GAAG,CAACM,SAAS,IACxCA,SAAS,CAACT,EAAE,KAAKH,MAAM,CAACE,OAAO,CAACC,EAAE,GAAG;UAAE,GAAGS,SAAS;UAAE,GAAGZ,MAAM,CAACE;QAAQ,CAAC,GAAGU,SAC7E,CAAC;QACDvC,OAAO,EAAE;MACX,CAAC;IAEH,KAAKE,WAAW,CAACsB,gBAAgB;MAC/B,OAAO;QACL,GAAGE,KAAK;QACR3B,UAAU,EAAE2B,KAAK,CAAC3B,UAAU,CAACoC,MAAM,CAACI,SAAS,IAAIA,SAAS,CAACT,EAAE,KAAKH,MAAM,CAACE,OAAO,CAAC;QACjF7B,OAAO,EAAE;MACX,CAAC;IAEH;MACE,OAAO0B,KAAK;EAChB;AACF,CAAC;;AAED;AACA,MAAMc,WAAW,gBAAGtD,aAAa,CAAC,CAAC;;AAEnC;AACA,OAAO,MAAMuD,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC5C,MAAM,CAACjB,KAAK,EAAEkB,QAAQ,CAAC,GAAGxD,UAAU,CAACqC,WAAW,EAAE/B,YAAY,CAAC;;EAE/D;EACA,MAAMmD,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClCD,QAAQ,CAAC;MAAEhB,IAAI,EAAE1B,WAAW,CAACC,WAAW;MAAE0B,OAAO,EAAE;IAAK,CAAC,CAAC;IAE1D,IAAI;MACF;MACA,MAAMiB,gBAAgB,GAAG,MAAMvD,WAAW,CAACwD,MAAM,CAAC,CAAC;MACnD,IAAID,gBAAgB,CAACE,OAAO,IAAIF,gBAAgB,CAACG,IAAI,CAACC,MAAM,GAAG,CAAC,EAAE;QAChEN,QAAQ,CAAC;UAAEhB,IAAI,EAAE1B,WAAW,CAACO,cAAc;UAAEoB,OAAO,EAAEiB,gBAAgB,CAACG;QAAK,CAAC,CAAC;MAChF,CAAC,MAAM;QACL;QACAE,wBAAwB,CAAC,CAAC;MAC5B;;MAEA;MACA,MAAMC,cAAc,GAAG,MAAM9D,UAAU,CAACyD,MAAM,CAAC,CAAC;MAChD,IAAIK,cAAc,CAACJ,OAAO,IAAII,cAAc,CAACH,IAAI,CAACC,MAAM,GAAG,CAAC,EAAE;QAC5DN,QAAQ,CAAC;UAAEhB,IAAI,EAAE1B,WAAW,CAACG,YAAY;UAAEwB,OAAO,EAAEuB,cAAc,CAACH;QAAK,CAAC,CAAC;MAC5E,CAAC,MAAM;QACL;QACAI,sBAAsB,CAAC,CAAC;MAC1B;IACF,CAAC,CAAC,OAAOpD,KAAK,EAAE;MACdqD,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;MAClEC,kBAAkB,CAAC,CAAC;IACtB,CAAC,SAAS;MACRZ,QAAQ,CAAC;QAAEhB,IAAI,EAAE1B,WAAW,CAACC,WAAW;QAAE0B,OAAO,EAAE;MAAM,CAAC,CAAC;IAC7D;EACF,CAAC;;EAED;EACA,MAAMsB,wBAAwB,GAAGA,CAAA,KAAM;IACrC,MAAMM,cAAc,GAAG,CACrB;MAAE3B,EAAE,EAAE,CAAC;MAAE4B,IAAI,EAAE,kBAAkB;MAAEC,WAAW,EAAE,4BAA4B;MAAEC,KAAK,EAAE,YAAY;MAAEC,SAAS,EAAE,cAAc;MAAEC,MAAM,EAAE,QAAQ;MAAEC,IAAI,EAAE;IAAmB,CAAC,EAC1K;MAAEjC,EAAE,EAAE,CAAC;MAAE4B,IAAI,EAAE,SAAS;MAAEC,WAAW,EAAE,6BAA6B;MAAEC,KAAK,EAAE,cAAc;MAAEC,SAAS,EAAE,gBAAgB;MAAEC,MAAM,EAAE,QAAQ;MAAEC,IAAI,EAAE;IAAU,CAAC,EAC7J;MAAEjC,EAAE,EAAE,CAAC;MAAE4B,IAAI,EAAE,UAAU;MAAEC,WAAW,EAAE,kCAAkC;MAAEC,KAAK,EAAE,eAAe;MAAEC,SAAS,EAAE,iBAAiB;MAAEC,MAAM,EAAE,QAAQ;MAAEC,IAAI,EAAE;IAAW,CAAC,EACtK;MAAEjC,EAAE,EAAE,CAAC;MAAE4B,IAAI,EAAE,UAAU;MAAEC,WAAW,EAAE,8BAA8B;MAAEC,KAAK,EAAE,aAAa;MAAEC,SAAS,EAAE,eAAe;MAAEC,MAAM,EAAE,QAAQ;MAAEC,IAAI,EAAE;IAAW,CAAC,EAC9J;MAAEjC,EAAE,EAAE,CAAC;MAAE4B,IAAI,EAAE,UAAU;MAAEC,WAAW,EAAE,2BAA2B;MAAEC,KAAK,EAAE,eAAe;MAAEC,SAAS,EAAE,iBAAiB;MAAEC,MAAM,EAAE,QAAQ;MAAEC,IAAI,EAAE;IAAW,CAAC,EAC/J;MAAEjC,EAAE,EAAE,CAAC;MAAE4B,IAAI,EAAE,sBAAsB;MAAEC,WAAW,EAAE,8BAA8B;MAAEC,KAAK,EAAE,aAAa;MAAEC,SAAS,EAAE,eAAe;MAAEC,MAAM,EAAE,QAAQ;MAAEC,IAAI,EAAE;IAAuB,CAAC,CACvL;IACDnB,QAAQ,CAAC;MAAEhB,IAAI,EAAE1B,WAAW,CAACO,cAAc;MAAEoB,OAAO,EAAE4B;IAAe,CAAC,CAAC;EACzE,CAAC;;EAED;EACA,MAAMJ,sBAAsB,GAAGA,CAAA,KAAM;IACnC,MAAMW,YAAY,GAAG,CACnB;MAAElC,EAAE,EAAE,CAAC;MAAE4B,IAAI,EAAE,sBAAsB;MAAEC,WAAW,EAAE,iCAAiC;MAAEM,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE,EAAE;MAAEC,WAAW,EAAE,CAAC;MAAEC,YAAY,EAAE,IAAI;MAAEC,SAAS,EAAE,oEAAoE;MAAEN,IAAI,EAAE;IAAuB,CAAC,EACnQ;MAAEjC,EAAE,EAAE,CAAC;MAAE4B,IAAI,EAAE,oBAAoB;MAAEC,WAAW,EAAE,6BAA6B;MAAEM,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE,CAAC;MAAEC,WAAW,EAAE,CAAC;MAAEC,YAAY,EAAE,IAAI;MAAEC,SAAS,EAAE,oEAAoE;MAAEN,IAAI,EAAE;IAAmB,CAAC,EACxP;MAAEjC,EAAE,EAAE,CAAC;MAAE4B,IAAI,EAAE,cAAc;MAAEC,WAAW,EAAE,4BAA4B;MAAEM,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE,EAAE;MAAEC,WAAW,EAAE,CAAC;MAAEC,YAAY,EAAE,KAAK;MAAEC,SAAS,EAAE,iEAAiE;MAAEN,IAAI,EAAE;IAAe,CAAC,EAC5O;MAAEjC,EAAE,EAAE,CAAC;MAAE4B,IAAI,EAAE,qBAAqB;MAAEC,WAAW,EAAE,gCAAgC;MAAEM,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE,EAAE;MAAEC,WAAW,EAAE,CAAC;MAAEC,YAAY,EAAE,KAAK;MAAEC,SAAS,EAAE,oEAAoE;MAAEN,IAAI,EAAE;IAAmB,CAAC,EAC9P;MAAEjC,EAAE,EAAE,CAAC;MAAE4B,IAAI,EAAE,qBAAqB;MAAEC,WAAW,EAAE,6BAA6B;MAAEM,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE,EAAE;MAAEC,WAAW,EAAE,CAAC;MAAEC,YAAY,EAAE,KAAK;MAAEC,SAAS,EAAE,oEAAoE;MAAEN,IAAI,EAAE;IAAmB,CAAC,EAC3P;MAAEjC,EAAE,EAAE,CAAC;MAAE4B,IAAI,EAAE,iBAAiB;MAAEC,WAAW,EAAE,+BAA+B;MAAEM,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE,EAAE;MAAEC,WAAW,EAAE,CAAC;MAAEC,YAAY,EAAE,KAAK;MAAEC,SAAS,EAAE,oEAAoE;MAAEN,IAAI,EAAE;IAAkB,CAAC,EACxP;MAAEjC,EAAE,EAAE,CAAC;MAAE4B,IAAI,EAAE,eAAe;MAAEC,WAAW,EAAE,oCAAoC;MAAEM,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE,CAAC;MAAEC,WAAW,EAAE,CAAC;MAAEC,YAAY,EAAE,IAAI;MAAEC,SAAS,EAAE,oEAAoE;MAAEN,IAAI,EAAE;IAAgB,CAAC,EACvP;MAAEjC,EAAE,EAAE,CAAC;MAAE4B,IAAI,EAAE,mBAAmB;MAAEC,WAAW,EAAE,2CAA2C;MAAEM,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE,EAAE;MAAEC,WAAW,EAAE,CAAC;MAAEC,YAAY,EAAE,KAAK;MAAEC,SAAS,EAAE,iEAAiE;MAAEN,IAAI,EAAE;IAAkB,CAAC,CACpQ;IACDnB,QAAQ,CAAC;MAAEhB,IAAI,EAAE1B,WAAW,CAACG,YAAY;MAAEwB,OAAO,EAAEmC;IAAa,CAAC,CAAC;EACrE,CAAC;;EAED;EACA,MAAMR,kBAAkB,GAAGA,CAAA,KAAM;IAC/BL,wBAAwB,CAAC,CAAC;IAC1BE,sBAAsB,CAAC,CAAC;EAC1B,CAAC;EAEDhE,SAAS,CAAC,MAAM;IACdwD,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEpD,OAAA,CAAC+C,WAAW,CAAC8B,QAAQ;IAACC,KAAK,EAAE;MAAE7C,KAAK;MAAEkB,QAAQ;MAAE1C;IAAY,CAAE;IAAAwC,QAAA,EAC3DA;EAAQ;IAAA8B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;;AAED;AAAAhC,EAAA,CA9EaF,YAAY;AAAAmC,EAAA,GAAZnC,YAAY;AA+EzB,OAAO,MAAMoC,OAAO,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC3B,MAAMC,OAAO,GAAG5F,UAAU,CAACqD,WAAW,CAAC;EACvC,IAAI,CAACuC,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,4CAA4C,CAAC;EAC/D;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,OAAO;AAQpB,eAAerC,WAAW;AAAC,IAAAoC,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}