{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Projet-Synthese\\\\frontend\\\\src\\\\components\\\\StatsCard.js\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StatsCard = ({\n  title,\n  value,\n  change,\n  icon,\n  color\n}) => {\n  const getColorClasses = color => {\n    const colors = {\n      blue: 'bg-blue-500',\n      green: 'bg-green-500',\n      purple: 'bg-purple-500',\n      orange: 'bg-orange-500',\n      red: 'bg-red-500',\n      yellow: 'bg-yellow-500'\n    };\n    return colors[color] || 'bg-gray-500';\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white p-4 lg:p-6 rounded-lg shadow-sm border\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm font-medium text-gray-600 mb-1\",\n          children: title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-2xl font-bold text-gray-900 mb-2\",\n          children: value\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 11\n        }, this), change && /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-600\",\n          children: change\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `p-3 rounded-full ${getColorClasses(color)}`,\n        children: icon\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 5\n  }, this);\n};\n_c = StatsCard;\nexport default StatsCard;\nvar _c;\n$RefreshReg$(_c, \"StatsCard\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "StatsCard", "title", "value", "change", "icon", "color", "getColorClasses", "colors", "blue", "green", "purple", "orange", "red", "yellow", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet-Synthese/frontend/src/components/StatsCard.js"], "sourcesContent": ["import React from 'react';\n\nconst StatsCard = ({ title, value, change, icon, color }) => {\n  const getColorClasses = (color) => {\n    const colors = {\n      blue: 'bg-blue-500',\n      green: 'bg-green-500',\n      purple: 'bg-purple-500',\n      orange: 'bg-orange-500',\n      red: 'bg-red-500',\n      yellow: 'bg-yellow-500'\n    };\n    return colors[color] || 'bg-gray-500';\n  };\n\n  return (\n    <div className=\"bg-white p-4 lg:p-6 rounded-lg shadow-sm border\">\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex-1\">\n          <p className=\"text-sm font-medium text-gray-600 mb-1\">{title}</p>\n          <p className=\"text-2xl font-bold text-gray-900 mb-2\">{value}</p>\n          {change && (\n            <p className=\"text-sm text-gray-600\">{change}</p>\n          )}\n        </div>\n        <div className={`p-3 rounded-full ${getColorClasses(color)}`}>\n          {icon}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default StatsCard;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,SAAS,GAAGA,CAAC;EAAEC,KAAK;EAAEC,KAAK;EAAEC,MAAM;EAAEC,IAAI;EAAEC;AAAM,CAAC,KAAK;EAC3D,MAAMC,eAAe,GAAID,KAAK,IAAK;IACjC,MAAME,MAAM,GAAG;MACbC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,cAAc;MACrBC,MAAM,EAAE,eAAe;MACvBC,MAAM,EAAE,eAAe;MACvBC,GAAG,EAAE,YAAY;MACjBC,MAAM,EAAE;IACV,CAAC;IACD,OAAON,MAAM,CAACF,KAAK,CAAC,IAAI,aAAa;EACvC,CAAC;EAED,oBACEN,OAAA;IAAKe,SAAS,EAAC,iDAAiD;IAAAC,QAAA,eAC9DhB,OAAA;MAAKe,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChDhB,OAAA;QAAKe,SAAS,EAAC,QAAQ;QAAAC,QAAA,gBACrBhB,OAAA;UAAGe,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAEd;QAAK;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjEpB,OAAA;UAAGe,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAEb;QAAK;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAC/DhB,MAAM,iBACLJ,OAAA;UAAGe,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAEZ;QAAM;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CACjD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACNpB,OAAA;QAAKe,SAAS,EAAE,oBAAoBR,eAAe,CAACD,KAAK,CAAC,EAAG;QAAAU,QAAA,EAC1DX;MAAI;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACC,EAAA,GA7BIpB,SAAS;AA+Bf,eAAeA,SAAS;AAAC,IAAAoB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}