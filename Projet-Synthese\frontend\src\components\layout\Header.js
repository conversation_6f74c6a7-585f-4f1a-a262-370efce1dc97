import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import useSafeAuth from '../../hooks/useSafeAuth';
import { useCart } from '../../context/CartContext';

const Header = () => {
  const { state: authState, logout } = useSafeAuth();
  const { state: cartState } = useCart();
  const navigate = useNavigate();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  
  const cartItemCount = cartState?.items ? cartState.items.length : 0;
  
  const handleLogout = () => {
    logout();
    navigate('/');
  };

  return (
    <header className="bg-white shadow-md">
      <div className="container mx-auto px-4 py-4">
        <div className="flex justify-between items-center">
          {/* Logo */}
          <Link to="/" className="text-2xl font-bold text-green-600">
            Épicerie en ligne
          </Link>
          
          {/* Navigation - Desktop */}
          <nav className="hidden md:flex items-center space-x-8">
            <Link to="/" className="text-gray-700 hover:text-green-600 transition">
              Accueil
            </Link>
            <Link to="/products" className="text-gray-700 hover:text-green-600 transition">
              Produits
            </Link>
            <Link to="/contact" className="text-gray-700 hover:text-green-600 transition">
              Contact
            </Link>
            <Link to="/about" className="text-gray-700 hover:text-green-600 transition">
              À propos
            </Link>
          </nav>
          
          {/* Actions - Desktop */}
          <div className="hidden md:flex items-center space-x-4">
            {authState && authState.isAuthenticated ? (
              <div className="relative group">
                <button className="flex items-center text-gray-700 hover:text-green-600 transition">
                  <span className="mr-1">{authState.user?.name || 'Mon compte'}</span>
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>
                <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10 hidden group-hover:block">
                  {authState.user?.role === 'admin' && (
                    <Link to="/admin/dashboard" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                      Administration
                    </Link>
                  )}
                  <Link to="/profile" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                    Mon profil
                  </Link>
                  <Link to="/orders" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                    Mes commandes
                  </Link>
                  <button 
                    onClick={handleLogout}
                    className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    Déconnexion
                  </button>
                </div>
              </div>
            ) : (
              <>
                <Link to="/login" className="text-gray-700 hover:text-green-600 transition">
                  Connexion
                </Link>
                <Link to="/register" className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition">
                  Inscription
                </Link>
              </>
            )}
            
            <Link to="/cart" className="relative text-gray-700 hover:text-green-600 transition">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
              {cartItemCount > 0 && (
                <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs w-5 h-5 flex items-center justify-center rounded-full">
                  {cartItemCount}
                </span>
              )}
            </Link>
          </div>
          
          {/* Mobile menu button */}
          <div className="md:hidden flex items-center space-x-4">
            <Link to="/cart" className="relative text-gray-700">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
              {cartItemCount > 0 && (
                <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs w-5 h-5 flex items-center justify-center rounded-full">
                  {cartItemCount}
                </span>
              )}
            </Link>
            <button 
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              className="text-gray-700"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
          </div>
        </div>
        
        {/* Mobile menu */}
        {mobileMenuOpen && (
          <div className="md:hidden mt-4 pb-4">
            <nav className="flex flex-col space-y-4">
              <Link to="/" className="text-gray-700 hover:text-green-600 transition">
                Accueil
              </Link>
              <Link to="/products" className="text-gray-700 hover:text-green-600 transition">
                Produits
              </Link>
              <Link to="/contact" className="text-gray-700 hover:text-green-600 transition">
                Contact
              </Link>
              <Link to="/about" className="text-gray-700 hover:text-green-600 transition">
                À propos
              </Link>
              
              {authState && authState.isAuthenticated ? (
                <>
                  <hr className="border-gray-200" />
                  {authState.user?.role === 'admin' && (
                    <Link to="/admin/dashboard" className="text-gray-700 hover:text-green-600 transition">
                      Administration
                    </Link>
                  )}
                  <Link to="/profile" className="text-gray-700 hover:text-green-600 transition">
                    Mon profil
                  </Link>
                  <Link to="/orders" className="text-gray-700 hover:text-green-600 transition">
                    Mes commandes
                  </Link>
                  <button 
                    onClick={handleLogout}
                    className="text-left text-gray-700 hover:text-green-600 transition"
                  >
                    Déconnexion
                  </button>
                </>
              ) : (
                <>
                  <hr className="border-gray-200" />
                  <Link to="/login" className="text-gray-700 hover:text-green-600 transition">
                    Connexion
                  </Link>
                  <Link to="/register" className="text-gray-700 hover:text-green-600 transition">
                    Inscription
                  </Link>
                </>
              )}
            </nav>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;

