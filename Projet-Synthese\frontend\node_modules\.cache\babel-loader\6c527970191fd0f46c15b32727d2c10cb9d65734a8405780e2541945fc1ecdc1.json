{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Projet-Synthese\\\\frontend\\\\src\\\\layouts\\\\AdminLayout.js\";\nimport React from 'react';\nimport { Outlet } from 'react-router-dom';\nimport AdminSidebar from '../components/admin/AdminSidebar';\nimport AdminHeader from '../components/admin/AdminHeader';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminLayout = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex h-screen bg-gray-100\",\n    children: [/*#__PURE__*/_jsxDEV(AdminSidebar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex flex-col overflow-hidden min-w-0\",\n      children: [/*#__PURE__*/_jsxDEV(AdminHeader, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"flex-1 overflow-x-auto overflow-y-auto bg-gray-100 p-4 lg:p-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"min-w-full\",\n          children: /*#__PURE__*/_jsxDEV(Outlet, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 14,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 13,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 8,\n    columnNumber: 5\n  }, this);\n};\n_c = AdminLayout;\nexport default AdminLayout;\nvar _c;\n$RefreshReg$(_c, \"AdminLayout\");", "map": {"version": 3, "names": ["React", "Outlet", "AdminSidebar", "Ad<PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "AdminLayout", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet-Synthese/frontend/src/layouts/AdminLayout.js"], "sourcesContent": ["import React from 'react';\nimport { Outlet } from 'react-router-dom';\nimport AdminSidebar from '../components/admin/AdminSidebar';\nimport AdminHeader from '../components/admin/AdminHeader';\n\nconst AdminLayout = () => {\n  return (\n    <div className=\"flex h-screen bg-gray-100\">\n      <AdminSidebar />\n      <div className=\"flex-1 flex flex-col overflow-hidden min-w-0\">\n        <AdminHeader />\n        <main className=\"flex-1 overflow-x-auto overflow-y-auto bg-gray-100 p-4 lg:p-6\">\n          <div className=\"min-w-full\">\n            <Outlet />\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n};\n\nexport default AdminLayout;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,kBAAkB;AACzC,OAAOC,YAAY,MAAM,kCAAkC;AAC3D,OAAOC,WAAW,MAAM,iCAAiC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1D,MAAMC,WAAW,GAAGA,CAAA,KAAM;EACxB,oBACED,OAAA;IAAKE,SAAS,EAAC,2BAA2B;IAAAC,QAAA,gBACxCH,OAAA,CAACH,YAAY;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAChBP,OAAA;MAAKE,SAAS,EAAC,8CAA8C;MAAAC,QAAA,gBAC3DH,OAAA,CAACF,WAAW;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACfP,OAAA;QAAME,SAAS,EAAC,+DAA+D;QAAAC,QAAA,eAC7EH,OAAA;UAAKE,SAAS,EAAC,YAAY;UAAAC,QAAA,eACzBH,OAAA,CAACJ,MAAM;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACC,EAAA,GAdIP,WAAW;AAgBjB,eAAeA,WAAW;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}