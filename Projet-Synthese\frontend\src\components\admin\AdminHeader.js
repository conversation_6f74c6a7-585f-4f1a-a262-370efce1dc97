import React from 'react';
import { Link } from 'react-router-dom';

const AdminHeader = () => {
  // Mode démonstration - interface simplifiée
  const userName = 'Admin YUMMY';

  const handleLogout = () => {
    // En mode démonstration, retour à l'accueil
    window.location.href = '/';
  };

  return (
    <header className="bg-white shadow-sm h-16 flex items-center px-6 sticky top-0 z-10">
      <div className="flex-1 flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <span className="text-gray-800 font-medium">
            Bonjour, {userName}
          </span>
          <span className="text-sm text-gray-500 bg-green-100 px-2 py-1 rounded">
            Mode Démonstration
          </span>
        </div>
        
        <div className="flex items-center space-x-4">
          <Link to="/" className="text-gray-600 hover:text-green-600 transition">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
            </svg>
          </Link>
          
          <button
            onClick={handleLogout}
            className="text-gray-600 hover:text-red-600 transition"
            title="Retour à l'accueil"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
            </svg>
          </button>
        </div>
      </div>
    </header>
  );
};

export default AdminHeader;