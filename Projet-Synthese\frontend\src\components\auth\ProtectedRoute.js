import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';

const ProtectedRoute = ({ children }) => {
  const { state } = useAuth();

  // Pour la démonstration, on permet l'accès sans authentification
  // En production, décommentez les lignes ci-dessous
  /*
  if (!state.isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  if (!state.user?.role || state.user.role !== 'admin') {
    return <Navigate to="/" replace />;
  }
  */

  // Mode démonstration - accès libre
  return children;
};

export default ProtectedRoute;