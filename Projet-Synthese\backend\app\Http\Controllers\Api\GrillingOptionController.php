<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\GrillingOption;
use Illuminate\Http\Request;

class GrillingOptionController extends Controller
{
    public function index()
    {
        $options = GrillingOption::where('is_active', true)->get();
        return response()->json($options);
    }

    public function show(GrillingOption $grillingOption)
    {
        return response()->json($grillingOption);
    }
}