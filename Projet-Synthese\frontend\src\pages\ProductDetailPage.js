import React, { useState, useEffect } from 'react';
import { useParams, Link, useNavigate } from 'react-router-dom';
import { productAPI } from '../services/apiService';
import { useCart } from '../context/CartContext';
import RelatedProducts from '../components/products/RelatedProducts';

const ProductDetailPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { dispatch } = useCart();
  const [product, setProduct] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [quantity, setQuantity] = useState(1);
  const [selectedGrillingOptions, setSelectedGrillingOptions] = useState({});

  useEffect(() => {
    fetchProduct();
  }, [id]);

  const fetchProduct = async () => {
    try {
      setLoading(true);
      console.log('🔄 Chargement du produit:', id);

      const response = await productAPI.getById(id);

      if (response.success) {
        setProduct(response.data);
        console.log('✅ Produit chargé:', response.data.name);
        setError(null);
      } else {
        console.log('⚠️ Produit non trouvé');
        setError('Produit non trouvé');
      }
    } catch (err) {
      console.error('❌ Erreur lors du chargement du produit:', err);
      setError('Erreur lors du chargement du produit');
    } finally {
      setLoading(false);
    }
  };

  const handleQuantityChange = (value) => {
    if (value < 1) return;
    if (product && product.stock && value > product.stock) return;
    setQuantity(value);
  };

  const handleGrillingOptionChange = (optionId, value) => {
    setSelectedGrillingOptions(prev => ({
      ...prev,
      [optionId]: value
    }));
  };

  const handleAddToCart = () => {
    if (!product) return;

    dispatch({
      type: 'ADD_ITEM',
      payload: {
        id: product.id,
        name: product.name,
        price: product.sale_price || product.price,
        image: product.image_url,
        quantity: quantity,
        grillingOptions: selectedGrillingOptions
      }
    });

    alert(`${product.name} ajouté au panier (${quantity})`);
  };

  const formatPrice = (price) => {
    return new Intl.NumberFormat('fr-MA', {
      style: 'currency',
      currency: 'MAD'
    }).format(price);
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-12 flex justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-500"></div>
      </div>
    );
  }

  if (error || !product) {
    return (
      <div className="container mx-auto px-4 py-12">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {error || "Produit non trouvé"}
        </div>
      </div>
    );
  }

  // Vérifier si le produit est en promotion
  const isOnSale = product.sale_price && product.sale_price < product.price;
  
  // Calculer le pourcentage de réduction si en promotion
  const discountPercentage = isOnSale 
    ? Math.round(((product.price - product.sale_price) / product.price) * 100) 
    : 0;

  return (
    <div className="container mx-auto px-4 py-12">
      {/* Fil d'Ariane */}
      <div className="mb-6 text-sm">
        <Link to="/" className="text-gray-500 hover:text-green-600">Accueil</Link>
        <span className="mx-2 text-gray-400">/</span>
        <Link to="/products" className="text-gray-500 hover:text-green-600">Produits</Link>
        <span className="mx-2 text-gray-400">/</span>
        <span className="text-gray-700">{product.name}</span>
      </div>
      
      <div className="bg-white rounded-lg shadow-md overflow-hidden mb-10">
        <div className="md:flex">
          {/* Image du produit */}
          <div className="md:w-1/2 relative">
            <img
              src={product.image_url || 'https://via.placeholder.com/600x600?text=Pas+d\'image'}
              alt={product.name}
              className="w-full h-full object-cover"
            />
            
            {/* Badge de promotion */}
            {isOnSale && (
              <div className="absolute top-4 left-4 bg-red-500 text-white font-bold px-3 py-1 rounded">
                -{discountPercentage}%
              </div>
            )}
            
            {/* Badge bio si applicable */}
            {product.is_organic && (
              <div className="absolute top-4 right-4 bg-green-500 text-white font-bold px-3 py-1 rounded">
                BIO
              </div>
            )}
          </div>
          
          {/* Informations du produit */}
          <div className="md:w-1/2 p-8">
            <h1 className="text-3xl font-bold mb-2">{product.name}</h1>
            
            <div className="flex items-center mb-4">
              {/* Affichage du prix */}
              <div className="flex items-baseline">
                {isOnSale ? (
                  <>
                    <span className="text-2xl font-bold text-red-600 mr-2">
                      {formatPrice(product.sale_price)}
                    </span>
                    <span className="text-lg text-gray-500 line-through">
                      {formatPrice(product.price)}
                    </span>
                  </>
                ) : (
                  <span className="text-2xl font-bold">
                    {formatPrice(product.price)}
                  </span>
                )}
              </div>
              
              {/* Affichage du prix au kilo/litre si disponible */}
              {product.price_per_unit && (
                <span className="ml-4 text-sm text-gray-500">
                  ({product.price_per_unit.toFixed(2)} € / {product.unit})
                </span>
              )}
            </div>
            
            {/* Disponibilité */}
            <div className="mb-6">
              {product.stock > 0 ? (
                <span className="text-green-600 font-medium">
                  En stock ({product.stock} disponibles)
                </span>
              ) : (
                <span className="text-red-600 font-medium">
                  Rupture de stock
                </span>
              )}
            </div>
            
            {/* Description */}
            <div className="mb-6">
              <h2 className="text-lg font-semibold mb-2">Description</h2>
              <p className="text-gray-700">{product.description}</p>
            </div>
            
            {/* Caractéristiques */}
            <div className="mb-6">
              <h2 className="text-lg font-semibold mb-2">Caractéristiques</h2>
              <ul className="text-gray-700 space-y-1">
                {product.weight && (
                  <li>Poids/Volume: {product.weight} {product.unit}</li>
                )}
                {product.origin && (
                  <li>Origine: {product.origin}</li>
                )}
                {product.is_organic && (
                  <li className="flex items-center">
                    <svg className="w-5 h-5 text-green-500 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    Produit biologique
                  </li>
                )}
              </ul>
            </div>
            
            {/* Options de cuisson pour les viandes */}
            {product.grilling_options && product.grilling_options.length > 0 && (
              <div className="mb-6">
                <h2 className="text-lg font-semibold mb-2">Options de cuisson</h2>
                <div className="space-y-3">
                  {product.grilling_options.map(option => (
                    <div key={option.id} className="flex items-center">
                      <input
                        type="radio"
                        id={`option-${option.id}`}
                        name="grilling-option"
                        className="mr-2"
                        onChange={() => handleGrillingOptionChange(option.id, true)}
                        checked={selectedGrillingOptions[option.id] === true}
                      />
                      <label htmlFor={`option-${option.id}`} className="flex-1">
                        <span className="font-medium">{option.name}</span>
                        {option.additional_cost > 0 && (
                          <span className="ml-2 text-sm text-gray-500">
                            (+{option.additional_cost.toFixed(2)} €)
                          </span>
                        )}
                        <p className="text-sm text-gray-600">{option.description}</p>
                      </label>
                    </div>
                  ))}
                </div>
              </div>
            )}
            
            {/* Quantité et ajout au panier */}
            <div className="flex items-center space-x-4">
              <div className="flex items-center border rounded-md">
                <button
                  onClick={() => handleQuantityChange(quantity - 1)}
                  className="px-3 py-1 text-gray-600 hover:bg-gray-100"
                  disabled={quantity <= 1}
                >
                  -
                </button>
                <span className="px-4 py-1">{quantity}</span>
                <button
                  onClick={() => handleQuantityChange(quantity + 1)}
                  className="px-3 py-1 text-gray-600 hover:bg-gray-100"
                  disabled={product.stock && quantity >= product.stock}
                >
                  +
                </button>
              </div>
              
              <button
                onClick={handleAddToCart}
                disabled={product.stock <= 0}
                className={`flex-1 py-2 px-4 rounded-md ${
                  product.stock > 0
                    ? 'bg-green-600 text-white hover:bg-green-700'
                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                } transition`}
              >
                {product.stock > 0 ? 'Ajouter au panier' : 'Indisponible'}
              </button>
            </div>
          </div>
        </div>
      </div>
      
      {/* Produits similaires */}
      <div className="mt-12">
        <h2 className="text-2xl font-bold mb-6">Produits similaires</h2>
        <RelatedProducts categoryId={product.category_id} currentProductId={product.id} />
      </div>
    </div>
  );
};

export default ProductDetailPage;
