{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Projet-Synthese\\\\frontend\\\\src\\\\pages\\\\Admin\\\\CategoriesCRUD.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useData } from '../../context/DataContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CategoriesCRUD = () => {\n  _s();\n  const {\n    state,\n    dispatch,\n    DataActions\n  } = useData();\n  const {\n    categories,\n    loading\n  } = state;\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const [currentCategory, setCurrentCategory] = useState(null);\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    color: 'bg-blue-100',\n    textColor: 'text-blue-800',\n    status: 'active'\n  });\n\n  // Couleurs disponibles pour les catégories\n  const colorOptions = [{\n    bg: 'bg-red-100',\n    text: 'text-red-800',\n    label: 'Rouge'\n  }, {\n    bg: 'bg-green-100',\n    text: 'text-green-800',\n    label: 'Vert'\n  }, {\n    bg: 'bg-blue-100',\n    text: 'text-blue-800',\n    label: 'Bleu'\n  }, {\n    bg: 'bg-yellow-100',\n    text: 'text-yellow-800',\n    label: 'Jaune'\n  }, {\n    bg: 'bg-purple-100',\n    text: 'text-purple-800',\n    label: 'Violet'\n  }, {\n    bg: 'bg-gray-100',\n    text: 'text-gray-800',\n    label: 'Gris'\n  }, {\n    bg: 'bg-orange-100',\n    text: 'text-orange-800',\n    label: 'Orange'\n  }, {\n    bg: 'bg-pink-100',\n    text: 'text-pink-800',\n    label: 'Rose'\n  }];\n\n  // Gestion des changements dans le formulaire\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  // Gestion du changement de couleur\n  const handleColorChange = colorOption => {\n    setFormData(prev => ({\n      ...prev,\n      color: colorOption.bg,\n      textColor: colorOption.text\n    }));\n  };\n\n  // Ouvrir le modal pour ajouter/modifier une catégorie\n  const openModal = (category = null) => {\n    if (category) {\n      setCurrentCategory(category);\n      setFormData({\n        name: category.name,\n        description: category.description,\n        color: category.color,\n        textColor: category.textColor,\n        status: category.status\n      });\n    } else {\n      setCurrentCategory(null);\n      setFormData({\n        name: '',\n        description: '',\n        color: 'bg-blue-100',\n        textColor: 'text-blue-800',\n        status: 'active'\n      });\n    }\n    setIsModalOpen(true);\n  };\n\n  // Fermer le modal\n  const closeModal = () => {\n    setIsModalOpen(false);\n    setCurrentCategory(null);\n    setFormData({\n      name: '',\n      description: '',\n      color: 'bg-blue-100',\n      textColor: 'text-blue-800',\n      status: 'active'\n    });\n  };\n\n  // Soumettre le formulaire\n  const handleSubmit = async e => {\n    e.preventDefault();\n    dispatch({\n      type: DataActions.SET_LOADING,\n      payload: true\n    });\n    try {\n      if (currentCategory) {\n        // Mise à jour d'une catégorie existante\n        const result = await categoryService.update(currentCategory.id, formData);\n        if (result.success) {\n          dispatch({\n            type: DataActions.UPDATE_CATEGORY,\n            payload: {\n              ...formData,\n              id: currentCategory.id\n            }\n          });\n          toast.success('Catégorie mise à jour avec succès');\n        } else {\n          // Fallback: mise à jour locale\n          dispatch({\n            type: DataActions.UPDATE_CATEGORY,\n            payload: {\n              ...formData,\n              id: currentCategory.id\n            }\n          });\n          toast.success('Catégorie mise à jour (mode démonstration)');\n        }\n      } else {\n        // Création d'une nouvelle catégorie\n        const result = await categoryService.create(formData);\n        if (result.success) {\n          dispatch({\n            type: DataActions.ADD_CATEGORY,\n            payload: result.data\n          });\n          toast.success('Catégorie créée avec succès');\n        } else {\n          // Fallback: ajout local\n          const newCategory = {\n            ...formData,\n            id: Date.now()\n          };\n          dispatch({\n            type: DataActions.ADD_CATEGORY,\n            payload: newCategory\n          });\n          toast.success('Catégorie créée (mode démonstration)');\n        }\n      }\n      closeModal();\n    } catch (err) {\n      console.error('Erreur lors de l\\'enregistrement de la catégorie', err);\n      toast.error('Erreur lors de l\\'enregistrement de la catégorie');\n    } finally {\n      dispatch({\n        type: DataActions.SET_LOADING,\n        payload: false\n      });\n    }\n  };\n\n  // Supprimer une catégorie\n  const handleDelete = async id => {\n    if (window.confirm('Êtes-vous sûr de vouloir supprimer cette catégorie ?')) {\n      dispatch({\n        type: DataActions.SET_LOADING,\n        payload: true\n      });\n      try {\n        const result = await categoryService.delete(id);\n        if (result.success) {\n          dispatch({\n            type: DataActions.DELETE_CATEGORY,\n            payload: id\n          });\n          toast.success('Catégorie supprimée avec succès');\n        } else {\n          // Fallback: suppression locale\n          dispatch({\n            type: DataActions.DELETE_CATEGORY,\n            payload: id\n          });\n          toast.success('Catégorie supprimée (mode démonstration)');\n        }\n      } catch (err) {\n        console.error('Erreur lors de la suppression de la catégorie', err);\n        toast.error('Erreur lors de la suppression de la catégorie');\n      } finally {\n        dispatch({\n          type: DataActions.SET_LOADING,\n          payload: false\n        });\n      }\n    }\n  };\n\n  // Basculer le statut d'une catégorie\n  const toggleStatus = async (id, currentStatus) => {\n    const newStatus = currentStatus === 'active' ? 'inactive' : 'active';\n    dispatch({\n      type: DataActions.SET_LOADING,\n      payload: true\n    });\n    try {\n      const result = await categoryService.update(id, {\n        status: newStatus\n      });\n      if (result.success) {\n        dispatch({\n          type: DataActions.UPDATE_CATEGORY,\n          payload: {\n            id,\n            status: newStatus\n          }\n        });\n        toast.success(`Catégorie ${newStatus === 'active' ? 'activée' : 'désactivée'}`);\n      } else {\n        // Fallback: mise à jour locale\n        dispatch({\n          type: DataActions.UPDATE_CATEGORY,\n          payload: {\n            id,\n            status: newStatus\n          }\n        });\n        toast.success(`Catégorie ${newStatus === 'active' ? 'activée' : 'désactivée'} (mode démonstration)`);\n      }\n    } catch (err) {\n      console.error('Erreur lors du changement de statut', err);\n      toast.error('Erreur lors du changement de statut');\n    } finally {\n      dispatch({\n        type: DataActions.SET_LOADING,\n        payload: false\n      });\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-gray-900\",\n          children: \"Gestion des Cat\\xE9gories\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-500\",\n          children: [categories.length, \" cat\\xE9gorie(s) \\u2022 CRUD complet activ\\xE9\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => openModal(),\n        className: \"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700\",\n        children: \"Ajouter une cat\\xE9gorie\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 191,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n      children: categories.map(category => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: `p-4 ${category.color}`,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-start\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: `text-lg font-semibold ${category.textColor}`,\n              children: category.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `px-2 py-1 text-xs rounded-full ${category.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`,\n              children: category.status === 'active' ? 'Actif' : 'Inactif'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 text-sm mb-4\",\n            children: category.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-center\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => openModal(category),\n                className: \"text-indigo-600 hover:text-indigo-900 text-sm\",\n                children: \"Modifier\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => toggleStatus(category.id, category.status),\n                className: `text-sm ${category.status === 'active' ? 'text-orange-600 hover:text-orange-900' : 'text-green-600 hover:text-green-900'}`,\n                children: category.status === 'active' ? 'Désactiver' : 'Activer'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleDelete(category.id),\n                className: \"text-red-600 hover:text-red-900 text-sm\",\n                children: \"Supprimer\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 13\n        }, this)]\n      }, category.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 207,\n      columnNumber: 7\n    }, this), isModalOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-lg w-full max-w-md mx-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-6 py-4 border-b\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold\",\n            children: currentCategory ? 'Modifier la catégorie' : 'Ajouter une catégorie'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"name\",\n                className: \"block text-gray-700 mb-2\",\n                children: \"Nom\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"name\",\n                name: \"name\",\n                value: formData.name,\n                onChange: handleInputChange,\n                className: \"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"description\",\n                className: \"block text-gray-700 mb-2\",\n                children: \"Description\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 286,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                id: \"description\",\n                name: \"description\",\n                value: formData.description,\n                onChange: handleInputChange,\n                className: \"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\",\n                rows: \"3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-gray-700 mb-2\",\n                children: \"Couleur\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-4 gap-2\",\n                children: colorOptions.map((colorOption, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: () => handleColorChange(colorOption),\n                  className: `p-3 rounded-lg border-2 ${colorOption.bg} ${colorOption.text} ${formData.color === colorOption.bg ? 'border-gray-800' : 'border-gray-300'}`,\n                  children: colorOption.label\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 301,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"status\",\n                className: \"block text-gray-700 mb-2\",\n                children: \"Statut\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 316,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                id: \"status\",\n                name: \"status\",\n                value: formData.status,\n                onChange: handleInputChange,\n                className: \"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"active\",\n                  children: \"Actif\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 324,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"inactive\",\n                  children: \"Inactif\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 325,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"px-6 py-4 bg-gray-50 border-t flex justify-end\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: closeModal,\n              className: \"px-4 py-2 border rounded-lg text-gray-700 mr-2 hover:bg-gray-100\",\n              children: \"Annuler\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              disabled: loading,\n              className: \"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50\",\n              children: loading ? 'Enregistrement...' : currentCategory ? 'Mettre à jour' : 'Ajouter'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 263,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 189,\n    columnNumber: 5\n  }, this);\n};\n_s(CategoriesCRUD, \"NvRcI3GuowguJWLhcYgAI14UFM0=\", false, function () {\n  return [useData];\n});\n_c = CategoriesCRUD;\nexport default CategoriesCRUD;\nvar _c;\n$RefreshReg$(_c, \"CategoriesCRUD\");", "map": {"version": 3, "names": ["React", "useState", "useData", "jsxDEV", "_jsxDEV", "CategoriesCRUD", "_s", "state", "dispatch", "DataActions", "categories", "loading", "isModalOpen", "setIsModalOpen", "currentCategory", "setCurrentCategory", "formData", "setFormData", "name", "description", "color", "textColor", "status", "colorOptions", "bg", "text", "label", "handleInputChange", "e", "value", "target", "prev", "handleColorChange", "colorOption", "openModal", "category", "closeModal", "handleSubmit", "preventDefault", "type", "SET_LOADING", "payload", "result", "categoryService", "update", "id", "success", "UPDATE_CATEGORY", "toast", "create", "ADD_CATEGORY", "data", "newCategory", "Date", "now", "err", "console", "error", "handleDelete", "window", "confirm", "delete", "DELETE_CATEGORY", "toggleStatus", "currentStatus", "newStatus", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "onClick", "map", "onSubmit", "htmlFor", "onChange", "required", "rows", "index", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet-Synthese/frontend/src/pages/Admin/CategoriesCRUD.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useData } from '../../context/DataContext';\n\nconst CategoriesCRUD = () => {\n  const { state, dispatch, DataActions } = useData();\n  const { categories, loading } = state;\n  \n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const [currentCategory, setCurrentCategory] = useState(null);\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    color: 'bg-blue-100',\n    textColor: 'text-blue-800',\n    status: 'active'\n  });\n\n  // Couleurs disponibles pour les catégories\n  const colorOptions = [\n    { bg: 'bg-red-100', text: 'text-red-800', label: 'Rouge' },\n    { bg: 'bg-green-100', text: 'text-green-800', label: 'Vert' },\n    { bg: 'bg-blue-100', text: 'text-blue-800', label: 'Bleu' },\n    { bg: 'bg-yellow-100', text: 'text-yellow-800', label: 'Jaune' },\n    { bg: 'bg-purple-100', text: 'text-purple-800', label: 'Violet' },\n    { bg: 'bg-gray-100', text: 'text-gray-800', label: 'Gris' },\n    { bg: 'bg-orange-100', text: 'text-orange-800', label: 'Orange' },\n    { bg: 'bg-pink-100', text: 'text-pink-800', label: 'Rose' }\n  ];\n\n  // Gestion des changements dans le formulaire\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({ ...prev, [name]: value }));\n  };\n\n  // Gestion du changement de couleur\n  const handleColorChange = (colorOption) => {\n    setFormData(prev => ({\n      ...prev,\n      color: colorOption.bg,\n      textColor: colorOption.text\n    }));\n  };\n\n  // Ouvrir le modal pour ajouter/modifier une catégorie\n  const openModal = (category = null) => {\n    if (category) {\n      setCurrentCategory(category);\n      setFormData({\n        name: category.name,\n        description: category.description,\n        color: category.color,\n        textColor: category.textColor,\n        status: category.status\n      });\n    } else {\n      setCurrentCategory(null);\n      setFormData({\n        name: '',\n        description: '',\n        color: 'bg-blue-100',\n        textColor: 'text-blue-800',\n        status: 'active'\n      });\n    }\n    setIsModalOpen(true);\n  };\n\n  // Fermer le modal\n  const closeModal = () => {\n    setIsModalOpen(false);\n    setCurrentCategory(null);\n    setFormData({\n      name: '',\n      description: '',\n      color: 'bg-blue-100',\n      textColor: 'text-blue-800',\n      status: 'active'\n    });\n  };\n\n  // Soumettre le formulaire\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    dispatch({ type: DataActions.SET_LOADING, payload: true });\n\n    try {\n      if (currentCategory) {\n        // Mise à jour d'une catégorie existante\n        const result = await categoryService.update(currentCategory.id, formData);\n        \n        if (result.success) {\n          dispatch({ \n            type: DataActions.UPDATE_CATEGORY, \n            payload: { ...formData, id: currentCategory.id }\n          });\n          toast.success('Catégorie mise à jour avec succès');\n        } else {\n          // Fallback: mise à jour locale\n          dispatch({ \n            type: DataActions.UPDATE_CATEGORY, \n            payload: { ...formData, id: currentCategory.id }\n          });\n          toast.success('Catégorie mise à jour (mode démonstration)');\n        }\n      } else {\n        // Création d'une nouvelle catégorie\n        const result = await categoryService.create(formData);\n        \n        if (result.success) {\n          dispatch({ type: DataActions.ADD_CATEGORY, payload: result.data });\n          toast.success('Catégorie créée avec succès');\n        } else {\n          // Fallback: ajout local\n          const newCategory = {\n            ...formData,\n            id: Date.now()\n          };\n          dispatch({ type: DataActions.ADD_CATEGORY, payload: newCategory });\n          toast.success('Catégorie créée (mode démonstration)');\n        }\n      }\n      \n      closeModal();\n    } catch (err) {\n      console.error('Erreur lors de l\\'enregistrement de la catégorie', err);\n      toast.error('Erreur lors de l\\'enregistrement de la catégorie');\n    } finally {\n      dispatch({ type: DataActions.SET_LOADING, payload: false });\n    }\n  };\n\n  // Supprimer une catégorie\n  const handleDelete = async (id) => {\n    if (window.confirm('Êtes-vous sûr de vouloir supprimer cette catégorie ?')) {\n      dispatch({ type: DataActions.SET_LOADING, payload: true });\n      \n      try {\n        const result = await categoryService.delete(id);\n        \n        if (result.success) {\n          dispatch({ type: DataActions.DELETE_CATEGORY, payload: id });\n          toast.success('Catégorie supprimée avec succès');\n        } else {\n          // Fallback: suppression locale\n          dispatch({ type: DataActions.DELETE_CATEGORY, payload: id });\n          toast.success('Catégorie supprimée (mode démonstration)');\n        }\n      } catch (err) {\n        console.error('Erreur lors de la suppression de la catégorie', err);\n        toast.error('Erreur lors de la suppression de la catégorie');\n      } finally {\n        dispatch({ type: DataActions.SET_LOADING, payload: false });\n      }\n    }\n  };\n\n  // Basculer le statut d'une catégorie\n  const toggleStatus = async (id, currentStatus) => {\n    const newStatus = currentStatus === 'active' ? 'inactive' : 'active';\n    dispatch({ type: DataActions.SET_LOADING, payload: true });\n    \n    try {\n      const result = await categoryService.update(id, { status: newStatus });\n      \n      if (result.success) {\n        dispatch({ \n          type: DataActions.UPDATE_CATEGORY, \n          payload: { id, status: newStatus }\n        });\n        toast.success(`Catégorie ${newStatus === 'active' ? 'activée' : 'désactivée'}`);\n      } else {\n        // Fallback: mise à jour locale\n        dispatch({ \n          type: DataActions.UPDATE_CATEGORY, \n          payload: { id, status: newStatus }\n        });\n        toast.success(`Catégorie ${newStatus === 'active' ? 'activée' : 'désactivée'} (mode démonstration)`);\n      }\n    } catch (err) {\n      console.error('Erreur lors du changement de statut', err);\n      toast.error('Erreur lors du changement de statut');\n    } finally {\n      dispatch({ type: DataActions.SET_LOADING, payload: false });\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* En-tête */}\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">Gestion des Catégories</h1>\n          <p className=\"text-sm text-gray-500\">\n            {categories.length} catégorie(s) • CRUD complet activé\n          </p>\n        </div>\n        <button\n          onClick={() => openModal()}\n          className=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700\"\n        >\n          Ajouter une catégorie\n        </button>\n      </div>\n\n      {/* Grille des catégories */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n        {categories.map((category) => (\n          <div key={category.id} className=\"bg-white rounded-lg shadow-sm border overflow-hidden\">\n            <div className={`p-4 ${category.color}`}>\n              <div className=\"flex justify-between items-start\">\n                <h3 className={`text-lg font-semibold ${category.textColor}`}>\n                  {category.name}\n                </h3>\n                <span className={`px-2 py-1 text-xs rounded-full ${\n                  category.status === 'active' \n                    ? 'bg-green-100 text-green-800' \n                    : 'bg-red-100 text-red-800'\n                }`}>\n                  {category.status === 'active' ? 'Actif' : 'Inactif'}\n                </span>\n              </div>\n            </div>\n            \n            <div className=\"p-4\">\n              <p className=\"text-gray-600 text-sm mb-4\">\n                {category.description}\n              </p>\n              \n              <div className=\"flex justify-between items-center\">\n                <div className=\"flex space-x-2\">\n                  <button\n                    onClick={() => openModal(category)}\n                    className=\"text-indigo-600 hover:text-indigo-900 text-sm\"\n                  >\n                    Modifier\n                  </button>\n                  <button\n                    onClick={() => toggleStatus(category.id, category.status)}\n                    className={`text-sm ${\n                      category.status === 'active' \n                        ? 'text-orange-600 hover:text-orange-900' \n                        : 'text-green-600 hover:text-green-900'\n                    }`}\n                  >\n                    {category.status === 'active' ? 'Désactiver' : 'Activer'}\n                  </button>\n                  <button\n                    onClick={() => handleDelete(category.id)}\n                    className=\"text-red-600 hover:text-red-900 text-sm\"\n                  >\n                    Supprimer\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* Modal pour ajouter/modifier une catégorie */}\n      {isModalOpen && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n          <div className=\"bg-white rounded-lg shadow-lg w-full max-w-md mx-4\">\n            <div className=\"px-6 py-4 border-b\">\n              <h3 className=\"text-lg font-semibold\">\n                {currentCategory ? 'Modifier la catégorie' : 'Ajouter une catégorie'}\n              </h3>\n            </div>\n            <form onSubmit={handleSubmit}>\n              <div className=\"p-6\">\n                <div className=\"mb-4\">\n                  <label htmlFor=\"name\" className=\"block text-gray-700 mb-2\">Nom</label>\n                  <input\n                    type=\"text\"\n                    id=\"name\"\n                    name=\"name\"\n                    value={formData.name}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n                    required\n                  />\n                </div>\n                \n                <div className=\"mb-4\">\n                  <label htmlFor=\"description\" className=\"block text-gray-700 mb-2\">Description</label>\n                  <textarea\n                    id=\"description\"\n                    name=\"description\"\n                    value={formData.description}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n                    rows=\"3\"\n                  ></textarea>\n                </div>\n                \n                <div className=\"mb-4\">\n                  <label className=\"block text-gray-700 mb-2\">Couleur</label>\n                  <div className=\"grid grid-cols-4 gap-2\">\n                    {colorOptions.map((colorOption, index) => (\n                      <button\n                        key={index}\n                        type=\"button\"\n                        onClick={() => handleColorChange(colorOption)}\n                        className={`p-3 rounded-lg border-2 ${colorOption.bg} ${colorOption.text} ${\n                          formData.color === colorOption.bg ? 'border-gray-800' : 'border-gray-300'\n                        }`}\n                      >\n                        {colorOption.label}\n                      </button>\n                    ))}\n                  </div>\n                </div>\n                \n                <div className=\"mb-4\">\n                  <label htmlFor=\"status\" className=\"block text-gray-700 mb-2\">Statut</label>\n                  <select\n                    id=\"status\"\n                    name=\"status\"\n                    value={formData.status}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n                  >\n                    <option value=\"active\">Actif</option>\n                    <option value=\"inactive\">Inactif</option>\n                  </select>\n                </div>\n              </div>\n              \n              <div className=\"px-6 py-4 bg-gray-50 border-t flex justify-end\">\n                <button\n                  type=\"button\"\n                  onClick={closeModal}\n                  className=\"px-4 py-2 border rounded-lg text-gray-700 mr-2 hover:bg-gray-100\"\n                >\n                  Annuler\n                </button>\n                <button\n                  type=\"submit\"\n                  disabled={loading}\n                  className=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50\"\n                >\n                  {loading ? 'Enregistrement...' : (currentCategory ? 'Mettre à jour' : 'Ajouter')}\n                </button>\n              </div>\n            </form>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default CategoriesCRUD;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,OAAO,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM;IAAEC,KAAK;IAAEC,QAAQ;IAAEC;EAAY,CAAC,GAAGP,OAAO,CAAC,CAAC;EAClD,MAAM;IAAEQ,UAAU;IAAEC;EAAQ,CAAC,GAAGJ,KAAK;EAErC,MAAM,CAACK,WAAW,EAAEC,cAAc,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACa,eAAe,EAAEC,kBAAkB,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACe,QAAQ,EAAEC,WAAW,CAAC,GAAGhB,QAAQ,CAAC;IACvCiB,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,KAAK,EAAE,aAAa;IACpBC,SAAS,EAAE,eAAe;IAC1BC,MAAM,EAAE;EACV,CAAC,CAAC;;EAEF;EACA,MAAMC,YAAY,GAAG,CACnB;IAAEC,EAAE,EAAE,YAAY;IAAEC,IAAI,EAAE,cAAc;IAAEC,KAAK,EAAE;EAAQ,CAAC,EAC1D;IAAEF,EAAE,EAAE,cAAc;IAAEC,IAAI,EAAE,gBAAgB;IAAEC,KAAK,EAAE;EAAO,CAAC,EAC7D;IAAEF,EAAE,EAAE,aAAa;IAAEC,IAAI,EAAE,eAAe;IAAEC,KAAK,EAAE;EAAO,CAAC,EAC3D;IAAEF,EAAE,EAAE,eAAe;IAAEC,IAAI,EAAE,iBAAiB;IAAEC,KAAK,EAAE;EAAQ,CAAC,EAChE;IAAEF,EAAE,EAAE,eAAe;IAAEC,IAAI,EAAE,iBAAiB;IAAEC,KAAK,EAAE;EAAS,CAAC,EACjE;IAAEF,EAAE,EAAE,aAAa;IAAEC,IAAI,EAAE,eAAe;IAAEC,KAAK,EAAE;EAAO,CAAC,EAC3D;IAAEF,EAAE,EAAE,eAAe;IAAEC,IAAI,EAAE,iBAAiB;IAAEC,KAAK,EAAE;EAAS,CAAC,EACjE;IAAEF,EAAE,EAAE,aAAa;IAAEC,IAAI,EAAE,eAAe;IAAEC,KAAK,EAAE;EAAO,CAAC,CAC5D;;EAED;EACA,MAAMC,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEV,IAAI;MAAEW;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChCb,WAAW,CAACc,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACb,IAAI,GAAGW;IAAM,CAAC,CAAC,CAAC;EACnD,CAAC;;EAED;EACA,MAAMG,iBAAiB,GAAIC,WAAW,IAAK;IACzChB,WAAW,CAACc,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPX,KAAK,EAAEa,WAAW,CAACT,EAAE;MACrBH,SAAS,EAAEY,WAAW,CAACR;IACzB,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMS,SAAS,GAAGA,CAACC,QAAQ,GAAG,IAAI,KAAK;IACrC,IAAIA,QAAQ,EAAE;MACZpB,kBAAkB,CAACoB,QAAQ,CAAC;MAC5BlB,WAAW,CAAC;QACVC,IAAI,EAAEiB,QAAQ,CAACjB,IAAI;QACnBC,WAAW,EAAEgB,QAAQ,CAAChB,WAAW;QACjCC,KAAK,EAAEe,QAAQ,CAACf,KAAK;QACrBC,SAAS,EAAEc,QAAQ,CAACd,SAAS;QAC7BC,MAAM,EAAEa,QAAQ,CAACb;MACnB,CAAC,CAAC;IACJ,CAAC,MAAM;MACLP,kBAAkB,CAAC,IAAI,CAAC;MACxBE,WAAW,CAAC;QACVC,IAAI,EAAE,EAAE;QACRC,WAAW,EAAE,EAAE;QACfC,KAAK,EAAE,aAAa;QACpBC,SAAS,EAAE,eAAe;QAC1BC,MAAM,EAAE;MACV,CAAC,CAAC;IACJ;IACAT,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;;EAED;EACA,MAAMuB,UAAU,GAAGA,CAAA,KAAM;IACvBvB,cAAc,CAAC,KAAK,CAAC;IACrBE,kBAAkB,CAAC,IAAI,CAAC;IACxBE,WAAW,CAAC;MACVC,IAAI,EAAE,EAAE;MACRC,WAAW,EAAE,EAAE;MACfC,KAAK,EAAE,aAAa;MACpBC,SAAS,EAAE,eAAe;MAC1BC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMe,YAAY,GAAG,MAAOT,CAAC,IAAK;IAChCA,CAAC,CAACU,cAAc,CAAC,CAAC;IAClB9B,QAAQ,CAAC;MAAE+B,IAAI,EAAE9B,WAAW,CAAC+B,WAAW;MAAEC,OAAO,EAAE;IAAK,CAAC,CAAC;IAE1D,IAAI;MACF,IAAI3B,eAAe,EAAE;QACnB;QACA,MAAM4B,MAAM,GAAG,MAAMC,eAAe,CAACC,MAAM,CAAC9B,eAAe,CAAC+B,EAAE,EAAE7B,QAAQ,CAAC;QAEzE,IAAI0B,MAAM,CAACI,OAAO,EAAE;UAClBtC,QAAQ,CAAC;YACP+B,IAAI,EAAE9B,WAAW,CAACsC,eAAe;YACjCN,OAAO,EAAE;cAAE,GAAGzB,QAAQ;cAAE6B,EAAE,EAAE/B,eAAe,CAAC+B;YAAG;UACjD,CAAC,CAAC;UACFG,KAAK,CAACF,OAAO,CAAC,mCAAmC,CAAC;QACpD,CAAC,MAAM;UACL;UACAtC,QAAQ,CAAC;YACP+B,IAAI,EAAE9B,WAAW,CAACsC,eAAe;YACjCN,OAAO,EAAE;cAAE,GAAGzB,QAAQ;cAAE6B,EAAE,EAAE/B,eAAe,CAAC+B;YAAG;UACjD,CAAC,CAAC;UACFG,KAAK,CAACF,OAAO,CAAC,4CAA4C,CAAC;QAC7D;MACF,CAAC,MAAM;QACL;QACA,MAAMJ,MAAM,GAAG,MAAMC,eAAe,CAACM,MAAM,CAACjC,QAAQ,CAAC;QAErD,IAAI0B,MAAM,CAACI,OAAO,EAAE;UAClBtC,QAAQ,CAAC;YAAE+B,IAAI,EAAE9B,WAAW,CAACyC,YAAY;YAAET,OAAO,EAAEC,MAAM,CAACS;UAAK,CAAC,CAAC;UAClEH,KAAK,CAACF,OAAO,CAAC,6BAA6B,CAAC;QAC9C,CAAC,MAAM;UACL;UACA,MAAMM,WAAW,GAAG;YAClB,GAAGpC,QAAQ;YACX6B,EAAE,EAAEQ,IAAI,CAACC,GAAG,CAAC;UACf,CAAC;UACD9C,QAAQ,CAAC;YAAE+B,IAAI,EAAE9B,WAAW,CAACyC,YAAY;YAAET,OAAO,EAAEW;UAAY,CAAC,CAAC;UAClEJ,KAAK,CAACF,OAAO,CAAC,sCAAsC,CAAC;QACvD;MACF;MAEAV,UAAU,CAAC,CAAC;IACd,CAAC,CAAC,OAAOmB,GAAG,EAAE;MACZC,OAAO,CAACC,KAAK,CAAC,kDAAkD,EAAEF,GAAG,CAAC;MACtEP,KAAK,CAACS,KAAK,CAAC,kDAAkD,CAAC;IACjE,CAAC,SAAS;MACRjD,QAAQ,CAAC;QAAE+B,IAAI,EAAE9B,WAAW,CAAC+B,WAAW;QAAEC,OAAO,EAAE;MAAM,CAAC,CAAC;IAC7D;EACF,CAAC;;EAED;EACA,MAAMiB,YAAY,GAAG,MAAOb,EAAE,IAAK;IACjC,IAAIc,MAAM,CAACC,OAAO,CAAC,sDAAsD,CAAC,EAAE;MAC1EpD,QAAQ,CAAC;QAAE+B,IAAI,EAAE9B,WAAW,CAAC+B,WAAW;QAAEC,OAAO,EAAE;MAAK,CAAC,CAAC;MAE1D,IAAI;QACF,MAAMC,MAAM,GAAG,MAAMC,eAAe,CAACkB,MAAM,CAAChB,EAAE,CAAC;QAE/C,IAAIH,MAAM,CAACI,OAAO,EAAE;UAClBtC,QAAQ,CAAC;YAAE+B,IAAI,EAAE9B,WAAW,CAACqD,eAAe;YAAErB,OAAO,EAAEI;UAAG,CAAC,CAAC;UAC5DG,KAAK,CAACF,OAAO,CAAC,iCAAiC,CAAC;QAClD,CAAC,MAAM;UACL;UACAtC,QAAQ,CAAC;YAAE+B,IAAI,EAAE9B,WAAW,CAACqD,eAAe;YAAErB,OAAO,EAAEI;UAAG,CAAC,CAAC;UAC5DG,KAAK,CAACF,OAAO,CAAC,0CAA0C,CAAC;QAC3D;MACF,CAAC,CAAC,OAAOS,GAAG,EAAE;QACZC,OAAO,CAACC,KAAK,CAAC,+CAA+C,EAAEF,GAAG,CAAC;QACnEP,KAAK,CAACS,KAAK,CAAC,+CAA+C,CAAC;MAC9D,CAAC,SAAS;QACRjD,QAAQ,CAAC;UAAE+B,IAAI,EAAE9B,WAAW,CAAC+B,WAAW;UAAEC,OAAO,EAAE;QAAM,CAAC,CAAC;MAC7D;IACF;EACF,CAAC;;EAED;EACA,MAAMsB,YAAY,GAAG,MAAAA,CAAOlB,EAAE,EAAEmB,aAAa,KAAK;IAChD,MAAMC,SAAS,GAAGD,aAAa,KAAK,QAAQ,GAAG,UAAU,GAAG,QAAQ;IACpExD,QAAQ,CAAC;MAAE+B,IAAI,EAAE9B,WAAW,CAAC+B,WAAW;MAAEC,OAAO,EAAE;IAAK,CAAC,CAAC;IAE1D,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMC,eAAe,CAACC,MAAM,CAACC,EAAE,EAAE;QAAEvB,MAAM,EAAE2C;MAAU,CAAC,CAAC;MAEtE,IAAIvB,MAAM,CAACI,OAAO,EAAE;QAClBtC,QAAQ,CAAC;UACP+B,IAAI,EAAE9B,WAAW,CAACsC,eAAe;UACjCN,OAAO,EAAE;YAAEI,EAAE;YAAEvB,MAAM,EAAE2C;UAAU;QACnC,CAAC,CAAC;QACFjB,KAAK,CAACF,OAAO,CAAC,aAAamB,SAAS,KAAK,QAAQ,GAAG,SAAS,GAAG,YAAY,EAAE,CAAC;MACjF,CAAC,MAAM;QACL;QACAzD,QAAQ,CAAC;UACP+B,IAAI,EAAE9B,WAAW,CAACsC,eAAe;UACjCN,OAAO,EAAE;YAAEI,EAAE;YAAEvB,MAAM,EAAE2C;UAAU;QACnC,CAAC,CAAC;QACFjB,KAAK,CAACF,OAAO,CAAC,aAAamB,SAAS,KAAK,QAAQ,GAAG,SAAS,GAAG,YAAY,uBAAuB,CAAC;MACtG;IACF,CAAC,CAAC,OAAOV,GAAG,EAAE;MACZC,OAAO,CAACC,KAAK,CAAC,qCAAqC,EAAEF,GAAG,CAAC;MACzDP,KAAK,CAACS,KAAK,CAAC,qCAAqC,CAAC;IACpD,CAAC,SAAS;MACRjD,QAAQ,CAAC;QAAE+B,IAAI,EAAE9B,WAAW,CAAC+B,WAAW;QAAEC,OAAO,EAAE;MAAM,CAAC,CAAC;IAC7D;EACF,CAAC;EAED,oBACErC,OAAA;IAAK8D,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExB/D,OAAA;MAAK8D,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChD/D,OAAA;QAAA+D,QAAA,gBACE/D,OAAA;UAAI8D,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5EnE,OAAA;UAAG8D,SAAS,EAAC,uBAAuB;UAAAC,QAAA,GACjCzD,UAAU,CAAC8D,MAAM,EAAC,gDACrB;QAAA;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACNnE,OAAA;QACEqE,OAAO,EAAEA,CAAA,KAAMvC,SAAS,CAAC,CAAE;QAC3BgC,SAAS,EAAC,iEAAiE;QAAAC,QAAA,EAC5E;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNnE,OAAA;MAAK8D,SAAS,EAAC,sDAAsD;MAAAC,QAAA,EAClEzD,UAAU,CAACgE,GAAG,CAAEvC,QAAQ,iBACvB/B,OAAA;QAAuB8D,SAAS,EAAC,sDAAsD;QAAAC,QAAA,gBACrF/D,OAAA;UAAK8D,SAAS,EAAE,OAAO/B,QAAQ,CAACf,KAAK,EAAG;UAAA+C,QAAA,eACtC/D,OAAA;YAAK8D,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC/C/D,OAAA;cAAI8D,SAAS,EAAE,yBAAyB/B,QAAQ,CAACd,SAAS,EAAG;cAAA8C,QAAA,EAC1DhC,QAAQ,CAACjB;YAAI;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eACLnE,OAAA;cAAM8D,SAAS,EAAE,kCACf/B,QAAQ,CAACb,MAAM,KAAK,QAAQ,GACxB,6BAA6B,GAC7B,yBAAyB,EAC5B;cAAA6C,QAAA,EACAhC,QAAQ,CAACb,MAAM,KAAK,QAAQ,GAAG,OAAO,GAAG;YAAS;cAAA8C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnE,OAAA;UAAK8D,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClB/D,OAAA;YAAG8D,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EACtChC,QAAQ,CAAChB;UAAW;YAAAiD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,eAEJnE,OAAA;YAAK8D,SAAS,EAAC,mCAAmC;YAAAC,QAAA,eAChD/D,OAAA;cAAK8D,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7B/D,OAAA;gBACEqE,OAAO,EAAEA,CAAA,KAAMvC,SAAS,CAACC,QAAQ,CAAE;gBACnC+B,SAAS,EAAC,+CAA+C;gBAAAC,QAAA,EAC1D;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTnE,OAAA;gBACEqE,OAAO,EAAEA,CAAA,KAAMV,YAAY,CAAC5B,QAAQ,CAACU,EAAE,EAAEV,QAAQ,CAACb,MAAM,CAAE;gBAC1D4C,SAAS,EAAE,WACT/B,QAAQ,CAACb,MAAM,KAAK,QAAQ,GACxB,uCAAuC,GACvC,qCAAqC,EACxC;gBAAA6C,QAAA,EAEFhC,QAAQ,CAACb,MAAM,KAAK,QAAQ,GAAG,YAAY,GAAG;cAAS;gBAAA8C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC,eACTnE,OAAA;gBACEqE,OAAO,EAAEA,CAAA,KAAMf,YAAY,CAACvB,QAAQ,CAACU,EAAE,CAAE;gBACzCqB,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,EACpD;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA,GA/CEpC,QAAQ,CAACU,EAAE;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAgDhB,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAGL3D,WAAW,iBACVR,OAAA;MAAK8D,SAAS,EAAC,4EAA4E;MAAAC,QAAA,eACzF/D,OAAA;QAAK8D,SAAS,EAAC,oDAAoD;QAAAC,QAAA,gBACjE/D,OAAA;UAAK8D,SAAS,EAAC,oBAAoB;UAAAC,QAAA,eACjC/D,OAAA;YAAI8D,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAClCrD,eAAe,GAAG,uBAAuB,GAAG;UAAuB;YAAAsD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACNnE,OAAA;UAAMuE,QAAQ,EAAEtC,YAAa;UAAA8B,QAAA,gBAC3B/D,OAAA;YAAK8D,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAClB/D,OAAA;cAAK8D,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB/D,OAAA;gBAAOwE,OAAO,EAAC,MAAM;gBAACV,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtEnE,OAAA;gBACEmC,IAAI,EAAC,MAAM;gBACXM,EAAE,EAAC,MAAM;gBACT3B,IAAI,EAAC,MAAM;gBACXW,KAAK,EAAEb,QAAQ,CAACE,IAAK;gBACrB2D,QAAQ,EAAElD,iBAAkB;gBAC5BuC,SAAS,EAAC,yFAAyF;gBACnGY,QAAQ;cAAA;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENnE,OAAA;cAAK8D,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB/D,OAAA;gBAAOwE,OAAO,EAAC,aAAa;gBAACV,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrFnE,OAAA;gBACEyC,EAAE,EAAC,aAAa;gBAChB3B,IAAI,EAAC,aAAa;gBAClBW,KAAK,EAAEb,QAAQ,CAACG,WAAY;gBAC5B0D,QAAQ,EAAElD,iBAAkB;gBAC5BuC,SAAS,EAAC,yFAAyF;gBACnGa,IAAI,EAAC;cAAG;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eAENnE,OAAA;cAAK8D,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB/D,OAAA;gBAAO8D,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3DnE,OAAA;gBAAK8D,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EACpC5C,YAAY,CAACmD,GAAG,CAAC,CAACzC,WAAW,EAAE+C,KAAK,kBACnC5E,OAAA;kBAEEmC,IAAI,EAAC,QAAQ;kBACbkC,OAAO,EAAEA,CAAA,KAAMzC,iBAAiB,CAACC,WAAW,CAAE;kBAC9CiC,SAAS,EAAE,2BAA2BjC,WAAW,CAACT,EAAE,IAAIS,WAAW,CAACR,IAAI,IACtET,QAAQ,CAACI,KAAK,KAAKa,WAAW,CAACT,EAAE,GAAG,iBAAiB,GAAG,iBAAiB,EACxE;kBAAA2C,QAAA,EAEFlC,WAAW,CAACP;gBAAK,GAPbsD,KAAK;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAQJ,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENnE,OAAA;cAAK8D,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB/D,OAAA;gBAAOwE,OAAO,EAAC,QAAQ;gBAACV,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3EnE,OAAA;gBACEyC,EAAE,EAAC,QAAQ;gBACX3B,IAAI,EAAC,QAAQ;gBACbW,KAAK,EAAEb,QAAQ,CAACM,MAAO;gBACvBuD,QAAQ,EAAElD,iBAAkB;gBAC5BuC,SAAS,EAAC,yFAAyF;gBAAAC,QAAA,gBAEnG/D,OAAA;kBAAQyB,KAAK,EAAC,QAAQ;kBAAAsC,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACrCnE,OAAA;kBAAQyB,KAAK,EAAC,UAAU;kBAAAsC,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENnE,OAAA;YAAK8D,SAAS,EAAC,gDAAgD;YAAAC,QAAA,gBAC7D/D,OAAA;cACEmC,IAAI,EAAC,QAAQ;cACbkC,OAAO,EAAErC,UAAW;cACpB8B,SAAS,EAAC,kEAAkE;cAAAC,QAAA,EAC7E;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTnE,OAAA;cACEmC,IAAI,EAAC,QAAQ;cACb0C,QAAQ,EAAEtE,OAAQ;cAClBuD,SAAS,EAAC,qFAAqF;cAAAC,QAAA,EAE9FxD,OAAO,GAAG,mBAAmB,GAAIG,eAAe,GAAG,eAAe,GAAG;YAAU;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACjE,EAAA,CA5VID,cAAc;EAAA,QACuBH,OAAO;AAAA;AAAAgF,EAAA,GAD5C7E,cAAc;AA8VpB,eAAeA,cAAc;AAAC,IAAA6E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}