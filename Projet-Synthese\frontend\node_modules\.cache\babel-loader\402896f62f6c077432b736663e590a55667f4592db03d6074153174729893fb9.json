{"ast": null, "code": "var _s = $RefreshSig$();\nimport React from 'react';\nimport { Navigate } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nconst ProtectedRoute = ({\n  children\n}) => {\n  _s();\n  const {\n    state\n  } = useAuth();\n\n  // Pour la démonstration, on permet l'accès sans authentification\n  // En production, décommentez les lignes ci-dessous\n  /*\n  if (!state.isAuthenticated) {\n    return <Navigate to=\"/login\" replace />;\n  }\n   if (!state.user?.role || state.user.role !== 'admin') {\n    return <Navigate to=\"/\" replace />;\n  }\n  */\n\n  // Mode démonstration - accès libre\n  return children;\n};\n_s(ProtectedRoute, \"SneUOyY5BSg4fkBm8CBpAOJjVdg=\", false, function () {\n  return [useAuth];\n});\n_c = ProtectedRoute;\nexport default ProtectedRoute;\nvar _c;\n$RefreshReg$(_c, \"ProtectedRoute\");", "map": {"version": 3, "names": ["React", "Navigate", "useAuth", "ProtectedRoute", "children", "_s", "state", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet-Synthese/frontend/src/components/auth/ProtectedRoute.js"], "sourcesContent": ["import React from 'react';\nimport { Navigate } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\n\nconst ProtectedRoute = ({ children }) => {\n  const { state } = useAuth();\n\n  // Pour la démonstration, on permet l'accès sans authentification\n  // En production, décommentez les lignes ci-dessous\n  /*\n  if (!state.isAuthenticated) {\n    return <Navigate to=\"/login\" replace />;\n  }\n\n  if (!state.user?.role || state.user.role !== 'admin') {\n    return <Navigate to=\"/\" replace />;\n  }\n  */\n\n  // Mode démonstration - accès libre\n  return children;\n};\n\nexport default ProtectedRoute;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,SAASC,OAAO,QAAQ,2BAA2B;AAEnD,MAAMC,cAAc,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACvC,MAAM;IAAEC;EAAM,CAAC,GAAGJ,OAAO,CAAC,CAAC;;EAE3B;EACA;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;;EAGE;EACA,OAAOE,QAAQ;AACjB,CAAC;AAACC,EAAA,CAjBIF,cAAc;EAAA,QACAD,OAAO;AAAA;AAAAK,EAAA,GADrBJ,cAAc;AAmBpB,eAAeA,cAAc;AAAC,IAAAI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}