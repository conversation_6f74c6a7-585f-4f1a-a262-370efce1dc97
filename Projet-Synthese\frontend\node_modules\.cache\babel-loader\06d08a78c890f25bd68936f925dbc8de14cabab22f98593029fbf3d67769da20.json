{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Projet-Synthese\\\\frontend\\\\src\\\\pages\\\\Admin\\\\CategoriesCRUD.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useData } from '../../context/DataContext';\nimport { categoryAPI } from '../../services/apiService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CategoriesCRUD = () => {\n  _s();\n  const {\n    state,\n    dispatch,\n    DataActions\n  } = useData();\n  const {\n    categories,\n    loading\n  } = state;\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const [currentCategory, setCurrentCategory] = useState(null);\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    color: 'bg-blue-100',\n    textColor: 'text-blue-800',\n    status: 'active'\n  });\n\n  // Couleurs disponibles pour les catégories\n  const colorOptions = [{\n    bg: 'bg-red-100',\n    text: 'text-red-800',\n    label: 'Rouge'\n  }, {\n    bg: 'bg-green-100',\n    text: 'text-green-800',\n    label: 'Vert'\n  }, {\n    bg: 'bg-blue-100',\n    text: 'text-blue-800',\n    label: 'Bleu'\n  }, {\n    bg: 'bg-yellow-100',\n    text: 'text-yellow-800',\n    label: 'Jaune'\n  }, {\n    bg: 'bg-purple-100',\n    text: 'text-purple-800',\n    label: 'Violet'\n  }, {\n    bg: 'bg-gray-100',\n    text: 'text-gray-800',\n    label: 'Gris'\n  }, {\n    bg: 'bg-orange-100',\n    text: 'text-orange-800',\n    label: 'Orange'\n  }, {\n    bg: 'bg-pink-100',\n    text: 'text-pink-800',\n    label: 'Rose'\n  }];\n\n  // Gestion des changements dans le formulaire\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  // Gestion du changement de couleur\n  const handleColorChange = colorOption => {\n    setFormData(prev => ({\n      ...prev,\n      color: colorOption.bg,\n      textColor: colorOption.text\n    }));\n  };\n\n  // Ouvrir le modal pour ajouter/modifier une catégorie\n  const openModal = (category = null) => {\n    if (category) {\n      setCurrentCategory(category);\n      setFormData({\n        name: category.name,\n        description: category.description,\n        color: category.color,\n        textColor: category.textColor,\n        status: category.status\n      });\n    } else {\n      setCurrentCategory(null);\n      setFormData({\n        name: '',\n        description: '',\n        color: 'bg-blue-100',\n        textColor: 'text-blue-800',\n        status: 'active'\n      });\n    }\n    setIsModalOpen(true);\n  };\n\n  // Fermer le modal\n  const closeModal = () => {\n    setIsModalOpen(false);\n    setCurrentCategory(null);\n    setFormData({\n      name: '',\n      description: '',\n      color: 'bg-blue-100',\n      textColor: 'text-blue-800',\n      status: 'active'\n    });\n  };\n\n  // Soumettre le formulaire\n  const handleSubmit = async e => {\n    e.preventDefault();\n    dispatch({\n      type: DataActions.SET_LOADING,\n      payload: true\n    });\n    try {\n      const categoryData = {\n        ...formData,\n        slug: formData.name.toLowerCase().replace(/\\s+/g, '-')\n      };\n      if (currentCategory) {\n        // Mise à jour d'une catégorie existante\n        const result = await categoryAPI.update(currentCategory.id, categoryData);\n        if (result.success) {\n          dispatch({\n            type: DataActions.UPDATE_CATEGORY,\n            payload: {\n              ...result.data,\n              id: currentCategory.id\n            }\n          });\n          console.log('Catégorie mise à jour via API');\n        } else {\n          // Fallback: mise à jour locale\n          dispatch({\n            type: DataActions.UPDATE_CATEGORY,\n            payload: {\n              ...categoryData,\n              id: currentCategory.id\n            }\n          });\n          console.log('Catégorie mise à jour localement (mode démonstration)');\n        }\n      } else {\n        // Création d'une nouvelle catégorie\n        const result = await categoryAPI.create(categoryData);\n        if (result.success) {\n          dispatch({\n            type: DataActions.ADD_CATEGORY,\n            payload: result.data\n          });\n          console.log('Catégorie créée via API');\n        } else {\n          // Fallback: création locale\n          const newCategory = {\n            ...categoryData,\n            id: Date.now()\n          };\n          dispatch({\n            type: DataActions.ADD_CATEGORY,\n            payload: newCategory\n          });\n          console.log('Catégorie créée localement (mode démonstration)');\n        }\n      }\n      closeModal();\n    } catch (err) {\n      console.error('Erreur lors de l\\'enregistrement de la catégorie', err);\n      // Fallback en cas d'erreur\n      const categoryData = {\n        ...formData,\n        slug: formData.name.toLowerCase().replace(/\\s+/g, '-')\n      };\n      if (currentCategory) {\n        dispatch({\n          type: DataActions.UPDATE_CATEGORY,\n          payload: {\n            ...categoryData,\n            id: currentCategory.id\n          }\n        });\n      } else {\n        const newCategory = {\n          ...categoryData,\n          id: Date.now()\n        };\n        dispatch({\n          type: DataActions.ADD_CATEGORY,\n          payload: newCategory\n        });\n      }\n      closeModal();\n    } finally {\n      dispatch({\n        type: DataActions.SET_LOADING,\n        payload: false\n      });\n    }\n  };\n\n  // Supprimer une catégorie\n  const handleDelete = id => {\n    if (window.confirm('Êtes-vous sûr de vouloir supprimer cette catégorie ?')) {\n      dispatch({\n        type: DataActions.SET_LOADING,\n        payload: true\n      });\n      try {\n        dispatch({\n          type: DataActions.DELETE_CATEGORY,\n          payload: id\n        });\n        console.log('Catégorie supprimée avec succès');\n      } catch (err) {\n        console.error('Erreur lors de la suppression de la catégorie', err);\n      } finally {\n        dispatch({\n          type: DataActions.SET_LOADING,\n          payload: false\n        });\n      }\n    }\n  };\n\n  // Basculer le statut d'une catégorie\n  const toggleStatus = (id, currentStatus) => {\n    const newStatus = currentStatus === 'active' ? 'inactive' : 'active';\n    dispatch({\n      type: DataActions.SET_LOADING,\n      payload: true\n    });\n    try {\n      dispatch({\n        type: DataActions.UPDATE_CATEGORY,\n        payload: {\n          id,\n          status: newStatus\n        }\n      });\n      console.log(`Catégorie ${newStatus === 'active' ? 'activée' : 'désactivée'}`);\n    } catch (err) {\n      console.error('Erreur lors du changement de statut', err);\n    } finally {\n      dispatch({\n        type: DataActions.SET_LOADING,\n        payload: false\n      });\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-gray-900\",\n          children: \"Gestion des Cat\\xE9gories\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-500\",\n          children: [categories.length, \" cat\\xE9gorie(s) \\u2022 CRUD complet activ\\xE9\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => openModal(),\n        className: \"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700\",\n        children: \"Ajouter une cat\\xE9gorie\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 194,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n      children: categories.map(category => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: `p-4 ${category.color}`,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-start\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: `text-lg font-semibold ${category.textColor}`,\n              children: category.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `px-2 py-1 text-xs rounded-full ${category.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`,\n              children: category.status === 'active' ? 'Actif' : 'Inactif'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 text-sm mb-4\",\n            children: category.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-center\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => openModal(category),\n                className: \"text-indigo-600 hover:text-indigo-900 text-sm\",\n                children: \"Modifier\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => toggleStatus(category.id, category.status),\n                className: `text-sm ${category.status === 'active' ? 'text-orange-600 hover:text-orange-900' : 'text-green-600 hover:text-green-900'}`,\n                children: category.status === 'active' ? 'Désactiver' : 'Activer'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleDelete(category.id),\n                className: \"text-red-600 hover:text-red-900 text-sm\",\n                children: \"Supprimer\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 251,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 13\n        }, this)]\n      }, category.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 210,\n      columnNumber: 7\n    }, this), isModalOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-lg w-full max-w-md mx-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-6 py-4 border-b\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold\",\n            children: currentCategory ? 'Modifier la catégorie' : 'Ajouter une catégorie'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"name\",\n                className: \"block text-gray-700 mb-2\",\n                children: \"Nom\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"name\",\n                name: \"name\",\n                value: formData.name,\n                onChange: handleInputChange,\n                className: \"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"description\",\n                className: \"block text-gray-700 mb-2\",\n                children: \"Description\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                id: \"description\",\n                name: \"description\",\n                value: formData.description,\n                onChange: handleInputChange,\n                className: \"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\",\n                rows: \"3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-gray-700 mb-2\",\n                children: \"Couleur\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-4 gap-2\",\n                children: colorOptions.map((colorOption, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: () => handleColorChange(colorOption),\n                  className: `p-3 rounded-lg border-2 ${colorOption.bg} ${colorOption.text} ${formData.color === colorOption.bg ? 'border-gray-800' : 'border-gray-300'}`,\n                  children: colorOption.label\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 304,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"status\",\n                className: \"block text-gray-700 mb-2\",\n                children: \"Statut\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                id: \"status\",\n                name: \"status\",\n                value: formData.status,\n                onChange: handleInputChange,\n                className: \"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"active\",\n                  children: \"Actif\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 327,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"inactive\",\n                  children: \"Inactif\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 328,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"px-6 py-4 bg-gray-50 border-t flex justify-end\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: closeModal,\n              className: \"px-4 py-2 border rounded-lg text-gray-700 mr-2 hover:bg-gray-100\",\n              children: \"Annuler\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              disabled: loading,\n              className: \"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50\",\n              children: loading ? 'Enregistrement...' : currentCategory ? 'Mettre à jour' : 'Ajouter'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 267,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 266,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 192,\n    columnNumber: 5\n  }, this);\n};\n_s(CategoriesCRUD, \"NvRcI3GuowguJWLhcYgAI14UFM0=\", false, function () {\n  return [useData];\n});\n_c = CategoriesCRUD;\nexport default CategoriesCRUD;\nvar _c;\n$RefreshReg$(_c, \"CategoriesCRUD\");", "map": {"version": 3, "names": ["React", "useState", "useData", "categoryAPI", "jsxDEV", "_jsxDEV", "CategoriesCRUD", "_s", "state", "dispatch", "DataActions", "categories", "loading", "isModalOpen", "setIsModalOpen", "currentCategory", "setCurrentCategory", "formData", "setFormData", "name", "description", "color", "textColor", "status", "colorOptions", "bg", "text", "label", "handleInputChange", "e", "value", "target", "prev", "handleColorChange", "colorOption", "openModal", "category", "closeModal", "handleSubmit", "preventDefault", "type", "SET_LOADING", "payload", "categoryData", "slug", "toLowerCase", "replace", "result", "update", "id", "success", "UPDATE_CATEGORY", "data", "console", "log", "create", "ADD_CATEGORY", "newCategory", "Date", "now", "err", "error", "handleDelete", "window", "confirm", "DELETE_CATEGORY", "toggleStatus", "currentStatus", "newStatus", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "onClick", "map", "onSubmit", "htmlFor", "onChange", "required", "rows", "index", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet-Synthese/frontend/src/pages/Admin/CategoriesCRUD.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useData } from '../../context/DataContext';\nimport { categoryAPI } from '../../services/apiService';\n\nconst CategoriesCRUD = () => {\n  const { state, dispatch, DataActions } = useData();\n  const { categories, loading } = state;\n  \n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const [currentCategory, setCurrentCategory] = useState(null);\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    color: 'bg-blue-100',\n    textColor: 'text-blue-800',\n    status: 'active'\n  });\n\n  // Couleurs disponibles pour les catégories\n  const colorOptions = [\n    { bg: 'bg-red-100', text: 'text-red-800', label: 'Rouge' },\n    { bg: 'bg-green-100', text: 'text-green-800', label: 'Vert' },\n    { bg: 'bg-blue-100', text: 'text-blue-800', label: 'Bleu' },\n    { bg: 'bg-yellow-100', text: 'text-yellow-800', label: 'Jaune' },\n    { bg: 'bg-purple-100', text: 'text-purple-800', label: 'Violet' },\n    { bg: 'bg-gray-100', text: 'text-gray-800', label: 'Gris' },\n    { bg: 'bg-orange-100', text: 'text-orange-800', label: 'Orange' },\n    { bg: 'bg-pink-100', text: 'text-pink-800', label: 'Rose' }\n  ];\n\n  // Gestion des changements dans le formulaire\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({ ...prev, [name]: value }));\n  };\n\n  // Gestion du changement de couleur\n  const handleColorChange = (colorOption) => {\n    setFormData(prev => ({\n      ...prev,\n      color: colorOption.bg,\n      textColor: colorOption.text\n    }));\n  };\n\n  // Ouvrir le modal pour ajouter/modifier une catégorie\n  const openModal = (category = null) => {\n    if (category) {\n      setCurrentCategory(category);\n      setFormData({\n        name: category.name,\n        description: category.description,\n        color: category.color,\n        textColor: category.textColor,\n        status: category.status\n      });\n    } else {\n      setCurrentCategory(null);\n      setFormData({\n        name: '',\n        description: '',\n        color: 'bg-blue-100',\n        textColor: 'text-blue-800',\n        status: 'active'\n      });\n    }\n    setIsModalOpen(true);\n  };\n\n  // Fermer le modal\n  const closeModal = () => {\n    setIsModalOpen(false);\n    setCurrentCategory(null);\n    setFormData({\n      name: '',\n      description: '',\n      color: 'bg-blue-100',\n      textColor: 'text-blue-800',\n      status: 'active'\n    });\n  };\n\n  // Soumettre le formulaire\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    dispatch({ type: DataActions.SET_LOADING, payload: true });\n\n    try {\n      const categoryData = {\n        ...formData,\n        slug: formData.name.toLowerCase().replace(/\\s+/g, '-')\n      };\n\n      if (currentCategory) {\n        // Mise à jour d'une catégorie existante\n        const result = await categoryAPI.update(currentCategory.id, categoryData);\n\n        if (result.success) {\n          dispatch({\n            type: DataActions.UPDATE_CATEGORY,\n            payload: { ...result.data, id: currentCategory.id }\n          });\n          console.log('Catégorie mise à jour via API');\n        } else {\n          // Fallback: mise à jour locale\n          dispatch({\n            type: DataActions.UPDATE_CATEGORY,\n            payload: { ...categoryData, id: currentCategory.id }\n          });\n          console.log('Catégorie mise à jour localement (mode démonstration)');\n        }\n      } else {\n        // Création d'une nouvelle catégorie\n        const result = await categoryAPI.create(categoryData);\n\n        if (result.success) {\n          dispatch({ type: DataActions.ADD_CATEGORY, payload: result.data });\n          console.log('Catégorie créée via API');\n        } else {\n          // Fallback: création locale\n          const newCategory = {\n            ...categoryData,\n            id: Date.now()\n          };\n          dispatch({ type: DataActions.ADD_CATEGORY, payload: newCategory });\n          console.log('Catégorie créée localement (mode démonstration)');\n        }\n      }\n\n      closeModal();\n    } catch (err) {\n      console.error('Erreur lors de l\\'enregistrement de la catégorie', err);\n      // Fallback en cas d'erreur\n      const categoryData = {\n        ...formData,\n        slug: formData.name.toLowerCase().replace(/\\s+/g, '-')\n      };\n\n      if (currentCategory) {\n        dispatch({\n          type: DataActions.UPDATE_CATEGORY,\n          payload: { ...categoryData, id: currentCategory.id }\n        });\n      } else {\n        const newCategory = {\n          ...categoryData,\n          id: Date.now()\n        };\n        dispatch({ type: DataActions.ADD_CATEGORY, payload: newCategory });\n      }\n      closeModal();\n    } finally {\n      dispatch({ type: DataActions.SET_LOADING, payload: false });\n    }\n  };\n\n  // Supprimer une catégorie\n  const handleDelete = (id) => {\n    if (window.confirm('Êtes-vous sûr de vouloir supprimer cette catégorie ?')) {\n      dispatch({ type: DataActions.SET_LOADING, payload: true });\n\n      try {\n        dispatch({ type: DataActions.DELETE_CATEGORY, payload: id });\n        console.log('Catégorie supprimée avec succès');\n      } catch (err) {\n        console.error('Erreur lors de la suppression de la catégorie', err);\n      } finally {\n        dispatch({ type: DataActions.SET_LOADING, payload: false });\n      }\n    }\n  };\n\n  // Basculer le statut d'une catégorie\n  const toggleStatus = (id, currentStatus) => {\n    const newStatus = currentStatus === 'active' ? 'inactive' : 'active';\n    dispatch({ type: DataActions.SET_LOADING, payload: true });\n\n    try {\n      dispatch({\n        type: DataActions.UPDATE_CATEGORY,\n        payload: { id, status: newStatus }\n      });\n      console.log(`Catégorie ${newStatus === 'active' ? 'activée' : 'désactivée'}`);\n    } catch (err) {\n      console.error('Erreur lors du changement de statut', err);\n    } finally {\n      dispatch({ type: DataActions.SET_LOADING, payload: false });\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* En-tête */}\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">Gestion des Catégories</h1>\n          <p className=\"text-sm text-gray-500\">\n            {categories.length} catégorie(s) • CRUD complet activé\n          </p>\n        </div>\n        <button\n          onClick={() => openModal()}\n          className=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700\"\n        >\n          Ajouter une catégorie\n        </button>\n      </div>\n\n      {/* Grille des catégories */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n        {categories.map((category) => (\n          <div key={category.id} className=\"bg-white rounded-lg shadow-sm border overflow-hidden\">\n            <div className={`p-4 ${category.color}`}>\n              <div className=\"flex justify-between items-start\">\n                <h3 className={`text-lg font-semibold ${category.textColor}`}>\n                  {category.name}\n                </h3>\n                <span className={`px-2 py-1 text-xs rounded-full ${\n                  category.status === 'active' \n                    ? 'bg-green-100 text-green-800' \n                    : 'bg-red-100 text-red-800'\n                }`}>\n                  {category.status === 'active' ? 'Actif' : 'Inactif'}\n                </span>\n              </div>\n            </div>\n            \n            <div className=\"p-4\">\n              <p className=\"text-gray-600 text-sm mb-4\">\n                {category.description}\n              </p>\n              \n              <div className=\"flex justify-between items-center\">\n                <div className=\"flex space-x-2\">\n                  <button\n                    onClick={() => openModal(category)}\n                    className=\"text-indigo-600 hover:text-indigo-900 text-sm\"\n                  >\n                    Modifier\n                  </button>\n                  <button\n                    onClick={() => toggleStatus(category.id, category.status)}\n                    className={`text-sm ${\n                      category.status === 'active' \n                        ? 'text-orange-600 hover:text-orange-900' \n                        : 'text-green-600 hover:text-green-900'\n                    }`}\n                  >\n                    {category.status === 'active' ? 'Désactiver' : 'Activer'}\n                  </button>\n                  <button\n                    onClick={() => handleDelete(category.id)}\n                    className=\"text-red-600 hover:text-red-900 text-sm\"\n                  >\n                    Supprimer\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* Modal pour ajouter/modifier une catégorie */}\n      {isModalOpen && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n          <div className=\"bg-white rounded-lg shadow-lg w-full max-w-md mx-4\">\n            <div className=\"px-6 py-4 border-b\">\n              <h3 className=\"text-lg font-semibold\">\n                {currentCategory ? 'Modifier la catégorie' : 'Ajouter une catégorie'}\n              </h3>\n            </div>\n            <form onSubmit={handleSubmit}>\n              <div className=\"p-6\">\n                <div className=\"mb-4\">\n                  <label htmlFor=\"name\" className=\"block text-gray-700 mb-2\">Nom</label>\n                  <input\n                    type=\"text\"\n                    id=\"name\"\n                    name=\"name\"\n                    value={formData.name}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n                    required\n                  />\n                </div>\n                \n                <div className=\"mb-4\">\n                  <label htmlFor=\"description\" className=\"block text-gray-700 mb-2\">Description</label>\n                  <textarea\n                    id=\"description\"\n                    name=\"description\"\n                    value={formData.description}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n                    rows=\"3\"\n                  ></textarea>\n                </div>\n                \n                <div className=\"mb-4\">\n                  <label className=\"block text-gray-700 mb-2\">Couleur</label>\n                  <div className=\"grid grid-cols-4 gap-2\">\n                    {colorOptions.map((colorOption, index) => (\n                      <button\n                        key={index}\n                        type=\"button\"\n                        onClick={() => handleColorChange(colorOption)}\n                        className={`p-3 rounded-lg border-2 ${colorOption.bg} ${colorOption.text} ${\n                          formData.color === colorOption.bg ? 'border-gray-800' : 'border-gray-300'\n                        }`}\n                      >\n                        {colorOption.label}\n                      </button>\n                    ))}\n                  </div>\n                </div>\n                \n                <div className=\"mb-4\">\n                  <label htmlFor=\"status\" className=\"block text-gray-700 mb-2\">Statut</label>\n                  <select\n                    id=\"status\"\n                    name=\"status\"\n                    value={formData.status}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n                  >\n                    <option value=\"active\">Actif</option>\n                    <option value=\"inactive\">Inactif</option>\n                  </select>\n                </div>\n              </div>\n              \n              <div className=\"px-6 py-4 bg-gray-50 border-t flex justify-end\">\n                <button\n                  type=\"button\"\n                  onClick={closeModal}\n                  className=\"px-4 py-2 border rounded-lg text-gray-700 mr-2 hover:bg-gray-100\"\n                >\n                  Annuler\n                </button>\n                <button\n                  type=\"submit\"\n                  disabled={loading}\n                  className=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50\"\n                >\n                  {loading ? 'Enregistrement...' : (currentCategory ? 'Mettre à jour' : 'Ajouter')}\n                </button>\n              </div>\n            </form>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default CategoriesCRUD;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,OAAO,QAAQ,2BAA2B;AACnD,SAASC,WAAW,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExD,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM;IAAEC,KAAK;IAAEC,QAAQ;IAAEC;EAAY,CAAC,GAAGR,OAAO,CAAC,CAAC;EAClD,MAAM;IAAES,UAAU;IAAEC;EAAQ,CAAC,GAAGJ,KAAK;EAErC,MAAM,CAACK,WAAW,EAAEC,cAAc,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACc,eAAe,EAAEC,kBAAkB,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACgB,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC;IACvCkB,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,KAAK,EAAE,aAAa;IACpBC,SAAS,EAAE,eAAe;IAC1BC,MAAM,EAAE;EACV,CAAC,CAAC;;EAEF;EACA,MAAMC,YAAY,GAAG,CACnB;IAAEC,EAAE,EAAE,YAAY;IAAEC,IAAI,EAAE,cAAc;IAAEC,KAAK,EAAE;EAAQ,CAAC,EAC1D;IAAEF,EAAE,EAAE,cAAc;IAAEC,IAAI,EAAE,gBAAgB;IAAEC,KAAK,EAAE;EAAO,CAAC,EAC7D;IAAEF,EAAE,EAAE,aAAa;IAAEC,IAAI,EAAE,eAAe;IAAEC,KAAK,EAAE;EAAO,CAAC,EAC3D;IAAEF,EAAE,EAAE,eAAe;IAAEC,IAAI,EAAE,iBAAiB;IAAEC,KAAK,EAAE;EAAQ,CAAC,EAChE;IAAEF,EAAE,EAAE,eAAe;IAAEC,IAAI,EAAE,iBAAiB;IAAEC,KAAK,EAAE;EAAS,CAAC,EACjE;IAAEF,EAAE,EAAE,aAAa;IAAEC,IAAI,EAAE,eAAe;IAAEC,KAAK,EAAE;EAAO,CAAC,EAC3D;IAAEF,EAAE,EAAE,eAAe;IAAEC,IAAI,EAAE,iBAAiB;IAAEC,KAAK,EAAE;EAAS,CAAC,EACjE;IAAEF,EAAE,EAAE,aAAa;IAAEC,IAAI,EAAE,eAAe;IAAEC,KAAK,EAAE;EAAO,CAAC,CAC5D;;EAED;EACA,MAAMC,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEV,IAAI;MAAEW;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChCb,WAAW,CAACc,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACb,IAAI,GAAGW;IAAM,CAAC,CAAC,CAAC;EACnD,CAAC;;EAED;EACA,MAAMG,iBAAiB,GAAIC,WAAW,IAAK;IACzChB,WAAW,CAACc,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPX,KAAK,EAAEa,WAAW,CAACT,EAAE;MACrBH,SAAS,EAAEY,WAAW,CAACR;IACzB,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMS,SAAS,GAAGA,CAACC,QAAQ,GAAG,IAAI,KAAK;IACrC,IAAIA,QAAQ,EAAE;MACZpB,kBAAkB,CAACoB,QAAQ,CAAC;MAC5BlB,WAAW,CAAC;QACVC,IAAI,EAAEiB,QAAQ,CAACjB,IAAI;QACnBC,WAAW,EAAEgB,QAAQ,CAAChB,WAAW;QACjCC,KAAK,EAAEe,QAAQ,CAACf,KAAK;QACrBC,SAAS,EAAEc,QAAQ,CAACd,SAAS;QAC7BC,MAAM,EAAEa,QAAQ,CAACb;MACnB,CAAC,CAAC;IACJ,CAAC,MAAM;MACLP,kBAAkB,CAAC,IAAI,CAAC;MACxBE,WAAW,CAAC;QACVC,IAAI,EAAE,EAAE;QACRC,WAAW,EAAE,EAAE;QACfC,KAAK,EAAE,aAAa;QACpBC,SAAS,EAAE,eAAe;QAC1BC,MAAM,EAAE;MACV,CAAC,CAAC;IACJ;IACAT,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;;EAED;EACA,MAAMuB,UAAU,GAAGA,CAAA,KAAM;IACvBvB,cAAc,CAAC,KAAK,CAAC;IACrBE,kBAAkB,CAAC,IAAI,CAAC;IACxBE,WAAW,CAAC;MACVC,IAAI,EAAE,EAAE;MACRC,WAAW,EAAE,EAAE;MACfC,KAAK,EAAE,aAAa;MACpBC,SAAS,EAAE,eAAe;MAC1BC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMe,YAAY,GAAG,MAAOT,CAAC,IAAK;IAChCA,CAAC,CAACU,cAAc,CAAC,CAAC;IAClB9B,QAAQ,CAAC;MAAE+B,IAAI,EAAE9B,WAAW,CAAC+B,WAAW;MAAEC,OAAO,EAAE;IAAK,CAAC,CAAC;IAE1D,IAAI;MACF,MAAMC,YAAY,GAAG;QACnB,GAAG1B,QAAQ;QACX2B,IAAI,EAAE3B,QAAQ,CAACE,IAAI,CAAC0B,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG;MACvD,CAAC;MAED,IAAI/B,eAAe,EAAE;QACnB;QACA,MAAMgC,MAAM,GAAG,MAAM5C,WAAW,CAAC6C,MAAM,CAACjC,eAAe,CAACkC,EAAE,EAAEN,YAAY,CAAC;QAEzE,IAAII,MAAM,CAACG,OAAO,EAAE;UAClBzC,QAAQ,CAAC;YACP+B,IAAI,EAAE9B,WAAW,CAACyC,eAAe;YACjCT,OAAO,EAAE;cAAE,GAAGK,MAAM,CAACK,IAAI;cAAEH,EAAE,EAAElC,eAAe,CAACkC;YAAG;UACpD,CAAC,CAAC;UACFI,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;QAC9C,CAAC,MAAM;UACL;UACA7C,QAAQ,CAAC;YACP+B,IAAI,EAAE9B,WAAW,CAACyC,eAAe;YACjCT,OAAO,EAAE;cAAE,GAAGC,YAAY;cAAEM,EAAE,EAAElC,eAAe,CAACkC;YAAG;UACrD,CAAC,CAAC;UACFI,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;QACtE;MACF,CAAC,MAAM;QACL;QACA,MAAMP,MAAM,GAAG,MAAM5C,WAAW,CAACoD,MAAM,CAACZ,YAAY,CAAC;QAErD,IAAII,MAAM,CAACG,OAAO,EAAE;UAClBzC,QAAQ,CAAC;YAAE+B,IAAI,EAAE9B,WAAW,CAAC8C,YAAY;YAAEd,OAAO,EAAEK,MAAM,CAACK;UAAK,CAAC,CAAC;UAClEC,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;QACxC,CAAC,MAAM;UACL;UACA,MAAMG,WAAW,GAAG;YAClB,GAAGd,YAAY;YACfM,EAAE,EAAES,IAAI,CAACC,GAAG,CAAC;UACf,CAAC;UACDlD,QAAQ,CAAC;YAAE+B,IAAI,EAAE9B,WAAW,CAAC8C,YAAY;YAAEd,OAAO,EAAEe;UAAY,CAAC,CAAC;UAClEJ,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;QAChE;MACF;MAEAjB,UAAU,CAAC,CAAC;IACd,CAAC,CAAC,OAAOuB,GAAG,EAAE;MACZP,OAAO,CAACQ,KAAK,CAAC,kDAAkD,EAAED,GAAG,CAAC;MACtE;MACA,MAAMjB,YAAY,GAAG;QACnB,GAAG1B,QAAQ;QACX2B,IAAI,EAAE3B,QAAQ,CAACE,IAAI,CAAC0B,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG;MACvD,CAAC;MAED,IAAI/B,eAAe,EAAE;QACnBN,QAAQ,CAAC;UACP+B,IAAI,EAAE9B,WAAW,CAACyC,eAAe;UACjCT,OAAO,EAAE;YAAE,GAAGC,YAAY;YAAEM,EAAE,EAAElC,eAAe,CAACkC;UAAG;QACrD,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,MAAMQ,WAAW,GAAG;UAClB,GAAGd,YAAY;UACfM,EAAE,EAAES,IAAI,CAACC,GAAG,CAAC;QACf,CAAC;QACDlD,QAAQ,CAAC;UAAE+B,IAAI,EAAE9B,WAAW,CAAC8C,YAAY;UAAEd,OAAO,EAAEe;QAAY,CAAC,CAAC;MACpE;MACApB,UAAU,CAAC,CAAC;IACd,CAAC,SAAS;MACR5B,QAAQ,CAAC;QAAE+B,IAAI,EAAE9B,WAAW,CAAC+B,WAAW;QAAEC,OAAO,EAAE;MAAM,CAAC,CAAC;IAC7D;EACF,CAAC;;EAED;EACA,MAAMoB,YAAY,GAAIb,EAAE,IAAK;IAC3B,IAAIc,MAAM,CAACC,OAAO,CAAC,sDAAsD,CAAC,EAAE;MAC1EvD,QAAQ,CAAC;QAAE+B,IAAI,EAAE9B,WAAW,CAAC+B,WAAW;QAAEC,OAAO,EAAE;MAAK,CAAC,CAAC;MAE1D,IAAI;QACFjC,QAAQ,CAAC;UAAE+B,IAAI,EAAE9B,WAAW,CAACuD,eAAe;UAAEvB,OAAO,EAAEO;QAAG,CAAC,CAAC;QAC5DI,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;MAChD,CAAC,CAAC,OAAOM,GAAG,EAAE;QACZP,OAAO,CAACQ,KAAK,CAAC,+CAA+C,EAAED,GAAG,CAAC;MACrE,CAAC,SAAS;QACRnD,QAAQ,CAAC;UAAE+B,IAAI,EAAE9B,WAAW,CAAC+B,WAAW;UAAEC,OAAO,EAAE;QAAM,CAAC,CAAC;MAC7D;IACF;EACF,CAAC;;EAED;EACA,MAAMwB,YAAY,GAAGA,CAACjB,EAAE,EAAEkB,aAAa,KAAK;IAC1C,MAAMC,SAAS,GAAGD,aAAa,KAAK,QAAQ,GAAG,UAAU,GAAG,QAAQ;IACpE1D,QAAQ,CAAC;MAAE+B,IAAI,EAAE9B,WAAW,CAAC+B,WAAW;MAAEC,OAAO,EAAE;IAAK,CAAC,CAAC;IAE1D,IAAI;MACFjC,QAAQ,CAAC;QACP+B,IAAI,EAAE9B,WAAW,CAACyC,eAAe;QACjCT,OAAO,EAAE;UAAEO,EAAE;UAAE1B,MAAM,EAAE6C;QAAU;MACnC,CAAC,CAAC;MACFf,OAAO,CAACC,GAAG,CAAC,aAAac,SAAS,KAAK,QAAQ,GAAG,SAAS,GAAG,YAAY,EAAE,CAAC;IAC/E,CAAC,CAAC,OAAOR,GAAG,EAAE;MACZP,OAAO,CAACQ,KAAK,CAAC,qCAAqC,EAAED,GAAG,CAAC;IAC3D,CAAC,SAAS;MACRnD,QAAQ,CAAC;QAAE+B,IAAI,EAAE9B,WAAW,CAAC+B,WAAW;QAAEC,OAAO,EAAE;MAAM,CAAC,CAAC;IAC7D;EACF,CAAC;EAED,oBACErC,OAAA;IAAKgE,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBjE,OAAA;MAAKgE,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChDjE,OAAA;QAAAiE,QAAA,gBACEjE,OAAA;UAAIgE,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5ErE,OAAA;UAAGgE,SAAS,EAAC,uBAAuB;UAAAC,QAAA,GACjC3D,UAAU,CAACgE,MAAM,EAAC,gDACrB;QAAA;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACNrE,OAAA;QACEuE,OAAO,EAAEA,CAAA,KAAMzC,SAAS,CAAC,CAAE;QAC3BkC,SAAS,EAAC,iEAAiE;QAAAC,QAAA,EAC5E;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNrE,OAAA;MAAKgE,SAAS,EAAC,sDAAsD;MAAAC,QAAA,EAClE3D,UAAU,CAACkE,GAAG,CAAEzC,QAAQ,iBACvB/B,OAAA;QAAuBgE,SAAS,EAAC,sDAAsD;QAAAC,QAAA,gBACrFjE,OAAA;UAAKgE,SAAS,EAAE,OAAOjC,QAAQ,CAACf,KAAK,EAAG;UAAAiD,QAAA,eACtCjE,OAAA;YAAKgE,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC/CjE,OAAA;cAAIgE,SAAS,EAAE,yBAAyBjC,QAAQ,CAACd,SAAS,EAAG;cAAAgD,QAAA,EAC1DlC,QAAQ,CAACjB;YAAI;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eACLrE,OAAA;cAAMgE,SAAS,EAAE,kCACfjC,QAAQ,CAACb,MAAM,KAAK,QAAQ,GACxB,6BAA6B,GAC7B,yBAAyB,EAC5B;cAAA+C,QAAA,EACAlC,QAAQ,CAACb,MAAM,KAAK,QAAQ,GAAG,OAAO,GAAG;YAAS;cAAAgD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENrE,OAAA;UAAKgE,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClBjE,OAAA;YAAGgE,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EACtClC,QAAQ,CAAChB;UAAW;YAAAmD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,eAEJrE,OAAA;YAAKgE,SAAS,EAAC,mCAAmC;YAAAC,QAAA,eAChDjE,OAAA;cAAKgE,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BjE,OAAA;gBACEuE,OAAO,EAAEA,CAAA,KAAMzC,SAAS,CAACC,QAAQ,CAAE;gBACnCiC,SAAS,EAAC,+CAA+C;gBAAAC,QAAA,EAC1D;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTrE,OAAA;gBACEuE,OAAO,EAAEA,CAAA,KAAMV,YAAY,CAAC9B,QAAQ,CAACa,EAAE,EAAEb,QAAQ,CAACb,MAAM,CAAE;gBAC1D8C,SAAS,EAAE,WACTjC,QAAQ,CAACb,MAAM,KAAK,QAAQ,GACxB,uCAAuC,GACvC,qCAAqC,EACxC;gBAAA+C,QAAA,EAEFlC,QAAQ,CAACb,MAAM,KAAK,QAAQ,GAAG,YAAY,GAAG;cAAS;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC,eACTrE,OAAA;gBACEuE,OAAO,EAAEA,CAAA,KAAMd,YAAY,CAAC1B,QAAQ,CAACa,EAAE,CAAE;gBACzCoB,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,EACpD;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA,GA/CEtC,QAAQ,CAACa,EAAE;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAgDhB,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAGL7D,WAAW,iBACVR,OAAA;MAAKgE,SAAS,EAAC,4EAA4E;MAAAC,QAAA,eACzFjE,OAAA;QAAKgE,SAAS,EAAC,oDAAoD;QAAAC,QAAA,gBACjEjE,OAAA;UAAKgE,SAAS,EAAC,oBAAoB;UAAAC,QAAA,eACjCjE,OAAA;YAAIgE,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAClCvD,eAAe,GAAG,uBAAuB,GAAG;UAAuB;YAAAwD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACNrE,OAAA;UAAMyE,QAAQ,EAAExC,YAAa;UAAAgC,QAAA,gBAC3BjE,OAAA;YAAKgE,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAClBjE,OAAA;cAAKgE,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBjE,OAAA;gBAAO0E,OAAO,EAAC,MAAM;gBAACV,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtErE,OAAA;gBACEmC,IAAI,EAAC,MAAM;gBACXS,EAAE,EAAC,MAAM;gBACT9B,IAAI,EAAC,MAAM;gBACXW,KAAK,EAAEb,QAAQ,CAACE,IAAK;gBACrB6D,QAAQ,EAAEpD,iBAAkB;gBAC5ByC,SAAS,EAAC,yFAAyF;gBACnGY,QAAQ;cAAA;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENrE,OAAA;cAAKgE,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBjE,OAAA;gBAAO0E,OAAO,EAAC,aAAa;gBAACV,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrFrE,OAAA;gBACE4C,EAAE,EAAC,aAAa;gBAChB9B,IAAI,EAAC,aAAa;gBAClBW,KAAK,EAAEb,QAAQ,CAACG,WAAY;gBAC5B4D,QAAQ,EAAEpD,iBAAkB;gBAC5ByC,SAAS,EAAC,yFAAyF;gBACnGa,IAAI,EAAC;cAAG;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eAENrE,OAAA;cAAKgE,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBjE,OAAA;gBAAOgE,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3DrE,OAAA;gBAAKgE,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EACpC9C,YAAY,CAACqD,GAAG,CAAC,CAAC3C,WAAW,EAAEiD,KAAK,kBACnC9E,OAAA;kBAEEmC,IAAI,EAAC,QAAQ;kBACboC,OAAO,EAAEA,CAAA,KAAM3C,iBAAiB,CAACC,WAAW,CAAE;kBAC9CmC,SAAS,EAAE,2BAA2BnC,WAAW,CAACT,EAAE,IAAIS,WAAW,CAACR,IAAI,IACtET,QAAQ,CAACI,KAAK,KAAKa,WAAW,CAACT,EAAE,GAAG,iBAAiB,GAAG,iBAAiB,EACxE;kBAAA6C,QAAA,EAEFpC,WAAW,CAACP;gBAAK,GAPbwD,KAAK;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAQJ,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENrE,OAAA;cAAKgE,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBjE,OAAA;gBAAO0E,OAAO,EAAC,QAAQ;gBAACV,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3ErE,OAAA;gBACE4C,EAAE,EAAC,QAAQ;gBACX9B,IAAI,EAAC,QAAQ;gBACbW,KAAK,EAAEb,QAAQ,CAACM,MAAO;gBACvByD,QAAQ,EAAEpD,iBAAkB;gBAC5ByC,SAAS,EAAC,yFAAyF;gBAAAC,QAAA,gBAEnGjE,OAAA;kBAAQyB,KAAK,EAAC,QAAQ;kBAAAwC,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACrCrE,OAAA;kBAAQyB,KAAK,EAAC,UAAU;kBAAAwC,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENrE,OAAA;YAAKgE,SAAS,EAAC,gDAAgD;YAAAC,QAAA,gBAC7DjE,OAAA;cACEmC,IAAI,EAAC,QAAQ;cACboC,OAAO,EAAEvC,UAAW;cACpBgC,SAAS,EAAC,kEAAkE;cAAAC,QAAA,EAC7E;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTrE,OAAA;cACEmC,IAAI,EAAC,QAAQ;cACb4C,QAAQ,EAAExE,OAAQ;cAClByD,SAAS,EAAC,qFAAqF;cAAAC,QAAA,EAE9F1D,OAAO,GAAG,mBAAmB,GAAIG,eAAe,GAAG,eAAe,GAAG;YAAU;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACnE,EAAA,CA9VID,cAAc;EAAA,QACuBJ,OAAO;AAAA;AAAAmF,EAAA,GAD5C/E,cAAc;AAgWpB,eAAeA,cAAc;AAAC,IAAA+E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}