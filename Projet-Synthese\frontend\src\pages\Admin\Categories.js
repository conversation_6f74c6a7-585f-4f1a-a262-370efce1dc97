import React, { useState, useEffect } from 'react';
import axios from 'axios';

const Categories = () => {
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currentCategory, setCurrentCategory] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    icon: '',
    color: '#4CAF50',
    is_active: true,
    image: null
  });

  useEffect(() => {
    fetchCategories();
  }, []);

  const fetchCategories = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/admin/categories');
      setCategories(response.data);
    } catch (err) {
      console.log('API non disponible, utilisation des données de test');

      // Données de test pour les catégories
      const testCategories = [
        {
          id: 1,
          name: 'Produits Grillés',
          description: 'Viandes, poissons et légumes à griller',
          icon: 'fa fa-fire',
          color: '#FF6B35',
          is_active: true,
          products_count: 15,
          image_url: 'https://images.unsplash.com/photo-1529692236671-f1f6cf9683ba?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80'
        },
        {
          id: 2,
          name: 'Salades',
          description: 'Salades fraîches et variées',
          icon: 'fa fa-leaf',
          color: '#4CAF50',
          is_active: true,
          products_count: 8,
          image_url: 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80'
        },
        {
          id: 3,
          name: 'Fromages',
          description: 'Fromages artisanaux et de qualité',
          icon: 'fa fa-cheese',
          color: '#FFC107',
          is_active: true,
          products_count: 12,
          image_url: 'https://images.unsplash.com/photo-1486297678162-eb2a19b0a32d?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80'
        },
        {
          id: 4,
          name: 'Boissons',
          description: 'Boissons fraîches et chaudes',
          icon: 'fa fa-glass',
          color: '#2196F3',
          is_active: true,
          products_count: 20,
          image_url: 'https://images.unsplash.com/photo-1613478223719-2ab802602423?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80'
        },
        {
          id: 5,
          name: 'Desserts',
          description: 'Desserts maison et gourmandises',
          icon: 'fa fa-birthday-cake',
          color: '#E91E63',
          is_active: true,
          products_count: 6,
          image_url: 'https://images.unsplash.com/photo-1571877227200-a0d98ea607e9?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80'
        },
        {
          id: 6,
          name: 'Produits Non Grillés',
          description: 'Plats préparés et spécialités',
          icon: 'fa fa-cutlery',
          color: '#9C27B0',
          is_active: true,
          products_count: 10,
          image_url: 'https://images.unsplash.com/photo-1546833999-b9f581a1996d?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80'
        }
      ];

      setCategories(testCategories);
      setError(null);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked, files } = e.target;
    
    if (type === 'file') {
      setFormData({
        ...formData,
        [name]: files[0]
      });
    } else if (type === 'checkbox') {
      setFormData({
        ...formData,
        [name]: checked
      });
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }
  };

  const openModal = (category = null) => {
    if (category) {
      setCurrentCategory(category);
      setFormData({
        name: category.name,
        description: category.description || '',
        icon: category.icon || '',
        color: category.color || '#4CAF50',
        is_active: category.is_active,
        image: null
      });
    } else {
      setCurrentCategory(null);
      setFormData({
        name: '',
        description: '',
        icon: '',
        color: '#4CAF50',
        is_active: true,
        image: null
      });
    }
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setCurrentCategory(null);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    const formDataToSend = new FormData();
    Object.keys(formData).forEach(key => {
      if (formData[key] !== null) {
        formDataToSend.append(key, formData[key]);
      }
    });
    
    try {
      if (currentCategory) {
        // Mise à jour d'une catégorie existante
        await axios.post(`/api/admin/categories/${currentCategory.id}`, formDataToSend, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        });
      } else {
        // Création d'une nouvelle catégorie
        await axios.post('/api/admin/categories', formDataToSend, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        });
      }
      
      closeModal();
      fetchCategories();
    } catch (err) {
      console.error('Erreur lors de l\'enregistrement de la catégorie', err);
      alert('Une erreur est survenue lors de l\'enregistrement de la catégorie');
    }
  };

  const handleDelete = async (id) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer cette catégorie ? Tous les produits associés seront également supprimés.')) {
      try {
        await axios.delete(`/api/admin/categories/${id}`);
        fetchCategories();
      } catch (err) {
        console.error('Erreur lors de la suppression de la catégorie', err);
        alert('Une erreur est survenue lors de la suppression de la catégorie');
      }
    }
  };

  if (loading && categories.length === 0) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-500"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold">Gestion des catégories</h1>
        <button
          onClick={() => openModal()}
          className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition"
        >
          Ajouter une catégorie
        </button>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
          <strong className="font-bold">Erreur!</strong>
          <span className="block sm:inline"> {error}</span>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {categories.map((category) => (
          <div key={category.id} className="bg-white rounded-lg shadow overflow-hidden">
            <div className="h-40 bg-gray-200 relative">
              {category.image_url ? (
                <img
                  src={category.image_url}
                  alt={category.name}
                  className="h-full w-full object-cover"
                />
              ) : (
                <div className="h-full w-full flex items-center justify-center text-gray-400">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                </div>
              )}
              <div 
                className="absolute top-0 right-0 m-2 px-2 py-1 rounded text-xs font-semibold"
                style={{ backgroundColor: category.color || '#4CAF50', color: '#fff' }}
              >
                {category.is_active ? 'Active' : 'Inactive'}
              </div>
            </div>
            <div className="p-4">
              <div className="flex items-center mb-2">
                {category.icon && (
                  <span className="mr-2 text-gray-600">
                    <i className={category.icon}></i>
                  </span>
                )}
                <h3 className="text-lg font-semibold">{category.name}</h3>
              </div>
              {category.description && (
                <p className="text-gray-600 text-sm mb-4">{category.description}</p>
              )}
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-500">
                  {category.products_count || 0} produits
                </span>
                <div>
                  <button
                    onClick={() => openModal(category)}
                    className="text-indigo-600 hover:text-indigo-900 mr-3"
                  >
                    Modifier
                  </button>
                  <button
                    onClick={() => handleDelete(category.id)}
                    className="text-red-600 hover:text-red-900"
                  >
                    Supprimer
                  </button>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Modal pour ajouter/modifier une catégorie */}
      {isModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg w-full max-w-md mx-4">
            <div className="px-6 py-4 border-b">
              <h3 className="text-lg font-semibold">
                {currentCategory ? 'Modifier la catégorie' : 'Ajouter une catégorie'}
              </h3>
            </div>
            <form onSubmit={handleSubmit}>
              <div className="p-6">
                <div className="mb-4">
                  <label htmlFor="name" className="block text-gray-700 mb-2">Nom</label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                    required
                  />
                </div>
                
                <div className="mb-4">
                  <label htmlFor="description" className="block text-gray-700 mb-2">Description</label>
                  <textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                    rows="3"
                  ></textarea>
                </div>
                
                <div className="mb-4">
                  <label htmlFor="icon" className="block text-gray-700 mb-2">Icône (classe CSS)</label>
                  <input
                    type="text"
                    id="icon"
                    name="icon"
                    value={formData.icon}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                    placeholder="fa fa-cutlery"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Exemple: fa fa-cutlery, material-icons, etc.
                  </p>
                </div>
                
                <div className="mb-4">
                  <label htmlFor="color" className="block text-gray-700 mb-2">Couleur</label>
                  <div className="flex items-center">
                    <input
                      type="color"
                      id="color"
                      name="color"
                      value={formData.color}
                      onChange={handleInputChange}
                      className="h-10 w-10 border-0 p-0"
                    />
                    <input
                      type="text"
                      value={formData.color}
                      onChange={handleInputChange}
                      name="color"
                      className="ml-2 flex-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                    />
                  </div>
                </div>
                
                <div className="mb-4">
                  <label htmlFor="image" className="block text-gray-700 mb-2">Image</label>
                  <input
                    type="file"
                    id="image"
                    name="image"
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                    accept="image/*"
                  />
                  {currentCategory && currentCategory.image_url && (
                    <div className="mt-2">
                      <p className="text-sm text-gray-500">Image actuelle:</p>
                      <div className="h-20 w-20 mt-1 rounded overflow-hidden bg-gray-100">
                        <img
                          src={currentCategory.image_url}
                          alt={currentCategory.name}
                          className="h-full w-full object-cover"
                        />
                      </div>
                    </div>
                  )}
                </div>
                
                <div className="mb-4">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="is_active"
                      name="is_active"
                      checked={formData.is_active}
                      onChange={handleInputChange}
                      className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                    />
                    <label htmlFor="is_active" className="ml-2 block text-gray-700">
                      Catégorie active
                    </label>
                  </div>
                </div>
              </div>
              
              <div className="px-6 py-4 bg-gray-50 border-t flex justify-end">
                <button
                  type="button"
                  onClick={closeModal}
                  className="px-4 py-2 border rounded-lg text-gray-700 mr-2 hover:bg-gray-100"
                >
                  Annuler
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
                >
                  {currentCategory ? 'Mettre à jour' : 'Ajouter'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default Categories;
