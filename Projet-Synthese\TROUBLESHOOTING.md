# 🔧 Guide de Dépannage - Projet YUMMY

## ❌ Problème : "Erreur lors du chargement des produits"

### **Causes possibles :**
1. **Serveur backend Laravel non démarré**
2. **Base de données non configurée**
3. **Problème de CORS**
4. **Variables d'environnement incorrectes**

---

## 🚀 Solutions étape par étape

### **1. Vérifier le serveur backend**

```bash
# Aller dans le dossier backend
cd backend

# Démarrer le serveur Laravel
php artisan serve

# Le serveur devrait être accessible sur http://localhost:8000
```

**Si erreur :** Vérifiez que PHP est installé et que les dépendances Composer sont installées.

### **2. Configurer la base de données**

```bash
# Copier le fichier d'environnement
cp .env.example .env

# Générer la clé d'application
php artisan key:generate

# Configurer la base de données dans .env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=yummy_db
DB_USERNAME=root
DB_PASSWORD=

# Créer la base de données
mysql -u root -p
CREATE DATABASE yummy_db;
exit

# Exécuter les migrations
php artisan migrate

# Exécuter les seeders (optionnel)
php artisan db:seed
```

### **3. Vérifier les variables d'environnement Frontend**

```bash
# Dans le dossier frontend, créer/vérifier .env
REACT_APP_API_URL=http://localhost:8000
```

### **4. Tester l'API manuellement**

```bash
# Tester si l'API répond
curl http://localhost:8000/api/products

# Ou dans le navigateur
http://localhost:8000/api/products
```

---

## 🔄 Mode de récupération automatique

Le frontend est configuré pour utiliser des **données de test** automatiquement si le backend n'est pas disponible.

### **Données de test incluses :**
- ✅ 6 produits d'exemple avec images
- ✅ 3 promotions actives
- ✅ Toutes les catégories

### **Indicateur visuel :**
Un bandeau jaune apparaît en haut de la page pour indiquer l'utilisation des données de test.

---

## 🛠️ Commandes utiles

### **Backend Laravel :**
```bash
# Démarrer le serveur
php artisan serve

# Vérifier les routes
php artisan route:list

# Nettoyer le cache
php artisan cache:clear
php artisan config:clear

# Vérifier la configuration
php artisan config:show

# Tester la base de données
php artisan tinker
```

### **Frontend React :**
```bash
# Démarrer le serveur de développement
npm start

# Installer les dépendances
npm install

# Vérifier les erreurs
npm run build
```

---

## 🐛 Erreurs courantes

### **1. "CORS Error"**
**Solution :** Vérifiez la configuration CORS dans `config/cors.php`

### **2. "Database connection failed"**
**Solution :** Vérifiez les paramètres de base de données dans `.env`

### **3. "404 Not Found" sur les routes API**
**Solution :** Vérifiez que `php artisan serve` est en cours d'exécution

### **4. "Module not found" dans React**
**Solution :** Exécutez `npm install` dans le dossier frontend

---

## 📞 Support

Si le problème persiste :

1. **Vérifiez les logs :**
   - Backend : `storage/logs/laravel.log`
   - Frontend : Console du navigateur (F12)

2. **Redémarrez tout :**
   ```bash
   # Arrêter tous les serveurs
   # Puis redémarrer dans l'ordre :
   
   # 1. Backend
   cd backend && php artisan serve
   
   # 2. Frontend (nouveau terminal)
   cd frontend && npm start
   ```

3. **Mode de démonstration :**
   Le projet fonctionne même sans backend grâce aux données de test intégrées.

---

## ✅ Checklist de vérification

- [ ] PHP installé et fonctionnel
- [ ] Composer installé
- [ ] MySQL/MariaDB en cours d'exécution
- [ ] Base de données `yummy_db` créée
- [ ] Fichier `.env` configuré (backend)
- [ ] Migrations exécutées
- [ ] Serveur Laravel démarré (`php artisan serve`)
- [ ] Node.js et npm installés
- [ ] Dépendances React installées (`npm install`)
- [ ] Serveur React démarré (`npm start`)
- [ ] Variables d'environnement React configurées

---

## 🎯 Résultat attendu

Une fois tout configuré correctement :
- ✅ Page d'accueil avec produits réels
- ✅ Dashboard admin fonctionnel
- ✅ API accessible sur http://localhost:8000
- ✅ Frontend accessible sur http://localhost:3000
- ✅ Pas de bandeau jaune (données réelles utilisées)
