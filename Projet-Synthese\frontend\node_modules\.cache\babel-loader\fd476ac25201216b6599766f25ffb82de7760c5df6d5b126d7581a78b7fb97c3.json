{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Projet-Synthese\\\\frontend\\\\src\\\\components\\\\products\\\\ProductCard.js\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ProductCard = ({\n  product\n}) => {\n  // Adapter les noms de propriétés selon l'API Laravel\n  const {\n    id,\n    name,\n    slug,\n    description,\n    price,\n    sale_price,\n    image_url,\n    discount_percentage,\n    category\n  } = product;\n\n  // Utiliser l'URL d'image fournie par Laravel ou une image par défaut\n  const imageUrl = image_url || 'https://via.placeholder.com/300x300?text=Pas+d\\'image';\n  return /*#__PURE__*/_jsxDEV(motion.div, {\n    className: \"card overflow-hidden hover:shadow-lg transition-shadow\",\n    whileHover: {\n      y: -5\n    },\n    transition: {\n      duration: 0.3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Link, {\n      to: `/products/${slug || id}`,\n      className: \"block relative\",\n      children: [discount_percentage && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-0 right-0 bg-red-500 text-white text-sm font-bold p-2 rounded-bl-lg\",\n        children: [\"-\", Math.round(discount_percentage), \"%\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n        src: imageUrl,\n        alt: name,\n        className: \"w-full aspect-square object-cover\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-sm text-gray-500 mb-1\",\n        children: (category === null || category === void 0 ? void 0 : category.name) || 'Catégorie'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: `/products/${slug}`,\n        className: \"block\",\n        children: /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900 hover:text-primary-600 transition-colors\",\n          children: name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600 text-sm mt-1 line-clamp-2\",\n        children: description\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center mt-3\",\n        children: sale_price ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-lg font-bold text-primary-600\",\n            children: [sale_price.toFixed(2), \" DH\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm text-gray-500 line-through ml-2\",\n            children: [price.toFixed(2), \" DH\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true) : /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-lg font-bold text-gray-900\",\n          children: [price.toFixed(2), \" DH\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-4\",\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: `/products/${slug}`,\n          className: \"btn-primary w-full text-center\",\n          children: \"Voir le produit\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 23,\n    columnNumber: 5\n  }, this);\n};\n_c = ProductCard;\nexport default ProductCard;\nvar _c;\n$RefreshReg$(_c, \"ProductCard\");", "map": {"version": 3, "names": ["React", "Link", "motion", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProductCard", "product", "id", "name", "slug", "description", "price", "sale_price", "image_url", "discount_percentage", "category", "imageUrl", "div", "className", "whileHover", "y", "transition", "duration", "children", "to", "Math", "round", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "src", "alt", "toFixed", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet-Synthese/frontend/src/components/products/ProductCard.js"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\nimport { motion } from 'framer-motion';\n\nconst ProductCard = ({ product }) => {\n  // Adapter les noms de propriétés selon l'API Laravel\n  const {\n    id,\n    name,\n    slug,\n    description,\n    price,\n    sale_price,\n    image_url,\n    discount_percentage,\n    category\n  } = product;\n\n  // Utiliser l'URL d'image fournie par Laravel ou une image par défaut\n  const imageUrl = image_url || 'https://via.placeholder.com/300x300?text=Pas+d\\'image';\n\n  return (\n    <motion.div\n      className=\"card overflow-hidden hover:shadow-lg transition-shadow\"\n      whileHover={{ y: -5 }}\n      transition={{ duration: 0.3 }}\n    >\n      <Link to={`/products/${slug || id}`} className=\"block relative\">\n        {discount_percentage && (\n          <div className=\"absolute top-0 right-0 bg-red-500 text-white text-sm font-bold p-2 rounded-bl-lg\">\n            -{Math.round(discount_percentage)}%\n          </div>\n        )}\n        <img \n          src={imageUrl} \n          alt={name} \n          className=\"w-full aspect-square object-cover\"\n        />\n      </Link>\n      \n      <div className=\"p-4\">\n        <div className=\"text-sm text-gray-500 mb-1\">\n          {category?.name || 'Catégorie'}\n        </div>\n        \n        <Link to={`/products/${slug}`} className=\"block\">\n          <h3 className=\"text-lg font-semibold text-gray-900 hover:text-primary-600 transition-colors\">\n            {name}\n          </h3>\n        </Link>\n        \n        <p className=\"text-gray-600 text-sm mt-1 line-clamp-2\">\n          {description}\n        </p>\n        \n        <div className=\"flex items-center mt-3\">\n          {sale_price ? (\n            <>\n              <span className=\"text-lg font-bold text-primary-600\">\n                {sale_price.toFixed(2)} DH\n              </span>\n              <span className=\"text-sm text-gray-500 line-through ml-2\">\n                {price.toFixed(2)} DH\n              </span>\n            </>\n          ) : (\n            <span className=\"text-lg font-bold text-gray-900\">\n              {price.toFixed(2)} DH\n            </span>\n          )}\n        </div>\n        \n        <div className=\"mt-4\">\n          <Link \n            to={`/products/${slug}`} \n            className=\"btn-primary w-full text-center\"\n          >\n            Voir le produit\n          </Link>\n        </div>\n      </div>\n    </motion.div>\n  );\n};\n\nexport default ProductCard;\n\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,MAAM,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEvC,MAAMC,WAAW,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EACnC;EACA,MAAM;IACJC,EAAE;IACFC,IAAI;IACJC,IAAI;IACJC,WAAW;IACXC,KAAK;IACLC,UAAU;IACVC,SAAS;IACTC,mBAAmB;IACnBC;EACF,CAAC,GAAGT,OAAO;;EAEX;EACA,MAAMU,QAAQ,GAAGH,SAAS,IAAI,uDAAuD;EAErF,oBACEX,OAAA,CAACF,MAAM,CAACiB,GAAG;IACTC,SAAS,EAAC,wDAAwD;IAClEC,UAAU,EAAE;MAAEC,CAAC,EAAE,CAAC;IAAE,CAAE;IACtBC,UAAU,EAAE;MAAEC,QAAQ,EAAE;IAAI,CAAE;IAAAC,QAAA,gBAE9BrB,OAAA,CAACH,IAAI;MAACyB,EAAE,EAAE,aAAaf,IAAI,IAAIF,EAAE,EAAG;MAACW,SAAS,EAAC,gBAAgB;MAAAK,QAAA,GAC5DT,mBAAmB,iBAClBZ,OAAA;QAAKgB,SAAS,EAAC,kFAAkF;QAAAK,QAAA,GAAC,GAC/F,EAACE,IAAI,CAACC,KAAK,CAACZ,mBAAmB,CAAC,EAAC,GACpC;MAAA;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CACN,eACD5B,OAAA;QACE6B,GAAG,EAAEf,QAAS;QACdgB,GAAG,EAAExB,IAAK;QACVU,SAAS,EAAC;MAAmC;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAEP5B,OAAA;MAAKgB,SAAS,EAAC,KAAK;MAAAK,QAAA,gBAClBrB,OAAA;QAAKgB,SAAS,EAAC,4BAA4B;QAAAK,QAAA,EACxC,CAAAR,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEP,IAAI,KAAI;MAAW;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B,CAAC,eAEN5B,OAAA,CAACH,IAAI;QAACyB,EAAE,EAAE,aAAaf,IAAI,EAAG;QAACS,SAAS,EAAC,OAAO;QAAAK,QAAA,eAC9CrB,OAAA;UAAIgB,SAAS,EAAC,8EAA8E;UAAAK,QAAA,EACzFf;QAAI;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAEP5B,OAAA;QAAGgB,SAAS,EAAC,yCAAyC;QAAAK,QAAA,EACnDb;MAAW;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC,eAEJ5B,OAAA;QAAKgB,SAAS,EAAC,wBAAwB;QAAAK,QAAA,EACpCX,UAAU,gBACTV,OAAA,CAAAE,SAAA;UAAAmB,QAAA,gBACErB,OAAA;YAAMgB,SAAS,EAAC,oCAAoC;YAAAK,QAAA,GACjDX,UAAU,CAACqB,OAAO,CAAC,CAAC,CAAC,EAAC,KACzB;UAAA;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACP5B,OAAA;YAAMgB,SAAS,EAAC,yCAAyC;YAAAK,QAAA,GACtDZ,KAAK,CAACsB,OAAO,CAAC,CAAC,CAAC,EAAC,KACpB;UAAA;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA,eACP,CAAC,gBAEH5B,OAAA;UAAMgB,SAAS,EAAC,iCAAiC;UAAAK,QAAA,GAC9CZ,KAAK,CAACsB,OAAO,CAAC,CAAC,CAAC,EAAC,KACpB;QAAA;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MACP;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEN5B,OAAA;QAAKgB,SAAS,EAAC,MAAM;QAAAK,QAAA,eACnBrB,OAAA,CAACH,IAAI;UACHyB,EAAE,EAAE,aAAaf,IAAI,EAAG;UACxBS,SAAS,EAAC,gCAAgC;UAAAK,QAAA,EAC3C;QAED;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEjB,CAAC;AAACI,EAAA,GA/EI7B,WAAW;AAiFjB,eAAeA,WAAW;AAAC,IAAA6B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}