import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, Link } from 'react-router-dom';
import { useData } from '../context/DataContext';
import ProductCard from '../components/products/ProductCard';

const CategoryPage = () => {
  const { slug } = useParams();
  const { state } = useData();
  const { categories, products } = state;
  
  const [category, setCategory] = useState(null);
  const [categoryProducts, setCategoryProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [sortBy, setSortBy] = useState('name');
  const [sortOrder, setSortOrder] = useState('asc');

  useEffect(() => {
    loadCategoryAndProducts();
  }, [slug, categories, products]);

  const loadCategoryAndProducts = () => {
    setLoading(true);

    // Chercher la catégorie par slug
    const foundCategory = categories.find(cat => cat.slug === slug);
    
    if (foundCategory) {
      setCategory(foundCategory);
      
      // Filtrer les produits de cette catégorie
      const filteredProducts = products.filter(product => 
        product.category_id === foundCategory.id
      );
      setCategoryProducts(filteredProducts);
    } else {
      setCategory(null);
      setCategoryProducts([]);
    }
    
    setLoading(false);
  };

  // Trier les produits
  const sortedProducts = [...categoryProducts].sort((a, b) => {
    let aValue = a[sortBy];
    let bValue = b[sortBy];
    
    if (sortBy === 'price') {
      aValue = parseFloat(aValue);
      bValue = parseFloat(bValue);
    }
    
    if (sortOrder === 'asc') {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });

  const handleSortChange = (newSortBy) => {
    if (sortBy === newSortBy) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(newSortBy);
      setSortOrder('asc');
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
        </div>
      </div>
    );
  }

  if (!category) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Catégorie non trouvée</h1>
          <p className="text-gray-600 mb-6">La catégorie demandée n'existe pas.</p>
          <Link 
            to="/products" 
            className="btn-primary"
          >
            Voir tous les produits
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* En-tête de la catégorie */}
      <div className="mb-8">
        <nav className="text-sm breadcrumbs mb-4">
          <Link to="/" className="text-gray-500 hover:text-primary-600">Accueil</Link>
          <span className="mx-2 text-gray-400">/</span>
          <Link to="/products" className="text-gray-500 hover:text-primary-600">Produits</Link>
          <span className="mx-2 text-gray-400">/</span>
          <span className="text-gray-900">{category.name}</span>
        </nav>
        
        <div className={`p-6 rounded-lg ${category.color || 'bg-gray-100'} mb-6`}>
          <h1 className={`text-3xl font-bold mb-2 ${category.textColor || 'text-gray-900'}`}>
            {category.name}
          </h1>
          {category.description && (
            <p className={`text-lg ${category.textColor || 'text-gray-700'}`}>
              {category.description}
            </p>
          )}
        </div>
      </div>

      {/* Barre de tri et filtres */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
        <div className="flex items-center space-x-4">
          <span className="text-gray-600">
            {sortedProducts.length} produit{sortedProducts.length > 1 ? 's' : ''}
          </span>
        </div>
        
        <div className="flex items-center space-x-4">
          <span className="text-sm text-gray-600">Trier par:</span>
          <button
            onClick={() => handleSortChange('name')}
            className={`text-sm px-3 py-1 rounded ${
              sortBy === 'name' 
                ? 'bg-primary-600 text-white' 
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            Nom {sortBy === 'name' && (sortOrder === 'asc' ? '↑' : '↓')}
          </button>
          <button
            onClick={() => handleSortChange('price')}
            className={`text-sm px-3 py-1 rounded ${
              sortBy === 'price' 
                ? 'bg-primary-600 text-white' 
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            Prix {sortBy === 'price' && (sortOrder === 'asc' ? '↑' : '↓')}
          </button>
        </div>
      </div>

      {/* Grille des produits */}
      {sortedProducts.length > 0 ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {sortedProducts.map((product) => (
            <ProductCard key={product.id} product={product} />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8V4a1 1 0 00-1-1H7a1 1 0 00-1 1v1m8 0V4.5" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Aucun produit disponible</h3>
          <p className="text-gray-600 mb-4">
            L'administrateur n'a pas encore ajouté de produits dans cette catégorie.
          </p>
          <p className="text-sm text-gray-500 mb-6">
            🔗 Les produits sont gérés via l'interface d'administration Laravel
          </p>
          <Link 
            to="/products" 
            className="btn-primary"
          >
            Voir tous les produits
          </Link>
        </div>
      )}
    </div>
  );
};

export default CategoryPage;
