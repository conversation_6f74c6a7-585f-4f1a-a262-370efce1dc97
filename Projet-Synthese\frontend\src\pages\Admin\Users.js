import React, { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import { FaEdit, FaTrash, Fa<PERSON>ye, FaUserShield, FaUser } from 'react-icons/fa';
import useSafeAuth from '../../hooks/useSafeAuth';

const Users = () => {
  const { state: authState } = useSafeAuth();
  const [users, setUsers] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    try {
      setIsLoading(true);
      // TODO: Implémenter l'appel API pour récupérer les utilisateurs
      // const response = await userService.getAllUsers(authState.token);
      // setUsers(response.data);
      
      // Données de test temporaires
      setUsers([
        {
          id: 1,
          name: '<PERSON><PERSON>',
          email: '<EMAIL>',
          role: 'admin',
          created_at: '2024-01-01T00:00:00Z',
          email_verified_at: '2024-01-01T00:00:00Z'
        },
        {
          id: 2,
          name: '<PERSON> <PERSON>ois',
          email: '<EMAIL>',
          role: 'client',
          created_at: '2024-01-15T00:00:00Z',
          email_verified_at: '2024-01-15T00:00:00Z'
        },
        {
          id: 3,
          name: 'Ahmed Hassan',
          email: '<EMAIL>',
          role: 'client',
          created_at: '2024-02-01T00:00:00Z',
          email_verified_at: '2024-02-01T00:00:00Z'
        },
        {
          id: 4,
          name: 'Sophie Martin',
          email: '<EMAIL>',
          role: 'client',
          created_at: '2024-02-10T00:00:00Z',
          email_verified_at: '2024-02-10T00:00:00Z'
        },
        {
          id: 5,
          name: 'Jean Dupont',
          email: '<EMAIL>',
          role: 'client',
          created_at: '2024-02-15T00:00:00Z',
          email_verified_at: null
        },
        {
          id: 6,
          name: 'Manager Store',
          email: '<EMAIL>',
          role: 'manager',
          created_at: '2024-01-05T00:00:00Z',
          email_verified_at: '2024-01-05T00:00:00Z'
        },
        {
          id: 7,
          name: 'Emma Wilson',
          email: '<EMAIL>',
          role: 'client',
          created_at: '2024-03-01T00:00:00Z',
          email_verified_at: '2024-03-01T00:00:00Z'
        },
        {
          id: 8,
          name: 'Lucas Bernard',
          email: '<EMAIL>',
          role: 'client',
          created_at: '2024-03-05T00:00:00Z',
          email_verified_at: null
        }
      ]);
    } catch (error) {
      toast.error('Erreur lors du chargement des utilisateurs');
    } finally {
      setIsLoading(false);
    }
  };

  const getRoleIcon = (role) => {
    if (role === 'admin') return <FaUserShield className="text-red-500" />;
    if (role === 'manager') return <FaUserShield className="text-orange-500" />;
    return <FaUser className="text-blue-500" />;
  };

  const getRoleBadge = (role) => {
    const baseClasses = "px-2 py-1 text-xs rounded-full";
    if (role === 'admin') {
      return <span className={`${baseClasses} bg-red-100 text-red-800`}>Admin</span>;
    }
    if (role === 'manager') {
      return <span className={`${baseClasses} bg-orange-100 text-orange-800`}>Manager</span>;
    }
    return <span className={`${baseClasses} bg-blue-100 text-blue-800`}>Client</span>;
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('fr-FR');
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">Gestion des Utilisateurs</h1>
      </div>

      {/* Tableau des utilisateurs */}
      <div className="bg-white shadow-sm rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Utilisateur
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Email
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Rôle
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date d'inscription
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Statut
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {isLoading ? (
                <tr>
                  <td colSpan="6" className="px-6 py-4 text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
                  </td>
                </tr>
              ) : users.length === 0 ? (
                <tr>
                  <td colSpan="6" className="px-6 py-4 text-center text-gray-500">
                    Aucun utilisateur trouvé
                  </td>
                </tr>
              ) : (
                users.map((user) => (
                  <tr key={user.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                            {getRoleIcon(user.role)}
                          </div>
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">{user.name}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{user.email}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getRoleBadge(user.role)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{formatDate(user.created_at)}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {user.email_verified_at ? (
                        <span className="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">
                          Vérifié
                        </span>
                      ) : (
                        <span className="px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded-full">
                          Non vérifié
                        </span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <button
                          className="text-primary-600 hover:text-primary-900"
                          title="Voir"
                        >
                          <FaEye />
                        </button>
                        <button
                          className="text-blue-600 hover:text-blue-900"
                          title="Modifier"
                        >
                          <FaEdit />
                        </button>
                        {user.role !== 'admin' && (
                          <button
                            className="text-red-600 hover:text-red-900"
                            title="Supprimer"
                          >
                            <FaTrash />
                          </button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default Users;
