{"ast": null, "code": "import axios from 'axios';\nconst API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';\n\n// Service pour gérer les statistiques du dashboard\nexport const dashboardService = {\n  // Récupérer les statistiques générales\n  getStats: async token => {\n    try {\n      const response = await axios.get(`${API_URL}/api/dashboard/stats`, {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Erreur lors de la récupération des statistiques, utilisation des données locales:', error);\n\n      // Retourner des données simulées en cas d'erreur\n      return {\n        status: 'success',\n        data: {\n          overview: {\n            total_orders: 125,\n            total_revenue: 15420.50,\n            total_users: 89,\n            total_products: 45,\n            orders_last_30_days: 32,\n            revenue_last_30_days: 4250.00,\n            new_users_last_30_days: 15,\n            active_promotions: 3,\n            low_stock_products: 2\n          },\n          charts: {\n            monthly_revenue: [{\n              month: 'Jan',\n              revenue: 2100\n            }, {\n              month: 'Fév',\n              revenue: 2800\n            }, {\n              month: 'Mar',\n              revenue: 3200\n            }, {\n              month: 'Avr',\n              revenue: 2900\n            }, {\n              month: 'Mai',\n              revenue: 3800\n            }, {\n              month: 'Jun',\n              revenue: 4250\n            }],\n            popular_categories: [{\n              name: 'Produits Grillés',\n              total_sold: 145\n            }, {\n              name: 'Non Grillés',\n              total_sold: 98\n            }, {\n              name: 'Fromages',\n              total_sold: 67\n            }, {\n              name: 'Boissons',\n              total_sold: 89\n            }]\n          },\n          top_products: [{\n            name: 'Poulet Grillé Entier',\n            total_sold: 45\n          }, {\n            name: 'Salade César',\n            total_sold: 32\n          }, {\n            name: 'Plateau de Fromages',\n            total_sold: 28\n          }, {\n            name: 'Brochettes de Bœuf',\n            total_sold: 25\n          }, {\n            name: 'Jus d\\'Orange Frais',\n            total_sold: 22\n          }]\n        }\n      };\n    }\n  },\n  // Récupérer les activités récentes\n  getRecentActivity: async token => {\n    try {\n      const response = await axios.get(`${API_URL}/api/dashboard/activity`, {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Erreur lors de la récupération des activités récentes:', error);\n      throw error;\n    }\n  }\n};\nexport default dashboardService;", "map": {"version": 3, "names": ["axios", "API_URL", "process", "env", "REACT_APP_API_URL", "dashboardService", "getStats", "token", "response", "get", "headers", "Authorization", "data", "error", "console", "status", "overview", "total_orders", "total_revenue", "total_users", "total_products", "orders_last_30_days", "revenue_last_30_days", "new_users_last_30_days", "active_promotions", "low_stock_products", "charts", "monthly_revenue", "month", "revenue", "popular_categories", "name", "total_sold", "top_products", "getRecentActivity"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet-Synthese/frontend/src/services/api/dashboardService.js"], "sourcesContent": ["import axios from 'axios';\n\nconst API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';\n\n// Service pour gérer les statistiques du dashboard\nexport const dashboardService = {\n  // Récupérer les statistiques générales\n  getStats: async (token) => {\n    try {\n      const response = await axios.get(`${API_URL}/api/dashboard/stats`, {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Erreur lors de la récupération des statistiques, utilisation des données locales:', error);\n\n      // Retourner des données simulées en cas d'erreur\n      return {\n        status: 'success',\n        data: {\n          overview: {\n            total_orders: 125,\n            total_revenue: 15420.50,\n            total_users: 89,\n            total_products: 45,\n            orders_last_30_days: 32,\n            revenue_last_30_days: 4250.00,\n            new_users_last_30_days: 15,\n            active_promotions: 3,\n            low_stock_products: 2\n          },\n          charts: {\n            monthly_revenue: [\n              { month: 'Jan', revenue: 2100 },\n              { month: 'Fév', revenue: 2800 },\n              { month: 'Mar', revenue: 3200 },\n              { month: 'Avr', revenue: 2900 },\n              { month: 'Mai', revenue: 3800 },\n              { month: 'Jun', revenue: 4250 }\n            ],\n            popular_categories: [\n              { name: 'Produits Grillés', total_sold: 145 },\n              { name: 'Non Grillés', total_sold: 98 },\n              { name: 'Fromages', total_sold: 67 },\n              { name: 'Boissons', total_sold: 89 }\n            ]\n          },\n          top_products: [\n            { name: 'Poulet Grillé Entier', total_sold: 45 },\n            { name: 'Salade César', total_sold: 32 },\n            { name: 'Plateau de Fromages', total_sold: 28 },\n            { name: 'Brochettes de Bœuf', total_sold: 25 },\n            { name: 'Jus d\\'Orange Frais', total_sold: 22 }\n          ]\n        }\n      };\n    }\n  },\n\n  // Récupérer les activités récentes\n  getRecentActivity: async (token) => {\n    try {\n      const response = await axios.get(`${API_URL}/api/dashboard/activity`, {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Erreur lors de la récupération des activités récentes:', error);\n      throw error;\n    }\n  }\n};\n\nexport default dashboardService;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;;AAExE;AACA,OAAO,MAAMC,gBAAgB,GAAG;EAC9B;EACAC,QAAQ,EAAE,MAAOC,KAAK,IAAK;IACzB,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMR,KAAK,CAACS,GAAG,CAAC,GAAGR,OAAO,sBAAsB,EAAE;QACjES,OAAO,EAAE;UACPC,aAAa,EAAE,UAAUJ,KAAK;QAChC;MACF,CAAC,CAAC;MACF,OAAOC,QAAQ,CAACI,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,mFAAmF,EAAEA,KAAK,CAAC;;MAEzG;MACA,OAAO;QACLE,MAAM,EAAE,SAAS;QACjBH,IAAI,EAAE;UACJI,QAAQ,EAAE;YACRC,YAAY,EAAE,GAAG;YACjBC,aAAa,EAAE,QAAQ;YACvBC,WAAW,EAAE,EAAE;YACfC,cAAc,EAAE,EAAE;YAClBC,mBAAmB,EAAE,EAAE;YACvBC,oBAAoB,EAAE,OAAO;YAC7BC,sBAAsB,EAAE,EAAE;YAC1BC,iBAAiB,EAAE,CAAC;YACpBC,kBAAkB,EAAE;UACtB,CAAC;UACDC,MAAM,EAAE;YACNC,eAAe,EAAE,CACf;cAAEC,KAAK,EAAE,KAAK;cAAEC,OAAO,EAAE;YAAK,CAAC,EAC/B;cAAED,KAAK,EAAE,KAAK;cAAEC,OAAO,EAAE;YAAK,CAAC,EAC/B;cAAED,KAAK,EAAE,KAAK;cAAEC,OAAO,EAAE;YAAK,CAAC,EAC/B;cAAED,KAAK,EAAE,KAAK;cAAEC,OAAO,EAAE;YAAK,CAAC,EAC/B;cAAED,KAAK,EAAE,KAAK;cAAEC,OAAO,EAAE;YAAK,CAAC,EAC/B;cAAED,KAAK,EAAE,KAAK;cAAEC,OAAO,EAAE;YAAK,CAAC,CAChC;YACDC,kBAAkB,EAAE,CAClB;cAAEC,IAAI,EAAE,kBAAkB;cAAEC,UAAU,EAAE;YAAI,CAAC,EAC7C;cAAED,IAAI,EAAE,aAAa;cAAEC,UAAU,EAAE;YAAG,CAAC,EACvC;cAAED,IAAI,EAAE,UAAU;cAAEC,UAAU,EAAE;YAAG,CAAC,EACpC;cAAED,IAAI,EAAE,UAAU;cAAEC,UAAU,EAAE;YAAG,CAAC;UAExC,CAAC;UACDC,YAAY,EAAE,CACZ;YAAEF,IAAI,EAAE,sBAAsB;YAAEC,UAAU,EAAE;UAAG,CAAC,EAChD;YAAED,IAAI,EAAE,cAAc;YAAEC,UAAU,EAAE;UAAG,CAAC,EACxC;YAAED,IAAI,EAAE,qBAAqB;YAAEC,UAAU,EAAE;UAAG,CAAC,EAC/C;YAAED,IAAI,EAAE,oBAAoB;YAAEC,UAAU,EAAE;UAAG,CAAC,EAC9C;YAAED,IAAI,EAAE,qBAAqB;YAAEC,UAAU,EAAE;UAAG,CAAC;QAEnD;MACF,CAAC;IACH;EACF,CAAC;EAED;EACAE,iBAAiB,EAAE,MAAO3B,KAAK,IAAK;IAClC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMR,KAAK,CAACS,GAAG,CAAC,GAAGR,OAAO,yBAAyB,EAAE;QACpES,OAAO,EAAE;UACPC,aAAa,EAAE,UAAUJ,KAAK;QAChC;MACF,CAAC,CAAC;MACF,OAAOC,QAAQ,CAACI,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wDAAwD,EAAEA,KAAK,CAAC;MAC9E,MAAMA,KAAK;IACb;EACF;AACF,CAAC;AAED,eAAeR,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}