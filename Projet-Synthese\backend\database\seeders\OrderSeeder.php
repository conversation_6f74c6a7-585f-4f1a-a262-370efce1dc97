<?php

namespace Database\Seeders;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\User;
use Illuminate\Database\Seeder;

class OrderSeeder extends Seeder
{
    public function run(): void
    {
        User::all()->each(function ($user) {
            Order::factory()
                ->count(fake()->numberBetween(1, 5))
                ->create(['user_id' => $user->id])
                ->each(function ($order) {
                    OrderItem::factory()
                        ->count(fake()->numberBetween(1, 4))
                        ->create(['order_id' => $order->id]);
                });
        });
    }
}