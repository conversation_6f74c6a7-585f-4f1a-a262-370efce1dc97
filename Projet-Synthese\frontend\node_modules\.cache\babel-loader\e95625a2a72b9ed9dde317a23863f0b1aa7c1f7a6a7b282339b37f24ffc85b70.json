{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Projet-Synthese\\\\frontend\\\\src\\\\components\\\\admin\\\\AdminSidebar.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminSidebar = () => {\n  _s();\n  const location = useLocation();\n  const isActive = path => {\n    return location.pathname === path || location.pathname.startsWith(`${path}/`);\n  };\n  const menuItems = [{\n    path: '/admin/dashboard',\n    label: 'Tableau de bord',\n    icon: 'M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6'\n  }, {\n    path: '/admin/products',\n    label: 'Produits',\n    icon: 'M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4'\n  }, {\n    path: '/admin/categories',\n    label: 'Catégories',\n    icon: 'M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01'\n  }, {\n    path: '/admin/orders',\n    label: 'Commandes',\n    icon: 'M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01'\n  }, {\n    path: '/admin/promotions',\n    label: 'Promotions',\n    icon: 'M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z'\n  }, {\n    path: '/admin/users',\n    label: 'Utilisateurs',\n    icon: 'M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white h-screen w-64 shadow-lg fixed left-0 top-0 z-10\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4 border-b\",\n      children: /*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-xl font-semibold text-green-600\",\n        children: \"Admin Panel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n      className: \"mt-6\",\n      children: /*#__PURE__*/_jsxDEV(\"ul\", {\n        children: menuItems.map(item => /*#__PURE__*/_jsxDEV(\"li\", {\n          className: \"mb-2\",\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: item.path,\n            className: `flex items-center px-6 py-3 text-gray-700 hover:bg-green-50 hover:text-green-600 transition-colors ${isActive(item.path) ? 'bg-green-50 text-green-600 border-r-4 border-green-600' : ''}`,\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              className: \"h-5 w-5 mr-3\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              stroke: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 1.5,\n                d: item.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 43,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 36,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: item.label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 30,\n            columnNumber: 15\n          }, this)\n        }, item.path, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 21,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminSidebar, \"pkHmaVRPskBaU4tMJuJJpV42k1I=\", false, function () {\n  return [useLocation];\n});\n_c = AdminSidebar;\nexport default AdminSidebar;\nvar _c;\n$RefreshReg$(_c, \"AdminSidebar\");", "map": {"version": 3, "names": ["React", "Link", "useLocation", "jsxDEV", "_jsxDEV", "AdminSidebar", "_s", "location", "isActive", "path", "pathname", "startsWith", "menuItems", "label", "icon", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "item", "to", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet-Synthese/frontend/src/components/admin/AdminSidebar.js"], "sourcesContent": ["import React from 'react';\nimport { Link, useLocation } from 'react-router-dom';\n\nconst AdminSidebar = () => {\n  const location = useLocation();\n\n  const isActive = (path) => {\n    return location.pathname === path || location.pathname.startsWith(`${path}/`);\n  };\n\n  const menuItems = [\n    { path: '/admin/dashboard', label: 'Tableau de bord', icon: 'M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6' },\n    { path: '/admin/products', label: 'Produits', icon: 'M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4' },\n    { path: '/admin/categories', label: 'Catégories', icon: 'M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01' },\n    { path: '/admin/orders', label: 'Commandes', icon: 'M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01' },\n    { path: '/admin/promotions', label: 'Promotions', icon: 'M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z' },\n    { path: '/admin/users', label: 'Utilisateurs', icon: 'M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z' }\n  ];\n\n  return (\n    <div className=\"bg-white h-screen w-64 shadow-lg fixed left-0 top-0 z-10\">\n      <div className=\"p-4 border-b\">\n        <h2 className=\"text-xl font-semibold text-green-600\">Admin Panel</h2>\n      </div>\n\n      <nav className=\"mt-6\">\n        <ul>\n          {menuItems.map((item) => (\n            <li key={item.path} className=\"mb-2\">\n              <Link\n                to={item.path}\n                className={`flex items-center px-6 py-3 text-gray-700 hover:bg-green-50 hover:text-green-600 transition-colors ${\n                  isActive(item.path) ? 'bg-green-50 text-green-600 border-r-4 border-green-600' : ''\n                }`}\n              >\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  className=\"h-5 w-5 mr-3\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke=\"currentColor\"\n                >\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d={item.icon} />\n                </svg>\n                <span>{item.label}</span>\n              </Link>\n            </li>\n          ))}\n        </ul>\n      </nav>\n    </div>\n  );\n};\n\nexport default AdminSidebar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAMC,QAAQ,GAAGL,WAAW,CAAC,CAAC;EAE9B,MAAMM,QAAQ,GAAIC,IAAI,IAAK;IACzB,OAAOF,QAAQ,CAACG,QAAQ,KAAKD,IAAI,IAAIF,QAAQ,CAACG,QAAQ,CAACC,UAAU,CAAC,GAAGF,IAAI,GAAG,CAAC;EAC/E,CAAC;EAED,MAAMG,SAAS,GAAG,CAChB;IAAEH,IAAI,EAAE,kBAAkB;IAAEI,KAAK,EAAE,iBAAiB;IAAEC,IAAI,EAAE;EAAmJ,CAAC,EAChN;IAAEL,IAAI,EAAE,iBAAiB;IAAEI,KAAK,EAAE,UAAU;IAAEC,IAAI,EAAE;EAAkE,CAAC,EACvH;IAAEL,IAAI,EAAE,mBAAmB;IAAEI,KAAK,EAAE,YAAY;IAAEC,IAAI,EAAE;EAAmM,CAAC,EAC5P;IAAEL,IAAI,EAAE,eAAe;IAAEI,KAAK,EAAE,WAAW;IAAEC,IAAI,EAAE;EAAkK,CAAC,EACtN;IAAEL,IAAI,EAAE,mBAAmB;IAAEI,KAAK,EAAE,YAAY;IAAEC,IAAI,EAAE;EAA+H,CAAC,EACxL;IAAEL,IAAI,EAAE,cAAc;IAAEI,KAAK,EAAE,cAAc;IAAEC,IAAI,EAAE;EAAgH,CAAC,CACvK;EAED,oBACEV,OAAA;IAAKW,SAAS,EAAC,0DAA0D;IAAAC,QAAA,gBACvEZ,OAAA;MAAKW,SAAS,EAAC,cAAc;MAAAC,QAAA,eAC3BZ,OAAA;QAAIW,SAAS,EAAC,sCAAsC;QAAAC,QAAA,EAAC;MAAW;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClE,CAAC,eAENhB,OAAA;MAAKW,SAAS,EAAC,MAAM;MAAAC,QAAA,eACnBZ,OAAA;QAAAY,QAAA,EACGJ,SAAS,CAACS,GAAG,CAAEC,IAAI,iBAClBlB,OAAA;UAAoBW,SAAS,EAAC,MAAM;UAAAC,QAAA,eAClCZ,OAAA,CAACH,IAAI;YACHsB,EAAE,EAAED,IAAI,CAACb,IAAK;YACdM,SAAS,EAAE,sGACTP,QAAQ,CAACc,IAAI,CAACb,IAAI,CAAC,GAAG,wDAAwD,GAAG,EAAE,EAClF;YAAAO,QAAA,gBAEHZ,OAAA;cACEoB,KAAK,EAAC,4BAA4B;cAClCT,SAAS,EAAC,cAAc;cACxBU,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnBC,MAAM,EAAC,cAAc;cAAAX,QAAA,eAErBZ,OAAA;gBAAMwB,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,GAAI;gBAACC,CAAC,EAAET,IAAI,CAACR;cAAK;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClF,CAAC,eACNhB,OAAA;cAAAY,QAAA,EAAOM,IAAI,CAACT;YAAK;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB;QAAC,GAjBAE,IAAI,CAACb,IAAI;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAkBd,CACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACd,EAAA,CAjDID,YAAY;EAAA,QACCH,WAAW;AAAA;AAAA8B,EAAA,GADxB3B,YAAY;AAmDlB,eAAeA,YAAY;AAAC,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}