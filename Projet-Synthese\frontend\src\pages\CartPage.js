import React from 'react';
import { Link } from 'react-router-dom';
import { useCart } from '../context/CartContext';
import CartItem from '../components/cart/CartItem';
import CartSummary from '../components/cart/CartSummary';

const CartPage = () => {
  const { state } = useCart();
  
  // Si le panier est vide
  if (state.items.length === 0) {
    return (
      <div className="container mx-auto px-4 py-12">
        <h1 className="text-3xl font-bold mb-8">Votre panier</h1>
        <div className="bg-white rounded-lg shadow-md p-8 text-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
          </svg>
          <h2 className="text-xl font-semibold mb-4">Votre panier est vide</h2>
          <p className="text-gray-600 mb-6">Découvrez nos produits et commencez à remplir votre panier</p>
          <Link to="/products" className="bg-green-600 text-white py-2 px-6 rounded-md hover:bg-green-700 transition">
            Parcourir les produits
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-12">
      <h1 className="text-3xl font-bold mb-8">Votre panier</h1>
      
      <div className="lg:flex lg:gap-8">
        {/* Liste des produits */}
        <div className="lg:w-2/3">
          <div className="bg-white rounded-lg shadow-md overflow-hidden mb-6 lg:mb-0">
            <div className="p-6">
              <div className="hidden md:grid md:grid-cols-12 text-sm font-medium text-gray-500 mb-4">
                <div className="md:col-span-6">Produit</div>
                <div className="md:col-span-2 text-center">Prix</div>
                <div className="md:col-span-2 text-center">Quantité</div>
                <div className="md:col-span-2 text-right">Total</div>
              </div>
              
              <div className="divide-y">
                {state.items.map(item => (
                  <CartItem key={item.id} item={item} />
                ))}
              </div>
            </div>
          </div>
        </div>
        
        {/* Résumé de la commande */}
        <div className="lg:w-1/3">
          <CartSummary />
          
          <div className="mt-6 flex flex-col space-y-4">
            <Link 
              to="/checkout" 
              className="bg-green-600 text-white py-3 px-6 rounded-md text-center font-medium hover:bg-green-700 transition"
            >
              Passer à la caisse
            </Link>
            
            <Link 
              to="/products" 
              className="text-green-600 py-3 px-6 rounded-md text-center border border-green-600 font-medium hover:bg-green-50 transition"
            >
              Continuer vos achats
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CartPage;


