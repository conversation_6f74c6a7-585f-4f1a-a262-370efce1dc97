{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Projet-Synthese\\\\frontend\\\\src\\\\pages\\\\HomePage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { useData } from '../context/DataContext';\nimport ProductCard from '../components/products/ProductCard';\nimport ActivePromotions from '../components/promotions/ActivePromotions';\nimport { FaArrowRight, FaUtensils, FaPizzaSlice, FaCheese, FaCocktail } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HomePage = () => {\n  _s();\n  const {\n    state\n  } = useData();\n  const {\n    products: allProducts,\n    categories: allCategories,\n    loading: dataLoading\n  } = state;\n  const [featuredProducts, setFeaturedProducts] = useState([]);\n  const [newProducts, setNewProducts] = useState([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [isUsingTestData, setIsUsingTestData] = useState(false);\n  useEffect(() => {\n    const fetchProducts = async () => {\n      try {\n        // Utiliser les données du contexte global en priorité\n        if (allProducts && allProducts.length > 0) {\n          setFeaturedProducts(allProducts.slice(0, 4));\n          setNewProducts(allProducts.slice(-4));\n          setIsLoading(false);\n          return;\n        }\n\n        // Fallback: essayer l'API (mais sans axios direct)\n        console.log('Tentative de chargement depuis l\\'API...');\n\n        // Cette section ne sera plus utilisée car on utilise le contexte\n        setIsLoading(false);\n      } catch (error) {\n        console.error('Erreur lors du chargement des produits:', error);\n\n        // Plus de données de test - tout vient de Laravel\n        console.log('❌ Aucune donnée disponible - Laravel requis');\n        setFeaturedProducts([]);\n        setNewProducts([]);\n        setIsUsingTestData(false);\n        setIsLoading(false);\n      }\n    };\n\n    // Vérifier d'abord si on a des données du contexte\n    if (allProducts && allProducts.length > 0) {\n      setFeaturedProducts(allProducts.slice(0, 4));\n      setNewProducts(allProducts.slice(-4));\n      setIsLoading(false);\n      console.log('Utilisation des données du contexte global');\n    } else {\n      fetchProducts();\n    }\n  }, [allProducts]);\n  const categories = [{\n    id: 1,\n    name: \"Produits à Griller\",\n    description: \"Viandes, saucisses et brochettes prêtes à griller\",\n    icon: /*#__PURE__*/_jsxDEV(FaUtensils, {\n      className: \"text-4xl text-yummy-grilled\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 13\n    }, this),\n    color: \"bg-yummy-grilled/10 border-yummy-grilled/20\",\n    textColor: \"text-yummy-grilled\",\n    path: \"/produits-grilles\"\n  }, {\n    id: 2,\n    name: \"Produits Non-Grillés\",\n    description: \"Salades, sandwichs et plats préparés\",\n    icon: /*#__PURE__*/_jsxDEV(FaPizzaSlice, {\n      className: \"text-4xl text-yummy-nongrilled\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 13\n    }, this),\n    color: \"bg-yummy-nongrilled/10 border-yummy-nongrilled/20\",\n    textColor: \"text-yummy-nongrilled\",\n    path: \"/produits-non-grilles\"\n  }, {\n    id: 3,\n    name: \"Fromages\",\n    description: \"Fromages locaux et importés de qualité\",\n    icon: /*#__PURE__*/_jsxDEV(FaCheese, {\n      className: \"text-4xl text-yummy-cheese\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 13\n    }, this),\n    color: \"bg-yummy-cheese/10 border-yummy-cheese/20\",\n    textColor: \"text-yummy-cheese\",\n    path: \"/fromages\"\n  }, {\n    id: 4,\n    name: \"Boissons\",\n    description: \"Boissons fraîches, chaudes et alcoolisées\",\n    icon: /*#__PURE__*/_jsxDEV(FaCocktail, {\n      className: \"text-4xl text-yummy-drinks\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 13\n    }, this),\n    color: \"bg-yummy-drinks/10 border-yummy-drinks/20\",\n    textColor: \"text-yummy-drinks\",\n    path: \"/boissons\"\n  }];\n\n  // Composant Hero Section\n  const HeroSection = () => /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"relative bg-gradient-to-r from-green-50 to-green-100 py-16 md:py-24\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 gap-8 items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(motion.h1, {\n            className: \"text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900\",\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.5\n            },\n            children: [\"Des produits frais pour \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-primary-600\",\n              children: \"tous vos repas\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 39\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n            className: \"text-lg text-gray-700\",\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.5,\n              delay: 0.1\n            },\n            children: \"D\\xE9couvrez notre s\\xE9lection de produits \\xE0 griller, non-grill\\xE9s, fromages et boissons de qualit\\xE9 pour satisfaire toutes vos envies gourmandes.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"flex flex-wrap gap-4\",\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.5,\n              delay: 0.2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/products\",\n              className: \"btn-primary\",\n              children: \"D\\xE9couvrir nos produits\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/about\",\n              className: \"btn-outline\",\n              children: \"En savoir plus\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"relative\",\n          initial: {\n            opacity: 0,\n            scale: 0.9\n          },\n          animate: {\n            opacity: 1,\n            scale: 1\n          },\n          transition: {\n            duration: 0.5\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: \"https://images.unsplash.com/photo-1504674900247-0877df9cc836?ixlib=rb-1.2.1&auto=format&fit=crop&w=1200&q=80\",\n            alt: \"Produits frais\",\n            className: \"rounded-lg shadow-xl\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute -bottom-6 -right-6 bg-white p-4 rounded-lg shadow-lg\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-green-100 p-2 rounded-full\",\n                children: /*#__PURE__*/_jsxDEV(FaUtensils, {\n                  className: \"text-primary-600 text-xl\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 149,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 148,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"font-semibold text-gray-900\",\n                  children: \"Livraison rapide\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 152,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600\",\n                  children: \"En 24h chez vous\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 153,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 98,\n    columnNumber: 5\n  }, this);\n\n  // Composant Category List\n  const CategoryList = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mt-8\",\n    children: categories.map(category => /*#__PURE__*/_jsxDEV(motion.div, {\n      className: `card p-6 border ${category.color} hover:shadow-lg transition-shadow`,\n      whileHover: {\n        y: -5\n      },\n      transition: {\n        duration: 0.3\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col items-center text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: `p-4 rounded-full ${category.color} mb-4`,\n          children: category.icon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: `text-xl font-semibold mb-2 ${category.textColor}`,\n          children: category.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mb-4\",\n          children: category.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: category.path,\n          className: `flex items-center ${category.textColor} font-medium hover:underline`,\n          children: [\"D\\xE9couvrir \", /*#__PURE__*/_jsxDEV(FaArrowRight, {\n            className: \"ml-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 11\n      }, this)\n    }, category.id, false, {\n      fileName: _jsxFileName,\n      lineNumber: 167,\n      columnNumber: 9\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 165,\n    columnNumber: 5\n  }, this);\n\n  // Composant Featured Products\n  const FeaturedProducts = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"mt-8\",\n    children: isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6\",\n      children: Array(4).fill(0).map((_, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card animate-pulse\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gray-300 aspect-square w-full\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4 space-y-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"h-4 bg-gray-300 rounded w-1/4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"h-6 bg-gray-300 rounded w-3/4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"h-4 bg-gray-300 rounded w-1/2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 15\n        }, this)]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 199,\n      columnNumber: 9\n    }, this) : featuredProducts.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6\",\n      children: featuredProducts.map(product => /*#__PURE__*/_jsxDEV(ProductCard, {\n        product: product\n      }, product.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 214,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 212,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-12\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-50 rounded-lg p-8 max-w-2xl mx-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-gray-400 mb-4\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"mx-auto h-16 w-16\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            stroke: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 1,\n              d: \"M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8V4a1 1 0 00-1-1H7a1 1 0 00-1 1v1m8 0V4.5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-xl font-semibold text-gray-900 mb-2\",\n          children: \"Aucun produit disponible\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mb-4\",\n          children: \"L'administrateur doit ajouter des produits pour qu'ils apparaissent ici.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-500\",\n          children: \"\\uD83D\\uDD17 Toutes les donn\\xE9es sont maintenant g\\xE9r\\xE9es via Laravel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 218,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 197,\n    columnNumber: 5\n  }, this);\n\n  // Composant Promo Section\n  const PromoSection = () => /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"py-16 bg-gradient-to-r from-secondary-500 to-secondary-600 text-white\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 gap-8 items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl md:text-4xl font-bold mb-4\",\n            children: \"Offre sp\\xE9ciale du moment\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-lg mb-6\",\n            children: \"Profitez de 15% de r\\xE9duction sur tous nos produits \\xE0 griller pour vos barbecues d'\\xE9t\\xE9 !\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-wrap gap-4\",\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/produits-grilles\",\n              className: \"bg-white text-secondary-600 px-6 py-3 rounded-full font-medium hover:bg-gray-100 transition-colors\",\n              children: \"En profiter maintenant\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: \"https://images.unsplash.com/photo-1544025162-d76694265947?ixlib=rb-1.2.1&auto=format&fit=crop&w=1200&q=80\",\n            alt: \"Barbecue\",\n            className: \"rounded-lg shadow-xl\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute top-0 right-0 bg-accent-500 text-white text-xl font-bold p-4 rounded-bl-lg rounded-tr-lg\",\n            children: \"-15%\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 242,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 241,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 240,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gradient-to-r from-green-600 to-green-700 text-white p-3\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto flex justify-between items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"h-5 w-5\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            stroke: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium\",\n            children: \"Mode D\\xE9monstration - Projet YUMMY\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"/admin/dashboard\",\n            className: \"bg-white text-green-600 px-4 py-1 rounded text-sm font-medium hover:bg-gray-100 transition-colors\",\n            children: \"\\uD83D\\uDD27 Dashboard Admin\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm opacity-75\",\n            children: \"Toutes les fonctionnalit\\xE9s sont accessibles\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 274,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 273,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(HeroSection, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 293,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-16 bg-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col md:flex-row justify-between items-center mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl font-bold text-gray-900\",\n            children: \"Nos cat\\xE9gories\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/products\",\n            className: \"flex items-center text-primary-600 font-medium hover:text-primary-700\",\n            children: [\"Voir tous les produits \", /*#__PURE__*/_jsxDEV(FaArrowRight, {\n              className: \"ml-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 38\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(CategoryList, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 296,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 295,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-16 bg-gray-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-3xl font-bold text-gray-900 mb-8\",\n          children: \"Produits en vedette\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FeaturedProducts, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 308,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 307,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(PromoSection, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 314,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-16 bg-gray-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-3xl font-bold text-gray-900 mb-8\",\n          children: \"Promotions en cours\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ActivePromotions, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 317,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 316,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-16 bg-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-3xl font-bold text-gray-900 mb-8\",\n          children: \"Nouveaux produits\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6\",\n          children: isLoading ? Array(4).fill(0).map((_, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card animate-pulse\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-300 aspect-square w-full\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-4 space-y-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"h-4 bg-gray-300 rounded w-1/4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"h-6 bg-gray-300 rounded w-3/4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"h-4 bg-gray-300 rounded w-1/2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 334,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 19\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 17\n          }, this)) : newProducts.map(product => /*#__PURE__*/_jsxDEV(ProductCard, {\n            product: product\n          }, product.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 340,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 324,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 323,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 271,\n    columnNumber: 5\n  }, this);\n};\n_s(HomePage, \"fW8VOQ2JbkaSsB8AAYp6DToti6o=\", false, function () {\n  return [useData];\n});\n_c = HomePage;\nexport default HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "motion", "useData", "ProductCard", "ActivePromotions", "FaArrowRight", "FaUtensils", "FaPizzaSlice", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FaCocktail", "jsxDEV", "_jsxDEV", "HomePage", "_s", "state", "products", "allProducts", "categories", "allCategories", "loading", "dataLoading", "featuredProducts", "setFeaturedProducts", "newProducts", "setNewProducts", "isLoading", "setIsLoading", "isUsingTestData", "setIsUsingTestData", "fetchProducts", "length", "slice", "console", "log", "error", "id", "name", "description", "icon", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "textColor", "path", "HeroSection", "children", "h1", "initial", "opacity", "y", "animate", "transition", "duration", "p", "delay", "div", "to", "scale", "src", "alt", "CategoryList", "map", "category", "whileHover", "FeaturedProducts", "Array", "fill", "_", "index", "product", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "PromoSection", "href", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet-Synthese/frontend/src/pages/HomePage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { useData } from '../context/DataContext';\nimport ProductCard from '../components/products/ProductCard';\nimport ActivePromotions from '../components/promotions/ActivePromotions';\nimport { FaArrowRight, FaUtensils, FaPizzaSlice, FaCheese, FaCocktail } from 'react-icons/fa';\n\nconst HomePage = () => {\n  const { state } = useData();\n  const { products: allProducts, categories: allCategories, loading: dataLoading } = state;\n\n  const [featuredProducts, setFeaturedProducts] = useState([]);\n  const [newProducts, setNewProducts] = useState([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [isUsingTestData, setIsUsingTestData] = useState(false);\n\n  useEffect(() => {\n    const fetchProducts = async () => {\n      try {\n        // Utiliser les données du contexte global en priorité\n        if (allProducts && allProducts.length > 0) {\n          setFeaturedProducts(allProducts.slice(0, 4));\n          setNewProducts(allProducts.slice(-4));\n          setIsLoading(false);\n          return;\n        }\n\n        // Fallback: essayer l'API (mais sans axios direct)\n        console.log('Tentative de chargement depuis l\\'API...');\n\n        // Cette section ne sera plus utilisée car on utilise le contexte\n        setIsLoading(false);\n      } catch (error) {\n        console.error('Erreur lors du chargement des produits:', error);\n\n        // Plus de données de test - tout vient de Laravel\n        console.log('❌ Aucune donnée disponible - Laravel requis');\n        setFeaturedProducts([]);\n        setNewProducts([]);\n        setIsUsingTestData(false);\n        setIsLoading(false);\n      }\n    };\n\n    // Vérifier d'abord si on a des données du contexte\n    if (allProducts && allProducts.length > 0) {\n      setFeaturedProducts(allProducts.slice(0, 4));\n      setNewProducts(allProducts.slice(-4));\n      setIsLoading(false);\n      console.log('Utilisation des données du contexte global');\n    } else {\n      fetchProducts();\n    }\n  }, [allProducts]);\n\n  const categories = [\n    {\n      id: 1,\n      name: \"Produits à Griller\",\n      description: \"Viandes, saucisses et brochettes prêtes à griller\",\n      icon: <FaUtensils className=\"text-4xl text-yummy-grilled\" />,\n      color: \"bg-yummy-grilled/10 border-yummy-grilled/20\",\n      textColor: \"text-yummy-grilled\",\n      path: \"/produits-grilles\"\n    },\n    {\n      id: 2,\n      name: \"Produits Non-Grillés\",\n      description: \"Salades, sandwichs et plats préparés\",\n      icon: <FaPizzaSlice className=\"text-4xl text-yummy-nongrilled\" />,\n      color: \"bg-yummy-nongrilled/10 border-yummy-nongrilled/20\",\n      textColor: \"text-yummy-nongrilled\",\n      path: \"/produits-non-grilles\"\n    },\n    {\n      id: 3,\n      name: \"Fromages\",\n      description: \"Fromages locaux et importés de qualité\",\n      icon: <FaCheese className=\"text-4xl text-yummy-cheese\" />,\n      color: \"bg-yummy-cheese/10 border-yummy-cheese/20\",\n      textColor: \"text-yummy-cheese\",\n      path: \"/fromages\"\n    },\n    {\n      id: 4,\n      name: \"Boissons\",\n      description: \"Boissons fraîches, chaudes et alcoolisées\",\n      icon: <FaCocktail className=\"text-4xl text-yummy-drinks\" />,\n      color: \"bg-yummy-drinks/10 border-yummy-drinks/20\",\n      textColor: \"text-yummy-drinks\",\n      path: \"/boissons\"\n    }\n  ];\n\n  // Composant Hero Section\n  const HeroSection = () => (\n    <section className=\"relative bg-gradient-to-r from-green-50 to-green-100 py-16 md:py-24\">\n      <div className=\"container\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8 items-center\">\n          <div className=\"space-y-6\">\n            <motion.h1\n              className=\"text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.5 }}\n            >\n              Des produits frais pour <span className=\"text-primary-600\">tous vos repas</span>\n            </motion.h1>\n\n            <motion.p\n              className=\"text-lg text-gray-700\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.5, delay: 0.1 }}\n            >\n              Découvrez notre sélection de produits à griller, non-grillés, fromages et boissons de qualité pour satisfaire toutes vos envies gourmandes.\n            </motion.p>\n\n            <motion.div\n              className=\"flex flex-wrap gap-4\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.5, delay: 0.2 }}\n            >\n              <Link to=\"/products\" className=\"btn-primary\">\n                Découvrir nos produits\n              </Link>\n              <Link to=\"/about\" className=\"btn-outline\">\n                En savoir plus\n              </Link>\n            </motion.div>\n          </div>\n\n          <motion.div\n            className=\"relative\"\n            initial={{ opacity: 0, scale: 0.9 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ duration: 0.5 }}\n          >\n            <img\n              src=\"https://images.unsplash.com/photo-1504674900247-0877df9cc836?ixlib=rb-1.2.1&auto=format&fit=crop&w=1200&q=80\"\n              alt=\"Produits frais\"\n              className=\"rounded-lg shadow-xl\"\n            />\n            <div className=\"absolute -bottom-6 -right-6 bg-white p-4 rounded-lg shadow-lg\">\n              <div className=\"flex items-center gap-2\">\n                <div className=\"bg-green-100 p-2 rounded-full\">\n                  <FaUtensils className=\"text-primary-600 text-xl\" />\n                </div>\n                <div>\n                  <p className=\"font-semibold text-gray-900\">Livraison rapide</p>\n                  <p className=\"text-sm text-gray-600\">En 24h chez vous</p>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  );\n\n  // Composant Category List\n  const CategoryList = () => (\n    <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mt-8\">\n      {categories.map((category) => (\n        <motion.div\n          key={category.id}\n          className={`card p-6 border ${category.color} hover:shadow-lg transition-shadow`}\n          whileHover={{ y: -5 }}\n          transition={{ duration: 0.3 }}\n        >\n          <div className=\"flex flex-col items-center text-center\">\n            <div className={`p-4 rounded-full ${category.color} mb-4`}>\n              {category.icon}\n            </div>\n            <h3 className={`text-xl font-semibold mb-2 ${category.textColor}`}>\n              {category.name}\n            </h3>\n            <p className=\"text-gray-600 mb-4\">\n              {category.description}\n            </p>\n            <Link\n              to={category.path}\n              className={`flex items-center ${category.textColor} font-medium hover:underline`}\n            >\n              Découvrir <FaArrowRight className=\"ml-2\" />\n            </Link>\n          </div>\n        </motion.div>\n      ))}\n    </div>\n  );\n\n  // Composant Featured Products\n  const FeaturedProducts = () => (\n    <div className=\"mt-8\">\n      {isLoading ? (\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6\">\n          {Array(4).fill(0).map((_, index) => (\n            <div key={index} className=\"card animate-pulse\">\n              <div className=\"bg-gray-300 aspect-square w-full\"></div>\n              <div className=\"p-4 space-y-3\">\n                <div className=\"h-4 bg-gray-300 rounded w-1/4\"></div>\n                <div className=\"h-6 bg-gray-300 rounded w-3/4\"></div>\n                <div className=\"h-4 bg-gray-300 rounded w-1/2\"></div>\n              </div>\n            </div>\n          ))}\n        </div>\n      ) : featuredProducts.length > 0 ? (\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6\">\n          {featuredProducts.map(product => (\n            <ProductCard key={product.id} product={product} />\n          ))}\n        </div>\n      ) : (\n        <div className=\"text-center py-12\">\n          <div className=\"bg-gray-50 rounded-lg p-8 max-w-2xl mx-auto\">\n            <div className=\"text-gray-400 mb-4\">\n              <svg className=\"mx-auto h-16 w-16\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1} d=\"M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8V4a1 1 0 00-1-1H7a1 1 0 00-1 1v1m8 0V4.5\" />\n              </svg>\n            </div>\n            <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">Aucun produit disponible</h3>\n            <p className=\"text-gray-600 mb-4\">\n              L'administrateur doit ajouter des produits pour qu'ils apparaissent ici.\n            </p>\n            <p className=\"text-sm text-gray-500\">\n              🔗 Toutes les données sont maintenant gérées via Laravel\n            </p>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n\n  // Composant Promo Section\n  const PromoSection = () => (\n    <section className=\"py-16 bg-gradient-to-r from-secondary-500 to-secondary-600 text-white\">\n      <div className=\"container\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8 items-center\">\n          <div>\n            <h2 className=\"text-3xl md:text-4xl font-bold mb-4\">Offre spéciale du moment</h2>\n            <p className=\"text-lg mb-6\">\n              Profitez de 15% de réduction sur tous nos produits à griller pour vos barbecues d'été !\n            </p>\n            <div className=\"flex flex-wrap gap-4\">\n              <Link to=\"/produits-grilles\" className=\"bg-white text-secondary-600 px-6 py-3 rounded-full font-medium hover:bg-gray-100 transition-colors\">\n                En profiter maintenant\n              </Link>\n            </div>\n          </div>\n\n          <div className=\"relative\">\n            <img\n              src=\"https://images.unsplash.com/photo-1544025162-d76694265947?ixlib=rb-1.2.1&auto=format&fit=crop&w=1200&q=80\"\n              alt=\"Barbecue\"\n              className=\"rounded-lg shadow-xl\"\n            />\n            <div className=\"absolute top-0 right-0 bg-accent-500 text-white text-xl font-bold p-4 rounded-bl-lg rounded-tr-lg\">\n              -15%\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n\n  return (\n    <div className=\"bg-white\">\n      {/* Bandeau d'accès rapide admin pour la démonstration */}\n      <div className=\"bg-gradient-to-r from-green-600 to-green-700 text-white p-3\">\n        <div className=\"container mx-auto flex justify-between items-center\">\n          <div className=\"flex items-center space-x-2\">\n            <svg className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n            </svg>\n            <span className=\"text-sm font-medium\">Mode Démonstration - Projet YUMMY</span>\n          </div>\n          <div className=\"flex items-center space-x-4\">\n            <a\n              href=\"/admin/dashboard\"\n              className=\"bg-white text-green-600 px-4 py-1 rounded text-sm font-medium hover:bg-gray-100 transition-colors\"\n            >\n              🔧 Dashboard Admin\n            </a>\n            <span className=\"text-sm opacity-75\">Toutes les fonctionnalités sont accessibles</span>\n          </div>\n        </div>\n      </div>\n\n      <HeroSection />\n\n      <section className=\"py-16 bg-white\">\n        <div className=\"container\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center mb-8\">\n            <h2 className=\"text-3xl font-bold text-gray-900\">Nos catégories</h2>\n            <Link to=\"/products\" className=\"flex items-center text-primary-600 font-medium hover:text-primary-700\">\n              Voir tous les produits <FaArrowRight className=\"ml-2\" />\n            </Link>\n          </div>\n          <CategoryList />\n        </div>\n      </section>\n\n      <section className=\"py-16 bg-gray-50\">\n        <div className=\"container\">\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-8\">Produits en vedette</h2>\n          <FeaturedProducts />\n        </div>\n      </section>\n\n      <PromoSection />\n\n      <section className=\"py-16 bg-gray-50\">\n        <div className=\"container\">\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-8\">Promotions en cours</h2>\n          <ActivePromotions />\n        </div>\n      </section>\n\n      <section className=\"py-16 bg-white\">\n        <div className=\"container\">\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-8\">Nouveaux produits</h2>\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6\">\n            {isLoading ? (\n              Array(4).fill(0).map((_, index) => (\n                <div key={index} className=\"card animate-pulse\">\n                  <div className=\"bg-gray-300 aspect-square w-full\"></div>\n                  <div className=\"p-4 space-y-3\">\n                    <div className=\"h-4 bg-gray-300 rounded w-1/4\"></div>\n                    <div className=\"h-6 bg-gray-300 rounded w-3/4\"></div>\n                    <div className=\"h-4 bg-gray-300 rounded w-1/2\"></div>\n                  </div>\n                </div>\n              ))\n            ) : (\n              newProducts.map(product => (\n                <ProductCard key={product.id} product={product} />\n              ))\n            )}\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n};\n\nexport default HomePage;\n\n\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,WAAW,MAAM,oCAAoC;AAC5D,OAAOC,gBAAgB,MAAM,2CAA2C;AACxE,SAASC,YAAY,EAAEC,UAAU,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,UAAU,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9F,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM;IAAEC;EAAM,CAAC,GAAGZ,OAAO,CAAC,CAAC;EAC3B,MAAM;IAAEa,QAAQ,EAAEC,WAAW;IAAEC,UAAU,EAAEC,aAAa;IAAEC,OAAO,EAAEC;EAAY,CAAC,GAAGN,KAAK;EAExF,MAAM,CAACO,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACyB,WAAW,EAAEC,cAAc,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC2B,SAAS,EAAEC,YAAY,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC6B,eAAe,EAAEC,kBAAkB,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAE7DC,SAAS,CAAC,MAAM;IACd,MAAM8B,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAI;QACF;QACA,IAAIb,WAAW,IAAIA,WAAW,CAACc,MAAM,GAAG,CAAC,EAAE;UACzCR,mBAAmB,CAACN,WAAW,CAACe,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UAC5CP,cAAc,CAACR,WAAW,CAACe,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;UACrCL,YAAY,CAAC,KAAK,CAAC;UACnB;QACF;;QAEA;QACAM,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;;QAEvD;QACAP,YAAY,CAAC,KAAK,CAAC;MACrB,CAAC,CAAC,OAAOQ,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;;QAE/D;QACAF,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;QAC1DX,mBAAmB,CAAC,EAAE,CAAC;QACvBE,cAAc,CAAC,EAAE,CAAC;QAClBI,kBAAkB,CAAC,KAAK,CAAC;QACzBF,YAAY,CAAC,KAAK,CAAC;MACrB;IACF,CAAC;;IAED;IACA,IAAIV,WAAW,IAAIA,WAAW,CAACc,MAAM,GAAG,CAAC,EAAE;MACzCR,mBAAmB,CAACN,WAAW,CAACe,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAC5CP,cAAc,CAACR,WAAW,CAACe,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;MACrCL,YAAY,CAAC,KAAK,CAAC;MACnBM,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;IAC3D,CAAC,MAAM;MACLJ,aAAa,CAAC,CAAC;IACjB;EACF,CAAC,EAAE,CAACb,WAAW,CAAC,CAAC;EAEjB,MAAMC,UAAU,GAAG,CACjB;IACEkB,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,oBAAoB;IAC1BC,WAAW,EAAE,mDAAmD;IAChEC,IAAI,eAAE3B,OAAA,CAACL,UAAU;MAACiC,SAAS,EAAC;IAA6B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC5DC,KAAK,EAAE,6CAA6C;IACpDC,SAAS,EAAE,oBAAoB;IAC/BC,IAAI,EAAE;EACR,CAAC,EACD;IACEX,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,sBAAsB;IAC5BC,WAAW,EAAE,sCAAsC;IACnDC,IAAI,eAAE3B,OAAA,CAACJ,YAAY;MAACgC,SAAS,EAAC;IAAgC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACjEC,KAAK,EAAE,mDAAmD;IAC1DC,SAAS,EAAE,uBAAuB;IAClCC,IAAI,EAAE;EACR,CAAC,EACD;IACEX,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,UAAU;IAChBC,WAAW,EAAE,wCAAwC;IACrDC,IAAI,eAAE3B,OAAA,CAACH,QAAQ;MAAC+B,SAAS,EAAC;IAA4B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzDC,KAAK,EAAE,2CAA2C;IAClDC,SAAS,EAAE,mBAAmB;IAC9BC,IAAI,EAAE;EACR,CAAC,EACD;IACEX,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,UAAU;IAChBC,WAAW,EAAE,2CAA2C;IACxDC,IAAI,eAAE3B,OAAA,CAACF,UAAU;MAAC8B,SAAS,EAAC;IAA4B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC3DC,KAAK,EAAE,2CAA2C;IAClDC,SAAS,EAAE,mBAAmB;IAC9BC,IAAI,EAAE;EACR,CAAC,CACF;;EAED;EACA,MAAMC,WAAW,GAAGA,CAAA,kBAClBpC,OAAA;IAAS4B,SAAS,EAAC,qEAAqE;IAAAS,QAAA,eACtFrC,OAAA;MAAK4B,SAAS,EAAC,WAAW;MAAAS,QAAA,eACxBrC,OAAA;QAAK4B,SAAS,EAAC,oDAAoD;QAAAS,QAAA,gBACjErC,OAAA;UAAK4B,SAAS,EAAC,WAAW;UAAAS,QAAA,gBACxBrC,OAAA,CAACV,MAAM,CAACgD,EAAE;YACRV,SAAS,EAAC,0DAA0D;YACpEW,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAAAP,QAAA,GAC/B,0BACyB,eAAArC,OAAA;cAAM4B,SAAS,EAAC,kBAAkB;cAAAS,QAAA,EAAC;YAAc;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC,eAEZhC,OAAA,CAACV,MAAM,CAACuD,CAAC;YACPjB,SAAS,EAAC,uBAAuB;YACjCW,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEE,KAAK,EAAE;YAAI,CAAE;YAAAT,QAAA,EAC3C;UAED;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eAEXhC,OAAA,CAACV,MAAM,CAACyD,GAAG;YACTnB,SAAS,EAAC,sBAAsB;YAChCW,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEE,KAAK,EAAE;YAAI,CAAE;YAAAT,QAAA,gBAE1CrC,OAAA,CAACX,IAAI;cAAC2D,EAAE,EAAC,WAAW;cAACpB,SAAS,EAAC,aAAa;cAAAS,QAAA,EAAC;YAE7C;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPhC,OAAA,CAACX,IAAI;cAAC2D,EAAE,EAAC,QAAQ;cAACpB,SAAS,EAAC,aAAa;cAAAS,QAAA,EAAC;YAE1C;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAENhC,OAAA,CAACV,MAAM,CAACyD,GAAG;UACTnB,SAAS,EAAC,UAAU;UACpBW,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAES,KAAK,EAAE;UAAI,CAAE;UACpCP,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAES,KAAK,EAAE;UAAE,CAAE;UAClCN,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAAAP,QAAA,gBAE9BrC,OAAA;YACEkD,GAAG,EAAC,8GAA8G;YAClHC,GAAG,EAAC,gBAAgB;YACpBvB,SAAS,EAAC;UAAsB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eACFhC,OAAA;YAAK4B,SAAS,EAAC,+DAA+D;YAAAS,QAAA,eAC5ErC,OAAA;cAAK4B,SAAS,EAAC,yBAAyB;cAAAS,QAAA,gBACtCrC,OAAA;gBAAK4B,SAAS,EAAC,+BAA+B;gBAAAS,QAAA,eAC5CrC,OAAA,CAACL,UAAU;kBAACiC,SAAS,EAAC;gBAA0B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,eACNhC,OAAA;gBAAAqC,QAAA,gBACErC,OAAA;kBAAG4B,SAAS,EAAC,6BAA6B;kBAAAS,QAAA,EAAC;gBAAgB;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAC/DhC,OAAA;kBAAG4B,SAAS,EAAC,uBAAuB;kBAAAS,QAAA,EAAC;gBAAgB;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CACV;;EAED;EACA,MAAMoB,YAAY,GAAGA,CAAA,kBACnBpD,OAAA;IAAK4B,SAAS,EAAC,2DAA2D;IAAAS,QAAA,EACvE/B,UAAU,CAAC+C,GAAG,CAAEC,QAAQ,iBACvBtD,OAAA,CAACV,MAAM,CAACyD,GAAG;MAETnB,SAAS,EAAE,mBAAmB0B,QAAQ,CAACrB,KAAK,oCAAqC;MACjFsB,UAAU,EAAE;QAAEd,CAAC,EAAE,CAAC;MAAE,CAAE;MACtBE,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE;MAAAP,QAAA,eAE9BrC,OAAA;QAAK4B,SAAS,EAAC,wCAAwC;QAAAS,QAAA,gBACrDrC,OAAA;UAAK4B,SAAS,EAAE,oBAAoB0B,QAAQ,CAACrB,KAAK,OAAQ;UAAAI,QAAA,EACvDiB,QAAQ,CAAC3B;QAAI;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eACNhC,OAAA;UAAI4B,SAAS,EAAE,8BAA8B0B,QAAQ,CAACpB,SAAS,EAAG;UAAAG,QAAA,EAC/DiB,QAAQ,CAAC7B;QAAI;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eACLhC,OAAA;UAAG4B,SAAS,EAAC,oBAAoB;UAAAS,QAAA,EAC9BiB,QAAQ,CAAC5B;QAAW;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eACJhC,OAAA,CAACX,IAAI;UACH2D,EAAE,EAAEM,QAAQ,CAACnB,IAAK;UAClBP,SAAS,EAAE,qBAAqB0B,QAAQ,CAACpB,SAAS,8BAA+B;UAAAG,QAAA,GAClF,eACW,eAAArC,OAAA,CAACN,YAAY;YAACkC,SAAS,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC,GArBDsB,QAAQ,CAAC9B,EAAE;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAsBN,CACb;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CACN;;EAED;EACA,MAAMwB,gBAAgB,GAAGA,CAAA,kBACvBxD,OAAA;IAAK4B,SAAS,EAAC,MAAM;IAAAS,QAAA,EAClBvB,SAAS,gBACRd,OAAA;MAAK4B,SAAS,EAAC,sDAAsD;MAAAS,QAAA,EAClEoB,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAACL,GAAG,CAAC,CAACM,CAAC,EAAEC,KAAK,kBAC7B5D,OAAA;QAAiB4B,SAAS,EAAC,oBAAoB;QAAAS,QAAA,gBAC7CrC,OAAA;UAAK4B,SAAS,EAAC;QAAkC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACxDhC,OAAA;UAAK4B,SAAS,EAAC,eAAe;UAAAS,QAAA,gBAC5BrC,OAAA;YAAK4B,SAAS,EAAC;UAA+B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACrDhC,OAAA;YAAK4B,SAAS,EAAC;UAA+B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACrDhC,OAAA;YAAK4B,SAAS,EAAC;UAA+B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC;MAAA,GANE4B,KAAK;QAAA/B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAOV,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,GACJtB,gBAAgB,CAACS,MAAM,GAAG,CAAC,gBAC7BnB,OAAA;MAAK4B,SAAS,EAAC,sDAAsD;MAAAS,QAAA,EAClE3B,gBAAgB,CAAC2C,GAAG,CAACQ,OAAO,iBAC3B7D,OAAA,CAACR,WAAW;QAAkBqE,OAAO,EAAEA;MAAQ,GAA7BA,OAAO,CAACrC,EAAE;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAqB,CAClD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,gBAENhC,OAAA;MAAK4B,SAAS,EAAC,mBAAmB;MAAAS,QAAA,eAChCrC,OAAA;QAAK4B,SAAS,EAAC,6CAA6C;QAAAS,QAAA,gBAC1DrC,OAAA;UAAK4B,SAAS,EAAC,oBAAoB;UAAAS,QAAA,eACjCrC,OAAA;YAAK4B,SAAS,EAAC,mBAAmB;YAAC8B,IAAI,EAAC,MAAM;YAACI,OAAO,EAAC,WAAW;YAACC,MAAM,EAAC,cAAc;YAAA1B,QAAA,eACtFrC,OAAA;cAAMgE,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAA0I;cAAAtC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/M;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNhC,OAAA;UAAI4B,SAAS,EAAC,0CAA0C;UAAAS,QAAA,EAAC;QAAwB;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtFhC,OAAA;UAAG4B,SAAS,EAAC,oBAAoB;UAAAS,QAAA,EAAC;QAElC;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJhC,OAAA;UAAG4B,SAAS,EAAC,uBAAuB;UAAAS,QAAA,EAAC;QAErC;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EACN;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CACN;;EAED;EACA,MAAMoC,YAAY,GAAGA,CAAA,kBACnBpE,OAAA;IAAS4B,SAAS,EAAC,uEAAuE;IAAAS,QAAA,eACxFrC,OAAA;MAAK4B,SAAS,EAAC,WAAW;MAAAS,QAAA,eACxBrC,OAAA;QAAK4B,SAAS,EAAC,oDAAoD;QAAAS,QAAA,gBACjErC,OAAA;UAAAqC,QAAA,gBACErC,OAAA;YAAI4B,SAAS,EAAC,qCAAqC;YAAAS,QAAA,EAAC;UAAwB;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjFhC,OAAA;YAAG4B,SAAS,EAAC,cAAc;YAAAS,QAAA,EAAC;UAE5B;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJhC,OAAA;YAAK4B,SAAS,EAAC,sBAAsB;YAAAS,QAAA,eACnCrC,OAAA,CAACX,IAAI;cAAC2D,EAAE,EAAC,mBAAmB;cAACpB,SAAS,EAAC,oGAAoG;cAAAS,QAAA,EAAC;YAE5I;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENhC,OAAA;UAAK4B,SAAS,EAAC,UAAU;UAAAS,QAAA,gBACvBrC,OAAA;YACEkD,GAAG,EAAC,2GAA2G;YAC/GC,GAAG,EAAC,UAAU;YACdvB,SAAS,EAAC;UAAsB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eACFhC,OAAA;YAAK4B,SAAS,EAAC,mGAAmG;YAAAS,QAAA,EAAC;UAEnH;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CACV;EAED,oBACEhC,OAAA;IAAK4B,SAAS,EAAC,UAAU;IAAAS,QAAA,gBAEvBrC,OAAA;MAAK4B,SAAS,EAAC,6DAA6D;MAAAS,QAAA,eAC1ErC,OAAA;QAAK4B,SAAS,EAAC,qDAAqD;QAAAS,QAAA,gBAClErC,OAAA;UAAK4B,SAAS,EAAC,6BAA6B;UAAAS,QAAA,gBAC1CrC,OAAA;YAAK4B,SAAS,EAAC,SAAS;YAAC8B,IAAI,EAAC,MAAM;YAACI,OAAO,EAAC,WAAW;YAACC,MAAM,EAAC,cAAc;YAAA1B,QAAA,eAC5ErC,OAAA;cAAMgE,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAA+C;cAAAtC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpH,CAAC,eACNhC,OAAA;YAAM4B,SAAS,EAAC,qBAAqB;YAAAS,QAAA,EAAC;UAAiC;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3E,CAAC,eACNhC,OAAA;UAAK4B,SAAS,EAAC,6BAA6B;UAAAS,QAAA,gBAC1CrC,OAAA;YACEqE,IAAI,EAAC,kBAAkB;YACvBzC,SAAS,EAAC,mGAAmG;YAAAS,QAAA,EAC9G;UAED;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJhC,OAAA;YAAM4B,SAAS,EAAC,oBAAoB;YAAAS,QAAA,EAAC;UAA2C;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENhC,OAAA,CAACoC,WAAW;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEfhC,OAAA;MAAS4B,SAAS,EAAC,gBAAgB;MAAAS,QAAA,eACjCrC,OAAA;QAAK4B,SAAS,EAAC,WAAW;QAAAS,QAAA,gBACxBrC,OAAA;UAAK4B,SAAS,EAAC,6DAA6D;UAAAS,QAAA,gBAC1ErC,OAAA;YAAI4B,SAAS,EAAC,kCAAkC;YAAAS,QAAA,EAAC;UAAc;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpEhC,OAAA,CAACX,IAAI;YAAC2D,EAAE,EAAC,WAAW;YAACpB,SAAS,EAAC,uEAAuE;YAAAS,QAAA,GAAC,yBAC9E,eAAArC,OAAA,CAACN,YAAY;cAACkC,SAAS,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNhC,OAAA,CAACoD,YAAY;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEVhC,OAAA;MAAS4B,SAAS,EAAC,kBAAkB;MAAAS,QAAA,eACnCrC,OAAA;QAAK4B,SAAS,EAAC,WAAW;QAAAS,QAAA,gBACxBrC,OAAA;UAAI4B,SAAS,EAAC,uCAAuC;UAAAS,QAAA,EAAC;QAAmB;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9EhC,OAAA,CAACwD,gBAAgB;UAAA3B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEVhC,OAAA,CAACoE,YAAY;MAAAvC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEhBhC,OAAA;MAAS4B,SAAS,EAAC,kBAAkB;MAAAS,QAAA,eACnCrC,OAAA;QAAK4B,SAAS,EAAC,WAAW;QAAAS,QAAA,gBACxBrC,OAAA;UAAI4B,SAAS,EAAC,uCAAuC;UAAAS,QAAA,EAAC;QAAmB;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9EhC,OAAA,CAACP,gBAAgB;UAAAoC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEVhC,OAAA;MAAS4B,SAAS,EAAC,gBAAgB;MAAAS,QAAA,eACjCrC,OAAA;QAAK4B,SAAS,EAAC,WAAW;QAAAS,QAAA,gBACxBrC,OAAA;UAAI4B,SAAS,EAAC,uCAAuC;UAAAS,QAAA,EAAC;QAAiB;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5EhC,OAAA;UAAK4B,SAAS,EAAC,sDAAsD;UAAAS,QAAA,EAClEvB,SAAS,GACR2C,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAACL,GAAG,CAAC,CAACM,CAAC,EAAEC,KAAK,kBAC5B5D,OAAA;YAAiB4B,SAAS,EAAC,oBAAoB;YAAAS,QAAA,gBAC7CrC,OAAA;cAAK4B,SAAS,EAAC;YAAkC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxDhC,OAAA;cAAK4B,SAAS,EAAC,eAAe;cAAAS,QAAA,gBAC5BrC,OAAA;gBAAK4B,SAAS,EAAC;cAA+B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACrDhC,OAAA;gBAAK4B,SAAS,EAAC;cAA+B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACrDhC,OAAA;gBAAK4B,SAAS,EAAC;cAA+B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC;UAAA,GANE4B,KAAK;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAOV,CACN,CAAC,GAEFpB,WAAW,CAACyC,GAAG,CAACQ,OAAO,iBACrB7D,OAAA,CAACR,WAAW;YAAkBqE,OAAO,EAAEA;UAAQ,GAA7BA,OAAO,CAACrC,EAAE;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAqB,CAClD;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAAC9B,EAAA,CAnVID,QAAQ;EAAA,QACMV,OAAO;AAAA;AAAA+E,EAAA,GADrBrE,QAAQ;AAqVd,eAAeA,QAAQ;AAAC,IAAAqE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}