{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Projet-Synthese\\\\frontend\\\\src\\\\components\\\\admin\\\\AdminHeader.js\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminHeader = () => {\n  // Mode démonstration - interface simplifiée\n  const userName = 'Admin YUMMY';\n  const handleLogout = () => {\n    // En mode démonstration, retour à l'accueil\n    window.location.href = '/';\n  };\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    className: \"bg-white shadow-sm h-16 flex items-center px-6 sticky top-0 z-10\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex justify-between items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-gray-800 font-medium\",\n          children: [\"Bonjour, \", userName]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-4\",\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          className: \"text-gray-600 hover:text-green-600 transition\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            className: \"h-5 w-5\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            stroke: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 25,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleLogout,\n          className: \"text-gray-600 hover:text-red-600 transition\",\n          title: \"Retour \\xE0 l'accueil\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            className: \"h-5 w-5\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            stroke: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 35,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 14,\n    columnNumber: 5\n  }, this);\n};\n_c = AdminHeader;\nexport default AdminHeader;\nvar _c;\n$RefreshReg$(_c, \"AdminHeader\");", "map": {"version": 3, "names": ["React", "Link", "jsxDEV", "_jsxDEV", "Ad<PERSON><PERSON><PERSON><PERSON>", "userName", "handleLogout", "window", "location", "href", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "onClick", "title", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet-Synthese/frontend/src/components/admin/AdminHeader.js"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\n\nconst AdminHeader = () => {\n  // Mode démonstration - interface simplifiée\n  const userName = 'Admin YUMMY';\n\n  const handleLogout = () => {\n    // En mode démonstration, retour à l'accueil\n    window.location.href = '/';\n  };\n\n  return (\n    <header className=\"bg-white shadow-sm h-16 flex items-center px-6 sticky top-0 z-10\">\n      <div className=\"flex-1 flex justify-between items-center\">\n        <div className=\"flex items-center\">\n          <span className=\"text-gray-800 font-medium\">\n            Bonjour, {userName}\n          </span>\n        </div>\n        \n        <div className=\"flex items-center space-x-4\">\n          <Link to=\"/\" className=\"text-gray-600 hover:text-green-600 transition\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\" />\n            </svg>\n          </Link>\n          \n          <button\n            onClick={handleLogout}\n            className=\"text-gray-600 hover:text-red-600 transition\"\n            title=\"Retour à l'accueil\"\n          >\n            <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\n            </svg>\n          </button>\n        </div>\n      </div>\n    </header>\n  );\n};\n\nexport default AdminHeader;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,WAAW,GAAGA,CAAA,KAAM;EACxB;EACA,MAAMC,QAAQ,GAAG,aAAa;EAE9B,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB;IACAC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;EAC5B,CAAC;EAED,oBACEN,OAAA;IAAQO,SAAS,EAAC,kEAAkE;IAAAC,QAAA,eAClFR,OAAA;MAAKO,SAAS,EAAC,0CAA0C;MAAAC,QAAA,gBACvDR,OAAA;QAAKO,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAChCR,OAAA;UAAMO,SAAS,EAAC,2BAA2B;UAAAC,QAAA,GAAC,WACjC,EAACN,QAAQ;QAAA;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENZ,OAAA;QAAKO,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1CR,OAAA,CAACF,IAAI;UAACe,EAAE,EAAC,GAAG;UAACN,SAAS,EAAC,+CAA+C;UAAAC,QAAA,eACpER,OAAA;YAAKc,KAAK,EAAC,4BAA4B;YAACP,SAAS,EAAC,SAAS;YAACQ,IAAI,EAAC,MAAM;YAACC,OAAO,EAAC,WAAW;YAACC,MAAM,EAAC,cAAc;YAAAT,QAAA,eAC/GR,OAAA;cAAMkB,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAAkJ;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEPZ,OAAA;UACEsB,OAAO,EAAEnB,YAAa;UACtBI,SAAS,EAAC,6CAA6C;UACvDgB,KAAK,EAAC,uBAAoB;UAAAf,QAAA,eAE1BR,OAAA;YAAKc,KAAK,EAAC,4BAA4B;YAACP,SAAS,EAAC,SAAS;YAACQ,IAAI,EAAC,MAAM;YAACC,OAAO,EAAC,WAAW;YAACC,MAAM,EAAC,cAAc;YAAAT,QAAA,eAC/GR,OAAA;cAAMkB,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAA2F;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACY,EAAA,GAtCIvB,WAAW;AAwCjB,eAAeA,WAAW;AAAC,IAAAuB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}