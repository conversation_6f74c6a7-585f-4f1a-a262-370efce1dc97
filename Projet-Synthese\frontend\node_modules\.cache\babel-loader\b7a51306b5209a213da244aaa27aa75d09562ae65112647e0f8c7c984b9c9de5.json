{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Projet-Synthese\\\\frontend\\\\src\\\\pages\\\\ProductsPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useData } from '../context/DataContext';\nimport { productAPI } from '../services/apiService';\nimport ProductCard from '../components/products/ProductCard';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProductsPage = () => {\n  _s();\n  const {\n    state\n  } = useData();\n  const {\n    products: contextProducts,\n    categories\n  } = state;\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('');\n  useEffect(() => {\n    fetchProducts();\n  }, [contextProducts, selectedCategory]);\n  const fetchProducts = async () => {\n    try {\n      setLoading(true);\n      console.log('🔄 Chargement des produits...');\n\n      // Utiliser d'abord les produits du contexte\n      if (contextProducts && contextProducts.length > 0) {\n        let filteredProducts = contextProducts;\n        if (selectedCategory) {\n          filteredProducts = contextProducts.filter(p => p.category_id == selectedCategory);\n        }\n        setProducts(filteredProducts);\n        console.log(`✅ ${filteredProducts.length} produits chargés depuis le contexte`);\n      } else {\n        // Sinon, charger depuis l'API\n        const response = await productAPI.getAll({\n          is_active: true,\n          category_id: selectedCategory || undefined\n        });\n        if (response.success) {\n          const productData = response.data.data || response.data;\n          setProducts(productData);\n          console.log(`✅ ${productData.length} produits chargés depuis l'API`);\n        } else {\n          console.log('⚠️ Aucun produit trouvé');\n          setProducts([]);\n        }\n      }\n      setError(null);\n    } catch (err) {\n      console.error('❌ Erreur lors du chargement des produits:', err);\n      setError('Erreur lors du chargement des produits');\n      setProducts([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleSearch = e => {\n    e.preventDefault();\n    if (searchTerm.trim()) {\n      const filtered = contextProducts.filter(product => product.name.toLowerCase().includes(searchTerm.toLowerCase()) || product.description.toLowerCase().includes(searchTerm.toLowerCase()));\n      setProducts(filtered);\n    } else {\n      fetchProducts();\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-center items-center h-64\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-500\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container mx-auto px-4 py-8\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"text-3xl font-bold mb-8\",\n      children: \"Nos produits\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6\",\n      children: products.map(product => /*#__PURE__*/_jsxDEV(ProductCard, {\n        product: product\n      }, product.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 95,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 92,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductsPage, \"kLQYRXMmj3J9hAA+QUCPKfyDLqo=\", false, function () {\n  return [useData];\n});\n_c = ProductsPage;\nexport default ProductsPage;\nvar _c;\n$RefreshReg$(_c, \"ProductsPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useData", "productAPI", "ProductCard", "jsxDEV", "_jsxDEV", "ProductsPage", "_s", "state", "products", "contextProducts", "categories", "setProducts", "loading", "setLoading", "error", "setError", "searchTerm", "setSearchTerm", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "fetchProducts", "console", "log", "length", "filteredProducts", "filter", "p", "category_id", "response", "getAll", "is_active", "undefined", "success", "productData", "data", "err", "handleSearch", "e", "preventDefault", "trim", "filtered", "product", "name", "toLowerCase", "includes", "description", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "id", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet-Synthese/frontend/src/pages/ProductsPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useData } from '../context/DataContext';\nimport { productAPI } from '../services/apiService';\nimport ProductCard from '../components/products/ProductCard';\n\nconst ProductsPage = () => {\n  const { state } = useData();\n  const { products: contextProducts, categories } = state;\n\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('');\n\n  useEffect(() => {\n    fetchProducts();\n  }, [contextProducts, selectedCategory]);\n\n  const fetchProducts = async () => {\n    try {\n      setLoading(true);\n      console.log('🔄 Chargement des produits...');\n\n      // Utiliser d'abord les produits du contexte\n      if (contextProducts && contextProducts.length > 0) {\n        let filteredProducts = contextProducts;\n\n        if (selectedCategory) {\n          filteredProducts = contextProducts.filter(p => p.category_id == selectedCategory);\n        }\n\n        setProducts(filteredProducts);\n        console.log(`✅ ${filteredProducts.length} produits chargés depuis le contexte`);\n      } else {\n        // Sinon, charger depuis l'API\n        const response = await productAPI.getAll({\n          is_active: true,\n          category_id: selectedCategory || undefined\n        });\n\n        if (response.success) {\n          const productData = response.data.data || response.data;\n          setProducts(productData);\n          console.log(`✅ ${productData.length} produits chargés depuis l'API`);\n        } else {\n          console.log('⚠️ Aucun produit trouvé');\n          setProducts([]);\n        }\n      }\n\n      setError(null);\n    } catch (err) {\n      console.error('❌ Erreur lors du chargement des produits:', err);\n      setError('Erreur lors du chargement des produits');\n      setProducts([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSearch = (e) => {\n    e.preventDefault();\n    if (searchTerm.trim()) {\n      const filtered = contextProducts.filter(product =>\n        product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        product.description.toLowerCase().includes(searchTerm.toLowerCase())\n      );\n      setProducts(filtered);\n    } else {\n      fetchProducts();\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex justify-center items-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-500\"></div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\">\n        {error}\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"container mx-auto px-4 py-8\">\n      <h1 className=\"text-3xl font-bold mb-8\">Nos produits</h1>\n      \n      <div className=\"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6\">\n        {products.map(product => (\n          <ProductCard key={product.id} product={product} />\n        ))}\n      </div>\n    </div>\n  );\n};\n\nexport default ProductsPage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,UAAU,QAAQ,wBAAwB;AACnD,OAAOC,WAAW,MAAM,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7D,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM;IAAEC;EAAM,CAAC,GAAGP,OAAO,CAAC,CAAC;EAC3B,MAAM;IAAEQ,QAAQ,EAAEC,eAAe;IAAEC;EAAW,CAAC,GAAGH,KAAK;EAEvD,MAAM,CAACC,QAAQ,EAAEG,WAAW,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgB,KAAK,EAAEC,QAAQ,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACkB,UAAU,EAAEC,aAAa,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACoB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAE5DC,SAAS,CAAC,MAAM;IACdqB,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAACX,eAAe,EAAES,gBAAgB,CAAC,CAAC;EAEvC,MAAME,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACFP,UAAU,CAAC,IAAI,CAAC;MAChBQ,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;;MAE5C;MACA,IAAIb,eAAe,IAAIA,eAAe,CAACc,MAAM,GAAG,CAAC,EAAE;QACjD,IAAIC,gBAAgB,GAAGf,eAAe;QAEtC,IAAIS,gBAAgB,EAAE;UACpBM,gBAAgB,GAAGf,eAAe,CAACgB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,WAAW,IAAIT,gBAAgB,CAAC;QACnF;QAEAP,WAAW,CAACa,gBAAgB,CAAC;QAC7BH,OAAO,CAACC,GAAG,CAAC,KAAKE,gBAAgB,CAACD,MAAM,sCAAsC,CAAC;MACjF,CAAC,MAAM;QACL;QACA,MAAMK,QAAQ,GAAG,MAAM3B,UAAU,CAAC4B,MAAM,CAAC;UACvCC,SAAS,EAAE,IAAI;UACfH,WAAW,EAAET,gBAAgB,IAAIa;QACnC,CAAC,CAAC;QAEF,IAAIH,QAAQ,CAACI,OAAO,EAAE;UACpB,MAAMC,WAAW,GAAGL,QAAQ,CAACM,IAAI,CAACA,IAAI,IAAIN,QAAQ,CAACM,IAAI;UACvDvB,WAAW,CAACsB,WAAW,CAAC;UACxBZ,OAAO,CAACC,GAAG,CAAC,KAAKW,WAAW,CAACV,MAAM,gCAAgC,CAAC;QACtE,CAAC,MAAM;UACLF,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;UACtCX,WAAW,CAAC,EAAE,CAAC;QACjB;MACF;MAEAI,QAAQ,CAAC,IAAI,CAAC;IAChB,CAAC,CAAC,OAAOoB,GAAG,EAAE;MACZd,OAAO,CAACP,KAAK,CAAC,2CAA2C,EAAEqB,GAAG,CAAC;MAC/DpB,QAAQ,CAAC,wCAAwC,CAAC;MAClDJ,WAAW,CAAC,EAAE,CAAC;IACjB,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMuB,YAAY,GAAIC,CAAC,IAAK;IAC1BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAItB,UAAU,CAACuB,IAAI,CAAC,CAAC,EAAE;MACrB,MAAMC,QAAQ,GAAG/B,eAAe,CAACgB,MAAM,CAACgB,OAAO,IAC7CA,OAAO,CAACC,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC5B,UAAU,CAAC2B,WAAW,CAAC,CAAC,CAAC,IAC7DF,OAAO,CAACI,WAAW,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC5B,UAAU,CAAC2B,WAAW,CAAC,CAAC,CACrE,CAAC;MACDhC,WAAW,CAAC6B,QAAQ,CAAC;IACvB,CAAC,MAAM;MACLpB,aAAa,CAAC,CAAC;IACjB;EACF,CAAC;EAED,IAAIR,OAAO,EAAE;IACX,oBACER,OAAA;MAAK0C,SAAS,EAAC,uCAAuC;MAAAC,QAAA,eACpD3C,OAAA;QAAK0C,SAAS,EAAC;MAA4E;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/F,CAAC;EAEV;EAEA,IAAIrC,KAAK,EAAE;IACT,oBACEV,OAAA;MAAK0C,SAAS,EAAC,iEAAiE;MAAAC,QAAA,EAC7EjC;IAAK;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE/C,OAAA;IAAK0C,SAAS,EAAC,6BAA6B;IAAAC,QAAA,gBAC1C3C,OAAA;MAAI0C,SAAS,EAAC,yBAAyB;MAAAC,QAAA,EAAC;IAAY;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAEzD/C,OAAA;MAAK0C,SAAS,EAAC,qEAAqE;MAAAC,QAAA,EACjFvC,QAAQ,CAAC4C,GAAG,CAACX,OAAO,iBACnBrC,OAAA,CAACF,WAAW;QAAkBuC,OAAO,EAAEA;MAAQ,GAA7BA,OAAO,CAACY,EAAE;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAqB,CAClD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC7C,EAAA,CAhGID,YAAY;EAAA,QACEL,OAAO;AAAA;AAAAsD,EAAA,GADrBjD,YAAY;AAkGlB,eAAeA,YAAY;AAAC,IAAAiD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}