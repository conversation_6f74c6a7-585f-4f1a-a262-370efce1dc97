{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Projet-Synthese\\\\frontend\\\\src\\\\components\\\\DashboardCharts.js\";\nimport React from 'react';\nimport { FaChartLine, FaChart<PERSON>ie } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DashboardCharts = ({\n  salesData,\n  categoryData\n}) => {\n  var _salesData$labels, _categoryData$labels;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6 mt-8\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white p-6 rounded-lg shadow-sm border\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900\",\n          children: \"Revenus Mensuels\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 10,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FaChartLine, {\n          className: \"text-blue-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 11,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"h-64 flex items-center justify-center bg-gray-50 rounded-lg\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(FaChartLine, {\n            className: \"mx-auto h-12 w-12 text-gray-400 mb-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 15,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-500\",\n            children: \"Graphique des revenus\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 16,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-400 mt-2\",\n            children: [(salesData === null || salesData === void 0 ? void 0 : (_salesData$labels = salesData.labels) === null || _salesData$labels === void 0 ? void 0 : _salesData$labels.length) || 0, \" mois de donn\\xE9es\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 17,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 14,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white p-6 rounded-lg shadow-sm border\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900\",\n          children: \"Cat\\xE9gories Populaires\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FaChartPie, {\n          className: \"text-green-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"h-64 flex items-center justify-center bg-gray-50 rounded-lg\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(FaChartPie, {\n            className: \"mx-auto h-12 w-12 text-gray-400 mb-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-500\",\n            children: \"R\\xE9partition par cat\\xE9gories\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 33,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-400 mt-2\",\n            children: [(categoryData === null || categoryData === void 0 ? void 0 : (_categoryData$labels = categoryData.labels) === null || _categoryData$labels === void 0 ? void 0 : _categoryData$labels.length) || 0, \" cat\\xE9gories\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = DashboardCharts;\nexport default DashboardCharts;\nvar _c;\n$RefreshReg$(_c, \"DashboardCharts\");", "map": {"version": 3, "names": ["React", "FaChartLine", "FaChart<PERSON>ie", "jsxDEV", "_jsxDEV", "DashboardCharts", "salesData", "categoryData", "_salesData$labels", "_categoryData$labels", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "labels", "length", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet-Synthese/frontend/src/components/DashboardCharts.js"], "sourcesContent": ["import React from 'react';\nimport { FaChartLine, FaChartPie } from 'react-icons/fa';\n\nconst DashboardCharts = ({ salesData, categoryData }) => {\n  return (\n    <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 mt-8\">\n      {/* Graphique des ventes */}\n      <div className=\"bg-white p-6 rounded-lg shadow-sm border\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <h3 className=\"text-lg font-semibold text-gray-900\">Revenus Mensuels</h3>\n          <FaChartLine className=\"text-blue-500\" />\n        </div>\n        <div className=\"h-64 flex items-center justify-center bg-gray-50 rounded-lg\">\n          <div className=\"text-center\">\n            <FaChartLine className=\"mx-auto h-12 w-12 text-gray-400 mb-4\" />\n            <p className=\"text-gray-500\">Graphique des revenus</p>\n            <p className=\"text-sm text-gray-400 mt-2\">\n              {salesData?.labels?.length || 0} mois de données\n            </p>\n          </div>\n        </div>\n      </div>\n\n      {/* Graphique des catégories */}\n      <div className=\"bg-white p-6 rounded-lg shadow-sm border\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <h3 className=\"text-lg font-semibold text-gray-900\">Catégories Populaires</h3>\n          <FaChartPie className=\"text-green-500\" />\n        </div>\n        <div className=\"h-64 flex items-center justify-center bg-gray-50 rounded-lg\">\n          <div className=\"text-center\">\n            <FaChartPie className=\"mx-auto h-12 w-12 text-gray-400 mb-4\" />\n            <p className=\"text-gray-500\">Répartition par catégories</p>\n            <p className=\"text-sm text-gray-400 mt-2\">\n              {categoryData?.labels?.length || 0} catégories\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default DashboardCharts;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,EAAEC,UAAU,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzD,MAAMC,eAAe,GAAGA,CAAC;EAAEC,SAAS;EAAEC;AAAa,CAAC,KAAK;EAAA,IAAAC,iBAAA,EAAAC,oBAAA;EACvD,oBACEL,OAAA;IAAKM,SAAS,EAAC,4CAA4C;IAAAC,QAAA,gBAEzDP,OAAA;MAAKM,SAAS,EAAC,0CAA0C;MAAAC,QAAA,gBACvDP,OAAA;QAAKM,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDP,OAAA;UAAIM,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzEX,OAAA,CAACH,WAAW;UAACS,SAAS,EAAC;QAAe;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC,eACNX,OAAA;QAAKM,SAAS,EAAC,6DAA6D;QAAAC,QAAA,eAC1EP,OAAA;UAAKM,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BP,OAAA,CAACH,WAAW;YAACS,SAAS,EAAC;UAAsC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChEX,OAAA;YAAGM,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACtDX,OAAA;YAAGM,SAAS,EAAC,4BAA4B;YAAAC,QAAA,GACtC,CAAAL,SAAS,aAATA,SAAS,wBAAAE,iBAAA,GAATF,SAAS,CAAEU,MAAM,cAAAR,iBAAA,uBAAjBA,iBAAA,CAAmBS,MAAM,KAAI,CAAC,EAAC,qBAClC;UAAA;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNX,OAAA;MAAKM,SAAS,EAAC,0CAA0C;MAAAC,QAAA,gBACvDP,OAAA;QAAKM,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDP,OAAA;UAAIM,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAC;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9EX,OAAA,CAACF,UAAU;UAACQ,SAAS,EAAC;QAAgB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC,eACNX,OAAA;QAAKM,SAAS,EAAC,6DAA6D;QAAAC,QAAA,eAC1EP,OAAA;UAAKM,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BP,OAAA,CAACF,UAAU;YAACQ,SAAS,EAAC;UAAsC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/DX,OAAA;YAAGM,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAA0B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC3DX,OAAA;YAAGM,SAAS,EAAC,4BAA4B;YAAAC,QAAA,GACtC,CAAAJ,YAAY,aAAZA,YAAY,wBAAAE,oBAAA,GAAZF,YAAY,CAAES,MAAM,cAAAP,oBAAA,uBAApBA,oBAAA,CAAsBQ,MAAM,KAAI,CAAC,EAAC,gBACrC;UAAA;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACG,EAAA,GAtCIb,eAAe;AAwCrB,eAAeA,eAAe;AAAC,IAAAa,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}