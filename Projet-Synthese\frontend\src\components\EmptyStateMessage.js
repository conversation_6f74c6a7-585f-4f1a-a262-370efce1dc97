import React from 'react';
import { Link } from 'react-router-dom';

const EmptyStateMessage = ({ 
  title = "Aucun contenu disponible",
  message = "L'administrateur n'a pas encore ajouté de contenu.",
  actionText = "Aller à l'administration",
  actionLink = "/admin",
  icon = null
}) => {
  const DefaultIcon = () => (
    <svg className="mx-auto h-16 w-16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8V4a1 1 0 00-1-1H7a1 1 0 00-1 1v1m8 0V4.5" />
    </svg>
  );

  return (
    <div className="text-center py-12">
      <div className="bg-gray-50 rounded-lg p-8 max-w-2xl mx-auto">
        <div className="text-gray-400 mb-4">
          {icon || <DefaultIcon />}
        </div>
        <h3 className="text-xl font-semibold text-gray-900 mb-2">{title}</h3>
        <p className="text-gray-600 mb-4">{message}</p>
        <p className="text-sm text-gray-500 mb-6">
          🔗 Toutes les données sont gérées via l'interface d'administration Laravel
        </p>
        <Link
          to={actionLink}
          className="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
        >
          {actionText}
        </Link>
      </div>
    </div>
  );
};

export default EmptyStateMessage;
