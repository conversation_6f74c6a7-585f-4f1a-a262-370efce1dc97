{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useAuth } from '../context/AuthContext';\n\n/**\n * Hook personnalisé pour utiliser l'authentification de manière sécurisée\n * Retourne des valeurs par défaut si le contexte n'est pas disponible\n */\nconst useSafeAuth = () => {\n  _s();\n  try {\n    return useAuth();\n  } catch (error) {\n    // Si le contexte n'est pas disponible, retourner des valeurs par défaut\n    console.warn('AuthContext non disponible, utilisation des valeurs par défaut');\n    return {\n      state: {\n        isAuthenticated: false,\n        user: null,\n        loading: false,\n        error: null,\n        token: null\n      },\n      login: () => Promise.reject(new Error('Authentification non disponible')),\n      register: () => Promise.reject(new Error('Authentification non disponible')),\n      logout: () => {\n        console.log('Déconnexion en mode démonstration');\n        window.location.href = '/';\n      },\n      clearError: () => {}\n    };\n  }\n};\n_s(useSafeAuth, \"aXa0DhOnbpb+WuJfaBQuXhXHp4U=\", false, function () {\n  return [useAuth];\n});\nexport default useSafeAuth;", "map": {"version": 3, "names": ["useAuth", "useSafeAuth", "_s", "error", "console", "warn", "state", "isAuthenticated", "user", "loading", "token", "login", "Promise", "reject", "Error", "register", "logout", "log", "window", "location", "href", "clearError"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet-Synthese/frontend/src/hooks/useSafeAuth.js"], "sourcesContent": ["import { useAuth } from '../context/AuthContext';\n\n/**\n * Hook personnalisé pour utiliser l'authentification de manière sécurisée\n * Retourne des valeurs par défaut si le contexte n'est pas disponible\n */\nconst useSafeAuth = () => {\n  try {\n    return useAuth();\n  } catch (error) {\n    // Si le contexte n'est pas disponible, retourner des valeurs par défaut\n    console.warn('AuthContext non disponible, utilisation des valeurs par défaut');\n    return {\n      state: {\n        isAuthenticated: false,\n        user: null,\n        loading: false,\n        error: null,\n        token: null\n      },\n      login: () => Promise.reject(new Error('Authentification non disponible')),\n      register: () => Promise.reject(new Error('Authentification non disponible')),\n      logout: () => {\n        console.log('Déconnexion en mode démonstration');\n        window.location.href = '/';\n      },\n      clearError: () => {}\n    };\n  }\n};\n\nexport default useSafeAuth;\n"], "mappings": ";AAAA,SAASA,OAAO,QAAQ,wBAAwB;;AAEhD;AACA;AACA;AACA;AACA,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,IAAI;IACF,OAAOF,OAAO,CAAC,CAAC;EAClB,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd;IACAC,OAAO,CAACC,IAAI,CAAC,gEAAgE,CAAC;IAC9E,OAAO;MACLC,KAAK,EAAE;QACLC,eAAe,EAAE,KAAK;QACtBC,IAAI,EAAE,IAAI;QACVC,OAAO,EAAE,KAAK;QACdN,KAAK,EAAE,IAAI;QACXO,KAAK,EAAE;MACT,CAAC;MACDC,KAAK,EAAEA,CAAA,KAAMC,OAAO,CAACC,MAAM,CAAC,IAAIC,KAAK,CAAC,iCAAiC,CAAC,CAAC;MACzEC,QAAQ,EAAEA,CAAA,KAAMH,OAAO,CAACC,MAAM,CAAC,IAAIC,KAAK,CAAC,iCAAiC,CAAC,CAAC;MAC5EE,MAAM,EAAEA,CAAA,KAAM;QACZZ,OAAO,CAACa,GAAG,CAAC,mCAAmC,CAAC;QAChDC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC5B,CAAC;MACDC,UAAU,EAAEA,CAAA,KAAM,CAAC;IACrB,CAAC;EACH;AACF,CAAC;AAACnB,EAAA,CAvBID,WAAW;EAAA,QAEND,OAAO;AAAA;AAuBlB,eAAeC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}