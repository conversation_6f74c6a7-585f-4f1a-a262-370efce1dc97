<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\Product;
use App\Models\Promotion;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class OrderController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:sanctum');
    }

    public function index()
    {
        $orders = auth()->user()->orders()
            ->with(['items.product', 'address', 'promotion'])
            ->latest()
            ->paginate(10);

        return response()->json($orders);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'address_id' => 'required|exists:addresses,id',
            'promotion_code' => 'nullable|string',
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|exists:products,id',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.is_grilled' => 'boolean',
            'special_instructions' => 'nullable|string'
        ]);

        try {
            DB::beginTransaction();

            $subtotal = 0;
            $orderItems = [];

            foreach ($validated['items'] as $item) {
                $product = Product::findOrFail($item['product_id']);

                if ($product->stock < $item['quantity']) {
                    throw new \Exception("Insufficient stock for product: {$product->name}");
                }

                $unitPrice = $product->sale_price ?? $product->price;
                $subtotal += $unitPrice * $item['quantity'];

                $product->decrement('stock', $item['quantity']);

                $orderItems[] = [
                    'product_id' => $product->id,
                    'quantity' => $item['quantity'],
                    'unit_price' => $unitPrice,
                    'subtotal' => $unitPrice * $item['quantity'],
                    'is_grilled' => $item['is_grilled'] ?? false
                ];
            }

            $tax = $subtotal * 0.15;
            $shipping = 15.00;
            $discount = 0;
            $promotion = null;

            // Gestion des promotions
            if (!empty($validated['promotion_code'])) {
                $promotion = Promotion::byCode($validated['promotion_code'])->first();

                if ($promotion && $promotion->canBeUsed($subtotal)) {
                    $discount = $promotion->calculateDiscount($subtotal);

                    // Gestion de la livraison gratuite
                    if ($promotion->type === Promotion::TYPE_FREE_SHIPPING) {
                        $shipping = 0;
                    }
                }
            }

            $total = $subtotal + $tax + $shipping - $discount;

            $order = auth()->user()->orders()->create([
                'address_id' => $validated['address_id'],
                'promotion_id' => $promotion?->id,
                'order_number' => 'ORD-' . uniqid(),
                'subtotal' => $subtotal,
                'tax' => $tax,
                'shipping_cost' => $shipping,
                'discount' => $discount,
                'total' => $total,
                'status' => Order::STATUS_PENDING,
                'payment_status' => Order::PAYMENT_PENDING,
                'special_instructions' => $validated['special_instructions'] ?? null
            ]);

            // Incrémenter l'utilisation de la promotion
            if ($promotion && $discount > 0) {
                $promotion->incrementUsage();
            }

            $order->items()->createMany($orderItems);

            DB::commit();

            return response()->json([
                'message' => 'Order created successfully',
                'order' => $order->load(['items.product', 'address', 'promotion'])
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'message' => 'Failed to create order',
                'error' => $e->getMessage()
            ], 422);
        }
    }

    public function show(Order $order)
    {
        $this->authorize('view', $order);
        return response()->json($order->load(['items.product', 'address']));
    }

    public function cancel(Order $order)
    {
        $this->authorize('update', $order);

        if (!in_array($order->status, [Order::STATUS_PENDING, Order::STATUS_PROCESSING])) {
            return response()->json([
                'message' => 'This order cannot be cancelled'
            ], 422);
        }

        $order->update([
            'status' => Order::STATUS_CANCELLED
        ]);

        // Restore stock
        foreach ($order->items as $item) {
            $item->product->increment('stock', $item->quantity);
        }

        return response()->json([
            'message' => 'Order cancelled successfully',
            'order' => $order
        ]);
    }
}
