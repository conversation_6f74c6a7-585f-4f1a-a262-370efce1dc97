{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Projet-Synthese\\\\frontend\\\\src\\\\pages\\\\Admin\\\\CategoriesCRUD.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useData } from '../../context/DataContext';\nimport { categoryService } from '../../services/crudService';\nimport { toast } from 'react-toastify';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CategoriesCRUD = () => {\n  _s();\n  const {\n    state,\n    dispatch,\n    DataActions\n  } = useData();\n  const {\n    categories,\n    loading\n  } = state;\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const [currentCategory, setCurrentCategory] = useState(null);\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    color: 'bg-blue-100',\n    textColor: 'text-blue-800',\n    status: 'active'\n  });\n\n  // Couleurs disponibles pour les catégories\n  const colorOptions = [{\n    bg: 'bg-red-100',\n    text: 'text-red-800',\n    label: 'Rouge'\n  }, {\n    bg: 'bg-green-100',\n    text: 'text-green-800',\n    label: 'Vert'\n  }, {\n    bg: 'bg-blue-100',\n    text: 'text-blue-800',\n    label: 'Bleu'\n  }, {\n    bg: 'bg-yellow-100',\n    text: 'text-yellow-800',\n    label: 'Jaune'\n  }, {\n    bg: 'bg-purple-100',\n    text: 'text-purple-800',\n    label: 'Violet'\n  }, {\n    bg: 'bg-gray-100',\n    text: 'text-gray-800',\n    label: 'Gris'\n  }, {\n    bg: 'bg-orange-100',\n    text: 'text-orange-800',\n    label: 'Orange'\n  }, {\n    bg: 'bg-pink-100',\n    text: 'text-pink-800',\n    label: 'Rose'\n  }];\n\n  // Gestion des changements dans le formulaire\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  // Gestion du changement de couleur\n  const handleColorChange = colorOption => {\n    setFormData(prev => ({\n      ...prev,\n      color: colorOption.bg,\n      textColor: colorOption.text\n    }));\n  };\n\n  // Ouvrir le modal pour ajouter/modifier une catégorie\n  const openModal = (category = null) => {\n    if (category) {\n      setCurrentCategory(category);\n      setFormData({\n        name: category.name,\n        description: category.description,\n        color: category.color,\n        textColor: category.textColor,\n        status: category.status\n      });\n    } else {\n      setCurrentCategory(null);\n      setFormData({\n        name: '',\n        description: '',\n        color: 'bg-blue-100',\n        textColor: 'text-blue-800',\n        status: 'active'\n      });\n    }\n    setIsModalOpen(true);\n  };\n\n  // Fermer le modal\n  const closeModal = () => {\n    setIsModalOpen(false);\n    setCurrentCategory(null);\n    setFormData({\n      name: '',\n      description: '',\n      color: 'bg-blue-100',\n      textColor: 'text-blue-800',\n      status: 'active'\n    });\n  };\n\n  // Soumettre le formulaire\n  const handleSubmit = async e => {\n    e.preventDefault();\n    dispatch({\n      type: DataActions.SET_LOADING,\n      payload: true\n    });\n    try {\n      if (currentCategory) {\n        // Mise à jour d'une catégorie existante\n        const result = await categoryService.update(currentCategory.id, formData);\n        if (result.success) {\n          dispatch({\n            type: DataActions.UPDATE_CATEGORY,\n            payload: {\n              ...formData,\n              id: currentCategory.id\n            }\n          });\n          toast.success('Catégorie mise à jour avec succès');\n        } else {\n          // Fallback: mise à jour locale\n          dispatch({\n            type: DataActions.UPDATE_CATEGORY,\n            payload: {\n              ...formData,\n              id: currentCategory.id\n            }\n          });\n          toast.success('Catégorie mise à jour (mode démonstration)');\n        }\n      } else {\n        // Création d'une nouvelle catégorie\n        const result = await categoryService.create(formData);\n        if (result.success) {\n          dispatch({\n            type: DataActions.ADD_CATEGORY,\n            payload: result.data\n          });\n          toast.success('Catégorie créée avec succès');\n        } else {\n          // Fallback: ajout local\n          const newCategory = {\n            ...formData,\n            id: Date.now()\n          };\n          dispatch({\n            type: DataActions.ADD_CATEGORY,\n            payload: newCategory\n          });\n          toast.success('Catégorie créée (mode démonstration)');\n        }\n      }\n      closeModal();\n    } catch (err) {\n      console.error('Erreur lors de l\\'enregistrement de la catégorie', err);\n      toast.error('Erreur lors de l\\'enregistrement de la catégorie');\n    } finally {\n      dispatch({\n        type: DataActions.SET_LOADING,\n        payload: false\n      });\n    }\n  };\n\n  // Supprimer une catégorie\n  const handleDelete = async id => {\n    if (window.confirm('Êtes-vous sûr de vouloir supprimer cette catégorie ?')) {\n      dispatch({\n        type: DataActions.SET_LOADING,\n        payload: true\n      });\n      try {\n        const result = await categoryService.delete(id);\n        if (result.success) {\n          dispatch({\n            type: DataActions.DELETE_CATEGORY,\n            payload: id\n          });\n          toast.success('Catégorie supprimée avec succès');\n        } else {\n          // Fallback: suppression locale\n          dispatch({\n            type: DataActions.DELETE_CATEGORY,\n            payload: id\n          });\n          toast.success('Catégorie supprimée (mode démonstration)');\n        }\n      } catch (err) {\n        console.error('Erreur lors de la suppression de la catégorie', err);\n        toast.error('Erreur lors de la suppression de la catégorie');\n      } finally {\n        dispatch({\n          type: DataActions.SET_LOADING,\n          payload: false\n        });\n      }\n    }\n  };\n\n  // Basculer le statut d'une catégorie\n  const toggleStatus = async (id, currentStatus) => {\n    const newStatus = currentStatus === 'active' ? 'inactive' : 'active';\n    dispatch({\n      type: DataActions.SET_LOADING,\n      payload: true\n    });\n    try {\n      const result = await categoryService.update(id, {\n        status: newStatus\n      });\n      if (result.success) {\n        dispatch({\n          type: DataActions.UPDATE_CATEGORY,\n          payload: {\n            id,\n            status: newStatus\n          }\n        });\n        toast.success(`Catégorie ${newStatus === 'active' ? 'activée' : 'désactivée'}`);\n      } else {\n        // Fallback: mise à jour locale\n        dispatch({\n          type: DataActions.UPDATE_CATEGORY,\n          payload: {\n            id,\n            status: newStatus\n          }\n        });\n        toast.success(`Catégorie ${newStatus === 'active' ? 'activée' : 'désactivée'} (mode démonstration)`);\n      }\n    } catch (err) {\n      console.error('Erreur lors du changement de statut', err);\n      toast.error('Erreur lors du changement de statut');\n    } finally {\n      dispatch({\n        type: DataActions.SET_LOADING,\n        payload: false\n      });\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-gray-900\",\n          children: \"Gestion des Cat\\xE9gories\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-500\",\n          children: [categories.length, \" cat\\xE9gorie(s) \\u2022 CRUD complet activ\\xE9\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => openModal(),\n        className: \"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700\",\n        children: \"Ajouter une cat\\xE9gorie\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 193,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n      children: categories.map(category => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: `p-4 ${category.color}`,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-start\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: `text-lg font-semibold ${category.textColor}`,\n              children: category.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `px-2 py-1 text-xs rounded-full ${category.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`,\n              children: category.status === 'active' ? 'Actif' : 'Inactif'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 text-sm mb-4\",\n            children: category.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-center\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => openModal(category),\n                className: \"text-indigo-600 hover:text-indigo-900 text-sm\",\n                children: \"Modifier\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => toggleStatus(category.id, category.status),\n                className: `text-sm ${category.status === 'active' ? 'text-orange-600 hover:text-orange-900' : 'text-green-600 hover:text-green-900'}`,\n                children: category.status === 'active' ? 'Désactiver' : 'Activer'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleDelete(category.id),\n                className: \"text-red-600 hover:text-red-900 text-sm\",\n                children: \"Supprimer\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 13\n        }, this)]\n      }, category.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 209,\n      columnNumber: 7\n    }, this), isModalOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-lg w-full max-w-md mx-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-6 py-4 border-b\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold\",\n            children: currentCategory ? 'Modifier la catégorie' : 'Ajouter une catégorie'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"name\",\n                className: \"block text-gray-700 mb-2\",\n                children: \"Nom\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"name\",\n                name: \"name\",\n                value: formData.name,\n                onChange: handleInputChange,\n                className: \"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"description\",\n                className: \"block text-gray-700 mb-2\",\n                children: \"Description\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                id: \"description\",\n                name: \"description\",\n                value: formData.description,\n                onChange: handleInputChange,\n                className: \"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\",\n                rows: \"3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-gray-700 mb-2\",\n                children: \"Couleur\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-4 gap-2\",\n                children: colorOptions.map((colorOption, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: () => handleColorChange(colorOption),\n                  className: `p-3 rounded-lg border-2 ${colorOption.bg} ${colorOption.text} ${formData.color === colorOption.bg ? 'border-gray-800' : 'border-gray-300'}`,\n                  children: colorOption.label\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 303,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"status\",\n                className: \"block text-gray-700 mb-2\",\n                children: \"Statut\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                id: \"status\",\n                name: \"status\",\n                value: formData.status,\n                onChange: handleInputChange,\n                className: \"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"active\",\n                  children: \"Actif\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 326,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"inactive\",\n                  children: \"Inactif\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 327,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"px-6 py-4 bg-gray-50 border-t flex justify-end\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: closeModal,\n              className: \"px-4 py-2 border rounded-lg text-gray-700 mr-2 hover:bg-gray-100\",\n              children: \"Annuler\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              disabled: loading,\n              className: \"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50\",\n              children: loading ? 'Enregistrement...' : currentCategory ? 'Mettre à jour' : 'Ajouter'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 266,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 265,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 191,\n    columnNumber: 5\n  }, this);\n};\n_s(CategoriesCRUD, \"NvRcI3GuowguJWLhcYgAI14UFM0=\", false, function () {\n  return [useData];\n});\n_c = CategoriesCRUD;\nexport default CategoriesCRUD;\nvar _c;\n$RefreshReg$(_c, \"CategoriesCRUD\");", "map": {"version": 3, "names": ["React", "useState", "useData", "categoryService", "toast", "jsxDEV", "_jsxDEV", "CategoriesCRUD", "_s", "state", "dispatch", "DataActions", "categories", "loading", "isModalOpen", "setIsModalOpen", "currentCategory", "setCurrentCategory", "formData", "setFormData", "name", "description", "color", "textColor", "status", "colorOptions", "bg", "text", "label", "handleInputChange", "e", "value", "target", "prev", "handleColorChange", "colorOption", "openModal", "category", "closeModal", "handleSubmit", "preventDefault", "type", "SET_LOADING", "payload", "result", "update", "id", "success", "UPDATE_CATEGORY", "create", "ADD_CATEGORY", "data", "newCategory", "Date", "now", "err", "console", "error", "handleDelete", "window", "confirm", "delete", "DELETE_CATEGORY", "toggleStatus", "currentStatus", "newStatus", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "onClick", "map", "onSubmit", "htmlFor", "onChange", "required", "rows", "index", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet-Synthese/frontend/src/pages/Admin/CategoriesCRUD.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useData } from '../../context/DataContext';\nimport { categoryService } from '../../services/crudService';\nimport { toast } from 'react-toastify';\n\nconst CategoriesCRUD = () => {\n  const { state, dispatch, DataActions } = useData();\n  const { categories, loading } = state;\n  \n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const [currentCategory, setCurrentCategory] = useState(null);\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    color: 'bg-blue-100',\n    textColor: 'text-blue-800',\n    status: 'active'\n  });\n\n  // Couleurs disponibles pour les catégories\n  const colorOptions = [\n    { bg: 'bg-red-100', text: 'text-red-800', label: 'Rouge' },\n    { bg: 'bg-green-100', text: 'text-green-800', label: 'Vert' },\n    { bg: 'bg-blue-100', text: 'text-blue-800', label: 'Bleu' },\n    { bg: 'bg-yellow-100', text: 'text-yellow-800', label: 'Jaune' },\n    { bg: 'bg-purple-100', text: 'text-purple-800', label: 'Violet' },\n    { bg: 'bg-gray-100', text: 'text-gray-800', label: 'Gris' },\n    { bg: 'bg-orange-100', text: 'text-orange-800', label: 'Orange' },\n    { bg: 'bg-pink-100', text: 'text-pink-800', label: 'Rose' }\n  ];\n\n  // Gestion des changements dans le formulaire\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({ ...prev, [name]: value }));\n  };\n\n  // Gestion du changement de couleur\n  const handleColorChange = (colorOption) => {\n    setFormData(prev => ({\n      ...prev,\n      color: colorOption.bg,\n      textColor: colorOption.text\n    }));\n  };\n\n  // Ouvrir le modal pour ajouter/modifier une catégorie\n  const openModal = (category = null) => {\n    if (category) {\n      setCurrentCategory(category);\n      setFormData({\n        name: category.name,\n        description: category.description,\n        color: category.color,\n        textColor: category.textColor,\n        status: category.status\n      });\n    } else {\n      setCurrentCategory(null);\n      setFormData({\n        name: '',\n        description: '',\n        color: 'bg-blue-100',\n        textColor: 'text-blue-800',\n        status: 'active'\n      });\n    }\n    setIsModalOpen(true);\n  };\n\n  // Fermer le modal\n  const closeModal = () => {\n    setIsModalOpen(false);\n    setCurrentCategory(null);\n    setFormData({\n      name: '',\n      description: '',\n      color: 'bg-blue-100',\n      textColor: 'text-blue-800',\n      status: 'active'\n    });\n  };\n\n  // Soumettre le formulaire\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    dispatch({ type: DataActions.SET_LOADING, payload: true });\n\n    try {\n      if (currentCategory) {\n        // Mise à jour d'une catégorie existante\n        const result = await categoryService.update(currentCategory.id, formData);\n        \n        if (result.success) {\n          dispatch({ \n            type: DataActions.UPDATE_CATEGORY, \n            payload: { ...formData, id: currentCategory.id }\n          });\n          toast.success('Catégorie mise à jour avec succès');\n        } else {\n          // Fallback: mise à jour locale\n          dispatch({ \n            type: DataActions.UPDATE_CATEGORY, \n            payload: { ...formData, id: currentCategory.id }\n          });\n          toast.success('Catégorie mise à jour (mode démonstration)');\n        }\n      } else {\n        // Création d'une nouvelle catégorie\n        const result = await categoryService.create(formData);\n        \n        if (result.success) {\n          dispatch({ type: DataActions.ADD_CATEGORY, payload: result.data });\n          toast.success('Catégorie créée avec succès');\n        } else {\n          // Fallback: ajout local\n          const newCategory = {\n            ...formData,\n            id: Date.now()\n          };\n          dispatch({ type: DataActions.ADD_CATEGORY, payload: newCategory });\n          toast.success('Catégorie créée (mode démonstration)');\n        }\n      }\n      \n      closeModal();\n    } catch (err) {\n      console.error('Erreur lors de l\\'enregistrement de la catégorie', err);\n      toast.error('Erreur lors de l\\'enregistrement de la catégorie');\n    } finally {\n      dispatch({ type: DataActions.SET_LOADING, payload: false });\n    }\n  };\n\n  // Supprimer une catégorie\n  const handleDelete = async (id) => {\n    if (window.confirm('Êtes-vous sûr de vouloir supprimer cette catégorie ?')) {\n      dispatch({ type: DataActions.SET_LOADING, payload: true });\n      \n      try {\n        const result = await categoryService.delete(id);\n        \n        if (result.success) {\n          dispatch({ type: DataActions.DELETE_CATEGORY, payload: id });\n          toast.success('Catégorie supprimée avec succès');\n        } else {\n          // Fallback: suppression locale\n          dispatch({ type: DataActions.DELETE_CATEGORY, payload: id });\n          toast.success('Catégorie supprimée (mode démonstration)');\n        }\n      } catch (err) {\n        console.error('Erreur lors de la suppression de la catégorie', err);\n        toast.error('Erreur lors de la suppression de la catégorie');\n      } finally {\n        dispatch({ type: DataActions.SET_LOADING, payload: false });\n      }\n    }\n  };\n\n  // Basculer le statut d'une catégorie\n  const toggleStatus = async (id, currentStatus) => {\n    const newStatus = currentStatus === 'active' ? 'inactive' : 'active';\n    dispatch({ type: DataActions.SET_LOADING, payload: true });\n    \n    try {\n      const result = await categoryService.update(id, { status: newStatus });\n      \n      if (result.success) {\n        dispatch({ \n          type: DataActions.UPDATE_CATEGORY, \n          payload: { id, status: newStatus }\n        });\n        toast.success(`Catégorie ${newStatus === 'active' ? 'activée' : 'désactivée'}`);\n      } else {\n        // Fallback: mise à jour locale\n        dispatch({ \n          type: DataActions.UPDATE_CATEGORY, \n          payload: { id, status: newStatus }\n        });\n        toast.success(`Catégorie ${newStatus === 'active' ? 'activée' : 'désactivée'} (mode démonstration)`);\n      }\n    } catch (err) {\n      console.error('Erreur lors du changement de statut', err);\n      toast.error('Erreur lors du changement de statut');\n    } finally {\n      dispatch({ type: DataActions.SET_LOADING, payload: false });\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* En-tête */}\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">Gestion des Catégories</h1>\n          <p className=\"text-sm text-gray-500\">\n            {categories.length} catégorie(s) • CRUD complet activé\n          </p>\n        </div>\n        <button\n          onClick={() => openModal()}\n          className=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700\"\n        >\n          Ajouter une catégorie\n        </button>\n      </div>\n\n      {/* Grille des catégories */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n        {categories.map((category) => (\n          <div key={category.id} className=\"bg-white rounded-lg shadow-sm border overflow-hidden\">\n            <div className={`p-4 ${category.color}`}>\n              <div className=\"flex justify-between items-start\">\n                <h3 className={`text-lg font-semibold ${category.textColor}`}>\n                  {category.name}\n                </h3>\n                <span className={`px-2 py-1 text-xs rounded-full ${\n                  category.status === 'active' \n                    ? 'bg-green-100 text-green-800' \n                    : 'bg-red-100 text-red-800'\n                }`}>\n                  {category.status === 'active' ? 'Actif' : 'Inactif'}\n                </span>\n              </div>\n            </div>\n            \n            <div className=\"p-4\">\n              <p className=\"text-gray-600 text-sm mb-4\">\n                {category.description}\n              </p>\n              \n              <div className=\"flex justify-between items-center\">\n                <div className=\"flex space-x-2\">\n                  <button\n                    onClick={() => openModal(category)}\n                    className=\"text-indigo-600 hover:text-indigo-900 text-sm\"\n                  >\n                    Modifier\n                  </button>\n                  <button\n                    onClick={() => toggleStatus(category.id, category.status)}\n                    className={`text-sm ${\n                      category.status === 'active' \n                        ? 'text-orange-600 hover:text-orange-900' \n                        : 'text-green-600 hover:text-green-900'\n                    }`}\n                  >\n                    {category.status === 'active' ? 'Désactiver' : 'Activer'}\n                  </button>\n                  <button\n                    onClick={() => handleDelete(category.id)}\n                    className=\"text-red-600 hover:text-red-900 text-sm\"\n                  >\n                    Supprimer\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* Modal pour ajouter/modifier une catégorie */}\n      {isModalOpen && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n          <div className=\"bg-white rounded-lg shadow-lg w-full max-w-md mx-4\">\n            <div className=\"px-6 py-4 border-b\">\n              <h3 className=\"text-lg font-semibold\">\n                {currentCategory ? 'Modifier la catégorie' : 'Ajouter une catégorie'}\n              </h3>\n            </div>\n            <form onSubmit={handleSubmit}>\n              <div className=\"p-6\">\n                <div className=\"mb-4\">\n                  <label htmlFor=\"name\" className=\"block text-gray-700 mb-2\">Nom</label>\n                  <input\n                    type=\"text\"\n                    id=\"name\"\n                    name=\"name\"\n                    value={formData.name}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n                    required\n                  />\n                </div>\n                \n                <div className=\"mb-4\">\n                  <label htmlFor=\"description\" className=\"block text-gray-700 mb-2\">Description</label>\n                  <textarea\n                    id=\"description\"\n                    name=\"description\"\n                    value={formData.description}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n                    rows=\"3\"\n                  ></textarea>\n                </div>\n                \n                <div className=\"mb-4\">\n                  <label className=\"block text-gray-700 mb-2\">Couleur</label>\n                  <div className=\"grid grid-cols-4 gap-2\">\n                    {colorOptions.map((colorOption, index) => (\n                      <button\n                        key={index}\n                        type=\"button\"\n                        onClick={() => handleColorChange(colorOption)}\n                        className={`p-3 rounded-lg border-2 ${colorOption.bg} ${colorOption.text} ${\n                          formData.color === colorOption.bg ? 'border-gray-800' : 'border-gray-300'\n                        }`}\n                      >\n                        {colorOption.label}\n                      </button>\n                    ))}\n                  </div>\n                </div>\n                \n                <div className=\"mb-4\">\n                  <label htmlFor=\"status\" className=\"block text-gray-700 mb-2\">Statut</label>\n                  <select\n                    id=\"status\"\n                    name=\"status\"\n                    value={formData.status}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n                  >\n                    <option value=\"active\">Actif</option>\n                    <option value=\"inactive\">Inactif</option>\n                  </select>\n                </div>\n              </div>\n              \n              <div className=\"px-6 py-4 bg-gray-50 border-t flex justify-end\">\n                <button\n                  type=\"button\"\n                  onClick={closeModal}\n                  className=\"px-4 py-2 border rounded-lg text-gray-700 mr-2 hover:bg-gray-100\"\n                >\n                  Annuler\n                </button>\n                <button\n                  type=\"submit\"\n                  disabled={loading}\n                  className=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50\"\n                >\n                  {loading ? 'Enregistrement...' : (currentCategory ? 'Mettre à jour' : 'Ajouter')}\n                </button>\n              </div>\n            </form>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default CategoriesCRUD;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,OAAO,QAAQ,2BAA2B;AACnD,SAASC,eAAe,QAAQ,4BAA4B;AAC5D,SAASC,KAAK,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM;IAAEC,KAAK;IAAEC,QAAQ;IAAEC;EAAY,CAAC,GAAGT,OAAO,CAAC,CAAC;EAClD,MAAM;IAAEU,UAAU;IAAEC;EAAQ,CAAC,GAAGJ,KAAK;EAErC,MAAM,CAACK,WAAW,EAAEC,cAAc,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACe,eAAe,EAAEC,kBAAkB,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACiB,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CAAC;IACvCmB,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,KAAK,EAAE,aAAa;IACpBC,SAAS,EAAE,eAAe;IAC1BC,MAAM,EAAE;EACV,CAAC,CAAC;;EAEF;EACA,MAAMC,YAAY,GAAG,CACnB;IAAEC,EAAE,EAAE,YAAY;IAAEC,IAAI,EAAE,cAAc;IAAEC,KAAK,EAAE;EAAQ,CAAC,EAC1D;IAAEF,EAAE,EAAE,cAAc;IAAEC,IAAI,EAAE,gBAAgB;IAAEC,KAAK,EAAE;EAAO,CAAC,EAC7D;IAAEF,EAAE,EAAE,aAAa;IAAEC,IAAI,EAAE,eAAe;IAAEC,KAAK,EAAE;EAAO,CAAC,EAC3D;IAAEF,EAAE,EAAE,eAAe;IAAEC,IAAI,EAAE,iBAAiB;IAAEC,KAAK,EAAE;EAAQ,CAAC,EAChE;IAAEF,EAAE,EAAE,eAAe;IAAEC,IAAI,EAAE,iBAAiB;IAAEC,KAAK,EAAE;EAAS,CAAC,EACjE;IAAEF,EAAE,EAAE,aAAa;IAAEC,IAAI,EAAE,eAAe;IAAEC,KAAK,EAAE;EAAO,CAAC,EAC3D;IAAEF,EAAE,EAAE,eAAe;IAAEC,IAAI,EAAE,iBAAiB;IAAEC,KAAK,EAAE;EAAS,CAAC,EACjE;IAAEF,EAAE,EAAE,aAAa;IAAEC,IAAI,EAAE,eAAe;IAAEC,KAAK,EAAE;EAAO,CAAC,CAC5D;;EAED;EACA,MAAMC,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEV,IAAI;MAAEW;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChCb,WAAW,CAACc,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACb,IAAI,GAAGW;IAAM,CAAC,CAAC,CAAC;EACnD,CAAC;;EAED;EACA,MAAMG,iBAAiB,GAAIC,WAAW,IAAK;IACzChB,WAAW,CAACc,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPX,KAAK,EAAEa,WAAW,CAACT,EAAE;MACrBH,SAAS,EAAEY,WAAW,CAACR;IACzB,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMS,SAAS,GAAGA,CAACC,QAAQ,GAAG,IAAI,KAAK;IACrC,IAAIA,QAAQ,EAAE;MACZpB,kBAAkB,CAACoB,QAAQ,CAAC;MAC5BlB,WAAW,CAAC;QACVC,IAAI,EAAEiB,QAAQ,CAACjB,IAAI;QACnBC,WAAW,EAAEgB,QAAQ,CAAChB,WAAW;QACjCC,KAAK,EAAEe,QAAQ,CAACf,KAAK;QACrBC,SAAS,EAAEc,QAAQ,CAACd,SAAS;QAC7BC,MAAM,EAAEa,QAAQ,CAACb;MACnB,CAAC,CAAC;IACJ,CAAC,MAAM;MACLP,kBAAkB,CAAC,IAAI,CAAC;MACxBE,WAAW,CAAC;QACVC,IAAI,EAAE,EAAE;QACRC,WAAW,EAAE,EAAE;QACfC,KAAK,EAAE,aAAa;QACpBC,SAAS,EAAE,eAAe;QAC1BC,MAAM,EAAE;MACV,CAAC,CAAC;IACJ;IACAT,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;;EAED;EACA,MAAMuB,UAAU,GAAGA,CAAA,KAAM;IACvBvB,cAAc,CAAC,KAAK,CAAC;IACrBE,kBAAkB,CAAC,IAAI,CAAC;IACxBE,WAAW,CAAC;MACVC,IAAI,EAAE,EAAE;MACRC,WAAW,EAAE,EAAE;MACfC,KAAK,EAAE,aAAa;MACpBC,SAAS,EAAE,eAAe;MAC1BC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMe,YAAY,GAAG,MAAOT,CAAC,IAAK;IAChCA,CAAC,CAACU,cAAc,CAAC,CAAC;IAClB9B,QAAQ,CAAC;MAAE+B,IAAI,EAAE9B,WAAW,CAAC+B,WAAW;MAAEC,OAAO,EAAE;IAAK,CAAC,CAAC;IAE1D,IAAI;MACF,IAAI3B,eAAe,EAAE;QACnB;QACA,MAAM4B,MAAM,GAAG,MAAMzC,eAAe,CAAC0C,MAAM,CAAC7B,eAAe,CAAC8B,EAAE,EAAE5B,QAAQ,CAAC;QAEzE,IAAI0B,MAAM,CAACG,OAAO,EAAE;UAClBrC,QAAQ,CAAC;YACP+B,IAAI,EAAE9B,WAAW,CAACqC,eAAe;YACjCL,OAAO,EAAE;cAAE,GAAGzB,QAAQ;cAAE4B,EAAE,EAAE9B,eAAe,CAAC8B;YAAG;UACjD,CAAC,CAAC;UACF1C,KAAK,CAAC2C,OAAO,CAAC,mCAAmC,CAAC;QACpD,CAAC,MAAM;UACL;UACArC,QAAQ,CAAC;YACP+B,IAAI,EAAE9B,WAAW,CAACqC,eAAe;YACjCL,OAAO,EAAE;cAAE,GAAGzB,QAAQ;cAAE4B,EAAE,EAAE9B,eAAe,CAAC8B;YAAG;UACjD,CAAC,CAAC;UACF1C,KAAK,CAAC2C,OAAO,CAAC,4CAA4C,CAAC;QAC7D;MACF,CAAC,MAAM;QACL;QACA,MAAMH,MAAM,GAAG,MAAMzC,eAAe,CAAC8C,MAAM,CAAC/B,QAAQ,CAAC;QAErD,IAAI0B,MAAM,CAACG,OAAO,EAAE;UAClBrC,QAAQ,CAAC;YAAE+B,IAAI,EAAE9B,WAAW,CAACuC,YAAY;YAAEP,OAAO,EAAEC,MAAM,CAACO;UAAK,CAAC,CAAC;UAClE/C,KAAK,CAAC2C,OAAO,CAAC,6BAA6B,CAAC;QAC9C,CAAC,MAAM;UACL;UACA,MAAMK,WAAW,GAAG;YAClB,GAAGlC,QAAQ;YACX4B,EAAE,EAAEO,IAAI,CAACC,GAAG,CAAC;UACf,CAAC;UACD5C,QAAQ,CAAC;YAAE+B,IAAI,EAAE9B,WAAW,CAACuC,YAAY;YAAEP,OAAO,EAAES;UAAY,CAAC,CAAC;UAClEhD,KAAK,CAAC2C,OAAO,CAAC,sCAAsC,CAAC;QACvD;MACF;MAEAT,UAAU,CAAC,CAAC;IACd,CAAC,CAAC,OAAOiB,GAAG,EAAE;MACZC,OAAO,CAACC,KAAK,CAAC,kDAAkD,EAAEF,GAAG,CAAC;MACtEnD,KAAK,CAACqD,KAAK,CAAC,kDAAkD,CAAC;IACjE,CAAC,SAAS;MACR/C,QAAQ,CAAC;QAAE+B,IAAI,EAAE9B,WAAW,CAAC+B,WAAW;QAAEC,OAAO,EAAE;MAAM,CAAC,CAAC;IAC7D;EACF,CAAC;;EAED;EACA,MAAMe,YAAY,GAAG,MAAOZ,EAAE,IAAK;IACjC,IAAIa,MAAM,CAACC,OAAO,CAAC,sDAAsD,CAAC,EAAE;MAC1ElD,QAAQ,CAAC;QAAE+B,IAAI,EAAE9B,WAAW,CAAC+B,WAAW;QAAEC,OAAO,EAAE;MAAK,CAAC,CAAC;MAE1D,IAAI;QACF,MAAMC,MAAM,GAAG,MAAMzC,eAAe,CAAC0D,MAAM,CAACf,EAAE,CAAC;QAE/C,IAAIF,MAAM,CAACG,OAAO,EAAE;UAClBrC,QAAQ,CAAC;YAAE+B,IAAI,EAAE9B,WAAW,CAACmD,eAAe;YAAEnB,OAAO,EAAEG;UAAG,CAAC,CAAC;UAC5D1C,KAAK,CAAC2C,OAAO,CAAC,iCAAiC,CAAC;QAClD,CAAC,MAAM;UACL;UACArC,QAAQ,CAAC;YAAE+B,IAAI,EAAE9B,WAAW,CAACmD,eAAe;YAAEnB,OAAO,EAAEG;UAAG,CAAC,CAAC;UAC5D1C,KAAK,CAAC2C,OAAO,CAAC,0CAA0C,CAAC;QAC3D;MACF,CAAC,CAAC,OAAOQ,GAAG,EAAE;QACZC,OAAO,CAACC,KAAK,CAAC,+CAA+C,EAAEF,GAAG,CAAC;QACnEnD,KAAK,CAACqD,KAAK,CAAC,+CAA+C,CAAC;MAC9D,CAAC,SAAS;QACR/C,QAAQ,CAAC;UAAE+B,IAAI,EAAE9B,WAAW,CAAC+B,WAAW;UAAEC,OAAO,EAAE;QAAM,CAAC,CAAC;MAC7D;IACF;EACF,CAAC;;EAED;EACA,MAAMoB,YAAY,GAAG,MAAAA,CAAOjB,EAAE,EAAEkB,aAAa,KAAK;IAChD,MAAMC,SAAS,GAAGD,aAAa,KAAK,QAAQ,GAAG,UAAU,GAAG,QAAQ;IACpEtD,QAAQ,CAAC;MAAE+B,IAAI,EAAE9B,WAAW,CAAC+B,WAAW;MAAEC,OAAO,EAAE;IAAK,CAAC,CAAC;IAE1D,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMzC,eAAe,CAAC0C,MAAM,CAACC,EAAE,EAAE;QAAEtB,MAAM,EAAEyC;MAAU,CAAC,CAAC;MAEtE,IAAIrB,MAAM,CAACG,OAAO,EAAE;QAClBrC,QAAQ,CAAC;UACP+B,IAAI,EAAE9B,WAAW,CAACqC,eAAe;UACjCL,OAAO,EAAE;YAAEG,EAAE;YAAEtB,MAAM,EAAEyC;UAAU;QACnC,CAAC,CAAC;QACF7D,KAAK,CAAC2C,OAAO,CAAC,aAAakB,SAAS,KAAK,QAAQ,GAAG,SAAS,GAAG,YAAY,EAAE,CAAC;MACjF,CAAC,MAAM;QACL;QACAvD,QAAQ,CAAC;UACP+B,IAAI,EAAE9B,WAAW,CAACqC,eAAe;UACjCL,OAAO,EAAE;YAAEG,EAAE;YAAEtB,MAAM,EAAEyC;UAAU;QACnC,CAAC,CAAC;QACF7D,KAAK,CAAC2C,OAAO,CAAC,aAAakB,SAAS,KAAK,QAAQ,GAAG,SAAS,GAAG,YAAY,uBAAuB,CAAC;MACtG;IACF,CAAC,CAAC,OAAOV,GAAG,EAAE;MACZC,OAAO,CAACC,KAAK,CAAC,qCAAqC,EAAEF,GAAG,CAAC;MACzDnD,KAAK,CAACqD,KAAK,CAAC,qCAAqC,CAAC;IACpD,CAAC,SAAS;MACR/C,QAAQ,CAAC;QAAE+B,IAAI,EAAE9B,WAAW,CAAC+B,WAAW;QAAEC,OAAO,EAAE;MAAM,CAAC,CAAC;IAC7D;EACF,CAAC;EAED,oBACErC,OAAA;IAAK4D,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExB7D,OAAA;MAAK4D,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChD7D,OAAA;QAAA6D,QAAA,gBACE7D,OAAA;UAAI4D,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5EjE,OAAA;UAAG4D,SAAS,EAAC,uBAAuB;UAAAC,QAAA,GACjCvD,UAAU,CAAC4D,MAAM,EAAC,gDACrB;QAAA;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACNjE,OAAA;QACEmE,OAAO,EAAEA,CAAA,KAAMrC,SAAS,CAAC,CAAE;QAC3B8B,SAAS,EAAC,iEAAiE;QAAAC,QAAA,EAC5E;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNjE,OAAA;MAAK4D,SAAS,EAAC,sDAAsD;MAAAC,QAAA,EAClEvD,UAAU,CAAC8D,GAAG,CAAErC,QAAQ,iBACvB/B,OAAA;QAAuB4D,SAAS,EAAC,sDAAsD;QAAAC,QAAA,gBACrF7D,OAAA;UAAK4D,SAAS,EAAE,OAAO7B,QAAQ,CAACf,KAAK,EAAG;UAAA6C,QAAA,eACtC7D,OAAA;YAAK4D,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC/C7D,OAAA;cAAI4D,SAAS,EAAE,yBAAyB7B,QAAQ,CAACd,SAAS,EAAG;cAAA4C,QAAA,EAC1D9B,QAAQ,CAACjB;YAAI;cAAAgD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eACLjE,OAAA;cAAM4D,SAAS,EAAE,kCACf7B,QAAQ,CAACb,MAAM,KAAK,QAAQ,GACxB,6BAA6B,GAC7B,yBAAyB,EAC5B;cAAA2C,QAAA,EACA9B,QAAQ,CAACb,MAAM,KAAK,QAAQ,GAAG,OAAO,GAAG;YAAS;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENjE,OAAA;UAAK4D,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClB7D,OAAA;YAAG4D,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EACtC9B,QAAQ,CAAChB;UAAW;YAAA+C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,eAEJjE,OAAA;YAAK4D,SAAS,EAAC,mCAAmC;YAAAC,QAAA,eAChD7D,OAAA;cAAK4D,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7B7D,OAAA;gBACEmE,OAAO,EAAEA,CAAA,KAAMrC,SAAS,CAACC,QAAQ,CAAE;gBACnC6B,SAAS,EAAC,+CAA+C;gBAAAC,QAAA,EAC1D;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTjE,OAAA;gBACEmE,OAAO,EAAEA,CAAA,KAAMV,YAAY,CAAC1B,QAAQ,CAACS,EAAE,EAAET,QAAQ,CAACb,MAAM,CAAE;gBAC1D0C,SAAS,EAAE,WACT7B,QAAQ,CAACb,MAAM,KAAK,QAAQ,GACxB,uCAAuC,GACvC,qCAAqC,EACxC;gBAAA2C,QAAA,EAEF9B,QAAQ,CAACb,MAAM,KAAK,QAAQ,GAAG,YAAY,GAAG;cAAS;gBAAA4C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC,eACTjE,OAAA;gBACEmE,OAAO,EAAEA,CAAA,KAAMf,YAAY,CAACrB,QAAQ,CAACS,EAAE,CAAE;gBACzCoB,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,EACpD;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA,GA/CElC,QAAQ,CAACS,EAAE;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAgDhB,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAGLzD,WAAW,iBACVR,OAAA;MAAK4D,SAAS,EAAC,4EAA4E;MAAAC,QAAA,eACzF7D,OAAA;QAAK4D,SAAS,EAAC,oDAAoD;QAAAC,QAAA,gBACjE7D,OAAA;UAAK4D,SAAS,EAAC,oBAAoB;UAAAC,QAAA,eACjC7D,OAAA;YAAI4D,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAClCnD,eAAe,GAAG,uBAAuB,GAAG;UAAuB;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACNjE,OAAA;UAAMqE,QAAQ,EAAEpC,YAAa;UAAA4B,QAAA,gBAC3B7D,OAAA;YAAK4D,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAClB7D,OAAA;cAAK4D,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB7D,OAAA;gBAAOsE,OAAO,EAAC,MAAM;gBAACV,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtEjE,OAAA;gBACEmC,IAAI,EAAC,MAAM;gBACXK,EAAE,EAAC,MAAM;gBACT1B,IAAI,EAAC,MAAM;gBACXW,KAAK,EAAEb,QAAQ,CAACE,IAAK;gBACrByD,QAAQ,EAAEhD,iBAAkB;gBAC5BqC,SAAS,EAAC,yFAAyF;gBACnGY,QAAQ;cAAA;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENjE,OAAA;cAAK4D,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB7D,OAAA;gBAAOsE,OAAO,EAAC,aAAa;gBAACV,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrFjE,OAAA;gBACEwC,EAAE,EAAC,aAAa;gBAChB1B,IAAI,EAAC,aAAa;gBAClBW,KAAK,EAAEb,QAAQ,CAACG,WAAY;gBAC5BwD,QAAQ,EAAEhD,iBAAkB;gBAC5BqC,SAAS,EAAC,yFAAyF;gBACnGa,IAAI,EAAC;cAAG;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eAENjE,OAAA;cAAK4D,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB7D,OAAA;gBAAO4D,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3DjE,OAAA;gBAAK4D,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EACpC1C,YAAY,CAACiD,GAAG,CAAC,CAACvC,WAAW,EAAE6C,KAAK,kBACnC1E,OAAA;kBAEEmC,IAAI,EAAC,QAAQ;kBACbgC,OAAO,EAAEA,CAAA,KAAMvC,iBAAiB,CAACC,WAAW,CAAE;kBAC9C+B,SAAS,EAAE,2BAA2B/B,WAAW,CAACT,EAAE,IAAIS,WAAW,CAACR,IAAI,IACtET,QAAQ,CAACI,KAAK,KAAKa,WAAW,CAACT,EAAE,GAAG,iBAAiB,GAAG,iBAAiB,EACxE;kBAAAyC,QAAA,EAEFhC,WAAW,CAACP;gBAAK,GAPboD,KAAK;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAQJ,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENjE,OAAA;cAAK4D,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB7D,OAAA;gBAAOsE,OAAO,EAAC,QAAQ;gBAACV,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3EjE,OAAA;gBACEwC,EAAE,EAAC,QAAQ;gBACX1B,IAAI,EAAC,QAAQ;gBACbW,KAAK,EAAEb,QAAQ,CAACM,MAAO;gBACvBqD,QAAQ,EAAEhD,iBAAkB;gBAC5BqC,SAAS,EAAC,yFAAyF;gBAAAC,QAAA,gBAEnG7D,OAAA;kBAAQyB,KAAK,EAAC,QAAQ;kBAAAoC,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACrCjE,OAAA;kBAAQyB,KAAK,EAAC,UAAU;kBAAAoC,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENjE,OAAA;YAAK4D,SAAS,EAAC,gDAAgD;YAAAC,QAAA,gBAC7D7D,OAAA;cACEmC,IAAI,EAAC,QAAQ;cACbgC,OAAO,EAAEnC,UAAW;cACpB4B,SAAS,EAAC,kEAAkE;cAAAC,QAAA,EAC7E;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTjE,OAAA;cACEmC,IAAI,EAAC,QAAQ;cACbwC,QAAQ,EAAEpE,OAAQ;cAClBqD,SAAS,EAAC,qFAAqF;cAAAC,QAAA,EAE9FtD,OAAO,GAAG,mBAAmB,GAAIG,eAAe,GAAG,eAAe,GAAG;YAAU;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC/D,EAAA,CA5VID,cAAc;EAAA,QACuBL,OAAO;AAAA;AAAAgF,EAAA,GAD5C3E,cAAc;AA8VpB,eAAeA,cAAc;AAAC,IAAA2E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}