{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Projet-Synthese\\\\frontend\\\\src\\\\pages\\\\Admin\\\\Users.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { toast } from 'react-toastify';\nimport { FaEdit, FaTrash, FaEye, FaUserShield, FaUser } from 'react-icons/fa';\nimport useSafeAuth from '../../hooks/useSafeAuth';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Users = () => {\n  _s();\n  const {\n    state: authState\n  } = useSafeAuth();\n  const [users, setUsers] = useState([]);\n  const [isLoading, setIsLoading] = useState(true);\n  useEffect(() => {\n    fetchUsers();\n  }, []);\n  const fetchUsers = async () => {\n    try {\n      setIsLoading(true);\n      // TODO: Implémenter l'appel API pour récupérer les utilisateurs\n      // const response = await userService.getAllUsers(authState.token);\n      // setUsers(response.data);\n\n      // Données de test temporaires\n      setUsers([{\n        id: 1,\n        name: 'Admin YUMMY',\n        email: '<EMAIL>',\n        role: 'admin',\n        created_at: '2024-01-01T00:00:00Z',\n        email_verified_at: '2024-01-01T00:00:00Z'\n      }, {\n        id: 2,\n        name: 'Marie Dubois',\n        email: '<EMAIL>',\n        role: 'client',\n        created_at: '2024-01-15T00:00:00Z',\n        email_verified_at: '2024-01-15T00:00:00Z'\n      }, {\n        id: 3,\n        name: 'Ahmed Hassan',\n        email: '<EMAIL>',\n        role: 'client',\n        created_at: '2024-02-01T00:00:00Z',\n        email_verified_at: '2024-02-01T00:00:00Z'\n      }, {\n        id: 4,\n        name: 'Sophie Martin',\n        email: '<EMAIL>',\n        role: 'client',\n        created_at: '2024-02-10T00:00:00Z',\n        email_verified_at: '2024-02-10T00:00:00Z'\n      }, {\n        id: 5,\n        name: 'Jean Dupont',\n        email: '<EMAIL>',\n        role: 'client',\n        created_at: '2024-02-15T00:00:00Z',\n        email_verified_at: null\n      }, {\n        id: 6,\n        name: 'Manager Store',\n        email: '<EMAIL>',\n        role: 'manager',\n        created_at: '2024-01-05T00:00:00Z',\n        email_verified_at: '2024-01-05T00:00:00Z'\n      }, {\n        id: 7,\n        name: 'Emma Wilson',\n        email: '<EMAIL>',\n        role: 'client',\n        created_at: '2024-03-01T00:00:00Z',\n        email_verified_at: '2024-03-01T00:00:00Z'\n      }, {\n        id: 8,\n        name: 'Lucas Bernard',\n        email: '<EMAIL>',\n        role: 'client',\n        created_at: '2024-03-05T00:00:00Z',\n        email_verified_at: null\n      }]);\n    } catch (error) {\n      toast.error('Erreur lors du chargement des utilisateurs');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const getRoleIcon = role => {\n    if (role === 'admin') return /*#__PURE__*/_jsxDEV(FaUserShield, {\n      className: \"text-red-500\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 34\n    }, this);\n    if (role === 'manager') return /*#__PURE__*/_jsxDEV(FaUserShield, {\n      className: \"text-orange-500\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 36\n    }, this);\n    return /*#__PURE__*/_jsxDEV(FaUser, {\n      className: \"text-blue-500\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 12\n    }, this);\n  };\n  const getRoleBadge = role => {\n    const baseClasses = \"px-2 py-1 text-xs rounded-full\";\n    if (role === 'admin') {\n      return /*#__PURE__*/_jsxDEV(\"span\", {\n        className: `${baseClasses} bg-red-100 text-red-800`,\n        children: \"Admin\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 14\n      }, this);\n    }\n    if (role === 'manager') {\n      return /*#__PURE__*/_jsxDEV(\"span\", {\n        className: `${baseClasses} bg-orange-100 text-orange-800`,\n        children: \"Manager\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 14\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(\"span\", {\n      className: `${baseClasses} bg-blue-100 text-blue-800`,\n      children: \"Client\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 110,\n      columnNumber: 12\n    }, this);\n  };\n  const formatDate = dateString => {\n    return new Date(dateString).toLocaleDateString('fr-FR');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center\",\n      children: /*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-2xl font-bold text-gray-900\",\n        children: \"Gestion des Utilisateurs\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow-sm rounded-lg overflow-hidden\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"overflow-x-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"min-w-full divide-y divide-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            className: \"bg-gray-50\",\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Utilisateur\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 129,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Email\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 132,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"R\\xF4le\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Date d'inscription\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Statut\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 141,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            className: \"bg-white divide-y divide-gray-200\",\n            children: isLoading ? /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: /*#__PURE__*/_jsxDEV(\"td\", {\n                colSpan: \"6\",\n                className: \"px-6 py-4 text-center\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 153,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 17\n            }, this) : users.length === 0 ? /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: /*#__PURE__*/_jsxDEV(\"td\", {\n                colSpan: \"6\",\n                className: \"px-6 py-4 text-center text-gray-500\",\n                children: \"Aucun utilisateur trouv\\xE9\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 17\n            }, this) : users.map(user => /*#__PURE__*/_jsxDEV(\"tr\", {\n              className: \"hover:bg-gray-50\",\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-shrink-0 h-10 w-10\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center\",\n                      children: getRoleIcon(user.role)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 168,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 167,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"ml-4\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm font-medium text-gray-900\",\n                      children: user.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 173,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 172,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 166,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-900\",\n                  children: user.email\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 178,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap\",\n                children: getRoleBadge(user.role)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-900\",\n                  children: formatDate(user.created_at)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 184,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap\",\n                children: user.email_verified_at ? /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full\",\n                  children: \"V\\xE9rifi\\xE9\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 188,\n                  columnNumber: 25\n                }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded-full\",\n                  children: \"Non v\\xE9rifi\\xE9\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 192,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"text-primary-600 hover:text-primary-900\",\n                    title: \"Voir\",\n                    children: /*#__PURE__*/_jsxDEV(FaEye, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 203,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 199,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"text-blue-600 hover:text-blue-900\",\n                    title: \"Modifier\",\n                    children: /*#__PURE__*/_jsxDEV(FaEdit, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 209,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 205,\n                    columnNumber: 25\n                  }, this), user.role !== 'admin' && /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"text-red-600 hover:text-red-900\",\n                    title: \"Supprimer\",\n                    children: /*#__PURE__*/_jsxDEV(FaTrash, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 216,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 212,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 198,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 21\n              }, this)]\n            }, user.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 118,\n    columnNumber: 5\n  }, this);\n};\n_s(Users, \"Fs8PWc8cJPc6R7J1/UdSgeZAQGs=\", false, function () {\n  return [useSafeAuth];\n});\n_c = Users;\nexport default Users;\nvar _c;\n$RefreshReg$(_c, \"Users\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "toast", "FaEdit", "FaTrash", "FaEye", "FaUserShield", "FaUser", "useSafeAuth", "jsxDEV", "_jsxDEV", "Users", "_s", "state", "authState", "users", "setUsers", "isLoading", "setIsLoading", "fetchUsers", "id", "name", "email", "role", "created_at", "email_verified_at", "error", "getRoleIcon", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getRoleBadge", "baseClasses", "children", "formatDate", "dateString", "Date", "toLocaleDateString", "colSpan", "length", "map", "user", "title", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet-Synthese/frontend/src/pages/Admin/Users.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { toast } from 'react-toastify';\nimport { FaEdit, FaTrash, Fa<PERSON>ye, FaUserShield, FaUser } from 'react-icons/fa';\nimport useSafeAuth from '../../hooks/useSafeAuth';\n\nconst Users = () => {\n  const { state: authState } = useSafeAuth();\n  const [users, setUsers] = useState([]);\n  const [isLoading, setIsLoading] = useState(true);\n\n  useEffect(() => {\n    fetchUsers();\n  }, []);\n\n  const fetchUsers = async () => {\n    try {\n      setIsLoading(true);\n      // TODO: Implémenter l'appel API pour récupérer les utilisateurs\n      // const response = await userService.getAllUsers(authState.token);\n      // setUsers(response.data);\n      \n      // Données de test temporaires\n      setUsers([\n        {\n          id: 1,\n          name: '<PERSON><PERSON>',\n          email: '<EMAIL>',\n          role: 'admin',\n          created_at: '2024-01-01T00:00:00Z',\n          email_verified_at: '2024-01-01T00:00:00Z'\n        },\n        {\n          id: 2,\n          name: '<PERSON> <PERSON>ois',\n          email: '<EMAIL>',\n          role: 'client',\n          created_at: '2024-01-15T00:00:00Z',\n          email_verified_at: '2024-01-15T00:00:00Z'\n        },\n        {\n          id: 3,\n          name: 'Ahmed Hassan',\n          email: '<EMAIL>',\n          role: 'client',\n          created_at: '2024-02-01T00:00:00Z',\n          email_verified_at: '2024-02-01T00:00:00Z'\n        },\n        {\n          id: 4,\n          name: 'Sophie Martin',\n          email: '<EMAIL>',\n          role: 'client',\n          created_at: '2024-02-10T00:00:00Z',\n          email_verified_at: '2024-02-10T00:00:00Z'\n        },\n        {\n          id: 5,\n          name: 'Jean Dupont',\n          email: '<EMAIL>',\n          role: 'client',\n          created_at: '2024-02-15T00:00:00Z',\n          email_verified_at: null\n        },\n        {\n          id: 6,\n          name: 'Manager Store',\n          email: '<EMAIL>',\n          role: 'manager',\n          created_at: '2024-01-05T00:00:00Z',\n          email_verified_at: '2024-01-05T00:00:00Z'\n        },\n        {\n          id: 7,\n          name: 'Emma Wilson',\n          email: '<EMAIL>',\n          role: 'client',\n          created_at: '2024-03-01T00:00:00Z',\n          email_verified_at: '2024-03-01T00:00:00Z'\n        },\n        {\n          id: 8,\n          name: 'Lucas Bernard',\n          email: '<EMAIL>',\n          role: 'client',\n          created_at: '2024-03-05T00:00:00Z',\n          email_verified_at: null\n        }\n      ]);\n    } catch (error) {\n      toast.error('Erreur lors du chargement des utilisateurs');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const getRoleIcon = (role) => {\n    if (role === 'admin') return <FaUserShield className=\"text-red-500\" />;\n    if (role === 'manager') return <FaUserShield className=\"text-orange-500\" />;\n    return <FaUser className=\"text-blue-500\" />;\n  };\n\n  const getRoleBadge = (role) => {\n    const baseClasses = \"px-2 py-1 text-xs rounded-full\";\n    if (role === 'admin') {\n      return <span className={`${baseClasses} bg-red-100 text-red-800`}>Admin</span>;\n    }\n    if (role === 'manager') {\n      return <span className={`${baseClasses} bg-orange-100 text-orange-800`}>Manager</span>;\n    }\n    return <span className={`${baseClasses} bg-blue-100 text-blue-800`}>Client</span>;\n  };\n\n  const formatDate = (dateString) => {\n    return new Date(dateString).toLocaleDateString('fr-FR');\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"flex justify-between items-center\">\n        <h1 className=\"text-2xl font-bold text-gray-900\">Gestion des Utilisateurs</h1>\n      </div>\n\n      {/* Tableau des utilisateurs */}\n      <div className=\"bg-white shadow-sm rounded-lg overflow-hidden\">\n        <div className=\"overflow-x-auto\">\n          <table className=\"min-w-full divide-y divide-gray-200\">\n            <thead className=\"bg-gray-50\">\n              <tr>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Utilisateur\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Email\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Rôle\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Date d'inscription\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Statut\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Actions\n                </th>\n              </tr>\n            </thead>\n            <tbody className=\"bg-white divide-y divide-gray-200\">\n              {isLoading ? (\n                <tr>\n                  <td colSpan=\"6\" className=\"px-6 py-4 text-center\">\n                    <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto\"></div>\n                  </td>\n                </tr>\n              ) : users.length === 0 ? (\n                <tr>\n                  <td colSpan=\"6\" className=\"px-6 py-4 text-center text-gray-500\">\n                    Aucun utilisateur trouvé\n                  </td>\n                </tr>\n              ) : (\n                users.map((user) => (\n                  <tr key={user.id} className=\"hover:bg-gray-50\">\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div className=\"flex items-center\">\n                        <div className=\"flex-shrink-0 h-10 w-10\">\n                          <div className=\"h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center\">\n                            {getRoleIcon(user.role)}\n                          </div>\n                        </div>\n                        <div className=\"ml-4\">\n                          <div className=\"text-sm font-medium text-gray-900\">{user.name}</div>\n                        </div>\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div className=\"text-sm text-gray-900\">{user.email}</div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      {getRoleBadge(user.role)}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div className=\"text-sm text-gray-900\">{formatDate(user.created_at)}</div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      {user.email_verified_at ? (\n                        <span className=\"px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full\">\n                          Vérifié\n                        </span>\n                      ) : (\n                        <span className=\"px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded-full\">\n                          Non vérifié\n                        </span>\n                      )}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                      <div className=\"flex space-x-2\">\n                        <button\n                          className=\"text-primary-600 hover:text-primary-900\"\n                          title=\"Voir\"\n                        >\n                          <FaEye />\n                        </button>\n                        <button\n                          className=\"text-blue-600 hover:text-blue-900\"\n                          title=\"Modifier\"\n                        >\n                          <FaEdit />\n                        </button>\n                        {user.role !== 'admin' && (\n                          <button\n                            className=\"text-red-600 hover:text-red-900\"\n                            title=\"Supprimer\"\n                          >\n                            <FaTrash />\n                          </button>\n                        )}\n                      </div>\n                    </td>\n                  </tr>\n                ))\n              )}\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Users;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,MAAM,EAAEC,OAAO,EAAEC,KAAK,EAAEC,YAAY,EAAEC,MAAM,QAAQ,gBAAgB;AAC7E,OAAOC,WAAW,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAM;IAAEC,KAAK,EAAEC;EAAU,CAAC,GAAGN,WAAW,CAAC,CAAC;EAC1C,MAAM,CAACO,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACiB,SAAS,EAAEC,YAAY,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAEhDC,SAAS,CAAC,MAAM;IACdkB,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFD,YAAY,CAAC,IAAI,CAAC;MAClB;MACA;MACA;;MAEA;MACAF,QAAQ,CAAC,CACP;QACEI,EAAE,EAAE,CAAC;QACLC,IAAI,EAAE,aAAa;QACnBC,KAAK,EAAE,iBAAiB;QACxBC,IAAI,EAAE,OAAO;QACbC,UAAU,EAAE,sBAAsB;QAClCC,iBAAiB,EAAE;MACrB,CAAC,EACD;QACEL,EAAE,EAAE,CAAC;QACLC,IAAI,EAAE,cAAc;QACpBC,KAAK,EAAE,wBAAwB;QAC/BC,IAAI,EAAE,QAAQ;QACdC,UAAU,EAAE,sBAAsB;QAClCC,iBAAiB,EAAE;MACrB,CAAC,EACD;QACEL,EAAE,EAAE,CAAC;QACLC,IAAI,EAAE,cAAc;QACpBC,KAAK,EAAE,wBAAwB;QAC/BC,IAAI,EAAE,QAAQ;QACdC,UAAU,EAAE,sBAAsB;QAClCC,iBAAiB,EAAE;MACrB,CAAC,EACD;QACEL,EAAE,EAAE,CAAC;QACLC,IAAI,EAAE,eAAe;QACrBC,KAAK,EAAE,yBAAyB;QAChCC,IAAI,EAAE,QAAQ;QACdC,UAAU,EAAE,sBAAsB;QAClCC,iBAAiB,EAAE;MACrB,CAAC,EACD;QACEL,EAAE,EAAE,CAAC;QACLC,IAAI,EAAE,aAAa;QACnBC,KAAK,EAAE,uBAAuB;QAC9BC,IAAI,EAAE,QAAQ;QACdC,UAAU,EAAE,sBAAsB;QAClCC,iBAAiB,EAAE;MACrB,CAAC,EACD;QACEL,EAAE,EAAE,CAAC;QACLC,IAAI,EAAE,eAAe;QACrBC,KAAK,EAAE,mBAAmB;QAC1BC,IAAI,EAAE,SAAS;QACfC,UAAU,EAAE,sBAAsB;QAClCC,iBAAiB,EAAE;MACrB,CAAC,EACD;QACEL,EAAE,EAAE,CAAC;QACLC,IAAI,EAAE,aAAa;QACnBC,KAAK,EAAE,uBAAuB;QAC9BC,IAAI,EAAE,QAAQ;QACdC,UAAU,EAAE,sBAAsB;QAClCC,iBAAiB,EAAE;MACrB,CAAC,EACD;QACEL,EAAE,EAAE,CAAC;QACLC,IAAI,EAAE,eAAe;QACrBC,KAAK,EAAE,yBAAyB;QAChCC,IAAI,EAAE,QAAQ;QACdC,UAAU,EAAE,sBAAsB;QAClCC,iBAAiB,EAAE;MACrB,CAAC,CACF,CAAC;IACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdxB,KAAK,CAACwB,KAAK,CAAC,4CAA4C,CAAC;IAC3D,CAAC,SAAS;MACRR,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMS,WAAW,GAAIJ,IAAI,IAAK;IAC5B,IAAIA,IAAI,KAAK,OAAO,EAAE,oBAAOb,OAAA,CAACJ,YAAY;MAACsB,SAAS,EAAC;IAAc;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtE,IAAIT,IAAI,KAAK,SAAS,EAAE,oBAAOb,OAAA,CAACJ,YAAY;MAACsB,SAAS,EAAC;IAAiB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC3E,oBAAOtB,OAAA,CAACH,MAAM;MAACqB,SAAS,EAAC;IAAe;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC7C,CAAC;EAED,MAAMC,YAAY,GAAIV,IAAI,IAAK;IAC7B,MAAMW,WAAW,GAAG,gCAAgC;IACpD,IAAIX,IAAI,KAAK,OAAO,EAAE;MACpB,oBAAOb,OAAA;QAAMkB,SAAS,EAAE,GAAGM,WAAW,0BAA2B;QAAAC,QAAA,EAAC;MAAK;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAChF;IACA,IAAIT,IAAI,KAAK,SAAS,EAAE;MACtB,oBAAOb,OAAA;QAAMkB,SAAS,EAAE,GAAGM,WAAW,gCAAiC;QAAAC,QAAA,EAAC;MAAO;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IACxF;IACA,oBAAOtB,OAAA;MAAMkB,SAAS,EAAE,GAAGM,WAAW,4BAA6B;MAAAC,QAAA,EAAC;IAAM;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EACnF,CAAC;EAED,MAAMI,UAAU,GAAIC,UAAU,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,OAAO,CAAC;EACzD,CAAC;EAED,oBACE7B,OAAA;IAAKkB,SAAS,EAAC,WAAW;IAAAO,QAAA,gBACxBzB,OAAA;MAAKkB,SAAS,EAAC,mCAAmC;MAAAO,QAAA,eAChDzB,OAAA;QAAIkB,SAAS,EAAC,kCAAkC;QAAAO,QAAA,EAAC;MAAwB;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3E,CAAC,eAGNtB,OAAA;MAAKkB,SAAS,EAAC,+CAA+C;MAAAO,QAAA,eAC5DzB,OAAA;QAAKkB,SAAS,EAAC,iBAAiB;QAAAO,QAAA,eAC9BzB,OAAA;UAAOkB,SAAS,EAAC,qCAAqC;UAAAO,QAAA,gBACpDzB,OAAA;YAAOkB,SAAS,EAAC,YAAY;YAAAO,QAAA,eAC3BzB,OAAA;cAAAyB,QAAA,gBACEzB,OAAA;gBAAIkB,SAAS,EAAC,gFAAgF;gBAAAO,QAAA,EAAC;cAE/F;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLtB,OAAA;gBAAIkB,SAAS,EAAC,gFAAgF;gBAAAO,QAAA,EAAC;cAE/F;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLtB,OAAA;gBAAIkB,SAAS,EAAC,gFAAgF;gBAAAO,QAAA,EAAC;cAE/F;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLtB,OAAA;gBAAIkB,SAAS,EAAC,gFAAgF;gBAAAO,QAAA,EAAC;cAE/F;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLtB,OAAA;gBAAIkB,SAAS,EAAC,gFAAgF;gBAAAO,QAAA,EAAC;cAE/F;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLtB,OAAA;gBAAIkB,SAAS,EAAC,gFAAgF;gBAAAO,QAAA,EAAC;cAE/F;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRtB,OAAA;YAAOkB,SAAS,EAAC,mCAAmC;YAAAO,QAAA,EACjDlB,SAAS,gBACRP,OAAA;cAAAyB,QAAA,eACEzB,OAAA;gBAAI8B,OAAO,EAAC,GAAG;gBAACZ,SAAS,EAAC,uBAAuB;gBAAAO,QAAA,eAC/CzB,OAAA;kBAAKkB,SAAS,EAAC;gBAAyE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7F;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,GACHjB,KAAK,CAAC0B,MAAM,KAAK,CAAC,gBACpB/B,OAAA;cAAAyB,QAAA,eACEzB,OAAA;gBAAI8B,OAAO,EAAC,GAAG;gBAACZ,SAAS,EAAC,qCAAqC;gBAAAO,QAAA,EAAC;cAEhE;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,GAELjB,KAAK,CAAC2B,GAAG,CAAEC,IAAI,iBACbjC,OAAA;cAAkBkB,SAAS,EAAC,kBAAkB;cAAAO,QAAA,gBAC5CzB,OAAA;gBAAIkB,SAAS,EAAC,6BAA6B;gBAAAO,QAAA,eACzCzB,OAAA;kBAAKkB,SAAS,EAAC,mBAAmB;kBAAAO,QAAA,gBAChCzB,OAAA;oBAAKkB,SAAS,EAAC,yBAAyB;oBAAAO,QAAA,eACtCzB,OAAA;sBAAKkB,SAAS,EAAC,qEAAqE;sBAAAO,QAAA,EACjFR,WAAW,CAACgB,IAAI,CAACpB,IAAI;oBAAC;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNtB,OAAA;oBAAKkB,SAAS,EAAC,MAAM;oBAAAO,QAAA,eACnBzB,OAAA;sBAAKkB,SAAS,EAAC,mCAAmC;sBAAAO,QAAA,EAAEQ,IAAI,CAACtB;oBAAI;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLtB,OAAA;gBAAIkB,SAAS,EAAC,6BAA6B;gBAAAO,QAAA,eACzCzB,OAAA;kBAAKkB,SAAS,EAAC,uBAAuB;kBAAAO,QAAA,EAAEQ,IAAI,CAACrB;gBAAK;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC,eACLtB,OAAA;gBAAIkB,SAAS,EAAC,6BAA6B;gBAAAO,QAAA,EACxCF,YAAY,CAACU,IAAI,CAACpB,IAAI;cAAC;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC,eACLtB,OAAA;gBAAIkB,SAAS,EAAC,6BAA6B;gBAAAO,QAAA,eACzCzB,OAAA;kBAAKkB,SAAS,EAAC,uBAAuB;kBAAAO,QAAA,EAAEC,UAAU,CAACO,IAAI,CAACnB,UAAU;gBAAC;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxE,CAAC,eACLtB,OAAA;gBAAIkB,SAAS,EAAC,6BAA6B;gBAAAO,QAAA,EACxCQ,IAAI,CAAClB,iBAAiB,gBACrBf,OAAA;kBAAMkB,SAAS,EAAC,4DAA4D;kBAAAO,QAAA,EAAC;gBAE7E;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,gBAEPtB,OAAA;kBAAMkB,SAAS,EAAC,8DAA8D;kBAAAO,QAAA,EAAC;gBAE/E;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cACP;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACLtB,OAAA;gBAAIkB,SAAS,EAAC,iDAAiD;gBAAAO,QAAA,eAC7DzB,OAAA;kBAAKkB,SAAS,EAAC,gBAAgB;kBAAAO,QAAA,gBAC7BzB,OAAA;oBACEkB,SAAS,EAAC,yCAAyC;oBACnDgB,KAAK,EAAC,MAAM;oBAAAT,QAAA,eAEZzB,OAAA,CAACL,KAAK;sBAAAwB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACTtB,OAAA;oBACEkB,SAAS,EAAC,mCAAmC;oBAC7CgB,KAAK,EAAC,UAAU;oBAAAT,QAAA,eAEhBzB,OAAA,CAACP,MAAM;sBAAA0B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,EACRW,IAAI,CAACpB,IAAI,KAAK,OAAO,iBACpBb,OAAA;oBACEkB,SAAS,EAAC,iCAAiC;oBAC3CgB,KAAK,EAAC,WAAW;oBAAAT,QAAA,eAEjBzB,OAAA,CAACN,OAAO;sBAAAyB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CACT;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,GAxDEW,IAAI,CAACvB,EAAE;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAyDZ,CACL;UACF;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACpB,EAAA,CAhOID,KAAK;EAAA,QACoBH,WAAW;AAAA;AAAAqC,EAAA,GADpClC,KAAK;AAkOX,eAAeA,KAAK;AAAC,IAAAkC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}