# 🍽️ YUMMY - Plateforme E-commerce Alimentaire

## 📋 Description

**YUMMY** est une plateforme e-commerce moderne spécialisée dans la vente de produits alimentaires. Le projet comprend un système complet de gestion des produits, commandes, promotions et un dashboard administrateur avancé.

## ✨ Fonctionnalités

### 🛒 **Frontend (React)**
- ✅ Page d'accueil moderne avec produits en vedette
- ✅ Catalogue de produits par catégories
- ✅ Système de panier et checkout
- ✅ Gestion des promotions en temps réel
- ✅ Interface responsive et animations
- ✅ Mode de récupération automatique (données de test)

### 🔧 **Backend (Laravel)**
- ✅ API REST complète
- ✅ Authentification avec Sanctum
- ✅ Gestion des produits et catégories
- ✅ Système de promotions avancé
- ✅ Dashboard admin avec statistiques
- ✅ Middleware de sécurité

### 📊 **Dashboard Admin**
- ✅ Statistiques en temps réel
- ✅ Graphiques interactifs (Chart.js)
- ✅ Gestion des utilisateurs
- ✅ Gestion des produits et promotions
- ✅ Monitoring des commandes

## 🚀 Démarrage Rapide

### **Option 1 : Scripts automatiques**

**Windows :**
```bash
# Double-cliquer sur start.bat
# ou en ligne de commande :
start.bat
```

**Linux/Mac :**
```bash
./start.sh
```

### **Option 2 : Démarrage manuel**

**1. Backend Laravel :**
```bash
cd backend
composer install
cp .env.example .env
php artisan key:generate
php artisan migrate
php artisan db:seed
php artisan serve
```

**2. Frontend React :**
```bash
cd frontend
npm install
npm start
```

## 🌐 Accès

- **Frontend :** http://localhost:3000
- **Backend API :** http://localhost:8000
- **Dashboard Admin :** http://localhost:3000/admin

### 🔑 Comptes de test

**Administrateur :**
- Email : `<EMAIL>`
- Mot de passe : `password123`

**Client :**
- Email : `<EMAIL>`
- Mot de passe : `password123`

## 📁 Structure du Projet

```
Projet-Synthese/
├── backend/                 # API Laravel
│   ├── app/
│   │   ├── Http/Controllers/Api/
│   │   ├── Models/
│   │   └── ...
│   ├── database/
│   │   ├── migrations/
│   │   └── seeders/
│   └── routes/api.php
│
├── frontend/               # Application React
│   ├── src/
│   │   ├── components/
│   │   ├── pages/
│   │   ├── services/
│   │   └── hooks/
│   └── public/
│
├── start.bat              # Script Windows
├── start.sh               # Script Linux/Mac
├── README.md              # Ce fichier
└── TROUBLESHOOTING.md     # Guide de dépannage
```

## 🛠️ Technologies Utilisées

### **Backend**
- **Laravel 10** - Framework PHP
- **MySQL** - Base de données
- **Sanctum** - Authentification API
- **Eloquent ORM** - Gestion des données

### **Frontend**
- **React 18** - Interface utilisateur
- **Tailwind CSS** - Styles
- **Framer Motion** - Animations
- **Chart.js** - Graphiques
- **Axios** - Requêtes HTTP
- **React Router** - Navigation

## 🔧 Configuration

### **Variables d'environnement Backend (.env)**
```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=yummy_db
DB_USERNAME=root
DB_PASSWORD=

FRONTEND_URL=http://localhost:3000
SANCTUM_STATEFUL_DOMAINS=localhost:3000
```

### **Variables d'environnement Frontend (.env)**
```env
REACT_APP_API_URL=http://localhost:8000
```

## 📊 Données de Test

Le projet inclut des **seeders complets** :
- 👥 6 utilisateurs (admin, manager, clients)
- 🏷️ 6 catégories de produits
- 🍽️ 10+ produits variés
- 🎯 3 promotions actives

## 🚨 Mode de Récupération

Si le backend n'est pas disponible, le frontend utilise automatiquement des **données de test** pour permettre la démonstration :
- ✅ Produits d'exemple avec images
- ✅ Promotions factices
- ✅ Bandeau d'information visible

## 🐛 Dépannage

En cas de problème, consultez le [Guide de Dépannage](TROUBLESHOOTING.md) qui couvre :
- ❌ Erreurs de connexion backend
- 🔧 Configuration de la base de données
- 🌐 Problèmes CORS
- 📦 Installation des dépendances

## 🎯 Fonctionnalités Avancées

### **Dashboard Admin**
- 📈 Graphiques de ventes en temps réel
- 👥 Gestion des utilisateurs
- 🛍️ Suivi des commandes
- 🎯 Gestion des promotions
- 📊 Statistiques détaillées

### **Système de Promotions**
- 💯 Réductions en pourcentage
- 💰 Montants fixes
- 🚚 Livraison gratuite
- ⏰ Dates de validité
- 🔢 Limites d'utilisation

### **Interface Utilisateur**
- 📱 Design responsive
- ✨ Animations fluides
- 🎨 Thème moderne
- 🔍 Recherche avancée
- 🛒 Panier persistant

## 📞 Support

Pour toute question ou problème :
1. Consultez le [Guide de Dépannage](TROUBLESHOOTING.md)
2. Vérifiez les logs dans la console du navigateur
3. Vérifiez les logs Laravel dans `backend/storage/logs/`

## 🏆 Statut du Projet

✅ **Prêt pour la démonstration**
✅ **Backend fonctionnel**
✅ **Frontend responsive**
✅ **Dashboard admin complet**
✅ **Mode de récupération automatique**
✅ **Documentation complète**

---

**Développé avec ❤️ pour le projet de synthèse**
