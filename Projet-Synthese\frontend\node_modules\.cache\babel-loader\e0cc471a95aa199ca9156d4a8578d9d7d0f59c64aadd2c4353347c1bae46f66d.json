{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Projet-Synthese\\\\frontend\\\\src\\\\components\\\\admin\\\\RecentActivity.js\";\nimport React from 'react';\nimport { FaShoppingCart, FaUsers, FaStar, FaEye, FaArrowUp, FaArrowDown } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst RecentActivity = ({\n  activities = []\n}) => {\n  const getActivityIcon = type => {\n    switch (type) {\n      case 'order':\n        return /*#__PURE__*/_jsxDEV(FaShoppingCart, {\n          className: \"text-blue-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 16\n        }, this);\n      case 'user':\n        return /*#__PURE__*/_jsxDEV(FaUsers, {\n          className: \"text-green-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 16\n        }, this);\n      case 'review':\n        return /*#__PURE__*/_jsxDEV(FaStar, {\n          className: \"text-yellow-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(FaEye, {\n          className: \"text-gray-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  const getActivityText = activity => {\n    switch (activity.type) {\n      case 'order':\n        return `Nouvelle commande de ${activity.user} - ${formatCurrency(activity.amount)}`;\n      case 'user':\n        return `Nouvel utilisateur: ${activity.name}`;\n      case 'review':\n        return `${activity.user} a noté ${activity.product} (${activity.rating}/5)`;\n      default:\n        return 'Activité inconnue';\n    }\n  };\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('fr-MA', {\n      style: 'currency',\n      currency: 'MAD'\n    }).format(amount);\n  };\n  const formatDate = dateString => {\n    return new Date(dateString).toLocaleString('fr-FR', {\n      day: '2-digit',\n      month: '2-digit',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  // Données de test si aucune activité n'est fournie\n  const defaultActivities = [{\n    id: 1,\n    type: 'order',\n    user: 'Marie Dubois',\n    amount: 45.99,\n    created_at: new Date(Date.now() - 1000 * 60 * 15).toISOString() // Il y a 15 minutes\n  }, {\n    id: 2,\n    type: 'user',\n    name: 'Ahmed Hassan',\n    email: '<EMAIL>',\n    created_at: new Date(Date.now() - 1000 * 60 * 30).toISOString() // Il y a 30 minutes\n  }, {\n    id: 3,\n    type: 'review',\n    user: 'John Doe',\n    product: 'Poulet Grillé Entier',\n    rating: 5,\n    created_at: new Date(Date.now() - 1000 * 60 * 45).toISOString() // Il y a 45 minutes\n  }, {\n    id: 4,\n    type: 'order',\n    user: 'Jane Smith',\n    amount: 28.50,\n    created_at: new Date(Date.now() - 1000 * 60 * 60).toISOString() // Il y a 1 heure\n  }, {\n    id: 5,\n    type: 'review',\n    user: 'Marie Dubois',\n    product: 'Salade César',\n    rating: 4,\n    created_at: new Date(Date.now() - 1000 * 60 * 90).toISOString() // Il y a 1h30\n  }];\n  const displayActivities = activities.length > 0 ? activities : defaultActivities;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white p-6 rounded-lg shadow-sm border\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-semibold text-gray-900\",\n        children: \"Activit\\xE9s r\\xE9centes\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"text-sm text-primary-600 hover:text-primary-700\",\n        children: \"Voir tout\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-4 max-h-80 overflow-y-auto\",\n      children: displayActivities.map((activity, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-start space-x-3 p-3 hover:bg-gray-50 rounded-lg transition-colors\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-shrink-0 mt-1\",\n          children: getActivityIcon(activity.type)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 min-w-0\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-900\",\n            children: getActivityText(activity)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs text-gray-500 mt-1\",\n            children: formatDate(activity.created_at)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-shrink-0\",\n          children: [activity.type === 'order' && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800\",\n            children: [/*#__PURE__*/_jsxDEV(FaArrowUp, {\n              className: \"mr-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 19\n            }, this), \"Vente\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 17\n          }, this), activity.type === 'user' && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800\",\n            children: \"Nouveau\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 17\n          }, this), activity.type === 'review' && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800\",\n            children: [activity.rating, \"/5\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 13\n        }, this)]\n      }, activity.id || index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 7\n    }, this), displayActivities.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-8\",\n      children: [/*#__PURE__*/_jsxDEV(FaEye, {\n        className: \"mx-auto h-12 w-12 text-gray-400 mb-4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-500\",\n        children: \"Aucune activit\\xE9 r\\xE9cente\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 143,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 98,\n    columnNumber: 5\n  }, this);\n};\n_c = RecentActivity;\nexport default RecentActivity;\nvar _c;\n$RefreshReg$(_c, \"RecentActivity\");", "map": {"version": 3, "names": ["React", "FaShoppingCart", "FaUsers", "FaStar", "FaEye", "FaArrowUp", "FaArrowDown", "jsxDEV", "_jsxDEV", "RecentActivity", "activities", "getActivityIcon", "type", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getActivityText", "activity", "user", "formatCurrency", "amount", "name", "product", "rating", "Intl", "NumberFormat", "style", "currency", "format", "formatDate", "dateString", "Date", "toLocaleString", "day", "month", "hour", "minute", "defaultActivities", "id", "created_at", "now", "toISOString", "email", "displayActivities", "length", "children", "map", "index", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet-Synthese/frontend/src/components/admin/RecentActivity.js"], "sourcesContent": ["import React from 'react';\nimport { \n  FaShoppingCart, \n  FaUsers, \n  FaStar, \n  FaEye,\n  FaArrowUp,\n  FaArrowDown \n} from 'react-icons/fa';\n\nconst RecentActivity = ({ activities = [] }) => {\n  const getActivityIcon = (type) => {\n    switch (type) {\n      case 'order':\n        return <FaShoppingCart className=\"text-blue-500\" />;\n      case 'user':\n        return <FaUsers className=\"text-green-500\" />;\n      case 'review':\n        return <FaStar className=\"text-yellow-500\" />;\n      default:\n        return <FaEye className=\"text-gray-500\" />;\n    }\n  };\n\n  const getActivityText = (activity) => {\n    switch (activity.type) {\n      case 'order':\n        return `Nouvelle commande de ${activity.user} - ${formatCurrency(activity.amount)}`;\n      case 'user':\n        return `Nouvel utilisateur: ${activity.name}`;\n      case 'review':\n        return `${activity.user} a noté ${activity.product} (${activity.rating}/5)`;\n      default:\n        return 'Activité inconnue';\n    }\n  };\n\n  const formatCurrency = (amount) => {\n    return new Intl.NumberFormat('fr-MA', {\n      style: 'currency',\n      currency: 'MAD'\n    }).format(amount);\n  };\n\n  const formatDate = (dateString) => {\n    return new Date(dateString).toLocaleString('fr-FR', {\n      day: '2-digit',\n      month: '2-digit',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  // Données de test si aucune activité n'est fournie\n  const defaultActivities = [\n    {\n      id: 1,\n      type: 'order',\n      user: 'Marie Dubois',\n      amount: 45.99,\n      created_at: new Date(Date.now() - 1000 * 60 * 15).toISOString() // Il y a 15 minutes\n    },\n    {\n      id: 2,\n      type: 'user',\n      name: 'Ahmed Hassan',\n      email: '<EMAIL>',\n      created_at: new Date(Date.now() - 1000 * 60 * 30).toISOString() // Il y a 30 minutes\n    },\n    {\n      id: 3,\n      type: 'review',\n      user: 'John Doe',\n      product: 'Poulet Grillé Entier',\n      rating: 5,\n      created_at: new Date(Date.now() - 1000 * 60 * 45).toISOString() // Il y a 45 minutes\n    },\n    {\n      id: 4,\n      type: 'order',\n      user: 'Jane Smith',\n      amount: 28.50,\n      created_at: new Date(Date.now() - 1000 * 60 * 60).toISOString() // Il y a 1 heure\n    },\n    {\n      id: 5,\n      type: 'review',\n      user: 'Marie Dubois',\n      product: 'Salade César',\n      rating: 4,\n      created_at: new Date(Date.now() - 1000 * 60 * 90).toISOString() // Il y a 1h30\n    }\n  ];\n\n  const displayActivities = activities.length > 0 ? activities : defaultActivities;\n\n  return (\n    <div className=\"bg-white p-6 rounded-lg shadow-sm border\">\n      <div className=\"flex items-center justify-between mb-4\">\n        <h3 className=\"text-lg font-semibold text-gray-900\">Activités récentes</h3>\n        <button className=\"text-sm text-primary-600 hover:text-primary-700\">\n          Voir tout\n        </button>\n      </div>\n      \n      <div className=\"space-y-4 max-h-80 overflow-y-auto\">\n        {displayActivities.map((activity, index) => (\n          <div key={activity.id || index} className=\"flex items-start space-x-3 p-3 hover:bg-gray-50 rounded-lg transition-colors\">\n            <div className=\"flex-shrink-0 mt-1\">\n              {getActivityIcon(activity.type)}\n            </div>\n            <div className=\"flex-1 min-w-0\">\n              <p className=\"text-sm text-gray-900\">\n                {getActivityText(activity)}\n              </p>\n              <p className=\"text-xs text-gray-500 mt-1\">\n                {formatDate(activity.created_at)}\n              </p>\n            </div>\n            <div className=\"flex-shrink-0\">\n              {activity.type === 'order' && (\n                <span className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800\">\n                  <FaArrowUp className=\"mr-1\" />\n                  Vente\n                </span>\n              )}\n              {activity.type === 'user' && (\n                <span className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800\">\n                  Nouveau\n                </span>\n              )}\n              {activity.type === 'review' && (\n                <span className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800\">\n                  {activity.rating}/5\n                </span>\n              )}\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {displayActivities.length === 0 && (\n        <div className=\"text-center py-8\">\n          <FaEye className=\"mx-auto h-12 w-12 text-gray-400 mb-4\" />\n          <p className=\"text-gray-500\">Aucune activité récente</p>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default RecentActivity;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,cAAc,EACdC,OAAO,EACPC,MAAM,EACNC,KAAK,EACLC,SAAS,EACTC,WAAW,QACN,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,cAAc,GAAGA,CAAC;EAAEC,UAAU,GAAG;AAAG,CAAC,KAAK;EAC9C,MAAMC,eAAe,GAAIC,IAAI,IAAK;IAChC,QAAQA,IAAI;MACV,KAAK,OAAO;QACV,oBAAOJ,OAAA,CAACP,cAAc;UAACY,SAAS,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACrD,KAAK,MAAM;QACT,oBAAOT,OAAA,CAACN,OAAO;UAACW,SAAS,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC/C,KAAK,QAAQ;QACX,oBAAOT,OAAA,CAACL,MAAM;UAACU,SAAS,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC/C;QACE,oBAAOT,OAAA,CAACJ,KAAK;UAACS,SAAS,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAC9C;EACF,CAAC;EAED,MAAMC,eAAe,GAAIC,QAAQ,IAAK;IACpC,QAAQA,QAAQ,CAACP,IAAI;MACnB,KAAK,OAAO;QACV,OAAO,wBAAwBO,QAAQ,CAACC,IAAI,MAAMC,cAAc,CAACF,QAAQ,CAACG,MAAM,CAAC,EAAE;MACrF,KAAK,MAAM;QACT,OAAO,uBAAuBH,QAAQ,CAACI,IAAI,EAAE;MAC/C,KAAK,QAAQ;QACX,OAAO,GAAGJ,QAAQ,CAACC,IAAI,WAAWD,QAAQ,CAACK,OAAO,KAAKL,QAAQ,CAACM,MAAM,KAAK;MAC7E;QACE,OAAO,mBAAmB;IAC9B;EACF,CAAC;EAED,MAAMJ,cAAc,GAAIC,MAAM,IAAK;IACjC,OAAO,IAAII,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAACC,MAAM,CAACR,MAAM,CAAC;EACnB,CAAC;EAED,MAAMS,UAAU,GAAIC,UAAU,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,cAAc,CAAC,OAAO,EAAE;MAClDC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAG,CACxB;IACEC,EAAE,EAAE,CAAC;IACL5B,IAAI,EAAE,OAAO;IACbQ,IAAI,EAAE,cAAc;IACpBE,MAAM,EAAE,KAAK;IACbmB,UAAU,EAAE,IAAIR,IAAI,CAACA,IAAI,CAACS,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC;EAClE,CAAC,EACD;IACEH,EAAE,EAAE,CAAC;IACL5B,IAAI,EAAE,MAAM;IACZW,IAAI,EAAE,cAAc;IACpBqB,KAAK,EAAE,mBAAmB;IAC1BH,UAAU,EAAE,IAAIR,IAAI,CAACA,IAAI,CAACS,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC;EAClE,CAAC,EACD;IACEH,EAAE,EAAE,CAAC;IACL5B,IAAI,EAAE,QAAQ;IACdQ,IAAI,EAAE,UAAU;IAChBI,OAAO,EAAE,sBAAsB;IAC/BC,MAAM,EAAE,CAAC;IACTgB,UAAU,EAAE,IAAIR,IAAI,CAACA,IAAI,CAACS,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC;EAClE,CAAC,EACD;IACEH,EAAE,EAAE,CAAC;IACL5B,IAAI,EAAE,OAAO;IACbQ,IAAI,EAAE,YAAY;IAClBE,MAAM,EAAE,KAAK;IACbmB,UAAU,EAAE,IAAIR,IAAI,CAACA,IAAI,CAACS,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC;EAClE,CAAC,EACD;IACEH,EAAE,EAAE,CAAC;IACL5B,IAAI,EAAE,QAAQ;IACdQ,IAAI,EAAE,cAAc;IACpBI,OAAO,EAAE,cAAc;IACvBC,MAAM,EAAE,CAAC;IACTgB,UAAU,EAAE,IAAIR,IAAI,CAACA,IAAI,CAACS,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC;EAClE,CAAC,CACF;EAED,MAAME,iBAAiB,GAAGnC,UAAU,CAACoC,MAAM,GAAG,CAAC,GAAGpC,UAAU,GAAG6B,iBAAiB;EAEhF,oBACE/B,OAAA;IAAKK,SAAS,EAAC,0CAA0C;IAAAkC,QAAA,gBACvDvC,OAAA;MAAKK,SAAS,EAAC,wCAAwC;MAAAkC,QAAA,gBACrDvC,OAAA;QAAIK,SAAS,EAAC,qCAAqC;QAAAkC,QAAA,EAAC;MAAkB;QAAAjC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC3ET,OAAA;QAAQK,SAAS,EAAC,iDAAiD;QAAAkC,QAAA,EAAC;MAEpE;QAAAjC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENT,OAAA;MAAKK,SAAS,EAAC,oCAAoC;MAAAkC,QAAA,EAChDF,iBAAiB,CAACG,GAAG,CAAC,CAAC7B,QAAQ,EAAE8B,KAAK,kBACrCzC,OAAA;QAAgCK,SAAS,EAAC,8EAA8E;QAAAkC,QAAA,gBACtHvC,OAAA;UAAKK,SAAS,EAAC,oBAAoB;UAAAkC,QAAA,EAChCpC,eAAe,CAACQ,QAAQ,CAACP,IAAI;QAAC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACNT,OAAA;UAAKK,SAAS,EAAC,gBAAgB;UAAAkC,QAAA,gBAC7BvC,OAAA;YAAGK,SAAS,EAAC,uBAAuB;YAAAkC,QAAA,EACjC7B,eAAe,CAACC,QAAQ;UAAC;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC,eACJT,OAAA;YAAGK,SAAS,EAAC,4BAA4B;YAAAkC,QAAA,EACtChB,UAAU,CAACZ,QAAQ,CAACsB,UAAU;UAAC;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNT,OAAA;UAAKK,SAAS,EAAC,eAAe;UAAAkC,QAAA,GAC3B5B,QAAQ,CAACP,IAAI,KAAK,OAAO,iBACxBJ,OAAA;YAAMK,SAAS,EAAC,iGAAiG;YAAAkC,QAAA,gBAC/GvC,OAAA,CAACH,SAAS;cAACQ,SAAS,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,SAEhC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACP,EACAE,QAAQ,CAACP,IAAI,KAAK,MAAM,iBACvBJ,OAAA;YAAMK,SAAS,EAAC,+FAA+F;YAAAkC,QAAA,EAAC;UAEhH;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACP,EACAE,QAAQ,CAACP,IAAI,KAAK,QAAQ,iBACzBJ,OAAA;YAAMK,SAAS,EAAC,mGAAmG;YAAAkC,QAAA,GAChH5B,QAAQ,CAACM,MAAM,EAAC,IACnB;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA,GA7BEE,QAAQ,CAACqB,EAAE,IAAIS,KAAK;QAAAnC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA8BzB,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAEL4B,iBAAiB,CAACC,MAAM,KAAK,CAAC,iBAC7BtC,OAAA;MAAKK,SAAS,EAAC,kBAAkB;MAAAkC,QAAA,gBAC/BvC,OAAA,CAACJ,KAAK;QAACS,SAAS,EAAC;MAAsC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC1DT,OAAA;QAAGK,SAAS,EAAC,eAAe;QAAAkC,QAAA,EAAC;MAAuB;QAAAjC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrD,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACiC,EAAA,GA3IIzC,cAAc;AA6IpB,eAAeA,cAAc;AAAC,IAAAyC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}