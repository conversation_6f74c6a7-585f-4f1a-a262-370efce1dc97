import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { productAPI, categoryAPI } from '../services/apiService';

// État initial
const initialState = {
  products: [],
  categories: [],
  orders: [],
  users: [],
  promotions: [],
  loading: false,
  error: null
};

// Actions
const DataActions = {
  SET_LOADING: 'SET_LOADING',
  SET_ERROR: 'SET_ERROR',
  SET_PRODUCTS: 'SET_PRODUCTS',
  ADD_PRODUCT: 'ADD_PRODUCT',
  UPDATE_PRODUCT: 'UPDATE_PRODUCT',
  DELETE_PRODUCT: 'DELETE_PRODUCT',
  SET_CATEGORIES: 'SET_CATEGORIES',
  ADD_CATEGORY: 'ADD_CATEGORY',
  UPDATE_CATEGORY: 'UPDATE_CATEGORY',
  DELETE_CATEGORY: 'DELETE_CATEGORY',
  SET_ORDERS: 'SET_ORDERS',
  ADD_ORDER: 'ADD_ORDER',
  UPDATE_ORDER: 'UPDATE_ORDER',
  DELETE_ORDER: 'DELETE_ORDER',
  SET_USERS: 'SET_USERS',
  ADD_USER: 'ADD_USER',
  UPDATE_USER: 'UPDATE_USER',
  DELETE_USER: 'DELETE_USER',
  SET_PROMOTIONS: 'SET_PROMOTIONS',
  ADD_PROMOTION: 'ADD_PROMOTION',
  UPDATE_PROMOTION: 'UPDATE_PROMOTION',
  DELETE_PROMOTION: 'DELETE_PROMOTION'
};

// Reducer
const dataReducer = (state, action) => {
  switch (action.type) {
    case DataActions.SET_LOADING:
      return { ...state, loading: action.payload };
    
    case DataActions.SET_ERROR:
      return { ...state, error: action.payload, loading: false };
    
    // Products
    case DataActions.SET_PRODUCTS:
      return { ...state, products: action.payload, loading: false };
    
    case DataActions.ADD_PRODUCT:
      return { 
        ...state, 
        products: [...state.products, { ...action.payload, id: Date.now() }],
        loading: false 
      };
    
    case DataActions.UPDATE_PRODUCT:
      return {
        ...state,
        products: state.products.map(product =>
          product.id === action.payload.id ? { ...product, ...action.payload } : product
        ),
        loading: false
      };
    
    case DataActions.DELETE_PRODUCT:
      return {
        ...state,
        products: state.products.filter(product => product.id !== action.payload),
        loading: false
      };
    
    // Categories
    case DataActions.SET_CATEGORIES:
      return { ...state, categories: action.payload, loading: false };
    
    case DataActions.ADD_CATEGORY:
      return { 
        ...state, 
        categories: [...state.categories, { ...action.payload, id: Date.now() }],
        loading: false 
      };
    
    case DataActions.UPDATE_CATEGORY:
      return {
        ...state,
        categories: state.categories.map(category =>
          category.id === action.payload.id ? { ...category, ...action.payload } : category
        ),
        loading: false
      };
    
    case DataActions.DELETE_CATEGORY:
      return {
        ...state,
        categories: state.categories.filter(category => category.id !== action.payload),
        loading: false
      };
    
    // Orders
    case DataActions.SET_ORDERS:
      return { ...state, orders: action.payload, loading: false };
    
    case DataActions.ADD_ORDER:
      return { 
        ...state, 
        orders: [...state.orders, { ...action.payload, id: Date.now() }],
        loading: false 
      };
    
    case DataActions.UPDATE_ORDER:
      return {
        ...state,
        orders: state.orders.map(order =>
          order.id === action.payload.id ? { ...order, ...action.payload } : order
        ),
        loading: false
      };
    
    case DataActions.DELETE_ORDER:
      return {
        ...state,
        orders: state.orders.filter(order => order.id !== action.payload),
        loading: false
      };
    
    // Users
    case DataActions.SET_USERS:
      return { ...state, users: action.payload, loading: false };
    
    case DataActions.ADD_USER:
      return { 
        ...state, 
        users: [...state.users, { ...action.payload, id: Date.now() }],
        loading: false 
      };
    
    case DataActions.UPDATE_USER:
      return {
        ...state,
        users: state.users.map(user =>
          user.id === action.payload.id ? { ...user, ...action.payload } : user
        ),
        loading: false
      };
    
    case DataActions.DELETE_USER:
      return {
        ...state,
        users: state.users.filter(user => user.id !== action.payload),
        loading: false
      };
    
    // Promotions
    case DataActions.SET_PROMOTIONS:
      return { ...state, promotions: action.payload, loading: false };
    
    case DataActions.ADD_PROMOTION:
      return { 
        ...state, 
        promotions: [...state.promotions, { ...action.payload, id: Date.now() }],
        loading: false 
      };
    
    case DataActions.UPDATE_PROMOTION:
      return {
        ...state,
        promotions: state.promotions.map(promotion =>
          promotion.id === action.payload.id ? { ...promotion, ...action.payload } : promotion
        ),
        loading: false
      };
    
    case DataActions.DELETE_PROMOTION:
      return {
        ...state,
        promotions: state.promotions.filter(promotion => promotion.id !== action.payload),
        loading: false
      };
    
    default:
      return state;
  }
};

// Context
const DataContext = createContext();

// Provider
export const DataProvider = ({ children }) => {
  const [state, dispatch] = useReducer(dataReducer, initialState);

  // Charger les données UNIQUEMENT depuis l'API Laravel
  const loadDataFromAPI = async () => {
    dispatch({ type: DataActions.SET_LOADING, payload: true });

    try {
      console.log('🔄 Chargement des données depuis Laravel...');

      // Charger les catégories depuis Laravel
      const categoriesResult = await categoryAPI.getAll();
      if (categoriesResult.success) {
        dispatch({ type: DataActions.SET_CATEGORIES, payload: categoriesResult.data });
        console.log('✅ Catégories chargées depuis Laravel:', categoriesResult.data.length);
      } else {
        console.log('⚠️ Aucune catégorie trouvée dans Laravel');
        dispatch({ type: DataActions.SET_CATEGORIES, payload: [] });
      }

      // Charger les produits depuis Laravel
      const productsResult = await productAPI.getAll();
      if (productsResult.success) {
        dispatch({ type: DataActions.SET_PRODUCTS, payload: productsResult.data });
        console.log('✅ Produits chargés depuis Laravel:', productsResult.data.length);
      } else {
        console.log('⚠️ Aucun produit trouvé dans Laravel');
        dispatch({ type: DataActions.SET_PRODUCTS, payload: [] });
      }

    } catch (error) {
      console.error('❌ Erreur de connexion à Laravel:', error);
      dispatch({ type: DataActions.SET_ERROR, payload: 'Impossible de se connecter au backend Laravel' });
      // Pas de données de test - forcer l'utilisation de Laravel
      dispatch({ type: DataActions.SET_CATEGORIES, payload: [] });
      dispatch({ type: DataActions.SET_PRODUCTS, payload: [] });
    } finally {
      dispatch({ type: DataActions.SET_LOADING, payload: false });
    }
  };

  // Plus de données de test - tout vient de Laravel

  useEffect(() => {
    loadDataFromAPI();
  }, []);

  return (
    <DataContext.Provider value={{ state, dispatch, DataActions }}>
      {children}
    </DataContext.Provider>
  );
};

// Hook personnalisé
export const useData = () => {
  const context = useContext(DataContext);
  if (!context) {
    throw new Error('useData must be used within a DataProvider');
  }
  return context;
};

export default DataContext;
