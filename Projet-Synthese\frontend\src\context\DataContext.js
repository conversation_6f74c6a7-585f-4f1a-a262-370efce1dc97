import React, { createContext, useContext, useReducer, useEffect } from 'react';
import axios from 'axios';

// État initial
const initialState = {
  products: [],
  categories: [],
  orders: [],
  users: [],
  promotions: [],
  loading: false,
  error: null
};

// Actions
const DataActions = {
  SET_LOADING: 'SET_LOADING',
  SET_ERROR: 'SET_ERROR',
  SET_PRODUCTS: 'SET_PRODUCTS',
  ADD_PRODUCT: 'ADD_PRODUCT',
  UPDATE_PRODUCT: 'UPDATE_PRODUCT',
  DELETE_PRODUCT: 'DELETE_PRODUCT',
  SET_CATEGORIES: 'SET_CATEGORIES',
  ADD_CATEGORY: 'ADD_CATEGORY',
  UPDATE_CATEGORY: 'UPDATE_CATEGORY',
  DELETE_CATEGORY: 'DELETE_CATEGORY',
  SET_ORDERS: 'SET_ORDERS',
  ADD_ORDER: 'ADD_ORDER',
  UPDATE_ORDER: 'UPDATE_ORDER',
  DELETE_ORDER: 'DELETE_ORDER',
  SET_USERS: 'SET_USERS',
  ADD_USER: 'ADD_USER',
  UPDATE_USER: 'UPDATE_USER',
  DELETE_USER: 'DELETE_USER',
  SET_PROMOTIONS: 'SET_PROMOTIONS',
  ADD_PROMOTION: 'ADD_PROMOTION',
  UPDATE_PROMOTION: 'UPDATE_PROMOTION',
  DELETE_PROMOTION: 'DELETE_PROMOTION'
};

// Reducer
const dataReducer = (state, action) => {
  switch (action.type) {
    case DataActions.SET_LOADING:
      return { ...state, loading: action.payload };
    
    case DataActions.SET_ERROR:
      return { ...state, error: action.payload, loading: false };
    
    // Products
    case DataActions.SET_PRODUCTS:
      return { ...state, products: action.payload, loading: false };
    
    case DataActions.ADD_PRODUCT:
      return { 
        ...state, 
        products: [...state.products, { ...action.payload, id: Date.now() }],
        loading: false 
      };
    
    case DataActions.UPDATE_PRODUCT:
      return {
        ...state,
        products: state.products.map(product =>
          product.id === action.payload.id ? { ...product, ...action.payload } : product
        ),
        loading: false
      };
    
    case DataActions.DELETE_PRODUCT:
      return {
        ...state,
        products: state.products.filter(product => product.id !== action.payload),
        loading: false
      };
    
    // Categories
    case DataActions.SET_CATEGORIES:
      return { ...state, categories: action.payload, loading: false };
    
    case DataActions.ADD_CATEGORY:
      return { 
        ...state, 
        categories: [...state.categories, { ...action.payload, id: Date.now() }],
        loading: false 
      };
    
    case DataActions.UPDATE_CATEGORY:
      return {
        ...state,
        categories: state.categories.map(category =>
          category.id === action.payload.id ? { ...category, ...action.payload } : category
        ),
        loading: false
      };
    
    case DataActions.DELETE_CATEGORY:
      return {
        ...state,
        categories: state.categories.filter(category => category.id !== action.payload),
        loading: false
      };
    
    // Orders
    case DataActions.SET_ORDERS:
      return { ...state, orders: action.payload, loading: false };
    
    case DataActions.ADD_ORDER:
      return { 
        ...state, 
        orders: [...state.orders, { ...action.payload, id: Date.now() }],
        loading: false 
      };
    
    case DataActions.UPDATE_ORDER:
      return {
        ...state,
        orders: state.orders.map(order =>
          order.id === action.payload.id ? { ...order, ...action.payload } : order
        ),
        loading: false
      };
    
    case DataActions.DELETE_ORDER:
      return {
        ...state,
        orders: state.orders.filter(order => order.id !== action.payload),
        loading: false
      };
    
    // Users
    case DataActions.SET_USERS:
      return { ...state, users: action.payload, loading: false };
    
    case DataActions.ADD_USER:
      return { 
        ...state, 
        users: [...state.users, { ...action.payload, id: Date.now() }],
        loading: false 
      };
    
    case DataActions.UPDATE_USER:
      return {
        ...state,
        users: state.users.map(user =>
          user.id === action.payload.id ? { ...user, ...action.payload } : user
        ),
        loading: false
      };
    
    case DataActions.DELETE_USER:
      return {
        ...state,
        users: state.users.filter(user => user.id !== action.payload),
        loading: false
      };
    
    // Promotions
    case DataActions.SET_PROMOTIONS:
      return { ...state, promotions: action.payload, loading: false };
    
    case DataActions.ADD_PROMOTION:
      return { 
        ...state, 
        promotions: [...state.promotions, { ...action.payload, id: Date.now() }],
        loading: false 
      };
    
    case DataActions.UPDATE_PROMOTION:
      return {
        ...state,
        promotions: state.promotions.map(promotion =>
          promotion.id === action.payload.id ? { ...promotion, ...action.payload } : promotion
        ),
        loading: false
      };
    
    case DataActions.DELETE_PROMOTION:
      return {
        ...state,
        promotions: state.promotions.filter(promotion => promotion.id !== action.payload),
        loading: false
      };
    
    default:
      return state;
  }
};

// Context
const DataContext = createContext();

// Provider
export const DataProvider = ({ children }) => {
  const [state, dispatch] = useReducer(dataReducer, initialState);

  // Données de test initiales
  const initializeTestData = () => {
    // Catégories de test
    const testCategories = [
      { id: 1, name: 'Produits Grillés', description: 'Viandes et légumes grillés', color: 'bg-red-100', textColor: 'text-red-800', status: 'active' },
      { id: 2, name: 'Salades', description: 'Salades fraîches et variées', color: 'bg-green-100', textColor: 'text-green-800', status: 'active' },
      { id: 3, name: 'Fromages', description: 'Sélection de fromages artisanaux', color: 'bg-yellow-100', textColor: 'text-yellow-800', status: 'active' },
      { id: 4, name: 'Boissons', description: 'Boissons fraîches et chaudes', color: 'bg-blue-100', textColor: 'text-blue-800', status: 'active' },
      { id: 5, name: 'Desserts', description: 'Desserts maison délicieux', color: 'bg-purple-100', textColor: 'text-purple-800', status: 'active' },
      { id: 6, name: 'Produits Non Grillés', description: 'Plats cuisinés traditionnels', color: 'bg-gray-100', textColor: 'text-gray-800', status: 'active' }
    ];

    // Produits de test
    const testProducts = [
      { id: 1, name: 'Poulet Grillé Entier', description: 'Poulet entier grillé aux herbes', price: 89.99, stock: 15, category_id: 1, category: testCategories[0], is_grillable: true, image_url: 'https://images.unsplash.com/photo-1598103442097-8b74394b95c6?w=300' },
      { id: 2, name: 'Brochettes de Bœuf', description: 'Brochettes de bœuf marinées', price: 65.50, stock: 8, category_id: 1, category: testCategories[0], is_grillable: true, image_url: 'https://images.unsplash.com/photo-1529692236671-f1f6cf9683ba?w=300' },
      { id: 3, name: 'Salade César', description: 'Salade César avec croûtons', price: 35.00, stock: 20, category_id: 2, category: testCategories[1], is_grillable: false, image_url: 'https://images.unsplash.com/photo-1546793665-c74683f339c1?w=300' },
      { id: 4, name: 'Plateau de Fromages', description: 'Assortiment de fromages locaux', price: 75.00, stock: 12, category_id: 3, category: testCategories[2], is_grillable: false, image_url: 'https://images.unsplash.com/photo-1486297678162-eb2a19b0a32d?w=300' },
      { id: 5, name: 'Jus d\'Orange Frais', description: 'Jus d\'orange pressé maison', price: 15.00, stock: 25, category_id: 4, category: testCategories[3], is_grillable: false, image_url: 'https://images.unsplash.com/photo-1621506289937-a8e4df240d0b?w=300' },
      { id: 6, name: 'Tiramisu Maison', description: 'Tiramisu traditionnel italien', price: 25.00, stock: 10, category_id: 5, category: testCategories[4], is_grillable: false, image_url: 'https://images.unsplash.com/photo-1571877227200-a0d98ea607e9?w=300' },
      { id: 7, name: 'Saumon Grillé', description: 'Filet de saumon grillé aux légumes', price: 95.00, stock: 6, category_id: 1, category: testCategories[0], is_grillable: true, image_url: 'https://images.unsplash.com/photo-1467003909585-2f8a72700288?w=300' },
      { id: 8, name: 'Carpaccio de Bœuf', description: 'Carpaccio de bœuf aux copeaux de parmesan', price: 55.00, stock: 14, category_id: 6, category: testCategories[5], is_grillable: false, image_url: 'https://images.unsplash.com/photo-**********-d76694265947?w=300' }
    ];

    dispatch({ type: DataActions.SET_CATEGORIES, payload: testCategories });
    dispatch({ type: DataActions.SET_PRODUCTS, payload: testProducts });
  };

  useEffect(() => {
    initializeTestData();
  }, []);

  return (
    <DataContext.Provider value={{ state, dispatch, DataActions }}>
      {children}
    </DataContext.Provider>
  );
};

// Hook personnalisé
export const useData = () => {
  const context = useContext(DataContext);
  if (!context) {
    throw new Error('useData must be used within a DataProvider');
  }
  return context;
};

export default DataContext;
