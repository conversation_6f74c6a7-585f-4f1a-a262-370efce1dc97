{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Projet-Synthese\\\\frontend\\\\src\\\\pages\\\\Admin\\\\Dashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { toast } from 'react-toastify';\nimport { FaUsers, FaShoppingCart, FaDollarSign, FaBox, FaExclamationTriangle, FaSync } from 'react-icons/fa';\nimport { dashboardService } from '../../services/dashboardService';\nimport StatsCard from '../../components/StatsCard';\nimport DashboardCharts from '../../components/DashboardCharts';\nimport 'react-toastify/dist/ReactToastify.css';\n\n// Dashboard principal\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  var _stats$overview, _stats$overview2, _stats$overview3, _stats$overview4, _stats$overview5, _stats$overview6, _stats$overview7, _stats$overview8, _stats$overview9, _stats$overview10, _stats$overview11, _stats$overview12, _stats$overview13, _stats$charts, _stats$charts$monthly, _stats$charts2, _stats$charts2$monthl, _stats$charts3, _stats$charts3$popula, _stats$charts4, _stats$charts4$popula;\n  const [stats, setStats] = useState(null);\n  const [recentActivity, setRecentActivity] = useState([]);\n  const [lowStockProducts, setLowStockProducts] = useState([]);\n  const [recentOrders, setRecentOrders] = useState([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [lastUpdated, setLastUpdated] = useState(new Date());\n  const [apiConnected, setApiConnected] = useState(false);\n  useEffect(() => {\n    fetchDashboardData();\n  }, []);\n  const fetchDashboardData = async () => {\n    try {\n      setIsLoading(true);\n      console.log('🔄 Chargement des données du dashboard...');\n\n      // Charger toutes les données en parallèle\n      const [statsResult, activityResult, lowStockResult, ordersResult] = await Promise.all([dashboardService.getStats(), dashboardService.getRecentActivity(), dashboardService.getLowStockProducts(), dashboardService.getRecentOrders()]);\n\n      // Traiter les statistiques\n      if (statsResult.success) {\n        console.log('✅ Statistiques chargées depuis l\\'API backend');\n        setApiConnected(true);\n        setStats({\n          overview: {\n            total_orders: statsResult.data.totalOrders || 0,\n            total_revenue: statsResult.data.totalRevenue || 0,\n            total_users: statsResult.data.totalUsers || 0,\n            total_products: statsResult.data.totalProducts || 0,\n            orders_last_30_days: statsResult.data.weeklyOrders || 0,\n            revenue_last_30_days: statsResult.data.monthlyRevenue || 0,\n            new_users_last_30_days: 15,\n            active_promotions: 3,\n            low_stock_products: statsResult.data.lowStockProducts || 0\n          }\n        });\n        toast.success('Données chargées depuis l\\'API');\n      } else {\n        console.log('⚠️ Mode démonstration - API non disponible');\n        setApiConnected(false);\n        setStats({\n          overview: {\n            total_orders: statsResult.data.totalOrders || 89,\n            total_revenue: statsResult.data.totalRevenue || 12450.75,\n            total_users: statsResult.data.totalUsers || 234,\n            total_products: statsResult.data.totalProducts || 156,\n            orders_last_30_days: statsResult.data.weeklyOrders || 23,\n            revenue_last_30_days: statsResult.data.monthlyRevenue || 8750.25,\n            new_users_last_30_days: 15,\n            active_promotions: 3,\n            low_stock_products: statsResult.data.lowStockProducts || 8\n          }\n        });\n        toast.info('Mode démonstration - Données locales');\n      }\n\n      // Traiter les autres données\n      setRecentActivity(activityResult.data || []);\n      setLowStockProducts(lowStockResult.data || []);\n      setRecentOrders(ordersResult.data || []);\n      setLastUpdated(new Date());\n    } catch (error) {\n      console.error('❌ Erreur dashboard:', error);\n      toast.error('Erreur lors du chargement des données');\n\n      // Pas de données de fallback - forcer l'utilisation de Laravel\n      setStats({\n        overview: {\n          total_orders: 0,\n          total_revenue: 0,\n          total_users: 0,\n          total_products: 0,\n          orders_last_30_days: 0,\n          revenue_last_30_days: 0,\n          new_users_last_30_days: 0,\n          active_promotions: 0,\n          low_stock_products: 0\n        }\n      });\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('fr-MA', {\n      style: 'currency',\n      currency: 'MAD'\n    }).format(amount);\n  };\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-center items-center h-64\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-gray-900\",\n          children: \"Tableau de bord\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4 mt-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500\",\n            children: [\"Derni\\xE8re mise \\xE0 jour: \", lastUpdated.toLocaleString('fr-FR')]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `flex items-center space-x-2 px-3 py-1 rounded-full text-xs ${apiConnected ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `w-2 h-2 rounded-full ${apiConnected ? 'bg-green-500' : 'bg-yellow-500'}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: apiConnected ? 'API Connectée' : 'Mode Démonstration'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: fetchDashboardData,\n        className: \"flex items-center space-x-2 px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors\",\n        disabled: isLoading,\n        children: [/*#__PURE__*/_jsxDEV(FaSync, {\n          className: `${isLoading ? 'animate-spin' : ''}`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Actualiser\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(StatsCard, {\n        title: \"Total Commandes\",\n        value: (stats === null || stats === void 0 ? void 0 : (_stats$overview = stats.overview) === null || _stats$overview === void 0 ? void 0 : _stats$overview.total_orders) || 0,\n        change: `+${(stats === null || stats === void 0 ? void 0 : (_stats$overview2 = stats.overview) === null || _stats$overview2 === void 0 ? void 0 : _stats$overview2.orders_last_30_days) || 0} ce mois`,\n        icon: /*#__PURE__*/_jsxDEV(FaShoppingCart, {\n          className: \"text-xl\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 17\n        }, this),\n        color: \"blue\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatsCard, {\n        title: \"Revenus Total\",\n        value: formatCurrency((stats === null || stats === void 0 ? void 0 : (_stats$overview3 = stats.overview) === null || _stats$overview3 === void 0 ? void 0 : _stats$overview3.total_revenue) || 0),\n        change: `+${formatCurrency((stats === null || stats === void 0 ? void 0 : (_stats$overview4 = stats.overview) === null || _stats$overview4 === void 0 ? void 0 : _stats$overview4.revenue_last_30_days) || 0)} ce mois`,\n        icon: /*#__PURE__*/_jsxDEV(FaDollarSign, {\n          className: \"text-xl\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 17\n        }, this),\n        color: \"green\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatsCard, {\n        title: \"Clients\",\n        value: (stats === null || stats === void 0 ? void 0 : (_stats$overview5 = stats.overview) === null || _stats$overview5 === void 0 ? void 0 : _stats$overview5.total_users) || 0,\n        change: `+${(stats === null || stats === void 0 ? void 0 : (_stats$overview6 = stats.overview) === null || _stats$overview6 === void 0 ? void 0 : _stats$overview6.new_users_last_30_days) || 0} nouveaux`,\n        icon: /*#__PURE__*/_jsxDEV(FaUsers, {\n          className: \"text-xl\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 17\n        }, this),\n        color: \"purple\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatsCard, {\n        title: \"Produits\",\n        value: (stats === null || stats === void 0 ? void 0 : (_stats$overview7 = stats.overview) === null || _stats$overview7 === void 0 ? void 0 : _stats$overview7.total_products) || 0,\n        change: (stats === null || stats === void 0 ? void 0 : (_stats$overview8 = stats.overview) === null || _stats$overview8 === void 0 ? void 0 : _stats$overview8.low_stock_products) > 0 ? `${stats.overview.low_stock_products} en rupture` : 'Stock suffisant',\n        icon: /*#__PURE__*/_jsxDEV(FaBox, {\n          className: \"text-xl\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 17\n        }, this),\n        color: (stats === null || stats === void 0 ? void 0 : (_stats$overview9 = stats.overview) === null || _stats$overview9 === void 0 ? void 0 : _stats$overview9.low_stock_products) > 0 ? 'red' : 'orange'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 164,\n      columnNumber: 7\n    }, this), (stats === null || stats === void 0 ? void 0 : (_stats$overview10 = stats.overview) === null || _stats$overview10 === void 0 ? void 0 : _stats$overview10.low_stock_products) > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(FaExclamationTriangle, {\n          className: \"text-red-500 mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-red-800\",\n          children: [\"Attention: \", stats.overview.low_stock_products, \" produit(s) en rupture de stock\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 203,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 xl:grid-cols-3 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"xl:col-span-2\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-sm p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-900 mb-4\",\n            children: \"Activit\\xE9s R\\xE9centes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: recentActivity.map(activity => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-2 h-2 rounded-full ${activity.status === 'success' ? 'bg-green-500' : activity.status === 'warning' ? 'bg-yellow-500' : activity.status === 'pending' ? 'bg-blue-500' : activity.status === 'completed' ? 'bg-green-600' : 'bg-gray-500'}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 224,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-900\",\n                  children: activity.message\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 232,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-500\",\n                  children: [activity.user, \" \\u2022 \", activity.time, activity.amount && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-2 font-medium text-green-600\",\n                    children: formatCurrency(activity.amount)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 236,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 233,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 19\n              }, this)]\n            }, activity.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white p-4 lg:p-6 rounded-lg shadow-sm border\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-900 mb-4\",\n            children: \"Promotions actives\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-3xl font-bold text-primary-600\",\n              children: (stats === null || stats === void 0 ? void 0 : (_stats$overview11 = stats.overview) === null || _stats$overview11 === void 0 ? void 0 : _stats$overview11.active_promotions) || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-500\",\n              children: \"promotions en cours\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white p-4 lg:p-6 rounded-lg shadow-sm border\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-900 mb-4\",\n            children: \"Performance\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Taux de conversion\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium\",\n                children: \"12.5%\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Panier moyen\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium\",\n                children: formatCurrency(((stats === null || stats === void 0 ? void 0 : (_stats$overview12 = stats.overview) === null || _stats$overview12 === void 0 ? void 0 : _stats$overview12.total_revenue) || 0) / ((stats === null || stats === void 0 ? void 0 : (_stats$overview13 = stats.overview) === null || _stats$overview13 === void 0 ? void 0 : _stats$overview13.total_orders) || 1))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Satisfaction client\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium\",\n                children: \"4.2/5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white p-4 lg:p-6 rounded-lg shadow-sm border\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-900 mb-4 flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(FaExclamationTriangle, {\n              className: \"mr-2 text-yellow-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 15\n            }, this), \"Stock Faible\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2\",\n            children: lowStockProducts.slice(0, 3).map(product => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between p-2 bg-red-50 rounded border border-red-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-900\",\n                  children: product.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 290,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-600\",\n                  children: product.category\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 291,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-right\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-bold text-red-600\",\n                  children: product.stock\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 294,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-500\",\n                  children: [\"Min: \", product.min_stock]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 295,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 19\n              }, this)]\n            }, product.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white p-4 lg:p-6 rounded-lg shadow-sm border\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-900 mb-4 flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(FaShoppingCart, {\n              className: \"mr-2 text-blue-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 15\n            }, this), \"Commandes R\\xE9centes\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2\",\n            children: recentOrders.slice(0, 3).map(order => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between p-2 bg-gray-50 rounded\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-900\",\n                  children: [\"#\", order.id]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 312,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-600\",\n                  children: order.user.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 313,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-right\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-bold text-green-600\",\n                  children: formatCurrency(order.total)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 316,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: `text-xs px-2 py-1 rounded-full ${order.status === 'completed' ? 'bg-green-100 text-green-800' : order.status === 'processing' ? 'bg-blue-100 text-blue-800' : order.status === 'pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800'}`,\n                  children: order.status === 'completed' ? 'Terminée' : order.status === 'processing' ? 'En cours' : order.status === 'pending' ? 'En attente' : order.status\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 317,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 19\n              }, this)]\n            }, order.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 216,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DashboardCharts, {\n      salesData: {\n        labels: (stats === null || stats === void 0 ? void 0 : (_stats$charts = stats.charts) === null || _stats$charts === void 0 ? void 0 : (_stats$charts$monthly = _stats$charts.monthly_revenue) === null || _stats$charts$monthly === void 0 ? void 0 : _stats$charts$monthly.map(item => item.month)) || [],\n        data: (stats === null || stats === void 0 ? void 0 : (_stats$charts2 = stats.charts) === null || _stats$charts2 === void 0 ? void 0 : (_stats$charts2$monthl = _stats$charts2.monthly_revenue) === null || _stats$charts2$monthl === void 0 ? void 0 : _stats$charts2$monthl.map(item => item.revenue)) || []\n      },\n      categoryData: {\n        labels: (stats === null || stats === void 0 ? void 0 : (_stats$charts3 = stats.charts) === null || _stats$charts3 === void 0 ? void 0 : (_stats$charts3$popula = _stats$charts3.popular_categories) === null || _stats$charts3$popula === void 0 ? void 0 : _stats$charts3$popula.map(item => item.name)) || [],\n        data: (stats === null || stats === void 0 ? void 0 : (_stats$charts4 = stats.charts) === null || _stats$charts4 === void 0 ? void 0 : (_stats$charts4$popula = _stats$charts4.popular_categories) === null || _stats$charts4$popula === void 0 ? void 0 : _stats$charts4$popula.map(item => item.total_sold)) || []\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 336,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 132,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"P8zhZpHWTbAONk+BG6MQGwtU+ok=\");\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "toast", "FaUsers", "FaShoppingCart", "FaDollarSign", "FaBox", "FaExclamationTriangle", "FaSync", "dashboardService", "StatsCard", "DashboardCharts", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "_stats$overview", "_stats$overview2", "_stats$overview3", "_stats$overview4", "_stats$overview5", "_stats$overview6", "_stats$overview7", "_stats$overview8", "_stats$overview9", "_stats$overview10", "_stats$overview11", "_stats$overview12", "_stats$overview13", "_stats$charts", "_stats$charts$monthly", "_stats$charts2", "_stats$charts2$monthl", "_stats$charts3", "_stats$charts3$popula", "_stats$charts4", "_stats$charts4$popula", "stats", "setStats", "recentActivity", "setRecentActivity", "lowStockProducts", "setLowStockProducts", "recentOrders", "setRecentOrders", "isLoading", "setIsLoading", "lastUpdated", "setLastUpdated", "Date", "apiConnected", "setApiConnected", "fetchDashboardData", "console", "log", "statsResult", "activityResult", "lowStockResult", "ordersResult", "Promise", "all", "getStats", "getRecentActivity", "getLowStockProducts", "getRecentOrders", "success", "overview", "total_orders", "data", "totalOrders", "total_revenue", "totalRevenue", "total_users", "totalUsers", "total_products", "totalProducts", "orders_last_30_days", "weeklyOrders", "revenue_last_30_days", "monthlyRevenue", "new_users_last_30_days", "active_promotions", "low_stock_products", "info", "error", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "format", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "toLocaleString", "onClick", "disabled", "title", "value", "change", "icon", "color", "map", "activity", "status", "message", "user", "time", "id", "slice", "product", "name", "category", "stock", "min_stock", "order", "total", "salesData", "labels", "charts", "monthly_revenue", "item", "month", "revenue", "categoryData", "popular_categories", "total_sold", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet-Synthese/frontend/src/pages/Admin/Dashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { toast } from 'react-toastify';\nimport {\n  FaUsers,\n  FaShoppingCart,\n  FaDollarSign,\n  FaBox,\n  FaExclamationTriangle,\n  FaSync\n} from 'react-icons/fa';\nimport { dashboardService } from '../../services/dashboardService';\nimport StatsCard from '../../components/StatsCard';\nimport DashboardCharts from '../../components/DashboardCharts';\nimport 'react-toastify/dist/ReactToastify.css';\n\n\n// Dashboard principal\n\nconst Dashboard = () => {\n  const [stats, setStats] = useState(null);\n  const [recentActivity, setRecentActivity] = useState([]);\n  const [lowStockProducts, setLowStockProducts] = useState([]);\n  const [recentOrders, setRecentOrders] = useState([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [lastUpdated, setLastUpdated] = useState(new Date());\n  const [apiConnected, setApiConnected] = useState(false);\n\n  useEffect(() => {\n    fetchDashboardData();\n  }, []);\n\n  const fetchDashboardData = async () => {\n    try {\n      setIsLoading(true);\n      console.log('🔄 Chargement des données du dashboard...');\n\n      // Charger toutes les données en parallèle\n      const [\n        statsResult,\n        activityResult,\n        lowStockResult,\n        ordersResult\n      ] = await Promise.all([\n        dashboardService.getStats(),\n        dashboardService.getRecentActivity(),\n        dashboardService.getLowStockProducts(),\n        dashboardService.getRecentOrders()\n      ]);\n\n      // Traiter les statistiques\n      if (statsResult.success) {\n        console.log('✅ Statistiques chargées depuis l\\'API backend');\n        setApiConnected(true);\n        setStats({\n          overview: {\n            total_orders: statsResult.data.totalOrders || 0,\n            total_revenue: statsResult.data.totalRevenue || 0,\n            total_users: statsResult.data.totalUsers || 0,\n            total_products: statsResult.data.totalProducts || 0,\n            orders_last_30_days: statsResult.data.weeklyOrders || 0,\n            revenue_last_30_days: statsResult.data.monthlyRevenue || 0,\n            new_users_last_30_days: 15,\n            active_promotions: 3,\n            low_stock_products: statsResult.data.lowStockProducts || 0\n          }\n        });\n        toast.success('Données chargées depuis l\\'API');\n      } else {\n        console.log('⚠️ Mode démonstration - API non disponible');\n        setApiConnected(false);\n        setStats({\n          overview: {\n            total_orders: statsResult.data.totalOrders || 89,\n            total_revenue: statsResult.data.totalRevenue || 12450.75,\n            total_users: statsResult.data.totalUsers || 234,\n            total_products: statsResult.data.totalProducts || 156,\n            orders_last_30_days: statsResult.data.weeklyOrders || 23,\n            revenue_last_30_days: statsResult.data.monthlyRevenue || 8750.25,\n            new_users_last_30_days: 15,\n            active_promotions: 3,\n            low_stock_products: statsResult.data.lowStockProducts || 8\n          }\n        });\n        toast.info('Mode démonstration - Données locales');\n      }\n\n      // Traiter les autres données\n      setRecentActivity(activityResult.data || []);\n      setLowStockProducts(lowStockResult.data || []);\n      setRecentOrders(ordersResult.data || []);\n\n      setLastUpdated(new Date());\n    } catch (error) {\n      console.error('❌ Erreur dashboard:', error);\n      toast.error('Erreur lors du chargement des données');\n\n      // Pas de données de fallback - forcer l'utilisation de Laravel\n      setStats({\n        overview: {\n          total_orders: 0,\n          total_revenue: 0,\n          total_users: 0,\n          total_products: 0,\n          orders_last_30_days: 0,\n          revenue_last_30_days: 0,\n          new_users_last_30_days: 0,\n          active_promotions: 0,\n          low_stock_products: 0\n        }\n      });\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const formatCurrency = (amount) => {\n    return new Intl.NumberFormat('fr-MA', {\n      style: 'currency',\n      currency: 'MAD'\n    }).format(amount);\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"flex justify-center items-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* En-tête */}\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">Tableau de bord</h1>\n          <div className=\"flex items-center space-x-4 mt-1\">\n            <p className=\"text-sm text-gray-500\">\n              Dernière mise à jour: {lastUpdated.toLocaleString('fr-FR')}\n            </p>\n            <div className={`flex items-center space-x-2 px-3 py-1 rounded-full text-xs ${\n              apiConnected\n                ? 'bg-green-100 text-green-800'\n                : 'bg-yellow-100 text-yellow-800'\n            }`}>\n              <div className={`w-2 h-2 rounded-full ${\n                apiConnected ? 'bg-green-500' : 'bg-yellow-500'\n              }`}></div>\n              <span>{apiConnected ? 'API Connectée' : 'Mode Démonstration'}</span>\n            </div>\n          </div>\n        </div>\n        <button\n          onClick={fetchDashboardData}\n          className=\"flex items-center space-x-2 px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors\"\n          disabled={isLoading}\n        >\n          <FaSync className={`${isLoading ? 'animate-spin' : ''}`} />\n          <span>Actualiser</span>\n        </button>\n      </div>\n\n      {/* Statistiques principales */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        <StatsCard\n          title=\"Total Commandes\"\n          value={stats?.overview?.total_orders || 0}\n          change={`+${stats?.overview?.orders_last_30_days || 0} ce mois`}\n          icon={<FaShoppingCart className=\"text-xl\" />}\n          color=\"blue\"\n        />\n\n        <StatsCard\n          title=\"Revenus Total\"\n          value={formatCurrency(stats?.overview?.total_revenue || 0)}\n          change={`+${formatCurrency(stats?.overview?.revenue_last_30_days || 0)} ce mois`}\n          icon={<FaDollarSign className=\"text-xl\" />}\n          color=\"green\"\n        />\n\n        <StatsCard\n          title=\"Clients\"\n          value={stats?.overview?.total_users || 0}\n          change={`+${stats?.overview?.new_users_last_30_days || 0} nouveaux`}\n          icon={<FaUsers className=\"text-xl\" />}\n          color=\"purple\"\n        />\n\n        <StatsCard\n          title=\"Produits\"\n          value={stats?.overview?.total_products || 0}\n          change={stats?.overview?.low_stock_products > 0 ?\n            `${stats.overview.low_stock_products} en rupture` :\n            'Stock suffisant'\n          }\n          icon={<FaBox className=\"text-xl\" />}\n          color={stats?.overview?.low_stock_products > 0 ? 'red' : 'orange'}\n        />\n      </div>\n\n      {/* Alertes */}\n      {stats?.overview?.low_stock_products > 0 && (\n        <div className=\"bg-red-50 border border-red-200 rounded-lg p-4\">\n          <div className=\"flex items-center\">\n            <FaExclamationTriangle className=\"text-red-500 mr-2\" />\n            <span className=\"text-red-800\">\n              Attention: {stats.overview.low_stock_products} produit(s) en rupture de stock\n            </span>\n          </div>\n        </div>\n      )}\n\n\n\n      {/* Section inférieure */}\n      <div className=\"grid grid-cols-1 xl:grid-cols-3 gap-6\">\n        {/* Activités récentes */}\n        <div className=\"xl:col-span-2\">\n          <div className=\"bg-white rounded-lg shadow-sm p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Activités Récentes</h3>\n            <div className=\"space-y-4\">\n              {recentActivity.map((activity) => (\n                <div key={activity.id} className=\"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg\">\n                  <div className={`w-2 h-2 rounded-full ${\n                    activity.status === 'success' ? 'bg-green-500' :\n                    activity.status === 'warning' ? 'bg-yellow-500' :\n                    activity.status === 'pending' ? 'bg-blue-500' :\n                    activity.status === 'completed' ? 'bg-green-600' :\n                    'bg-gray-500'\n                  }`}></div>\n                  <div className=\"flex-1\">\n                    <p className=\"text-sm font-medium text-gray-900\">{activity.message}</p>\n                    <p className=\"text-xs text-gray-500\">\n                      {activity.user} • {activity.time}\n                      {activity.amount && (\n                        <span className=\"ml-2 font-medium text-green-600\">\n                          {formatCurrency(activity.amount)}\n                        </span>\n                      )}\n                    </p>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n\n        {/* Statistiques supplémentaires */}\n        <div className=\"space-y-6\">\n          <div className=\"bg-white p-4 lg:p-6 rounded-lg shadow-sm border\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Promotions actives</h3>\n            <div className=\"text-center\">\n              <p className=\"text-3xl font-bold text-primary-600\">\n                {stats?.overview?.active_promotions || 0}\n              </p>\n              <p className=\"text-sm text-gray-500\">promotions en cours</p>\n            </div>\n          </div>\n\n          <div className=\"bg-white p-4 lg:p-6 rounded-lg shadow-sm border\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Performance</h3>\n            <div className=\"space-y-3\">\n              <div className=\"flex justify-between\">\n                <span className=\"text-sm text-gray-600\">Taux de conversion</span>\n                <span className=\"text-sm font-medium\">12.5%</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-sm text-gray-600\">Panier moyen</span>\n                <span className=\"text-sm font-medium\">\n                  {formatCurrency((stats?.overview?.total_revenue || 0) / (stats?.overview?.total_orders || 1))}\n                </span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-sm text-gray-600\">Satisfaction client</span>\n                <span className=\"text-sm font-medium\">4.2/5</span>\n              </div>\n            </div>\n          </div>\n\n          {/* Produits en stock faible */}\n          <div className=\"bg-white p-4 lg:p-6 rounded-lg shadow-sm border\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4 flex items-center\">\n              <FaExclamationTriangle className=\"mr-2 text-yellow-500\" />\n              Stock Faible\n            </h3>\n            <div className=\"space-y-2\">\n              {lowStockProducts.slice(0, 3).map((product) => (\n                <div key={product.id} className=\"flex items-center justify-between p-2 bg-red-50 rounded border border-red-200\">\n                  <div>\n                    <p className=\"text-sm font-medium text-gray-900\">{product.name}</p>\n                    <p className=\"text-xs text-gray-600\">{product.category}</p>\n                  </div>\n                  <div className=\"text-right\">\n                    <p className=\"text-sm font-bold text-red-600\">{product.stock}</p>\n                    <p className=\"text-xs text-gray-500\">Min: {product.min_stock}</p>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n\n          {/* Commandes récentes */}\n          <div className=\"bg-white p-4 lg:p-6 rounded-lg shadow-sm border\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4 flex items-center\">\n              <FaShoppingCart className=\"mr-2 text-blue-500\" />\n              Commandes Récentes\n            </h3>\n            <div className=\"space-y-2\">\n              {recentOrders.slice(0, 3).map((order) => (\n                <div key={order.id} className=\"flex items-center justify-between p-2 bg-gray-50 rounded\">\n                  <div>\n                    <p className=\"text-sm font-medium text-gray-900\">#{order.id}</p>\n                    <p className=\"text-xs text-gray-600\">{order.user.name}</p>\n                  </div>\n                  <div className=\"text-right\">\n                    <p className=\"text-sm font-bold text-green-600\">{formatCurrency(order.total)}</p>\n                    <p className={`text-xs px-2 py-1 rounded-full ${\n                      order.status === 'completed' ? 'bg-green-100 text-green-800' :\n                      order.status === 'processing' ? 'bg-blue-100 text-blue-800' :\n                      order.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :\n                      'bg-gray-100 text-gray-800'\n                    }`}>\n                      {order.status === 'completed' ? 'Terminée' :\n                       order.status === 'processing' ? 'En cours' :\n                       order.status === 'pending' ? 'En attente' : order.status}\n                    </p>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Section graphiques */}\n      <DashboardCharts\n        salesData={{\n          labels: stats?.charts?.monthly_revenue?.map(item => item.month) || [],\n          data: stats?.charts?.monthly_revenue?.map(item => item.revenue) || []\n        }}\n        categoryData={{\n          labels: stats?.charts?.popular_categories?.map(item => item.name) || [],\n          data: stats?.charts?.popular_categories?.map(item => item.total_sold) || []\n        }}\n      />\n    </div>\n  );\n};\n\nexport default Dashboard;\n\n\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SACEC,OAAO,EACPC,cAAc,EACdC,YAAY,EACZC,KAAK,EACLC,qBAAqB,EACrBC,MAAM,QACD,gBAAgB;AACvB,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,OAAOC,SAAS,MAAM,4BAA4B;AAClD,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,OAAO,uCAAuC;;AAG9C;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEA,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,eAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,iBAAA,EAAAC,iBAAA,EAAAC,iBAAA,EAAAC,iBAAA,EAAAC,aAAA,EAAAC,qBAAA,EAAAC,cAAA,EAAAC,qBAAA,EAAAC,cAAA,EAAAC,qBAAA,EAAAC,cAAA,EAAAC,qBAAA;EACtB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGtC,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACuC,cAAc,EAAEC,iBAAiB,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACyC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAAC2C,YAAY,EAAEC,eAAe,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC6C,SAAS,EAAEC,YAAY,CAAC,GAAG9C,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC+C,WAAW,EAAEC,cAAc,CAAC,GAAGhD,QAAQ,CAAC,IAAIiD,IAAI,CAAC,CAAC,CAAC;EAC1D,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;EAEvDC,SAAS,CAAC,MAAM;IACdmD,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACFN,YAAY,CAAC,IAAI,CAAC;MAClBO,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;;MAExD;MACA,MAAM,CACJC,WAAW,EACXC,cAAc,EACdC,cAAc,EACdC,YAAY,CACb,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACpBnD,gBAAgB,CAACoD,QAAQ,CAAC,CAAC,EAC3BpD,gBAAgB,CAACqD,iBAAiB,CAAC,CAAC,EACpCrD,gBAAgB,CAACsD,mBAAmB,CAAC,CAAC,EACtCtD,gBAAgB,CAACuD,eAAe,CAAC,CAAC,CACnC,CAAC;;MAEF;MACA,IAAIT,WAAW,CAACU,OAAO,EAAE;QACvBZ,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;QAC5DH,eAAe,CAAC,IAAI,CAAC;QACrBb,QAAQ,CAAC;UACP4B,QAAQ,EAAE;YACRC,YAAY,EAAEZ,WAAW,CAACa,IAAI,CAACC,WAAW,IAAI,CAAC;YAC/CC,aAAa,EAAEf,WAAW,CAACa,IAAI,CAACG,YAAY,IAAI,CAAC;YACjDC,WAAW,EAAEjB,WAAW,CAACa,IAAI,CAACK,UAAU,IAAI,CAAC;YAC7CC,cAAc,EAAEnB,WAAW,CAACa,IAAI,CAACO,aAAa,IAAI,CAAC;YACnDC,mBAAmB,EAAErB,WAAW,CAACa,IAAI,CAACS,YAAY,IAAI,CAAC;YACvDC,oBAAoB,EAAEvB,WAAW,CAACa,IAAI,CAACW,cAAc,IAAI,CAAC;YAC1DC,sBAAsB,EAAE,EAAE;YAC1BC,iBAAiB,EAAE,CAAC;YACpBC,kBAAkB,EAAE3B,WAAW,CAACa,IAAI,CAAC3B,gBAAgB,IAAI;UAC3D;QACF,CAAC,CAAC;QACFvC,KAAK,CAAC+D,OAAO,CAAC,gCAAgC,CAAC;MACjD,CAAC,MAAM;QACLZ,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;QACzDH,eAAe,CAAC,KAAK,CAAC;QACtBb,QAAQ,CAAC;UACP4B,QAAQ,EAAE;YACRC,YAAY,EAAEZ,WAAW,CAACa,IAAI,CAACC,WAAW,IAAI,EAAE;YAChDC,aAAa,EAAEf,WAAW,CAACa,IAAI,CAACG,YAAY,IAAI,QAAQ;YACxDC,WAAW,EAAEjB,WAAW,CAACa,IAAI,CAACK,UAAU,IAAI,GAAG;YAC/CC,cAAc,EAAEnB,WAAW,CAACa,IAAI,CAACO,aAAa,IAAI,GAAG;YACrDC,mBAAmB,EAAErB,WAAW,CAACa,IAAI,CAACS,YAAY,IAAI,EAAE;YACxDC,oBAAoB,EAAEvB,WAAW,CAACa,IAAI,CAACW,cAAc,IAAI,OAAO;YAChEC,sBAAsB,EAAE,EAAE;YAC1BC,iBAAiB,EAAE,CAAC;YACpBC,kBAAkB,EAAE3B,WAAW,CAACa,IAAI,CAAC3B,gBAAgB,IAAI;UAC3D;QACF,CAAC,CAAC;QACFvC,KAAK,CAACiF,IAAI,CAAC,sCAAsC,CAAC;MACpD;;MAEA;MACA3C,iBAAiB,CAACgB,cAAc,CAACY,IAAI,IAAI,EAAE,CAAC;MAC5C1B,mBAAmB,CAACe,cAAc,CAACW,IAAI,IAAI,EAAE,CAAC;MAC9CxB,eAAe,CAACc,YAAY,CAACU,IAAI,IAAI,EAAE,CAAC;MAExCpB,cAAc,CAAC,IAAIC,IAAI,CAAC,CAAC,CAAC;IAC5B,CAAC,CAAC,OAAOmC,KAAK,EAAE;MACd/B,OAAO,CAAC+B,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3ClF,KAAK,CAACkF,KAAK,CAAC,uCAAuC,CAAC;;MAEpD;MACA9C,QAAQ,CAAC;QACP4B,QAAQ,EAAE;UACRC,YAAY,EAAE,CAAC;UACfG,aAAa,EAAE,CAAC;UAChBE,WAAW,EAAE,CAAC;UACdE,cAAc,EAAE,CAAC;UACjBE,mBAAmB,EAAE,CAAC;UACtBE,oBAAoB,EAAE,CAAC;UACvBE,sBAAsB,EAAE,CAAC;UACzBC,iBAAiB,EAAE,CAAC;UACpBC,kBAAkB,EAAE;QACtB;MACF,CAAC,CAAC;IACJ,CAAC,SAAS;MACRpC,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMuC,cAAc,GAAIC,MAAM,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAACC,MAAM,CAACL,MAAM,CAAC;EACnB,CAAC;EAED,IAAIzC,SAAS,EAAE;IACb,oBACEhC,OAAA;MAAK+E,SAAS,EAAC,uCAAuC;MAAAC,QAAA,eACpDhF,OAAA;QAAK+E,SAAS,EAAC;MAAmE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtF,CAAC;EAEV;EAEA,oBACEpF,OAAA;IAAK+E,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBhF,OAAA;MAAK+E,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChDhF,OAAA;QAAAgF,QAAA,gBACEhF,OAAA;UAAI+E,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrEpF,OAAA;UAAK+E,SAAS,EAAC,kCAAkC;UAAAC,QAAA,gBAC/ChF,OAAA;YAAG+E,SAAS,EAAC,uBAAuB;YAAAC,QAAA,GAAC,8BACb,EAAC9C,WAAW,CAACmD,cAAc,CAAC,OAAO,CAAC;UAAA;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,eACJpF,OAAA;YAAK+E,SAAS,EAAE,8DACd1C,YAAY,GACR,6BAA6B,GAC7B,+BAA+B,EAClC;YAAA2C,QAAA,gBACDhF,OAAA;cAAK+E,SAAS,EAAE,wBACd1C,YAAY,GAAG,cAAc,GAAG,eAAe;YAC9C;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACVpF,OAAA;cAAAgF,QAAA,EAAO3C,YAAY,GAAG,eAAe,GAAG;YAAoB;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNpF,OAAA;QACEsF,OAAO,EAAE/C,kBAAmB;QAC5BwC,SAAS,EAAC,mHAAmH;QAC7HQ,QAAQ,EAAEvD,SAAU;QAAAgD,QAAA,gBAEpBhF,OAAA,CAACL,MAAM;UAACoF,SAAS,EAAE,GAAG/C,SAAS,GAAG,cAAc,GAAG,EAAE;QAAG;UAAAiD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3DpF,OAAA;UAAAgF,QAAA,EAAM;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNpF,OAAA;MAAK+E,SAAS,EAAC,sDAAsD;MAAAC,QAAA,gBACnEhF,OAAA,CAACH,SAAS;QACR2F,KAAK,EAAC,iBAAiB;QACvBC,KAAK,EAAE,CAAAjE,KAAK,aAALA,KAAK,wBAAArB,eAAA,GAALqB,KAAK,CAAE6B,QAAQ,cAAAlD,eAAA,uBAAfA,eAAA,CAAiBmD,YAAY,KAAI,CAAE;QAC1CoC,MAAM,EAAE,IAAI,CAAAlE,KAAK,aAALA,KAAK,wBAAApB,gBAAA,GAALoB,KAAK,CAAE6B,QAAQ,cAAAjD,gBAAA,uBAAfA,gBAAA,CAAiB2D,mBAAmB,KAAI,CAAC,UAAW;QAChE4B,IAAI,eAAE3F,OAAA,CAACT,cAAc;UAACwF,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC7CQ,KAAK,EAAC;MAAM;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC,eAEFpF,OAAA,CAACH,SAAS;QACR2F,KAAK,EAAC,eAAe;QACrBC,KAAK,EAAEjB,cAAc,CAAC,CAAAhD,KAAK,aAALA,KAAK,wBAAAnB,gBAAA,GAALmB,KAAK,CAAE6B,QAAQ,cAAAhD,gBAAA,uBAAfA,gBAAA,CAAiBoD,aAAa,KAAI,CAAC,CAAE;QAC3DiC,MAAM,EAAE,IAAIlB,cAAc,CAAC,CAAAhD,KAAK,aAALA,KAAK,wBAAAlB,gBAAA,GAALkB,KAAK,CAAE6B,QAAQ,cAAA/C,gBAAA,uBAAfA,gBAAA,CAAiB2D,oBAAoB,KAAI,CAAC,CAAC,UAAW;QACjF0B,IAAI,eAAE3F,OAAA,CAACR,YAAY;UAACuF,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC3CQ,KAAK,EAAC;MAAO;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC,eAEFpF,OAAA,CAACH,SAAS;QACR2F,KAAK,EAAC,SAAS;QACfC,KAAK,EAAE,CAAAjE,KAAK,aAALA,KAAK,wBAAAjB,gBAAA,GAALiB,KAAK,CAAE6B,QAAQ,cAAA9C,gBAAA,uBAAfA,gBAAA,CAAiBoD,WAAW,KAAI,CAAE;QACzC+B,MAAM,EAAE,IAAI,CAAAlE,KAAK,aAALA,KAAK,wBAAAhB,gBAAA,GAALgB,KAAK,CAAE6B,QAAQ,cAAA7C,gBAAA,uBAAfA,gBAAA,CAAiB2D,sBAAsB,KAAI,CAAC,WAAY;QACpEwB,IAAI,eAAE3F,OAAA,CAACV,OAAO;UAACyF,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACtCQ,KAAK,EAAC;MAAQ;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC,eAEFpF,OAAA,CAACH,SAAS;QACR2F,KAAK,EAAC,UAAU;QAChBC,KAAK,EAAE,CAAAjE,KAAK,aAALA,KAAK,wBAAAf,gBAAA,GAALe,KAAK,CAAE6B,QAAQ,cAAA5C,gBAAA,uBAAfA,gBAAA,CAAiBoD,cAAc,KAAI,CAAE;QAC5C6B,MAAM,EAAE,CAAAlE,KAAK,aAALA,KAAK,wBAAAd,gBAAA,GAALc,KAAK,CAAE6B,QAAQ,cAAA3C,gBAAA,uBAAfA,gBAAA,CAAiB2D,kBAAkB,IAAG,CAAC,GAC7C,GAAG7C,KAAK,CAAC6B,QAAQ,CAACgB,kBAAkB,aAAa,GACjD,iBACD;QACDsB,IAAI,eAAE3F,OAAA,CAACP,KAAK;UAACsF,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACpCQ,KAAK,EAAE,CAAApE,KAAK,aAALA,KAAK,wBAAAb,gBAAA,GAALa,KAAK,CAAE6B,QAAQ,cAAA1C,gBAAA,uBAAfA,gBAAA,CAAiB0D,kBAAkB,IAAG,CAAC,GAAG,KAAK,GAAG;MAAS;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAGL,CAAA5D,KAAK,aAALA,KAAK,wBAAAZ,iBAAA,GAALY,KAAK,CAAE6B,QAAQ,cAAAzC,iBAAA,uBAAfA,iBAAA,CAAiByD,kBAAkB,IAAG,CAAC,iBACtCrE,OAAA;MAAK+E,SAAS,EAAC,gDAAgD;MAAAC,QAAA,eAC7DhF,OAAA;QAAK+E,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChChF,OAAA,CAACN,qBAAqB;UAACqF,SAAS,EAAC;QAAmB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACvDpF,OAAA;UAAM+E,SAAS,EAAC,cAAc;UAAAC,QAAA,GAAC,aAClB,EAACxD,KAAK,CAAC6B,QAAQ,CAACgB,kBAAkB,EAAC,iCAChD;QAAA;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAKDpF,OAAA;MAAK+E,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBAEpDhF,OAAA;QAAK+E,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC5BhF,OAAA;UAAK+E,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDhF,OAAA;YAAI+E,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChFpF,OAAA;YAAK+E,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvBtD,cAAc,CAACmE,GAAG,CAAEC,QAAQ,iBAC3B9F,OAAA;cAAuB+E,SAAS,EAAC,uDAAuD;cAAAC,QAAA,gBACtFhF,OAAA;gBAAK+E,SAAS,EAAE,wBACde,QAAQ,CAACC,MAAM,KAAK,SAAS,GAAG,cAAc,GAC9CD,QAAQ,CAACC,MAAM,KAAK,SAAS,GAAG,eAAe,GAC/CD,QAAQ,CAACC,MAAM,KAAK,SAAS,GAAG,aAAa,GAC7CD,QAAQ,CAACC,MAAM,KAAK,WAAW,GAAG,cAAc,GAChD,aAAa;cACZ;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACVpF,OAAA;gBAAK+E,SAAS,EAAC,QAAQ;gBAAAC,QAAA,gBACrBhF,OAAA;kBAAG+E,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAEc,QAAQ,CAACE;gBAAO;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACvEpF,OAAA;kBAAG+E,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,GACjCc,QAAQ,CAACG,IAAI,EAAC,UAAG,EAACH,QAAQ,CAACI,IAAI,EAC/BJ,QAAQ,CAACrB,MAAM,iBACdzE,OAAA;oBAAM+E,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAC9CR,cAAc,CAACsB,QAAQ,CAACrB,MAAM;kBAAC;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CACP;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA,GAlBEU,QAAQ,CAACK,EAAE;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAmBhB,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNpF,OAAA;QAAK+E,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBhF,OAAA;UAAK+E,SAAS,EAAC,iDAAiD;UAAAC,QAAA,gBAC9DhF,OAAA;YAAI+E,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChFpF,OAAA;YAAK+E,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BhF,OAAA;cAAG+E,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAC/C,CAAAxD,KAAK,aAALA,KAAK,wBAAAX,iBAAA,GAALW,KAAK,CAAE6B,QAAQ,cAAAxC,iBAAA,uBAAfA,iBAAA,CAAiBuD,iBAAiB,KAAI;YAAC;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC,eACJpF,OAAA;cAAG+E,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENpF,OAAA;UAAK+E,SAAS,EAAC,iDAAiD;UAAAC,QAAA,gBAC9DhF,OAAA;YAAI+E,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzEpF,OAAA;YAAK+E,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBhF,OAAA;cAAK+E,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACnChF,OAAA;gBAAM+E,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACjEpF,OAAA;gBAAM+E,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC,eACNpF,OAAA;cAAK+E,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACnChF,OAAA;gBAAM+E,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC3DpF,OAAA;gBAAM+E,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAClCR,cAAc,CAAC,CAAC,CAAAhD,KAAK,aAALA,KAAK,wBAAAV,iBAAA,GAALU,KAAK,CAAE6B,QAAQ,cAAAvC,iBAAA,uBAAfA,iBAAA,CAAiB2C,aAAa,KAAI,CAAC,KAAK,CAAAjC,KAAK,aAALA,KAAK,wBAAAT,iBAAA,GAALS,KAAK,CAAE6B,QAAQ,cAAAtC,iBAAA,uBAAfA,iBAAA,CAAiBuC,YAAY,KAAI,CAAC,CAAC;cAAC;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNpF,OAAA;cAAK+E,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACnChF,OAAA;gBAAM+E,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAClEpF,OAAA;gBAAM+E,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNpF,OAAA;UAAK+E,SAAS,EAAC,iDAAiD;UAAAC,QAAA,gBAC9DhF,OAAA;YAAI+E,SAAS,EAAC,4DAA4D;YAAAC,QAAA,gBACxEhF,OAAA,CAACN,qBAAqB;cAACqF,SAAS,EAAC;YAAsB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAE5D;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLpF,OAAA;YAAK+E,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvBpD,gBAAgB,CAACwE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACP,GAAG,CAAEQ,OAAO,iBACxCrG,OAAA;cAAsB+E,SAAS,EAAC,+EAA+E;cAAAC,QAAA,gBAC7GhF,OAAA;gBAAAgF,QAAA,gBACEhF,OAAA;kBAAG+E,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAEqB,OAAO,CAACC;gBAAI;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnEpF,OAAA;kBAAG+E,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAEqB,OAAO,CAACE;gBAAQ;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC,eACNpF,OAAA;gBAAK+E,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBhF,OAAA;kBAAG+E,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,EAAEqB,OAAO,CAACG;gBAAK;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjEpF,OAAA;kBAAG+E,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,GAAC,OAAK,EAACqB,OAAO,CAACI,SAAS;gBAAA;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D,CAAC;YAAA,GAREiB,OAAO,CAACF,EAAE;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OASf,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNpF,OAAA;UAAK+E,SAAS,EAAC,iDAAiD;UAAAC,QAAA,gBAC9DhF,OAAA;YAAI+E,SAAS,EAAC,4DAA4D;YAAAC,QAAA,gBACxEhF,OAAA,CAACT,cAAc;cAACwF,SAAS,EAAC;YAAoB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,yBAEnD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLpF,OAAA;YAAK+E,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvBlD,YAAY,CAACsE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACP,GAAG,CAAEa,KAAK,iBAClC1G,OAAA;cAAoB+E,SAAS,EAAC,0DAA0D;cAAAC,QAAA,gBACtFhF,OAAA;gBAAAgF,QAAA,gBACEhF,OAAA;kBAAG+E,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,GAAC,GAAC,EAAC0B,KAAK,CAACP,EAAE;gBAAA;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAChEpF,OAAA;kBAAG+E,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAE0B,KAAK,CAACT,IAAI,CAACK;gBAAI;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC,eACNpF,OAAA;gBAAK+E,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBhF,OAAA;kBAAG+E,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAER,cAAc,CAACkC,KAAK,CAACC,KAAK;gBAAC;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjFpF,OAAA;kBAAG+E,SAAS,EAAE,kCACZ2B,KAAK,CAACX,MAAM,KAAK,WAAW,GAAG,6BAA6B,GAC5DW,KAAK,CAACX,MAAM,KAAK,YAAY,GAAG,2BAA2B,GAC3DW,KAAK,CAACX,MAAM,KAAK,SAAS,GAAG,+BAA+B,GAC5D,2BAA2B,EAC1B;kBAAAf,QAAA,EACA0B,KAAK,CAACX,MAAM,KAAK,WAAW,GAAG,UAAU,GACzCW,KAAK,CAACX,MAAM,KAAK,YAAY,GAAG,UAAU,GAC1CW,KAAK,CAACX,MAAM,KAAK,SAAS,GAAG,YAAY,GAAGW,KAAK,CAACX;gBAAM;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA,GAjBEsB,KAAK,CAACP,EAAE;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAkBb,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNpF,OAAA,CAACF,eAAe;MACd8G,SAAS,EAAE;QACTC,MAAM,EAAE,CAAArF,KAAK,aAALA,KAAK,wBAAAR,aAAA,GAALQ,KAAK,CAAEsF,MAAM,cAAA9F,aAAA,wBAAAC,qBAAA,GAAbD,aAAA,CAAe+F,eAAe,cAAA9F,qBAAA,uBAA9BA,qBAAA,CAAgC4E,GAAG,CAACmB,IAAI,IAAIA,IAAI,CAACC,KAAK,CAAC,KAAI,EAAE;QACrE1D,IAAI,EAAE,CAAA/B,KAAK,aAALA,KAAK,wBAAAN,cAAA,GAALM,KAAK,CAAEsF,MAAM,cAAA5F,cAAA,wBAAAC,qBAAA,GAAbD,cAAA,CAAe6F,eAAe,cAAA5F,qBAAA,uBAA9BA,qBAAA,CAAgC0E,GAAG,CAACmB,IAAI,IAAIA,IAAI,CAACE,OAAO,CAAC,KAAI;MACrE,CAAE;MACFC,YAAY,EAAE;QACZN,MAAM,EAAE,CAAArF,KAAK,aAALA,KAAK,wBAAAJ,cAAA,GAALI,KAAK,CAAEsF,MAAM,cAAA1F,cAAA,wBAAAC,qBAAA,GAAbD,cAAA,CAAegG,kBAAkB,cAAA/F,qBAAA,uBAAjCA,qBAAA,CAAmCwE,GAAG,CAACmB,IAAI,IAAIA,IAAI,CAACV,IAAI,CAAC,KAAI,EAAE;QACvE/C,IAAI,EAAE,CAAA/B,KAAK,aAALA,KAAK,wBAAAF,cAAA,GAALE,KAAK,CAAEsF,MAAM,cAAAxF,cAAA,wBAAAC,qBAAA,GAAbD,cAAA,CAAe8F,kBAAkB,cAAA7F,qBAAA,uBAAjCA,qBAAA,CAAmCsE,GAAG,CAACmB,IAAI,IAAIA,IAAI,CAACK,UAAU,CAAC,KAAI;MAC3E;IAAE;MAAApC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAClF,EAAA,CAzUID,SAAS;AAAAqH,EAAA,GAATrH,SAAS;AA2Uf,eAAeA,SAAS;AAAC,IAAAqH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}