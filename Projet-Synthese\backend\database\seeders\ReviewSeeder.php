<?php

namespace Database\Seeders;

use App\Models\Product;
use App\Models\Review;
use App\Models\User;
use Illuminate\Database\Seeder;

class ReviewSeeder extends Seeder
{
    public function run(): void
    {
        Product::all()->each(function ($product) {
            $users = User::inRandomOrder()->limit(3)->get();
            foreach ($users as $user) {
                Review::factory()->create([
                    'product_id' => $product->id,
                    'user_id' => $user->id
                ]);
            }
        });
    }
}