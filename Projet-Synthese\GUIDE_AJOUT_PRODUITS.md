# 📦 Guide d'ajout de produits avec images

## 🎯 **Comment ajouter des produits à l'application**

### **1. Se connecter en tant qu'admin :**
- URL : `http://localhost:3000/admin`
- Email : `<EMAIL>`
- Mot de passe : `password`

### **2. Créer des catégories d'abord :**
1. Aller dans **"Catégories"** dans le menu admin
2. Cliquer sur **"Ajouter une catégorie"**
3. Remplir :
   - **Nom** : ex. "Produits Grillés"
   - **Description** : ex. "Viandes et légumes prêts à griller"
   - **Slug** : ex. "produits-grilles" (généré automatiquement)
4. Cliquer **"Enregistrer"**

### **3. Ajouter des produits :**
1. Aller dans **"Produits"** dans le menu admin
2. Cliquer sur **"Ajouter un produit"**
3. Remplir le formulaire :

#### **Informations de base :**
- **Nom** : ex. "Poulet Grillé Entier"
- **Description** : ex. "Poulet entier grillé aux herbes de Provence"
- **Prix** : ex. 89.99
- **Prix de vente** (optionnel) : ex. 79.99 (pour les promotions)
- **Stock** : ex. 15
- **SKU** : ex. "POULET-001" (code unique)

#### **Catégorie et options :**
- **Catégorie** : Sélectionner dans la liste
- **Peut être grillé** : Cocher si applicable
- **Produit vedette** : Cocher pour afficher sur la page d'accueil
- **Poids** : ex. 1.5 (en kg)

#### **Image :**
- **Image principale** : Choisir un fichier image (JPG, PNG)
- **Images supplémentaires** : Optionnel

4. Cliquer **"Enregistrer"**

---

## 🖼️ **Gestion des images**

### **Format des images :**
- **Types acceptés** : JPG, PNG, JPEG
- **Taille maximale** : 2 MB par image
- **Résolution recommandée** : 800x800 pixels (carré)

### **Où les images sont stockées :**
- **Backend** : `storage/app/public/products/`
- **URL publique** : `http://localhost:8000/storage/products/nom-image.jpg`

### **Comment les images s'affichent :**
1. **Laravel** sauvegarde l'image dans `storage/products/`
2. **Le modèle Product** génère automatiquement `image_url`
3. **Le frontend** utilise `product.image_url` pour afficher l'image
4. **ProductCard** affiche l'image avec fallback si pas d'image

---

## 🔗 **Flux complet d'ajout de produit**

```
Admin ajoute produit → Laravel sauvegarde → Image stockée → URL générée → Frontend affiche
```

### **Exemple concret :**

**1. Admin remplit :**
```
Nom: "Brochettes de Bœuf"
Prix: 65.50
Image: brochettes.jpg (fichier uploadé)
Catégorie: "Produits Grillés"
```

**2. Laravel traite :**
```
- Sauvegarde en base de données
- Stocke l'image dans storage/products/brochettes-xyz.jpg
- Génère le slug: "brochettes-de-boeuf"
- Crée image_url: "http://localhost:8000/storage/products/brochettes-xyz.jpg"
```

**3. Frontend affiche :**
```
- Page d'accueil: Produit visible si "vedette"
- Page produits: Visible dans la liste complète
- Page catégorie: Visible dans "Produits Grillés"
- Image: Affichée via image_url
```

---

## 🎨 **Où les produits apparaissent**

### **Page d'accueil (`/`) :**
- **Produits vedettes** : Produits avec `is_featured = true`
- **Nouveaux produits** : 4 derniers produits ajoutés

### **Page produits (`/products`) :**
- **Tous les produits** actifs
- **Filtres** : Par catégorie, recherche, tri
- **Pagination** : Si beaucoup de produits

### **Pages catégories (`/categories/slug`) :**
- **Produits de la catégorie** uniquement
- **Tri** : Par nom, prix, date
- **URL** : ex. `/categories/produits-grilles`

### **Interface admin (`/admin/products`) :**
- **Gestion complète** : CRUD, images, statuts
- **Aperçu** : Miniatures des images
- **Actions** : Modifier, supprimer, activer/désactiver

---

## 🚀 **Test de l'ajout de produit**

### **1. Ajouter une catégorie :**
```
Nom: "Produits Grillés"
Description: "Viandes et légumes prêts à griller"
```

### **2. Ajouter un produit :**
```
Nom: "Poulet Grillé Entier"
Prix: 89.99
Catégorie: "Produits Grillés"
Image: poulet.jpg
Produit vedette: ✓
```

### **3. Vérifier l'affichage :**
- ✅ **Admin** : Produit visible dans la liste admin
- ✅ **Page d'accueil** : Produit dans "Produits vedettes"
- ✅ **Page produits** : Produit dans la liste complète
- ✅ **Page catégorie** : Produit dans "Produits Grillés"
- ✅ **Image** : Affichée correctement partout

---

## 🔧 **Résolution de problèmes**

### **Images ne s'affichent pas :**
1. Vérifier que le lien symbolique existe : `php artisan storage:link`
2. Vérifier les permissions du dossier `storage/`
3. Vérifier l'URL dans le navigateur : `http://localhost:8000/storage/products/`

### **Produits n'apparaissent pas :**
1. Vérifier que `is_active = true`
2. Vérifier que la catégorie existe
3. Vérifier les logs Laravel : `storage/logs/laravel.log`

### **Erreurs de validation :**
1. Vérifier que tous les champs requis sont remplis
2. Vérifier la taille de l'image (< 2MB)
3. Vérifier que le SKU est unique

---

## 🎉 **Résultat attendu**

**Après avoir ajouté des produits :**
- 🏠 **Page d'accueil** : Remplie avec les produits vedettes
- 📦 **Page produits** : Liste complète avec filtres
- 📁 **Pages catégories** : Produits organisés par catégorie
- 🖼️ **Images** : Affichées correctement partout
- 📊 **Dashboard** : Statistiques mises à jour
- 🔄 **Synchronisation** : Temps réel entre admin et client

**L'application passe d'un état vide à une boutique complète !** 🎯
