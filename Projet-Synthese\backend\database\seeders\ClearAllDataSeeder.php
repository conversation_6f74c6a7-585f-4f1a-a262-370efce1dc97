<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ClearAllDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Désactiver les contraintes de clés étrangères
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        
        // Vider toutes les tables
        DB::table('products')->truncate();
        DB::table('categories')->truncate();
        DB::table('orders')->truncate();
        DB::table('order_items')->truncate();
        DB::table('promotions')->truncate();
        
        // Supprimer tous les utilisateurs sauf l'admin
        DB::table('users')->where('email', '!=', '<EMAIL>')->delete();
        
        // Réactiver les contraintes de clés étrangères
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');
        
        $this->command->info('✅ Toutes les données ont été supprimées');
        $this->command->info('🔧 L\'admin doit maintenant gérer l\'application');
        $this->command->info('📝 Seul l\'utilisateur <EMAIL> est conservé');
        $this->command->info('🚀 Application prête pour la gestion par l\'admin');
    }
}
