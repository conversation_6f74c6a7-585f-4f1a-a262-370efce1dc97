<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('grilling_options', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('description')->nullable();
            $table->decimal('additional_cost', 10, 2)->default(0);
            $table->integer('cooking_time')->comment('temps de cuisson en minutes');
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('product_grilling_options', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_id')->constrained()->onDelete('cascade');
            $table->foreignId('grilling_option_id')->constrained()->onDelete('cascade');
            $table->timestamps();

            $table->unique(['product_id', 'grilling_option_id']);
        });
    }

    public function down()
    {
        Schema::dropIfExists('product_grilling_options');
        Schema::dropIfExists('grilling_options');
    }
};







