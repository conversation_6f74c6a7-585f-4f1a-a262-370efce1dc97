{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Projet-Synthese\\\\frontend\\\\src\\\\pages\\\\Admin\\\\Categories.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Categories = () => {\n  _s();\n  const [categories, setCategories] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [currentCategory, setCurrentCategory] = useState(null);\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    icon: '',\n    color: '#4CAF50',\n    is_active: true,\n    image: null\n  });\n  useEffect(() => {\n    fetchCategories();\n  }, []);\n  const fetchCategories = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get('/api/admin/categories');\n      setCategories(response.data);\n    } catch (err) {\n      console.log('API non disponible, utilisation des données de test');\n\n      // Données de test pour les catégories\n      const testCategories = [{\n        id: 1,\n        name: 'Produits Grillés',\n        description: 'Viandes, poissons et légumes à griller',\n        icon: 'fa fa-fire',\n        color: '#FF6B35',\n        is_active: true,\n        products_count: 15,\n        image_url: 'https://images.unsplash.com/photo-1529692236671-f1f6cf9683ba?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80'\n      }, {\n        id: 2,\n        name: 'Salades',\n        description: 'Salades fraîches et variées',\n        icon: 'fa fa-leaf',\n        color: '#4CAF50',\n        is_active: true,\n        products_count: 8,\n        image_url: 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80'\n      }, {\n        id: 3,\n        name: 'Fromages',\n        description: 'Fromages artisanaux et de qualité',\n        icon: 'fa fa-cheese',\n        color: '#FFC107',\n        is_active: true,\n        products_count: 12,\n        image_url: 'https://images.unsplash.com/photo-1486297678162-eb2a19b0a32d?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80'\n      }, {\n        id: 4,\n        name: 'Boissons',\n        description: 'Boissons fraîches et chaudes',\n        icon: 'fa fa-glass',\n        color: '#2196F3',\n        is_active: true,\n        products_count: 20,\n        image_url: 'https://images.unsplash.com/photo-1613478223719-2ab802602423?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80'\n      }, {\n        id: 5,\n        name: 'Desserts',\n        description: 'Desserts maison et gourmandises',\n        icon: 'fa fa-birthday-cake',\n        color: '#E91E63',\n        is_active: true,\n        products_count: 6,\n        image_url: 'https://images.unsplash.com/photo-1571877227200-a0d98ea607e9?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80'\n      }, {\n        id: 6,\n        name: 'Produits Non Grillés',\n        description: 'Plats préparés et spécialités',\n        icon: 'fa fa-cutlery',\n        color: '#9C27B0',\n        is_active: true,\n        products_count: 10,\n        image_url: 'https://images.unsplash.com/photo-1546833999-b9f581a1996d?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80'\n      }];\n      setCategories(testCategories);\n      setError(null);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value,\n      type,\n      checked,\n      files\n    } = e.target;\n    if (type === 'file') {\n      setFormData({\n        ...formData,\n        [name]: files[0]\n      });\n    } else if (type === 'checkbox') {\n      setFormData({\n        ...formData,\n        [name]: checked\n      });\n    } else {\n      setFormData({\n        ...formData,\n        [name]: value\n      });\n    }\n  };\n  const openModal = (category = null) => {\n    if (category) {\n      setCurrentCategory(category);\n      setFormData({\n        name: category.name,\n        description: category.description || '',\n        icon: category.icon || '',\n        color: category.color || '#4CAF50',\n        is_active: category.is_active,\n        image: null\n      });\n    } else {\n      setCurrentCategory(null);\n      setFormData({\n        name: '',\n        description: '',\n        icon: '',\n        color: '#4CAF50',\n        is_active: true,\n        image: null\n      });\n    }\n    setIsModalOpen(true);\n  };\n  const closeModal = () => {\n    setIsModalOpen(false);\n    setCurrentCategory(null);\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    const formDataToSend = new FormData();\n    Object.keys(formData).forEach(key => {\n      if (formData[key] !== null) {\n        formDataToSend.append(key, formData[key]);\n      }\n    });\n    try {\n      if (currentCategory) {\n        // Mise à jour d'une catégorie existante\n        await axios.post(`/api/admin/categories/${currentCategory.id}`, formDataToSend, {\n          headers: {\n            'Content-Type': 'multipart/form-data'\n          }\n        });\n      } else {\n        // Création d'une nouvelle catégorie\n        await axios.post('/api/admin/categories', formDataToSend, {\n          headers: {\n            'Content-Type': 'multipart/form-data'\n          }\n        });\n      }\n      closeModal();\n      fetchCategories();\n    } catch (err) {\n      console.error('Erreur lors de l\\'enregistrement de la catégorie', err);\n      alert('Une erreur est survenue lors de l\\'enregistrement de la catégorie');\n    }\n  };\n  const handleDelete = async id => {\n    if (window.confirm('Êtes-vous sûr de vouloir supprimer cette catégorie ? Tous les produits associés seront également supprimés.')) {\n      try {\n        await axios.delete(`/api/admin/categories/${id}`);\n        fetchCategories();\n      } catch (err) {\n        console.error('Erreur lors de la suppression de la catégorie', err);\n        alert('Une erreur est survenue lors de la suppression de la catégorie');\n      }\n    }\n  };\n  if (loading && categories.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-center items-center h-64\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-500\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 202,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container mx-auto px-4 py-8\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-2xl font-semibold\",\n        children: \"Gestion des cat\\xE9gories\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => openModal(),\n        className: \"bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition\",\n        children: \"Ajouter une cat\\xE9gorie\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 210,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4\",\n      role: \"alert\",\n      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n        className: \"font-bold\",\n        children: \"Erreur!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 222,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"block sm:inline\",\n        children: [\" \", error]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 223,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 221,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n      children: categories.map(category => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-40 bg-gray-200 relative\",\n          children: [category.image_url ? /*#__PURE__*/_jsxDEV(\"img\", {\n            src: category.image_url,\n            alt: category.name,\n            className: \"h-full w-full object-cover\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"h-full w-full flex items-center justify-center text-gray-400\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              className: \"h-12 w-12\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              stroke: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute top-0 right-0 m-2 px-2 py-1 rounded text-xs font-semibold\",\n            style: {\n              backgroundColor: category.color || '#4CAF50',\n              color: '#fff'\n            },\n            children: category.is_active ? 'Active' : 'Inactive'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center mb-2\",\n            children: [category.icon && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mr-2 text-gray-600\",\n              children: /*#__PURE__*/_jsxDEV(\"i\", {\n                className: category.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold\",\n              children: category.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 15\n          }, this), category.description && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 text-sm mb-4\",\n            children: category.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-gray-500\",\n              children: [category.products_count || 0, \" produits\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => openModal(category),\n                className: \"text-indigo-600 hover:text-indigo-900 mr-3\",\n                children: \"Modifier\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleDelete(category.id),\n                className: \"text-red-600 hover:text-red-900\",\n                children: \"Supprimer\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 13\n        }, this)]\n      }, category.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 227,\n      columnNumber: 7\n    }, this), isModalOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-lg w-full max-w-md mx-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-6 py-4 border-b\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold\",\n            children: currentCategory ? 'Modifier la catégorie' : 'Ajouter une catégorie'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"name\",\n                className: \"block text-gray-700 mb-2\",\n                children: \"Nom\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"name\",\n                name: \"name\",\n                value: formData.name,\n                onChange: handleInputChange,\n                className: \"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"description\",\n                className: \"block text-gray-700 mb-2\",\n                children: \"Description\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                id: \"description\",\n                name: \"description\",\n                value: formData.description,\n                onChange: handleInputChange,\n                className: \"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\",\n                rows: \"3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 313,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"icon\",\n                className: \"block text-gray-700 mb-2\",\n                children: \"Ic\\xF4ne (classe CSS)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 324,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"icon\",\n                name: \"icon\",\n                value: formData.icon,\n                onChange: handleInputChange,\n                className: \"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\",\n                placeholder: \"fa fa-cutlery\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 325,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-500 mt-1\",\n                children: \"Exemple: fa fa-cutlery, material-icons, etc.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 334,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"color\",\n                className: \"block text-gray-700 mb-2\",\n                children: \"Couleur\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 340,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"color\",\n                  id: \"color\",\n                  name: \"color\",\n                  value: formData.color,\n                  onChange: handleInputChange,\n                  className: \"h-10 w-10 border-0 p-0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 342,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: formData.color,\n                  onChange: handleInputChange,\n                  name: \"color\",\n                  className: \"ml-2 flex-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 350,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 341,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 339,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"image\",\n                className: \"block text-gray-700 mb-2\",\n                children: \"Image\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 361,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"file\",\n                id: \"image\",\n                name: \"image\",\n                onChange: handleInputChange,\n                className: \"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\",\n                accept: \"image/*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 362,\n                columnNumber: 19\n              }, this), currentCategory && currentCategory.image_url && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-500\",\n                  children: \"Image actuelle:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 372,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"h-20 w-20 mt-1 rounded overflow-hidden bg-gray-100\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: currentCategory.image_url,\n                    alt: currentCategory.name,\n                    className: \"h-full w-full object-cover\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 374,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 373,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 371,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 360,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  id: \"is_active\",\n                  name: \"is_active\",\n                  checked: formData.is_active,\n                  onChange: handleInputChange,\n                  className: \"h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 386,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"is_active\",\n                  className: \"ml-2 block text-gray-700\",\n                  children: \"Cat\\xE9gorie active\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 394,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 385,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"px-6 py-4 bg-gray-50 border-t flex justify-end\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: closeModal,\n              className: \"px-4 py-2 border rounded-lg text-gray-700 mr-2 hover:bg-gray-100\",\n              children: \"Annuler\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700\",\n              children: currentCategory ? 'Mettre à jour' : 'Ajouter'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 409,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 401,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 290,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 289,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 209,\n    columnNumber: 5\n  }, this);\n};\n_s(Categories, \"84UulU7JfAUypyPu8NRokRGsExk=\");\n_c = Categories;\nexport default Categories;\nvar _c;\n$RefreshReg$(_c, \"Categories\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "jsxDEV", "_jsxDEV", "Categories", "_s", "categories", "setCategories", "loading", "setLoading", "error", "setError", "currentCategory", "setCurrentCategory", "isModalOpen", "setIsModalOpen", "formData", "setFormData", "name", "description", "icon", "color", "is_active", "image", "fetchCategories", "response", "get", "data", "err", "console", "log", "testCategories", "id", "products_count", "image_url", "handleInputChange", "e", "value", "type", "checked", "files", "target", "openModal", "category", "closeModal", "handleSubmit", "preventDefault", "formDataToSend", "FormData", "Object", "keys", "for<PERSON>ach", "key", "append", "post", "headers", "alert", "handleDelete", "window", "confirm", "delete", "length", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "role", "map", "src", "alt", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "style", "backgroundColor", "onSubmit", "htmlFor", "onChange", "required", "rows", "placeholder", "accept", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet-Synthese/frontend/src/pages/Admin/Categories.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\n\nconst Categories = () => {\n  const [categories, setCategories] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [currentCategory, setCurrentCategory] = useState(null);\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    icon: '',\n    color: '#4CAF50',\n    is_active: true,\n    image: null\n  });\n\n  useEffect(() => {\n    fetchCategories();\n  }, []);\n\n  const fetchCategories = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get('/api/admin/categories');\n      setCategories(response.data);\n    } catch (err) {\n      console.log('API non disponible, utilisation des données de test');\n\n      // Données de test pour les catégories\n      const testCategories = [\n        {\n          id: 1,\n          name: 'Produits Grillés',\n          description: 'Viandes, poissons et légumes à griller',\n          icon: 'fa fa-fire',\n          color: '#FF6B35',\n          is_active: true,\n          products_count: 15,\n          image_url: 'https://images.unsplash.com/photo-1529692236671-f1f6cf9683ba?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80'\n        },\n        {\n          id: 2,\n          name: 'Salades',\n          description: 'Salades fraîches et variées',\n          icon: 'fa fa-leaf',\n          color: '#4CAF50',\n          is_active: true,\n          products_count: 8,\n          image_url: 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80'\n        },\n        {\n          id: 3,\n          name: 'Fromages',\n          description: 'Fromages artisanaux et de qualité',\n          icon: 'fa fa-cheese',\n          color: '#FFC107',\n          is_active: true,\n          products_count: 12,\n          image_url: 'https://images.unsplash.com/photo-1486297678162-eb2a19b0a32d?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80'\n        },\n        {\n          id: 4,\n          name: 'Boissons',\n          description: 'Boissons fraîches et chaudes',\n          icon: 'fa fa-glass',\n          color: '#2196F3',\n          is_active: true,\n          products_count: 20,\n          image_url: 'https://images.unsplash.com/photo-1613478223719-2ab802602423?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80'\n        },\n        {\n          id: 5,\n          name: 'Desserts',\n          description: 'Desserts maison et gourmandises',\n          icon: 'fa fa-birthday-cake',\n          color: '#E91E63',\n          is_active: true,\n          products_count: 6,\n          image_url: 'https://images.unsplash.com/photo-1571877227200-a0d98ea607e9?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80'\n        },\n        {\n          id: 6,\n          name: 'Produits Non Grillés',\n          description: 'Plats préparés et spécialités',\n          icon: 'fa fa-cutlery',\n          color: '#9C27B0',\n          is_active: true,\n          products_count: 10,\n          image_url: 'https://images.unsplash.com/photo-1546833999-b9f581a1996d?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80'\n        }\n      ];\n\n      setCategories(testCategories);\n      setError(null);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleInputChange = (e) => {\n    const { name, value, type, checked, files } = e.target;\n    \n    if (type === 'file') {\n      setFormData({\n        ...formData,\n        [name]: files[0]\n      });\n    } else if (type === 'checkbox') {\n      setFormData({\n        ...formData,\n        [name]: checked\n      });\n    } else {\n      setFormData({\n        ...formData,\n        [name]: value\n      });\n    }\n  };\n\n  const openModal = (category = null) => {\n    if (category) {\n      setCurrentCategory(category);\n      setFormData({\n        name: category.name,\n        description: category.description || '',\n        icon: category.icon || '',\n        color: category.color || '#4CAF50',\n        is_active: category.is_active,\n        image: null\n      });\n    } else {\n      setCurrentCategory(null);\n      setFormData({\n        name: '',\n        description: '',\n        icon: '',\n        color: '#4CAF50',\n        is_active: true,\n        image: null\n      });\n    }\n    setIsModalOpen(true);\n  };\n\n  const closeModal = () => {\n    setIsModalOpen(false);\n    setCurrentCategory(null);\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    const formDataToSend = new FormData();\n    Object.keys(formData).forEach(key => {\n      if (formData[key] !== null) {\n        formDataToSend.append(key, formData[key]);\n      }\n    });\n    \n    try {\n      if (currentCategory) {\n        // Mise à jour d'une catégorie existante\n        await axios.post(`/api/admin/categories/${currentCategory.id}`, formDataToSend, {\n          headers: {\n            'Content-Type': 'multipart/form-data'\n          }\n        });\n      } else {\n        // Création d'une nouvelle catégorie\n        await axios.post('/api/admin/categories', formDataToSend, {\n          headers: {\n            'Content-Type': 'multipart/form-data'\n          }\n        });\n      }\n      \n      closeModal();\n      fetchCategories();\n    } catch (err) {\n      console.error('Erreur lors de l\\'enregistrement de la catégorie', err);\n      alert('Une erreur est survenue lors de l\\'enregistrement de la catégorie');\n    }\n  };\n\n  const handleDelete = async (id) => {\n    if (window.confirm('Êtes-vous sûr de vouloir supprimer cette catégorie ? Tous les produits associés seront également supprimés.')) {\n      try {\n        await axios.delete(`/api/admin/categories/${id}`);\n        fetchCategories();\n      } catch (err) {\n        console.error('Erreur lors de la suppression de la catégorie', err);\n        alert('Une erreur est survenue lors de la suppression de la catégorie');\n      }\n    }\n  };\n\n  if (loading && categories.length === 0) {\n    return (\n      <div className=\"flex justify-center items-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-500\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"container mx-auto px-4 py-8\">\n      <div className=\"flex justify-between items-center mb-6\">\n        <h1 className=\"text-2xl font-semibold\">Gestion des catégories</h1>\n        <button\n          onClick={() => openModal()}\n          className=\"bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition\"\n        >\n          Ajouter une catégorie\n        </button>\n      </div>\n\n      {error && (\n        <div className=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4\" role=\"alert\">\n          <strong className=\"font-bold\">Erreur!</strong>\n          <span className=\"block sm:inline\"> {error}</span>\n        </div>\n      )}\n\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n        {categories.map((category) => (\n          <div key={category.id} className=\"bg-white rounded-lg shadow overflow-hidden\">\n            <div className=\"h-40 bg-gray-200 relative\">\n              {category.image_url ? (\n                <img\n                  src={category.image_url}\n                  alt={category.name}\n                  className=\"h-full w-full object-cover\"\n                />\n              ) : (\n                <div className=\"h-full w-full flex items-center justify-center text-gray-400\">\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-12 w-12\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n                  </svg>\n                </div>\n              )}\n              <div \n                className=\"absolute top-0 right-0 m-2 px-2 py-1 rounded text-xs font-semibold\"\n                style={{ backgroundColor: category.color || '#4CAF50', color: '#fff' }}\n              >\n                {category.is_active ? 'Active' : 'Inactive'}\n              </div>\n            </div>\n            <div className=\"p-4\">\n              <div className=\"flex items-center mb-2\">\n                {category.icon && (\n                  <span className=\"mr-2 text-gray-600\">\n                    <i className={category.icon}></i>\n                  </span>\n                )}\n                <h3 className=\"text-lg font-semibold\">{category.name}</h3>\n              </div>\n              {category.description && (\n                <p className=\"text-gray-600 text-sm mb-4\">{category.description}</p>\n              )}\n              <div className=\"flex justify-between items-center\">\n                <span className=\"text-sm text-gray-500\">\n                  {category.products_count || 0} produits\n                </span>\n                <div>\n                  <button\n                    onClick={() => openModal(category)}\n                    className=\"text-indigo-600 hover:text-indigo-900 mr-3\"\n                  >\n                    Modifier\n                  </button>\n                  <button\n                    onClick={() => handleDelete(category.id)}\n                    className=\"text-red-600 hover:text-red-900\"\n                  >\n                    Supprimer\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* Modal pour ajouter/modifier une catégorie */}\n      {isModalOpen && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n          <div className=\"bg-white rounded-lg shadow-lg w-full max-w-md mx-4\">\n            <div className=\"px-6 py-4 border-b\">\n              <h3 className=\"text-lg font-semibold\">\n                {currentCategory ? 'Modifier la catégorie' : 'Ajouter une catégorie'}\n              </h3>\n            </div>\n            <form onSubmit={handleSubmit}>\n              <div className=\"p-6\">\n                <div className=\"mb-4\">\n                  <label htmlFor=\"name\" className=\"block text-gray-700 mb-2\">Nom</label>\n                  <input\n                    type=\"text\"\n                    id=\"name\"\n                    name=\"name\"\n                    value={formData.name}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n                    required\n                  />\n                </div>\n                \n                <div className=\"mb-4\">\n                  <label htmlFor=\"description\" className=\"block text-gray-700 mb-2\">Description</label>\n                  <textarea\n                    id=\"description\"\n                    name=\"description\"\n                    value={formData.description}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n                    rows=\"3\"\n                  ></textarea>\n                </div>\n                \n                <div className=\"mb-4\">\n                  <label htmlFor=\"icon\" className=\"block text-gray-700 mb-2\">Icône (classe CSS)</label>\n                  <input\n                    type=\"text\"\n                    id=\"icon\"\n                    name=\"icon\"\n                    value={formData.icon}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n                    placeholder=\"fa fa-cutlery\"\n                  />\n                  <p className=\"text-xs text-gray-500 mt-1\">\n                    Exemple: fa fa-cutlery, material-icons, etc.\n                  </p>\n                </div>\n                \n                <div className=\"mb-4\">\n                  <label htmlFor=\"color\" className=\"block text-gray-700 mb-2\">Couleur</label>\n                  <div className=\"flex items-center\">\n                    <input\n                      type=\"color\"\n                      id=\"color\"\n                      name=\"color\"\n                      value={formData.color}\n                      onChange={handleInputChange}\n                      className=\"h-10 w-10 border-0 p-0\"\n                    />\n                    <input\n                      type=\"text\"\n                      value={formData.color}\n                      onChange={handleInputChange}\n                      name=\"color\"\n                      className=\"ml-2 flex-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n                    />\n                  </div>\n                </div>\n                \n                <div className=\"mb-4\">\n                  <label htmlFor=\"image\" className=\"block text-gray-700 mb-2\">Image</label>\n                  <input\n                    type=\"file\"\n                    id=\"image\"\n                    name=\"image\"\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n                    accept=\"image/*\"\n                  />\n                  {currentCategory && currentCategory.image_url && (\n                    <div className=\"mt-2\">\n                      <p className=\"text-sm text-gray-500\">Image actuelle:</p>\n                      <div className=\"h-20 w-20 mt-1 rounded overflow-hidden bg-gray-100\">\n                        <img\n                          src={currentCategory.image_url}\n                          alt={currentCategory.name}\n                          className=\"h-full w-full object-cover\"\n                        />\n                      </div>\n                    </div>\n                  )}\n                </div>\n                \n                <div className=\"mb-4\">\n                  <div className=\"flex items-center\">\n                    <input\n                      type=\"checkbox\"\n                      id=\"is_active\"\n                      name=\"is_active\"\n                      checked={formData.is_active}\n                      onChange={handleInputChange}\n                      className=\"h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded\"\n                    />\n                    <label htmlFor=\"is_active\" className=\"ml-2 block text-gray-700\">\n                      Catégorie active\n                    </label>\n                  </div>\n                </div>\n              </div>\n              \n              <div className=\"px-6 py-4 bg-gray-50 border-t flex justify-end\">\n                <button\n                  type=\"button\"\n                  onClick={closeModal}\n                  className=\"px-4 py-2 border rounded-lg text-gray-700 mr-2 hover:bg-gray-100\"\n                >\n                  Annuler\n                </button>\n                <button\n                  type=\"submit\"\n                  className=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700\"\n                >\n                  {currentCategory ? 'Mettre à jour' : 'Ajouter'}\n                </button>\n              </div>\n            </form>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default Categories;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACS,OAAO,EAAEC,UAAU,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACW,KAAK,EAAEC,QAAQ,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACa,eAAe,EAAEC,kBAAkB,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACe,WAAW,EAAEC,cAAc,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACiB,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CAAC;IACvCmB,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,SAAS;IAChBC,SAAS,EAAE,IAAI;IACfC,KAAK,EAAE;EACT,CAAC,CAAC;EAEFvB,SAAS,CAAC,MAAM;IACdwB,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACFf,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMgB,QAAQ,GAAG,MAAMxB,KAAK,CAACyB,GAAG,CAAC,uBAAuB,CAAC;MACzDnB,aAAa,CAACkB,QAAQ,CAACE,IAAI,CAAC;IAC9B,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZC,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;;MAElE;MACA,MAAMC,cAAc,GAAG,CACrB;QACEC,EAAE,EAAE,CAAC;QACLd,IAAI,EAAE,kBAAkB;QACxBC,WAAW,EAAE,wCAAwC;QACrDC,IAAI,EAAE,YAAY;QAClBC,KAAK,EAAE,SAAS;QAChBC,SAAS,EAAE,IAAI;QACfW,cAAc,EAAE,EAAE;QAClBC,SAAS,EAAE;MACb,CAAC,EACD;QACEF,EAAE,EAAE,CAAC;QACLd,IAAI,EAAE,SAAS;QACfC,WAAW,EAAE,6BAA6B;QAC1CC,IAAI,EAAE,YAAY;QAClBC,KAAK,EAAE,SAAS;QAChBC,SAAS,EAAE,IAAI;QACfW,cAAc,EAAE,CAAC;QACjBC,SAAS,EAAE;MACb,CAAC,EACD;QACEF,EAAE,EAAE,CAAC;QACLd,IAAI,EAAE,UAAU;QAChBC,WAAW,EAAE,mCAAmC;QAChDC,IAAI,EAAE,cAAc;QACpBC,KAAK,EAAE,SAAS;QAChBC,SAAS,EAAE,IAAI;QACfW,cAAc,EAAE,EAAE;QAClBC,SAAS,EAAE;MACb,CAAC,EACD;QACEF,EAAE,EAAE,CAAC;QACLd,IAAI,EAAE,UAAU;QAChBC,WAAW,EAAE,8BAA8B;QAC3CC,IAAI,EAAE,aAAa;QACnBC,KAAK,EAAE,SAAS;QAChBC,SAAS,EAAE,IAAI;QACfW,cAAc,EAAE,EAAE;QAClBC,SAAS,EAAE;MACb,CAAC,EACD;QACEF,EAAE,EAAE,CAAC;QACLd,IAAI,EAAE,UAAU;QAChBC,WAAW,EAAE,iCAAiC;QAC9CC,IAAI,EAAE,qBAAqB;QAC3BC,KAAK,EAAE,SAAS;QAChBC,SAAS,EAAE,IAAI;QACfW,cAAc,EAAE,CAAC;QACjBC,SAAS,EAAE;MACb,CAAC,EACD;QACEF,EAAE,EAAE,CAAC;QACLd,IAAI,EAAE,sBAAsB;QAC5BC,WAAW,EAAE,+BAA+B;QAC5CC,IAAI,EAAE,eAAe;QACrBC,KAAK,EAAE,SAAS;QAChBC,SAAS,EAAE,IAAI;QACfW,cAAc,EAAE,EAAE;QAClBC,SAAS,EAAE;MACb,CAAC,CACF;MAED3B,aAAa,CAACwB,cAAc,CAAC;MAC7BpB,QAAQ,CAAC,IAAI,CAAC;IAChB,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM0B,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAElB,IAAI;MAAEmB,KAAK;MAAEC,IAAI;MAAEC,OAAO;MAAEC;IAAM,CAAC,GAAGJ,CAAC,CAACK,MAAM;IAEtD,IAAIH,IAAI,KAAK,MAAM,EAAE;MACnBrB,WAAW,CAAC;QACV,GAAGD,QAAQ;QACX,CAACE,IAAI,GAAGsB,KAAK,CAAC,CAAC;MACjB,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIF,IAAI,KAAK,UAAU,EAAE;MAC9BrB,WAAW,CAAC;QACV,GAAGD,QAAQ;QACX,CAACE,IAAI,GAAGqB;MACV,CAAC,CAAC;IACJ,CAAC,MAAM;MACLtB,WAAW,CAAC;QACV,GAAGD,QAAQ;QACX,CAACE,IAAI,GAAGmB;MACV,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMK,SAAS,GAAGA,CAACC,QAAQ,GAAG,IAAI,KAAK;IACrC,IAAIA,QAAQ,EAAE;MACZ9B,kBAAkB,CAAC8B,QAAQ,CAAC;MAC5B1B,WAAW,CAAC;QACVC,IAAI,EAAEyB,QAAQ,CAACzB,IAAI;QACnBC,WAAW,EAAEwB,QAAQ,CAACxB,WAAW,IAAI,EAAE;QACvCC,IAAI,EAAEuB,QAAQ,CAACvB,IAAI,IAAI,EAAE;QACzBC,KAAK,EAAEsB,QAAQ,CAACtB,KAAK,IAAI,SAAS;QAClCC,SAAS,EAAEqB,QAAQ,CAACrB,SAAS;QAC7BC,KAAK,EAAE;MACT,CAAC,CAAC;IACJ,CAAC,MAAM;MACLV,kBAAkB,CAAC,IAAI,CAAC;MACxBI,WAAW,CAAC;QACVC,IAAI,EAAE,EAAE;QACRC,WAAW,EAAE,EAAE;QACfC,IAAI,EAAE,EAAE;QACRC,KAAK,EAAE,SAAS;QAChBC,SAAS,EAAE,IAAI;QACfC,KAAK,EAAE;MACT,CAAC,CAAC;IACJ;IACAR,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EAED,MAAM6B,UAAU,GAAGA,CAAA,KAAM;IACvB7B,cAAc,CAAC,KAAK,CAAC;IACrBF,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMgC,YAAY,GAAG,MAAOT,CAAC,IAAK;IAChCA,CAAC,CAACU,cAAc,CAAC,CAAC;IAElB,MAAMC,cAAc,GAAG,IAAIC,QAAQ,CAAC,CAAC;IACrCC,MAAM,CAACC,IAAI,CAAClC,QAAQ,CAAC,CAACmC,OAAO,CAACC,GAAG,IAAI;MACnC,IAAIpC,QAAQ,CAACoC,GAAG,CAAC,KAAK,IAAI,EAAE;QAC1BL,cAAc,CAACM,MAAM,CAACD,GAAG,EAAEpC,QAAQ,CAACoC,GAAG,CAAC,CAAC;MAC3C;IACF,CAAC,CAAC;IAEF,IAAI;MACF,IAAIxC,eAAe,EAAE;QACnB;QACA,MAAMX,KAAK,CAACqD,IAAI,CAAC,yBAAyB1C,eAAe,CAACoB,EAAE,EAAE,EAAEe,cAAc,EAAE;UAC9EQ,OAAO,EAAE;YACP,cAAc,EAAE;UAClB;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA,MAAMtD,KAAK,CAACqD,IAAI,CAAC,uBAAuB,EAAEP,cAAc,EAAE;UACxDQ,OAAO,EAAE;YACP,cAAc,EAAE;UAClB;QACF,CAAC,CAAC;MACJ;MAEAX,UAAU,CAAC,CAAC;MACZpB,eAAe,CAAC,CAAC;IACnB,CAAC,CAAC,OAAOI,GAAG,EAAE;MACZC,OAAO,CAACnB,KAAK,CAAC,kDAAkD,EAAEkB,GAAG,CAAC;MACtE4B,KAAK,CAAC,mEAAmE,CAAC;IAC5E;EACF,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOzB,EAAE,IAAK;IACjC,IAAI0B,MAAM,CAACC,OAAO,CAAC,6GAA6G,CAAC,EAAE;MACjI,IAAI;QACF,MAAM1D,KAAK,CAAC2D,MAAM,CAAC,yBAAyB5B,EAAE,EAAE,CAAC;QACjDR,eAAe,CAAC,CAAC;MACnB,CAAC,CAAC,OAAOI,GAAG,EAAE;QACZC,OAAO,CAACnB,KAAK,CAAC,+CAA+C,EAAEkB,GAAG,CAAC;QACnE4B,KAAK,CAAC,gEAAgE,CAAC;MACzE;IACF;EACF,CAAC;EAED,IAAIhD,OAAO,IAAIF,UAAU,CAACuD,MAAM,KAAK,CAAC,EAAE;IACtC,oBACE1D,OAAA;MAAK2D,SAAS,EAAC,uCAAuC;MAAAC,QAAA,eACpD5D,OAAA;QAAK2D,SAAS,EAAC;MAA4E;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/F,CAAC;EAEV;EAEA,oBACEhE,OAAA;IAAK2D,SAAS,EAAC,6BAA6B;IAAAC,QAAA,gBAC1C5D,OAAA;MAAK2D,SAAS,EAAC,wCAAwC;MAAAC,QAAA,gBACrD5D,OAAA;QAAI2D,SAAS,EAAC,wBAAwB;QAAAC,QAAA,EAAC;MAAsB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAClEhE,OAAA;QACEiE,OAAO,EAAEA,CAAA,KAAM1B,SAAS,CAAC,CAAE;QAC3BoB,SAAS,EAAC,yEAAyE;QAAAC,QAAA,EACpF;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAELzD,KAAK,iBACJP,OAAA;MAAK2D,SAAS,EAAC,+EAA+E;MAACO,IAAI,EAAC,OAAO;MAAAN,QAAA,gBACzG5D,OAAA;QAAQ2D,SAAS,EAAC,WAAW;QAAAC,QAAA,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC9ChE,OAAA;QAAM2D,SAAS,EAAC,iBAAiB;QAAAC,QAAA,GAAC,GAAC,EAACrD,KAAK;MAAA;QAAAsD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9C,CACN,eAEDhE,OAAA;MAAK2D,SAAS,EAAC,sDAAsD;MAAAC,QAAA,EAClEzD,UAAU,CAACgE,GAAG,CAAE3B,QAAQ,iBACvBxC,OAAA;QAAuB2D,SAAS,EAAC,4CAA4C;QAAAC,QAAA,gBAC3E5D,OAAA;UAAK2D,SAAS,EAAC,2BAA2B;UAAAC,QAAA,GACvCpB,QAAQ,CAACT,SAAS,gBACjB/B,OAAA;YACEoE,GAAG,EAAE5B,QAAQ,CAACT,SAAU;YACxBsC,GAAG,EAAE7B,QAAQ,CAACzB,IAAK;YACnB4C,SAAS,EAAC;UAA4B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,gBAEFhE,OAAA;YAAK2D,SAAS,EAAC,8DAA8D;YAAAC,QAAA,eAC3E5D,OAAA;cAAKsE,KAAK,EAAC,4BAA4B;cAACX,SAAS,EAAC,WAAW;cAACY,IAAI,EAAC,MAAM;cAACC,OAAO,EAAC,WAAW;cAACC,MAAM,EAAC,cAAc;cAAAb,QAAA,eACjH5D,OAAA;gBAAM0E,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAA2J;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eACDhE,OAAA;YACE2D,SAAS,EAAC,oEAAoE;YAC9EmB,KAAK,EAAE;cAAEC,eAAe,EAAEvC,QAAQ,CAACtB,KAAK,IAAI,SAAS;cAAEA,KAAK,EAAE;YAAO,CAAE;YAAA0C,QAAA,EAEtEpB,QAAQ,CAACrB,SAAS,GAAG,QAAQ,GAAG;UAAU;YAAA0C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNhE,OAAA;UAAK2D,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClB5D,OAAA;YAAK2D,SAAS,EAAC,wBAAwB;YAAAC,QAAA,GACpCpB,QAAQ,CAACvB,IAAI,iBACZjB,OAAA;cAAM2D,SAAS,EAAC,oBAAoB;cAAAC,QAAA,eAClC5D,OAAA;gBAAG2D,SAAS,EAAEnB,QAAQ,CAACvB;cAAK;gBAAA4C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CACP,eACDhE,OAAA;cAAI2D,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAEpB,QAAQ,CAACzB;YAAI;cAAA8C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CAAC,EACLxB,QAAQ,CAACxB,WAAW,iBACnBhB,OAAA;YAAG2D,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAEpB,QAAQ,CAACxB;UAAW;YAAA6C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CACpE,eACDhE,OAAA;YAAK2D,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChD5D,OAAA;cAAM2D,SAAS,EAAC,uBAAuB;cAAAC,QAAA,GACpCpB,QAAQ,CAACV,cAAc,IAAI,CAAC,EAAC,WAChC;YAAA;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPhE,OAAA;cAAA4D,QAAA,gBACE5D,OAAA;gBACEiE,OAAO,EAAEA,CAAA,KAAM1B,SAAS,CAACC,QAAQ,CAAE;gBACnCmB,SAAS,EAAC,4CAA4C;gBAAAC,QAAA,EACvD;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACThE,OAAA;gBACEiE,OAAO,EAAEA,CAAA,KAAMX,YAAY,CAACd,QAAQ,CAACX,EAAE,CAAE;gBACzC8B,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,EAC5C;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA,GArDExB,QAAQ,CAACX,EAAE;QAAAgC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAsDhB,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAGLrD,WAAW,iBACVX,OAAA;MAAK2D,SAAS,EAAC,4EAA4E;MAAAC,QAAA,eACzF5D,OAAA;QAAK2D,SAAS,EAAC,oDAAoD;QAAAC,QAAA,gBACjE5D,OAAA;UAAK2D,SAAS,EAAC,oBAAoB;UAAAC,QAAA,eACjC5D,OAAA;YAAI2D,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAClCnD,eAAe,GAAG,uBAAuB,GAAG;UAAuB;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACNhE,OAAA;UAAMgF,QAAQ,EAAEtC,YAAa;UAAAkB,QAAA,gBAC3B5D,OAAA;YAAK2D,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAClB5D,OAAA;cAAK2D,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB5D,OAAA;gBAAOiF,OAAO,EAAC,MAAM;gBAACtB,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtEhE,OAAA;gBACEmC,IAAI,EAAC,MAAM;gBACXN,EAAE,EAAC,MAAM;gBACTd,IAAI,EAAC,MAAM;gBACXmB,KAAK,EAAErB,QAAQ,CAACE,IAAK;gBACrBmE,QAAQ,EAAElD,iBAAkB;gBAC5B2B,SAAS,EAAC,yFAAyF;gBACnGwB,QAAQ;cAAA;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENhE,OAAA;cAAK2D,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB5D,OAAA;gBAAOiF,OAAO,EAAC,aAAa;gBAACtB,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrFhE,OAAA;gBACE6B,EAAE,EAAC,aAAa;gBAChBd,IAAI,EAAC,aAAa;gBAClBmB,KAAK,EAAErB,QAAQ,CAACG,WAAY;gBAC5BkE,QAAQ,EAAElD,iBAAkB;gBAC5B2B,SAAS,EAAC,yFAAyF;gBACnGyB,IAAI,EAAC;cAAG;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eAENhE,OAAA;cAAK2D,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB5D,OAAA;gBAAOiF,OAAO,EAAC,MAAM;gBAACtB,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAC;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrFhE,OAAA;gBACEmC,IAAI,EAAC,MAAM;gBACXN,EAAE,EAAC,MAAM;gBACTd,IAAI,EAAC,MAAM;gBACXmB,KAAK,EAAErB,QAAQ,CAACI,IAAK;gBACrBiE,QAAQ,EAAElD,iBAAkB;gBAC5B2B,SAAS,EAAC,yFAAyF;gBACnG0B,WAAW,EAAC;cAAe;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC,eACFhE,OAAA;gBAAG2D,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAE1C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAENhE,OAAA;cAAK2D,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB5D,OAAA;gBAAOiF,OAAO,EAAC,OAAO;gBAACtB,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3EhE,OAAA;gBAAK2D,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChC5D,OAAA;kBACEmC,IAAI,EAAC,OAAO;kBACZN,EAAE,EAAC,OAAO;kBACVd,IAAI,EAAC,OAAO;kBACZmB,KAAK,EAAErB,QAAQ,CAACK,KAAM;kBACtBgE,QAAQ,EAAElD,iBAAkB;kBAC5B2B,SAAS,EAAC;gBAAwB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC,eACFhE,OAAA;kBACEmC,IAAI,EAAC,MAAM;kBACXD,KAAK,EAAErB,QAAQ,CAACK,KAAM;kBACtBgE,QAAQ,EAAElD,iBAAkB;kBAC5BjB,IAAI,EAAC,OAAO;kBACZ4C,SAAS,EAAC;gBAA8F;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENhE,OAAA;cAAK2D,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB5D,OAAA;gBAAOiF,OAAO,EAAC,OAAO;gBAACtB,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACzEhE,OAAA;gBACEmC,IAAI,EAAC,MAAM;gBACXN,EAAE,EAAC,OAAO;gBACVd,IAAI,EAAC,OAAO;gBACZmE,QAAQ,EAAElD,iBAAkB;gBAC5B2B,SAAS,EAAC,yFAAyF;gBACnG2B,MAAM,EAAC;cAAS;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC,EACDvD,eAAe,IAAIA,eAAe,CAACsB,SAAS,iBAC3C/B,OAAA;gBAAK2D,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnB5D,OAAA;kBAAG2D,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACxDhE,OAAA;kBAAK2D,SAAS,EAAC,oDAAoD;kBAAAC,QAAA,eACjE5D,OAAA;oBACEoE,GAAG,EAAE3D,eAAe,CAACsB,SAAU;oBAC/BsC,GAAG,EAAE5D,eAAe,CAACM,IAAK;oBAC1B4C,SAAS,EAAC;kBAA4B;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAENhE,OAAA;cAAK2D,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnB5D,OAAA;gBAAK2D,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChC5D,OAAA;kBACEmC,IAAI,EAAC,UAAU;kBACfN,EAAE,EAAC,WAAW;kBACdd,IAAI,EAAC,WAAW;kBAChBqB,OAAO,EAAEvB,QAAQ,CAACM,SAAU;kBAC5B+D,QAAQ,EAAElD,iBAAkB;kBAC5B2B,SAAS,EAAC;gBAAqE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChF,CAAC,eACFhE,OAAA;kBAAOiF,OAAO,EAAC,WAAW;kBAACtB,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENhE,OAAA;YAAK2D,SAAS,EAAC,gDAAgD;YAAAC,QAAA,gBAC7D5D,OAAA;cACEmC,IAAI,EAAC,QAAQ;cACb8B,OAAO,EAAExB,UAAW;cACpBkB,SAAS,EAAC,kEAAkE;cAAAC,QAAA,EAC7E;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACThE,OAAA;cACEmC,IAAI,EAAC,QAAQ;cACbwB,SAAS,EAAC,iEAAiE;cAAAC,QAAA,EAE1EnD,eAAe,GAAG,eAAe,GAAG;YAAS;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC9D,EAAA,CAlaID,UAAU;AAAAsF,EAAA,GAAVtF,UAAU;AAoahB,eAAeA,UAAU;AAAC,IAAAsF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}