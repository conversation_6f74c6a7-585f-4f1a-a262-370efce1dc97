{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Projet-Synthese\\\\frontend\\\\src\\\\pages\\\\Admin\\\\ProductsCRUD.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useData } from '../../context/DataContext';\nimport { productAPI } from '../../services/apiService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProductsCRUD = () => {\n  _s();\n  const {\n    state,\n    dispatch,\n    DataActions\n  } = useData();\n  const {\n    products,\n    categories,\n    loading\n  } = state;\n  const [error, setError] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const [currentProduct, setCurrentProduct] = useState(null);\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    price: '',\n    stock: '',\n    category_id: '',\n    is_grillable: false,\n    image: null\n  });\n  const [filteredProducts, setFilteredProducts] = useState([]);\n\n  // Filtrer les produits selon le terme de recherche\n  useEffect(() => {\n    if (searchTerm) {\n      const filtered = products.filter(product => product.name.toLowerCase().includes(searchTerm.toLowerCase()) || product.description.toLowerCase().includes(searchTerm.toLowerCase()));\n      setFilteredProducts(filtered);\n    } else {\n      setFilteredProducts(products);\n    }\n  }, [products, searchTerm]);\n\n  // Fonction pour rafraîchir les données depuis l'API\n  const refreshData = async () => {\n    dispatch({\n      type: DataActions.SET_LOADING,\n      payload: true\n    });\n    try {\n      const result = await productAPI.getAll();\n      if (result.success) {\n        dispatch({\n          type: DataActions.SET_PRODUCTS,\n          payload: result.data\n        });\n        setError(null);\n        console.log('Données actualisées depuis l\\'API');\n      } else {\n        setError('Mode démonstration - Données de test utilisées');\n        console.log('API non disponible, données de test utilisées');\n      }\n    } catch (err) {\n      console.log('Erreur API, utilisation des données de test');\n      setError('Mode démonstration - Données de test utilisées');\n    } finally {\n      dispatch({\n        type: DataActions.SET_LOADING,\n        payload: false\n      });\n    }\n  };\n\n  // Gestion des changements dans le formulaire\n  const handleInputChange = e => {\n    const {\n      name,\n      value,\n      type,\n      checked,\n      files\n    } = e.target;\n    if (type === 'checkbox') {\n      setFormData(prev => ({\n        ...prev,\n        [name]: checked\n      }));\n    } else if (type === 'file') {\n      setFormData(prev => ({\n        ...prev,\n        [name]: files[0]\n      }));\n    } else {\n      setFormData(prev => ({\n        ...prev,\n        [name]: value\n      }));\n    }\n  };\n\n  // Ouvrir le modal pour ajouter/modifier un produit\n  const openModal = (product = null) => {\n    if (product) {\n      setCurrentProduct(product);\n      setFormData({\n        name: product.name,\n        description: product.description,\n        price: product.price,\n        stock: product.stock,\n        category_id: product.category_id,\n        is_grillable: product.is_grillable,\n        image: null\n      });\n    } else {\n      setCurrentProduct(null);\n      setFormData({\n        name: '',\n        description: '',\n        price: '',\n        stock: '',\n        category_id: '',\n        is_grillable: false,\n        image: null\n      });\n    }\n    setIsModalOpen(true);\n  };\n\n  // Fermer le modal\n  const closeModal = () => {\n    setIsModalOpen(false);\n    setCurrentProduct(null);\n    setFormData({\n      name: '',\n      description: '',\n      price: '',\n      stock: '',\n      category_id: '',\n      is_grillable: false,\n      image: null\n    });\n  };\n\n  // Soumettre le formulaire\n  const handleSubmit = async e => {\n    e.preventDefault();\n    dispatch({\n      type: DataActions.SET_LOADING,\n      payload: true\n    });\n    try {\n      const productData = {\n        ...formData,\n        price: parseFloat(formData.price),\n        stock: parseInt(formData.stock),\n        category_id: parseInt(formData.category_id)\n      };\n      if (currentProduct) {\n        // Mise à jour d'un produit existant\n        const result = await productAPI.update(currentProduct.id, productData);\n        if (result.success) {\n          // Mise à jour réussie via API\n          dispatch({\n            type: DataActions.UPDATE_PRODUCT,\n            payload: {\n              ...result.data,\n              id: currentProduct.id\n            }\n          });\n          console.log('Produit mis à jour via API');\n        } else {\n          // Fallback: mise à jour locale\n          dispatch({\n            type: DataActions.UPDATE_PRODUCT,\n            payload: {\n              ...productData,\n              id: currentProduct.id,\n              category: categories.find(cat => cat.id === productData.category_id)\n            }\n          });\n          console.log('Produit mis à jour localement (mode démonstration)');\n        }\n      } else {\n        // Création d'un nouveau produit\n        const result = await productAPI.create(productData);\n        if (result.success) {\n          // Création réussie via API\n          dispatch({\n            type: DataActions.ADD_PRODUCT,\n            payload: result.data\n          });\n          console.log('Produit créé via API');\n        } else {\n          // Fallback: création locale\n          const newProduct = {\n            ...productData,\n            id: Date.now(),\n            category: categories.find(cat => cat.id === productData.category_id),\n            image_url: 'https://images.unsplash.com/photo-1546069901-ba9599a7e63c?w=300',\n            slug: productData.name.toLowerCase().replace(/\\s+/g, '-')\n          };\n          dispatch({\n            type: DataActions.ADD_PRODUCT,\n            payload: newProduct\n          });\n          console.log('Produit créé localement (mode démonstration)');\n        }\n      }\n      closeModal();\n    } catch (err) {\n      console.error('Erreur lors de l\\'enregistrement du produit', err);\n      // En cas d'erreur, fallback vers le mode local\n      const productData = {\n        ...formData,\n        price: parseFloat(formData.price),\n        stock: parseInt(formData.stock),\n        category_id: parseInt(formData.category_id)\n      };\n      if (currentProduct) {\n        dispatch({\n          type: DataActions.UPDATE_PRODUCT,\n          payload: {\n            ...productData,\n            id: currentProduct.id,\n            category: categories.find(cat => cat.id === productData.category_id)\n          }\n        });\n      } else {\n        const newProduct = {\n          ...productData,\n          id: Date.now(),\n          category: categories.find(cat => cat.id === productData.category_id),\n          image_url: 'https://images.unsplash.com/photo-1546069901-ba9599a7e63c?w=300',\n          slug: productData.name.toLowerCase().replace(/\\s+/g, '-')\n        };\n        dispatch({\n          type: DataActions.ADD_PRODUCT,\n          payload: newProduct\n        });\n      }\n      closeModal();\n    } finally {\n      dispatch({\n        type: DataActions.SET_LOADING,\n        payload: false\n      });\n    }\n  };\n\n  // Supprimer un produit\n  const handleDelete = id => {\n    if (window.confirm('Êtes-vous sûr de vouloir supprimer ce produit ?')) {\n      dispatch({\n        type: DataActions.SET_LOADING,\n        payload: true\n      });\n      try {\n        dispatch({\n          type: DataActions.DELETE_PRODUCT,\n          payload: id\n        });\n        console.log('Produit supprimé avec succès');\n      } catch (err) {\n        console.error('Erreur lors de la suppression du produit', err);\n      } finally {\n        dispatch({\n          type: DataActions.SET_LOADING,\n          payload: false\n        });\n      }\n    }\n  };\n\n  // Gestion de la recherche\n  const handleSearch = e => {\n    setSearchTerm(e.target.value);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-gray-900\",\n          children: \"Gestion des Produits\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-500\",\n          children: [filteredProducts.length, \" produit(s) \\u2022 CRUD complet activ\\xE9\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 226,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex space-x-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: refreshData,\n          disabled: loading,\n          className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50\",\n          children: loading ? 'Actualisation...' : 'Actualiser'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => openModal(),\n          className: \"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700\",\n          children: \"Ajouter un produit\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 225,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white p-4 rounded-lg shadow\",\n      children: /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        placeholder: \"Rechercher un produit...\",\n        value: searchTerm,\n        onChange: handleSearch,\n        className: \"w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 250,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative\",\n      role: \"alert\",\n      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n        className: \"font-bold\",\n        children: \"Info!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 262,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"block sm:inline\",\n        children: [\" \", error]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 261,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow overflow-hidden\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"overflow-x-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"min-w-full divide-y divide-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            className: \"bg-gray-50\",\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Image\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Nom\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Prix\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Stock\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Cat\\xE9gorie\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Grillable\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 279,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            className: \"bg-white divide-y divide-gray-200\",\n            children: filteredProducts.map(product => /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"h-10 w-10 rounded-full overflow-hidden bg-gray-100\",\n                  children: product.image_url ? /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: product.image_url,\n                    alt: product.name,\n                    className: \"h-full w-full object-cover\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 288,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"h-full w-full flex items-center justify-center text-gray-400\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      className: \"h-6 w-6\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      stroke: \"currentColor\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 296,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 295,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 294,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 286,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                children: product.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                children: [product.price, \" DH\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${product.stock > 10 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`,\n                  children: product.stock\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 305,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                children: product.category ? product.category.name : 'Non catégorisé'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                children: product.is_grillable ? /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-green-600\",\n                  children: \"Oui\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 316,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-600\",\n                  children: \"Non\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 318,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => openModal(product),\n                  className: \"text-indigo-600 hover:text-indigo-900 mr-3\",\n                  children: \"Modifier\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 322,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleDelete(product.id),\n                  className: \"text-red-600 hover:text-red-900\",\n                  children: \"Supprimer\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 328,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 19\n              }, this)]\n            }, product.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 268,\n      columnNumber: 7\n    }, this), isModalOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-lg w-full max-w-md mx-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-6 py-4 border-b\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold\",\n            children: currentProduct ? 'Modifier le produit' : 'Ajouter un produit'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 346,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"name\",\n                className: \"block text-gray-700 mb-2\",\n                children: \"Nom\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"name\",\n                name: \"name\",\n                value: formData.name,\n                onChange: handleInputChange,\n                className: \"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 355,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"description\",\n                className: \"block text-gray-700 mb-2\",\n                children: \"Description\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 367,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                id: \"description\",\n                name: \"description\",\n                value: formData.description,\n                onChange: handleInputChange,\n                className: \"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\",\n                rows: \"3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 368,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 366,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-2 gap-4 mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"price\",\n                  className: \"block text-gray-700 mb-2\",\n                  children: \"Prix (DH)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 380,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  id: \"price\",\n                  name: \"price\",\n                  value: formData.price,\n                  onChange: handleInputChange,\n                  className: \"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\",\n                  step: \"0.01\",\n                  min: \"0\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 381,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 379,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"stock\",\n                  className: \"block text-gray-700 mb-2\",\n                  children: \"Stock\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 395,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  id: \"stock\",\n                  name: \"stock\",\n                  value: formData.stock,\n                  onChange: handleInputChange,\n                  className: \"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\",\n                  min: \"0\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 396,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 394,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 378,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"category_id\",\n                className: \"block text-gray-700 mb-2\",\n                children: \"Cat\\xE9gorie\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 410,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                id: \"category_id\",\n                name: \"category_id\",\n                value: formData.category_id,\n                onChange: handleInputChange,\n                className: \"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\",\n                required: true,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"S\\xE9lectionner une cat\\xE9gorie\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 419,\n                  columnNumber: 21\n                }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: category.id,\n                  children: category.name\n                }, category.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 421,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 411,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 409,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"image\",\n                className: \"block text-gray-700 mb-2\",\n                children: \"Image\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 429,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"file\",\n                id: \"image\",\n                name: \"image\",\n                onChange: handleInputChange,\n                className: \"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\",\n                accept: \"image/*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 430,\n                columnNumber: 19\n              }, this), currentProduct && currentProduct.image_url && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-500\",\n                  children: \"Image actuelle:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 440,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"h-20 w-20 mt-1 rounded overflow-hidden bg-gray-100\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: currentProduct.image_url,\n                    alt: currentProduct.name,\n                    className: \"h-full w-full object-cover\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 442,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 441,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 439,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 428,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  id: \"is_grillable\",\n                  name: \"is_grillable\",\n                  checked: formData.is_grillable,\n                  onChange: handleInputChange,\n                  className: \"h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 454,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"is_grillable\",\n                  className: \"ml-2 block text-gray-700\",\n                  children: \"Produit grillable\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 462,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 453,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 452,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"px-6 py-4 bg-gray-50 border-t flex justify-end\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: closeModal,\n              className: \"px-4 py-2 border rounded-lg text-gray-700 mr-2 hover:bg-gray-100\",\n              children: \"Annuler\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 470,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              disabled: loading,\n              className: \"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50\",\n              children: loading ? 'Enregistrement...' : currentProduct ? 'Mettre à jour' : 'Ajouter'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 477,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 469,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 345,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 344,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 223,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductsCRUD, \"Cjuq0AMGyBtILmIzTnVAtgC6QTE=\", false, function () {\n  return [useData];\n});\n_c = ProductsCRUD;\nexport default ProductsCRUD;\nvar _c;\n$RefreshReg$(_c, \"ProductsCRUD\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useData", "productAPI", "jsxDEV", "_jsxDEV", "ProductsCRUD", "_s", "state", "dispatch", "DataActions", "products", "categories", "loading", "error", "setError", "searchTerm", "setSearchTerm", "isModalOpen", "setIsModalOpen", "currentProduct", "setCurrentProduct", "formData", "setFormData", "name", "description", "price", "stock", "category_id", "is_grillable", "image", "filteredProducts", "setFilteredProducts", "filtered", "filter", "product", "toLowerCase", "includes", "refreshData", "type", "SET_LOADING", "payload", "result", "getAll", "success", "SET_PRODUCTS", "data", "console", "log", "err", "handleInputChange", "e", "value", "checked", "files", "target", "prev", "openModal", "closeModal", "handleSubmit", "preventDefault", "productData", "parseFloat", "parseInt", "update", "id", "UPDATE_PRODUCT", "category", "find", "cat", "create", "ADD_PRODUCT", "newProduct", "Date", "now", "image_url", "slug", "replace", "handleDelete", "window", "confirm", "DELETE_PRODUCT", "handleSearch", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "onClick", "disabled", "placeholder", "onChange", "role", "map", "src", "alt", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "onSubmit", "htmlFor", "required", "rows", "step", "min", "accept", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet-Synthese/frontend/src/pages/Admin/ProductsCRUD.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useData } from '../../context/DataContext';\nimport { productAPI } from '../../services/apiService';\n\nconst ProductsCRUD = () => {\n  const { state, dispatch, DataActions } = useData();\n  const { products, categories, loading } = state;\n  \n  const [error, setError] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const [currentProduct, setCurrentProduct] = useState(null);\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    price: '',\n    stock: '',\n    category_id: '',\n    is_grillable: false,\n    image: null\n  });\n  const [filteredProducts, setFilteredProducts] = useState([]);\n\n  // Filtrer les produits selon le terme de recherche\n  useEffect(() => {\n    if (searchTerm) {\n      const filtered = products.filter(product =>\n        product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        product.description.toLowerCase().includes(searchTerm.toLowerCase())\n      );\n      setFilteredProducts(filtered);\n    } else {\n      setFilteredProducts(products);\n    }\n  }, [products, searchTerm]);\n\n  // Fonction pour rafraîchir les données depuis l'API\n  const refreshData = async () => {\n    dispatch({ type: DataActions.SET_LOADING, payload: true });\n\n    try {\n      const result = await productAPI.getAll();\n      if (result.success) {\n        dispatch({ type: DataActions.SET_PRODUCTS, payload: result.data });\n        setError(null);\n        console.log('Données actualisées depuis l\\'API');\n      } else {\n        setError('Mode démonstration - Données de test utilisées');\n        console.log('API non disponible, données de test utilisées');\n      }\n    } catch (err) {\n      console.log('Erreur API, utilisation des données de test');\n      setError('Mode démonstration - Données de test utilisées');\n    } finally {\n      dispatch({ type: DataActions.SET_LOADING, payload: false });\n    }\n  };\n\n  // Gestion des changements dans le formulaire\n  const handleInputChange = (e) => {\n    const { name, value, type, checked, files } = e.target;\n    \n    if (type === 'checkbox') {\n      setFormData(prev => ({ ...prev, [name]: checked }));\n    } else if (type === 'file') {\n      setFormData(prev => ({ ...prev, [name]: files[0] }));\n    } else {\n      setFormData(prev => ({ ...prev, [name]: value }));\n    }\n  };\n\n  // Ouvrir le modal pour ajouter/modifier un produit\n  const openModal = (product = null) => {\n    if (product) {\n      setCurrentProduct(product);\n      setFormData({\n        name: product.name,\n        description: product.description,\n        price: product.price,\n        stock: product.stock,\n        category_id: product.category_id,\n        is_grillable: product.is_grillable,\n        image: null\n      });\n    } else {\n      setCurrentProduct(null);\n      setFormData({\n        name: '',\n        description: '',\n        price: '',\n        stock: '',\n        category_id: '',\n        is_grillable: false,\n        image: null\n      });\n    }\n    setIsModalOpen(true);\n  };\n\n  // Fermer le modal\n  const closeModal = () => {\n    setIsModalOpen(false);\n    setCurrentProduct(null);\n    setFormData({\n      name: '',\n      description: '',\n      price: '',\n      stock: '',\n      category_id: '',\n      is_grillable: false,\n      image: null\n    });\n  };\n\n  // Soumettre le formulaire\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    dispatch({ type: DataActions.SET_LOADING, payload: true });\n\n    try {\n      const productData = {\n        ...formData,\n        price: parseFloat(formData.price),\n        stock: parseInt(formData.stock),\n        category_id: parseInt(formData.category_id)\n      };\n\n      if (currentProduct) {\n        // Mise à jour d'un produit existant\n        const result = await productAPI.update(currentProduct.id, productData);\n\n        if (result.success) {\n          // Mise à jour réussie via API\n          dispatch({\n            type: DataActions.UPDATE_PRODUCT,\n            payload: { ...result.data, id: currentProduct.id }\n          });\n          console.log('Produit mis à jour via API');\n        } else {\n          // Fallback: mise à jour locale\n          dispatch({\n            type: DataActions.UPDATE_PRODUCT,\n            payload: { ...productData, id: currentProduct.id, category: categories.find(cat => cat.id === productData.category_id) }\n          });\n          console.log('Produit mis à jour localement (mode démonstration)');\n        }\n      } else {\n        // Création d'un nouveau produit\n        const result = await productAPI.create(productData);\n\n        if (result.success) {\n          // Création réussie via API\n          dispatch({ type: DataActions.ADD_PRODUCT, payload: result.data });\n          console.log('Produit créé via API');\n        } else {\n          // Fallback: création locale\n          const newProduct = {\n            ...productData,\n            id: Date.now(),\n            category: categories.find(cat => cat.id === productData.category_id),\n            image_url: 'https://images.unsplash.com/photo-1546069901-ba9599a7e63c?w=300',\n            slug: productData.name.toLowerCase().replace(/\\s+/g, '-')\n          };\n          dispatch({ type: DataActions.ADD_PRODUCT, payload: newProduct });\n          console.log('Produit créé localement (mode démonstration)');\n        }\n      }\n\n      closeModal();\n    } catch (err) {\n      console.error('Erreur lors de l\\'enregistrement du produit', err);\n      // En cas d'erreur, fallback vers le mode local\n      const productData = {\n        ...formData,\n        price: parseFloat(formData.price),\n        stock: parseInt(formData.stock),\n        category_id: parseInt(formData.category_id)\n      };\n\n      if (currentProduct) {\n        dispatch({\n          type: DataActions.UPDATE_PRODUCT,\n          payload: { ...productData, id: currentProduct.id, category: categories.find(cat => cat.id === productData.category_id) }\n        });\n      } else {\n        const newProduct = {\n          ...productData,\n          id: Date.now(),\n          category: categories.find(cat => cat.id === productData.category_id),\n          image_url: 'https://images.unsplash.com/photo-1546069901-ba9599a7e63c?w=300',\n          slug: productData.name.toLowerCase().replace(/\\s+/g, '-')\n        };\n        dispatch({ type: DataActions.ADD_PRODUCT, payload: newProduct });\n      }\n      closeModal();\n    } finally {\n      dispatch({ type: DataActions.SET_LOADING, payload: false });\n    }\n  };\n\n  // Supprimer un produit\n  const handleDelete = (id) => {\n    if (window.confirm('Êtes-vous sûr de vouloir supprimer ce produit ?')) {\n      dispatch({ type: DataActions.SET_LOADING, payload: true });\n\n      try {\n        dispatch({ type: DataActions.DELETE_PRODUCT, payload: id });\n        console.log('Produit supprimé avec succès');\n      } catch (err) {\n        console.error('Erreur lors de la suppression du produit', err);\n      } finally {\n        dispatch({ type: DataActions.SET_LOADING, payload: false });\n      }\n    }\n  };\n\n  // Gestion de la recherche\n  const handleSearch = (e) => {\n    setSearchTerm(e.target.value);\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* En-tête */}\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">Gestion des Produits</h1>\n          <p className=\"text-sm text-gray-500\">\n            {filteredProducts.length} produit(s) • CRUD complet activé\n          </p>\n        </div>\n        <div className=\"flex space-x-3\">\n          <button\n            onClick={refreshData}\n            disabled={loading}\n            className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50\"\n          >\n            {loading ? 'Actualisation...' : 'Actualiser'}\n          </button>\n          <button\n            onClick={() => openModal()}\n            className=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700\"\n          >\n            Ajouter un produit\n          </button>\n        </div>\n      </div>\n\n      {/* Barre de recherche */}\n      <div className=\"bg-white p-4 rounded-lg shadow\">\n        <input\n          type=\"text\"\n          placeholder=\"Rechercher un produit...\"\n          value={searchTerm}\n          onChange={handleSearch}\n          className=\"w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n        />\n      </div>\n\n      {error && (\n        <div className=\"bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative\" role=\"alert\">\n          <strong className=\"font-bold\">Info!</strong>\n          <span className=\"block sm:inline\"> {error}</span>\n        </div>\n      )}\n\n      {/* Tableau des produits */}\n      <div className=\"bg-white rounded-lg shadow overflow-hidden\">\n        <div className=\"overflow-x-auto\">\n          <table className=\"min-w-full divide-y divide-gray-200\">\n            <thead className=\"bg-gray-50\">\n              <tr>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Image</th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Nom</th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Prix</th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Stock</th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Catégorie</th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Grillable</th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Actions</th>\n              </tr>\n            </thead>\n            <tbody className=\"bg-white divide-y divide-gray-200\">\n              {filteredProducts.map((product) => (\n                <tr key={product.id}>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"h-10 w-10 rounded-full overflow-hidden bg-gray-100\">\n                      {product.image_url ? (\n                        <img\n                          src={product.image_url}\n                          alt={product.name}\n                          className=\"h-full w-full object-cover\"\n                        />\n                      ) : (\n                        <div className=\"h-full w-full flex items-center justify-center text-gray-400\">\n                          <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n                          </svg>\n                        </div>\n                      )}\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">{product.name}</td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">{product.price} DH</td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${\n                      product.stock > 10 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'\n                    }`}>\n                      {product.stock}\n                    </span>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                    {product.category ? product.category.name : 'Non catégorisé'}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                    {product.is_grillable ? (\n                      <span className=\"text-green-600\">Oui</span>\n                    ) : (\n                      <span className=\"text-red-600\">Non</span>\n                    )}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                    <button\n                      onClick={() => openModal(product)}\n                      className=\"text-indigo-600 hover:text-indigo-900 mr-3\"\n                    >\n                      Modifier\n                    </button>\n                    <button\n                      onClick={() => handleDelete(product.id)}\n                      className=\"text-red-600 hover:text-red-900\"\n                    >\n                      Supprimer\n                    </button>\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      </div>\n\n      {/* Modal pour ajouter/modifier un produit */}\n      {isModalOpen && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n          <div className=\"bg-white rounded-lg shadow-lg w-full max-w-md mx-4\">\n            <div className=\"px-6 py-4 border-b\">\n              <h3 className=\"text-lg font-semibold\">\n                {currentProduct ? 'Modifier le produit' : 'Ajouter un produit'}\n              </h3>\n            </div>\n            <form onSubmit={handleSubmit}>\n              <div className=\"p-6\">\n                <div className=\"mb-4\">\n                  <label htmlFor=\"name\" className=\"block text-gray-700 mb-2\">Nom</label>\n                  <input\n                    type=\"text\"\n                    id=\"name\"\n                    name=\"name\"\n                    value={formData.name}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n                    required\n                  />\n                </div>\n\n                <div className=\"mb-4\">\n                  <label htmlFor=\"description\" className=\"block text-gray-700 mb-2\">Description</label>\n                  <textarea\n                    id=\"description\"\n                    name=\"description\"\n                    value={formData.description}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n                    rows=\"3\"\n                  ></textarea>\n                </div>\n\n                <div className=\"grid grid-cols-2 gap-4 mb-4\">\n                  <div>\n                    <label htmlFor=\"price\" className=\"block text-gray-700 mb-2\">Prix (DH)</label>\n                    <input\n                      type=\"number\"\n                      id=\"price\"\n                      name=\"price\"\n                      value={formData.price}\n                      onChange={handleInputChange}\n                      className=\"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n                      step=\"0.01\"\n                      min=\"0\"\n                      required\n                    />\n                  </div>\n\n                  <div>\n                    <label htmlFor=\"stock\" className=\"block text-gray-700 mb-2\">Stock</label>\n                    <input\n                      type=\"number\"\n                      id=\"stock\"\n                      name=\"stock\"\n                      value={formData.stock}\n                      onChange={handleInputChange}\n                      className=\"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n                      min=\"0\"\n                      required\n                    />\n                  </div>\n                </div>\n\n                <div className=\"mb-4\">\n                  <label htmlFor=\"category_id\" className=\"block text-gray-700 mb-2\">Catégorie</label>\n                  <select\n                    id=\"category_id\"\n                    name=\"category_id\"\n                    value={formData.category_id}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n                    required\n                  >\n                    <option value=\"\">Sélectionner une catégorie</option>\n                    {categories.map(category => (\n                      <option key={category.id} value={category.id}>\n                        {category.name}\n                      </option>\n                    ))}\n                  </select>\n                </div>\n\n                <div className=\"mb-4\">\n                  <label htmlFor=\"image\" className=\"block text-gray-700 mb-2\">Image</label>\n                  <input\n                    type=\"file\"\n                    id=\"image\"\n                    name=\"image\"\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n                    accept=\"image/*\"\n                  />\n                  {currentProduct && currentProduct.image_url && (\n                    <div className=\"mt-2\">\n                      <p className=\"text-sm text-gray-500\">Image actuelle:</p>\n                      <div className=\"h-20 w-20 mt-1 rounded overflow-hidden bg-gray-100\">\n                        <img\n                          src={currentProduct.image_url}\n                          alt={currentProduct.name}\n                          className=\"h-full w-full object-cover\"\n                        />\n                      </div>\n                    </div>\n                  )}\n                </div>\n\n                <div className=\"mb-4\">\n                  <div className=\"flex items-center\">\n                    <input\n                      type=\"checkbox\"\n                      id=\"is_grillable\"\n                      name=\"is_grillable\"\n                      checked={formData.is_grillable}\n                      onChange={handleInputChange}\n                      className=\"h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded\"\n                    />\n                    <label htmlFor=\"is_grillable\" className=\"ml-2 block text-gray-700\">\n                      Produit grillable\n                    </label>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"px-6 py-4 bg-gray-50 border-t flex justify-end\">\n                <button\n                  type=\"button\"\n                  onClick={closeModal}\n                  className=\"px-4 py-2 border rounded-lg text-gray-700 mr-2 hover:bg-gray-100\"\n                >\n                  Annuler\n                </button>\n                <button\n                  type=\"submit\"\n                  disabled={loading}\n                  className=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50\"\n                >\n                  {loading ? 'Enregistrement...' : (currentProduct ? 'Mettre à jour' : 'Ajouter')}\n                </button>\n              </div>\n            </form>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default ProductsCRUD;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,2BAA2B;AACnD,SAASC,UAAU,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvD,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM;IAAEC,KAAK;IAAEC,QAAQ;IAAEC;EAAY,CAAC,GAAGR,OAAO,CAAC,CAAC;EAClD,MAAM;IAAES,QAAQ;IAAEC,UAAU;IAAEC;EAAQ,CAAC,GAAGL,KAAK;EAE/C,MAAM,CAACM,KAAK,EAAEC,QAAQ,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACgB,UAAU,EAAEC,aAAa,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACkB,WAAW,EAAEC,cAAc,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACoB,cAAc,EAAEC,iBAAiB,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACsB,QAAQ,EAAEC,WAAW,CAAC,GAAGvB,QAAQ,CAAC;IACvCwB,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,YAAY,EAAE,KAAK;IACnBC,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;;EAE5D;EACAC,SAAS,CAAC,MAAM;IACd,IAAIe,UAAU,EAAE;MACd,MAAMiB,QAAQ,GAAGtB,QAAQ,CAACuB,MAAM,CAACC,OAAO,IACtCA,OAAO,CAACX,IAAI,CAACY,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACrB,UAAU,CAACoB,WAAW,CAAC,CAAC,CAAC,IAC7DD,OAAO,CAACV,WAAW,CAACW,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACrB,UAAU,CAACoB,WAAW,CAAC,CAAC,CACrE,CAAC;MACDJ,mBAAmB,CAACC,QAAQ,CAAC;IAC/B,CAAC,MAAM;MACLD,mBAAmB,CAACrB,QAAQ,CAAC;IAC/B;EACF,CAAC,EAAE,CAACA,QAAQ,EAAEK,UAAU,CAAC,CAAC;;EAE1B;EACA,MAAMsB,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B7B,QAAQ,CAAC;MAAE8B,IAAI,EAAE7B,WAAW,CAAC8B,WAAW;MAAEC,OAAO,EAAE;IAAK,CAAC,CAAC;IAE1D,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMvC,UAAU,CAACwC,MAAM,CAAC,CAAC;MACxC,IAAID,MAAM,CAACE,OAAO,EAAE;QAClBnC,QAAQ,CAAC;UAAE8B,IAAI,EAAE7B,WAAW,CAACmC,YAAY;UAAEJ,OAAO,EAAEC,MAAM,CAACI;QAAK,CAAC,CAAC;QAClE/B,QAAQ,CAAC,IAAI,CAAC;QACdgC,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;MAClD,CAAC,MAAM;QACLjC,QAAQ,CAAC,gDAAgD,CAAC;QAC1DgC,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;MAC9D;IACF,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZF,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;MAC1DjC,QAAQ,CAAC,gDAAgD,CAAC;IAC5D,CAAC,SAAS;MACRN,QAAQ,CAAC;QAAE8B,IAAI,EAAE7B,WAAW,CAAC8B,WAAW;QAAEC,OAAO,EAAE;MAAM,CAAC,CAAC;IAC7D;EACF,CAAC;;EAED;EACA,MAAMS,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAE3B,IAAI;MAAE4B,KAAK;MAAEb,IAAI;MAAEc,OAAO;MAAEC;IAAM,CAAC,GAAGH,CAAC,CAACI,MAAM;IAEtD,IAAIhB,IAAI,KAAK,UAAU,EAAE;MACvBhB,WAAW,CAACiC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAAChC,IAAI,GAAG6B;MAAQ,CAAC,CAAC,CAAC;IACrD,CAAC,MAAM,IAAId,IAAI,KAAK,MAAM,EAAE;MAC1BhB,WAAW,CAACiC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAAChC,IAAI,GAAG8B,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,CAAC;IACtD,CAAC,MAAM;MACL/B,WAAW,CAACiC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAAChC,IAAI,GAAG4B;MAAM,CAAC,CAAC,CAAC;IACnD;EACF,CAAC;;EAED;EACA,MAAMK,SAAS,GAAGA,CAACtB,OAAO,GAAG,IAAI,KAAK;IACpC,IAAIA,OAAO,EAAE;MACXd,iBAAiB,CAACc,OAAO,CAAC;MAC1BZ,WAAW,CAAC;QACVC,IAAI,EAAEW,OAAO,CAACX,IAAI;QAClBC,WAAW,EAAEU,OAAO,CAACV,WAAW;QAChCC,KAAK,EAAES,OAAO,CAACT,KAAK;QACpBC,KAAK,EAAEQ,OAAO,CAACR,KAAK;QACpBC,WAAW,EAAEO,OAAO,CAACP,WAAW;QAChCC,YAAY,EAAEM,OAAO,CAACN,YAAY;QAClCC,KAAK,EAAE;MACT,CAAC,CAAC;IACJ,CAAC,MAAM;MACLT,iBAAiB,CAAC,IAAI,CAAC;MACvBE,WAAW,CAAC;QACVC,IAAI,EAAE,EAAE;QACRC,WAAW,EAAE,EAAE;QACfC,KAAK,EAAE,EAAE;QACTC,KAAK,EAAE,EAAE;QACTC,WAAW,EAAE,EAAE;QACfC,YAAY,EAAE,KAAK;QACnBC,KAAK,EAAE;MACT,CAAC,CAAC;IACJ;IACAX,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;;EAED;EACA,MAAMuC,UAAU,GAAGA,CAAA,KAAM;IACvBvC,cAAc,CAAC,KAAK,CAAC;IACrBE,iBAAiB,CAAC,IAAI,CAAC;IACvBE,WAAW,CAAC;MACVC,IAAI,EAAE,EAAE;MACRC,WAAW,EAAE,EAAE;MACfC,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE,EAAE;MACTC,WAAW,EAAE,EAAE;MACfC,YAAY,EAAE,KAAK;MACnBC,KAAK,EAAE;IACT,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAM6B,YAAY,GAAG,MAAOR,CAAC,IAAK;IAChCA,CAAC,CAACS,cAAc,CAAC,CAAC;IAClBnD,QAAQ,CAAC;MAAE8B,IAAI,EAAE7B,WAAW,CAAC8B,WAAW;MAAEC,OAAO,EAAE;IAAK,CAAC,CAAC;IAE1D,IAAI;MACF,MAAMoB,WAAW,GAAG;QAClB,GAAGvC,QAAQ;QACXI,KAAK,EAAEoC,UAAU,CAACxC,QAAQ,CAACI,KAAK,CAAC;QACjCC,KAAK,EAAEoC,QAAQ,CAACzC,QAAQ,CAACK,KAAK,CAAC;QAC/BC,WAAW,EAAEmC,QAAQ,CAACzC,QAAQ,CAACM,WAAW;MAC5C,CAAC;MAED,IAAIR,cAAc,EAAE;QAClB;QACA,MAAMsB,MAAM,GAAG,MAAMvC,UAAU,CAAC6D,MAAM,CAAC5C,cAAc,CAAC6C,EAAE,EAAEJ,WAAW,CAAC;QAEtE,IAAInB,MAAM,CAACE,OAAO,EAAE;UAClB;UACAnC,QAAQ,CAAC;YACP8B,IAAI,EAAE7B,WAAW,CAACwD,cAAc;YAChCzB,OAAO,EAAE;cAAE,GAAGC,MAAM,CAACI,IAAI;cAAEmB,EAAE,EAAE7C,cAAc,CAAC6C;YAAG;UACnD,CAAC,CAAC;UACFlB,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;QAC3C,CAAC,MAAM;UACL;UACAvC,QAAQ,CAAC;YACP8B,IAAI,EAAE7B,WAAW,CAACwD,cAAc;YAChCzB,OAAO,EAAE;cAAE,GAAGoB,WAAW;cAAEI,EAAE,EAAE7C,cAAc,CAAC6C,EAAE;cAAEE,QAAQ,EAAEvD,UAAU,CAACwD,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACJ,EAAE,KAAKJ,WAAW,CAACjC,WAAW;YAAE;UACzH,CAAC,CAAC;UACFmB,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;QACnE;MACF,CAAC,MAAM;QACL;QACA,MAAMN,MAAM,GAAG,MAAMvC,UAAU,CAACmE,MAAM,CAACT,WAAW,CAAC;QAEnD,IAAInB,MAAM,CAACE,OAAO,EAAE;UAClB;UACAnC,QAAQ,CAAC;YAAE8B,IAAI,EAAE7B,WAAW,CAAC6D,WAAW;YAAE9B,OAAO,EAAEC,MAAM,CAACI;UAAK,CAAC,CAAC;UACjEC,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;QACrC,CAAC,MAAM;UACL;UACA,MAAMwB,UAAU,GAAG;YACjB,GAAGX,WAAW;YACdI,EAAE,EAAEQ,IAAI,CAACC,GAAG,CAAC,CAAC;YACdP,QAAQ,EAAEvD,UAAU,CAACwD,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACJ,EAAE,KAAKJ,WAAW,CAACjC,WAAW,CAAC;YACpE+C,SAAS,EAAE,iEAAiE;YAC5EC,IAAI,EAAEf,WAAW,CAACrC,IAAI,CAACY,WAAW,CAAC,CAAC,CAACyC,OAAO,CAAC,MAAM,EAAE,GAAG;UAC1D,CAAC;UACDpE,QAAQ,CAAC;YAAE8B,IAAI,EAAE7B,WAAW,CAAC6D,WAAW;YAAE9B,OAAO,EAAE+B;UAAW,CAAC,CAAC;UAChEzB,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;QAC7D;MACF;MAEAU,UAAU,CAAC,CAAC;IACd,CAAC,CAAC,OAAOT,GAAG,EAAE;MACZF,OAAO,CAACjC,KAAK,CAAC,6CAA6C,EAAEmC,GAAG,CAAC;MACjE;MACA,MAAMY,WAAW,GAAG;QAClB,GAAGvC,QAAQ;QACXI,KAAK,EAAEoC,UAAU,CAACxC,QAAQ,CAACI,KAAK,CAAC;QACjCC,KAAK,EAAEoC,QAAQ,CAACzC,QAAQ,CAACK,KAAK,CAAC;QAC/BC,WAAW,EAAEmC,QAAQ,CAACzC,QAAQ,CAACM,WAAW;MAC5C,CAAC;MAED,IAAIR,cAAc,EAAE;QAClBX,QAAQ,CAAC;UACP8B,IAAI,EAAE7B,WAAW,CAACwD,cAAc;UAChCzB,OAAO,EAAE;YAAE,GAAGoB,WAAW;YAAEI,EAAE,EAAE7C,cAAc,CAAC6C,EAAE;YAAEE,QAAQ,EAAEvD,UAAU,CAACwD,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACJ,EAAE,KAAKJ,WAAW,CAACjC,WAAW;UAAE;QACzH,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,MAAM4C,UAAU,GAAG;UACjB,GAAGX,WAAW;UACdI,EAAE,EAAEQ,IAAI,CAACC,GAAG,CAAC,CAAC;UACdP,QAAQ,EAAEvD,UAAU,CAACwD,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACJ,EAAE,KAAKJ,WAAW,CAACjC,WAAW,CAAC;UACpE+C,SAAS,EAAE,iEAAiE;UAC5EC,IAAI,EAAEf,WAAW,CAACrC,IAAI,CAACY,WAAW,CAAC,CAAC,CAACyC,OAAO,CAAC,MAAM,EAAE,GAAG;QAC1D,CAAC;QACDpE,QAAQ,CAAC;UAAE8B,IAAI,EAAE7B,WAAW,CAAC6D,WAAW;UAAE9B,OAAO,EAAE+B;QAAW,CAAC,CAAC;MAClE;MACAd,UAAU,CAAC,CAAC;IACd,CAAC,SAAS;MACRjD,QAAQ,CAAC;QAAE8B,IAAI,EAAE7B,WAAW,CAAC8B,WAAW;QAAEC,OAAO,EAAE;MAAM,CAAC,CAAC;IAC7D;EACF,CAAC;;EAED;EACA,MAAMqC,YAAY,GAAIb,EAAE,IAAK;IAC3B,IAAIc,MAAM,CAACC,OAAO,CAAC,iDAAiD,CAAC,EAAE;MACrEvE,QAAQ,CAAC;QAAE8B,IAAI,EAAE7B,WAAW,CAAC8B,WAAW;QAAEC,OAAO,EAAE;MAAK,CAAC,CAAC;MAE1D,IAAI;QACFhC,QAAQ,CAAC;UAAE8B,IAAI,EAAE7B,WAAW,CAACuE,cAAc;UAAExC,OAAO,EAAEwB;QAAG,CAAC,CAAC;QAC3DlB,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;MAC7C,CAAC,CAAC,OAAOC,GAAG,EAAE;QACZF,OAAO,CAACjC,KAAK,CAAC,0CAA0C,EAAEmC,GAAG,CAAC;MAChE,CAAC,SAAS;QACRxC,QAAQ,CAAC;UAAE8B,IAAI,EAAE7B,WAAW,CAAC8B,WAAW;UAAEC,OAAO,EAAE;QAAM,CAAC,CAAC;MAC7D;IACF;EACF,CAAC;;EAED;EACA,MAAMyC,YAAY,GAAI/B,CAAC,IAAK;IAC1BlC,aAAa,CAACkC,CAAC,CAACI,MAAM,CAACH,KAAK,CAAC;EAC/B,CAAC;EAED,oBACE/C,OAAA;IAAK8E,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExB/E,OAAA;MAAK8E,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChD/E,OAAA;QAAA+E,QAAA,gBACE/E,OAAA;UAAI8E,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1EnF,OAAA;UAAG8E,SAAS,EAAC,uBAAuB;UAAAC,QAAA,GACjCrD,gBAAgB,CAAC0D,MAAM,EAAC,2CAC3B;QAAA;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACNnF,OAAA;QAAK8E,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B/E,OAAA;UACEqF,OAAO,EAAEpD,WAAY;UACrBqD,QAAQ,EAAE9E,OAAQ;UAClBsE,SAAS,EAAC,mFAAmF;UAAAC,QAAA,EAE5FvE,OAAO,GAAG,kBAAkB,GAAG;QAAY;UAAAwE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC,eACTnF,OAAA;UACEqF,OAAO,EAAEA,CAAA,KAAMjC,SAAS,CAAC,CAAE;UAC3B0B,SAAS,EAAC,iEAAiE;UAAAC,QAAA,EAC5E;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnF,OAAA;MAAK8E,SAAS,EAAC,gCAAgC;MAAAC,QAAA,eAC7C/E,OAAA;QACEkC,IAAI,EAAC,MAAM;QACXqD,WAAW,EAAC,0BAA0B;QACtCxC,KAAK,EAAEpC,UAAW;QAClB6E,QAAQ,EAAEX,YAAa;QACvBC,SAAS,EAAC;MAAyF;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAEL1E,KAAK,iBACJT,OAAA;MAAK8E,SAAS,EAAC,mFAAmF;MAACW,IAAI,EAAC,OAAO;MAAAV,QAAA,gBAC7G/E,OAAA;QAAQ8E,SAAS,EAAC,WAAW;QAAAC,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC5CnF,OAAA;QAAM8E,SAAS,EAAC,iBAAiB;QAAAC,QAAA,GAAC,GAAC,EAACtE,KAAK;MAAA;QAAAuE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9C,CACN,eAGDnF,OAAA;MAAK8E,SAAS,EAAC,4CAA4C;MAAAC,QAAA,eACzD/E,OAAA;QAAK8E,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9B/E,OAAA;UAAO8E,SAAS,EAAC,qCAAqC;UAAAC,QAAA,gBACpD/E,OAAA;YAAO8E,SAAS,EAAC,YAAY;YAAAC,QAAA,eAC3B/E,OAAA;cAAA+E,QAAA,gBACE/E,OAAA;gBAAI8E,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzGnF,OAAA;gBAAI8E,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvGnF,OAAA;gBAAI8E,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxGnF,OAAA;gBAAI8E,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzGnF,OAAA;gBAAI8E,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7GnF,OAAA;gBAAI8E,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7GnF,OAAA;gBAAI8E,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRnF,OAAA;YAAO8E,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EACjDrD,gBAAgB,CAACgE,GAAG,CAAE5D,OAAO,iBAC5B9B,OAAA;cAAA+E,QAAA,gBACE/E,OAAA;gBAAI8E,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,eACzC/E,OAAA;kBAAK8E,SAAS,EAAC,oDAAoD;kBAAAC,QAAA,EAChEjD,OAAO,CAACwC,SAAS,gBAChBtE,OAAA;oBACE2F,GAAG,EAAE7D,OAAO,CAACwC,SAAU;oBACvBsB,GAAG,EAAE9D,OAAO,CAACX,IAAK;oBAClB2D,SAAS,EAAC;kBAA4B;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC,CAAC,gBAEFnF,OAAA;oBAAK8E,SAAS,EAAC,8DAA8D;oBAAAC,QAAA,eAC3E/E,OAAA;sBAAK6F,KAAK,EAAC,4BAA4B;sBAACf,SAAS,EAAC,SAAS;sBAACgB,IAAI,EAAC,MAAM;sBAACC,OAAO,EAAC,WAAW;sBAACC,MAAM,EAAC,cAAc;sBAAAjB,QAAA,eAC/G/E,OAAA;wBAAMiG,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,WAAW,EAAE,CAAE;wBAACC,CAAC,EAAC;sBAA2J;wBAAApB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLnF,OAAA;gBAAI8E,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,EAAEjD,OAAO,CAACX;cAAI;gBAAA6D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACrFnF,OAAA;gBAAI8E,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,GAAEjD,OAAO,CAACT,KAAK,EAAC,KAAG;cAAA;gBAAA2D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzFnF,OAAA;gBAAI8E,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,eAC/D/E,OAAA;kBAAM8E,SAAS,EAAE,iEACfhD,OAAO,CAACR,KAAK,GAAG,EAAE,GAAG,6BAA6B,GAAG,yBAAyB,EAC7E;kBAAAyD,QAAA,EACAjD,OAAO,CAACR;gBAAK;kBAAA0D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACLnF,OAAA;gBAAI8E,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,EAC9DjD,OAAO,CAACgC,QAAQ,GAAGhC,OAAO,CAACgC,QAAQ,CAAC3C,IAAI,GAAG;cAAgB;gBAAA6D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC,eACLnF,OAAA;gBAAI8E,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,EAC9DjD,OAAO,CAACN,YAAY,gBACnBxB,OAAA;kBAAM8E,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,gBAE3CnF,OAAA;kBAAM8E,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cACzC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACLnF,OAAA;gBAAI8E,SAAS,EAAC,iDAAiD;gBAAAC,QAAA,gBAC7D/E,OAAA;kBACEqF,OAAO,EAAEA,CAAA,KAAMjC,SAAS,CAACtB,OAAO,CAAE;kBAClCgD,SAAS,EAAC,4CAA4C;kBAAAC,QAAA,EACvD;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTnF,OAAA;kBACEqF,OAAO,EAAEA,CAAA,KAAMZ,YAAY,CAAC3C,OAAO,CAAC8B,EAAE,CAAE;kBACxCkB,SAAS,EAAC,iCAAiC;kBAAAC,QAAA,EAC5C;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC;YAAA,GAlDErD,OAAO,CAAC8B,EAAE;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAmDf,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLtE,WAAW,iBACVb,OAAA;MAAK8E,SAAS,EAAC,4EAA4E;MAAAC,QAAA,eACzF/E,OAAA;QAAK8E,SAAS,EAAC,oDAAoD;QAAAC,QAAA,gBACjE/E,OAAA;UAAK8E,SAAS,EAAC,oBAAoB;UAAAC,QAAA,eACjC/E,OAAA;YAAI8E,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAClChE,cAAc,GAAG,qBAAqB,GAAG;UAAoB;YAAAiE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACNnF,OAAA;UAAMqG,QAAQ,EAAE/C,YAAa;UAAAyB,QAAA,gBAC3B/E,OAAA;YAAK8E,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAClB/E,OAAA;cAAK8E,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB/E,OAAA;gBAAOsG,OAAO,EAAC,MAAM;gBAACxB,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtEnF,OAAA;gBACEkC,IAAI,EAAC,MAAM;gBACX0B,EAAE,EAAC,MAAM;gBACTzC,IAAI,EAAC,MAAM;gBACX4B,KAAK,EAAE9B,QAAQ,CAACE,IAAK;gBACrBqE,QAAQ,EAAE3C,iBAAkB;gBAC5BiC,SAAS,EAAC,yFAAyF;gBACnGyB,QAAQ;cAAA;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENnF,OAAA;cAAK8E,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB/E,OAAA;gBAAOsG,OAAO,EAAC,aAAa;gBAACxB,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrFnF,OAAA;gBACE4D,EAAE,EAAC,aAAa;gBAChBzC,IAAI,EAAC,aAAa;gBAClB4B,KAAK,EAAE9B,QAAQ,CAACG,WAAY;gBAC5BoE,QAAQ,EAAE3C,iBAAkB;gBAC5BiC,SAAS,EAAC,yFAAyF;gBACnG0B,IAAI,EAAC;cAAG;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eAENnF,OAAA;cAAK8E,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1C/E,OAAA;gBAAA+E,QAAA,gBACE/E,OAAA;kBAAOsG,OAAO,EAAC,OAAO;kBAACxB,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC7EnF,OAAA;kBACEkC,IAAI,EAAC,QAAQ;kBACb0B,EAAE,EAAC,OAAO;kBACVzC,IAAI,EAAC,OAAO;kBACZ4B,KAAK,EAAE9B,QAAQ,CAACI,KAAM;kBACtBmE,QAAQ,EAAE3C,iBAAkB;kBAC5BiC,SAAS,EAAC,yFAAyF;kBACnG2B,IAAI,EAAC,MAAM;kBACXC,GAAG,EAAC,GAAG;kBACPH,QAAQ;gBAAA;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENnF,OAAA;gBAAA+E,QAAA,gBACE/E,OAAA;kBAAOsG,OAAO,EAAC,OAAO;kBAACxB,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACzEnF,OAAA;kBACEkC,IAAI,EAAC,QAAQ;kBACb0B,EAAE,EAAC,OAAO;kBACVzC,IAAI,EAAC,OAAO;kBACZ4B,KAAK,EAAE9B,QAAQ,CAACK,KAAM;kBACtBkE,QAAQ,EAAE3C,iBAAkB;kBAC5BiC,SAAS,EAAC,yFAAyF;kBACnG4B,GAAG,EAAC,GAAG;kBACPH,QAAQ;gBAAA;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENnF,OAAA;cAAK8E,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB/E,OAAA;gBAAOsG,OAAO,EAAC,aAAa;gBAACxB,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACnFnF,OAAA;gBACE4D,EAAE,EAAC,aAAa;gBAChBzC,IAAI,EAAC,aAAa;gBAClB4B,KAAK,EAAE9B,QAAQ,CAACM,WAAY;gBAC5BiE,QAAQ,EAAE3C,iBAAkB;gBAC5BiC,SAAS,EAAC,yFAAyF;gBACnGyB,QAAQ;gBAAAxB,QAAA,gBAER/E,OAAA;kBAAQ+C,KAAK,EAAC,EAAE;kBAAAgC,QAAA,EAAC;gBAA0B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EACnD5E,UAAU,CAACmF,GAAG,CAAC5B,QAAQ,iBACtB9D,OAAA;kBAA0B+C,KAAK,EAAEe,QAAQ,CAACF,EAAG;kBAAAmB,QAAA,EAC1CjB,QAAQ,CAAC3C;gBAAI,GADH2C,QAAQ,CAACF,EAAE;kBAAAoB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEhB,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENnF,OAAA;cAAK8E,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB/E,OAAA;gBAAOsG,OAAO,EAAC,OAAO;gBAACxB,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACzEnF,OAAA;gBACEkC,IAAI,EAAC,MAAM;gBACX0B,EAAE,EAAC,OAAO;gBACVzC,IAAI,EAAC,OAAO;gBACZqE,QAAQ,EAAE3C,iBAAkB;gBAC5BiC,SAAS,EAAC,yFAAyF;gBACnG6B,MAAM,EAAC;cAAS;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC,EACDpE,cAAc,IAAIA,cAAc,CAACuD,SAAS,iBACzCtE,OAAA;gBAAK8E,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnB/E,OAAA;kBAAG8E,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACxDnF,OAAA;kBAAK8E,SAAS,EAAC,oDAAoD;kBAAAC,QAAA,eACjE/E,OAAA;oBACE2F,GAAG,EAAE5E,cAAc,CAACuD,SAAU;oBAC9BsB,GAAG,EAAE7E,cAAc,CAACI,IAAK;oBACzB2D,SAAS,EAAC;kBAA4B;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAENnF,OAAA;cAAK8E,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnB/E,OAAA;gBAAK8E,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChC/E,OAAA;kBACEkC,IAAI,EAAC,UAAU;kBACf0B,EAAE,EAAC,cAAc;kBACjBzC,IAAI,EAAC,cAAc;kBACnB6B,OAAO,EAAE/B,QAAQ,CAACO,YAAa;kBAC/BgE,QAAQ,EAAE3C,iBAAkB;kBAC5BiC,SAAS,EAAC;gBAAqE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChF,CAAC,eACFnF,OAAA;kBAAOsG,OAAO,EAAC,cAAc;kBAACxB,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,EAAC;gBAEnE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENnF,OAAA;YAAK8E,SAAS,EAAC,gDAAgD;YAAAC,QAAA,gBAC7D/E,OAAA;cACEkC,IAAI,EAAC,QAAQ;cACbmD,OAAO,EAAEhC,UAAW;cACpByB,SAAS,EAAC,kEAAkE;cAAAC,QAAA,EAC7E;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTnF,OAAA;cACEkC,IAAI,EAAC,QAAQ;cACboD,QAAQ,EAAE9E,OAAQ;cAClBsE,SAAS,EAAC,qFAAqF;cAAAC,QAAA,EAE9FvE,OAAO,GAAG,mBAAmB,GAAIO,cAAc,GAAG,eAAe,GAAG;YAAU;cAAAiE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACjF,EAAA,CAteID,YAAY;EAAA,QACyBJ,OAAO;AAAA;AAAA+G,EAAA,GAD5C3G,YAAY;AAwelB,eAAeA,YAAY;AAAC,IAAA2G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}