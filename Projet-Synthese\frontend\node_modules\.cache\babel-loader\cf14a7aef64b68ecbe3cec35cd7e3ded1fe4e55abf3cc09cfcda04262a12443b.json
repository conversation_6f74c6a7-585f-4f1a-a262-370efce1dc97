{"ast": null, "code": "var _s = $RefreshSig$();\nimport React from 'react';\nimport { Navigate } from 'react-router-dom';\nimport useSafeAuth from '../../hooks/useSafeAuth';\nconst ProtectedRoute = ({\n  children\n}) => {\n  _s();\n  const {\n    state\n  } = useSafeAuth();\n\n  // Pour la démonstration, on permet l'accès sans authentification\n  // En production, décommentez les lignes ci-dessous\n  /*\n  if (!state.isAuthenticated) {\n    return <Navigate to=\"/login\" replace />;\n  }\n   if (!state.user?.role || state.user.role !== 'admin') {\n    return <Navigate to=\"/\" replace />;\n  }\n  */\n\n  // Mode démonstration - accès libre\n  return children;\n};\n_s(ProtectedRoute, \"pF0SD8pQpemaFuSPSDRZzqrLtII=\", false, function () {\n  return [useSafeAuth];\n});\n_c = ProtectedRoute;\nexport default ProtectedRoute;\nvar _c;\n$RefreshReg$(_c, \"ProtectedRoute\");", "map": {"version": 3, "names": ["React", "Navigate", "useSafeAuth", "ProtectedRoute", "children", "_s", "state", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet-Synthese/frontend/src/components/auth/ProtectedRoute.js"], "sourcesContent": ["import React from 'react';\nimport { Navigate } from 'react-router-dom';\nimport useSafeAuth from '../../hooks/useSafeAuth';\n\nconst ProtectedRoute = ({ children }) => {\n  const { state } = useSafeAuth();\n\n  // Pour la démonstration, on permet l'accès sans authentification\n  // En production, décommentez les lignes ci-dessous\n  /*\n  if (!state.isAuthenticated) {\n    return <Navigate to=\"/login\" replace />;\n  }\n\n  if (!state.user?.role || state.user.role !== 'admin') {\n    return <Navigate to=\"/\" replace />;\n  }\n  */\n\n  // Mode démonstration - accès libre\n  return children;\n};\n\nexport default ProtectedRoute;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,OAAOC,WAAW,MAAM,yBAAyB;AAEjD,MAAMC,cAAc,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACvC,MAAM;IAAEC;EAAM,CAAC,GAAGJ,WAAW,CAAC,CAAC;;EAE/B;EACA;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;;EAGE;EACA,OAAOE,QAAQ;AACjB,CAAC;AAACC,EAAA,CAjBIF,cAAc;EAAA,QACAD,WAAW;AAAA;AAAAK,EAAA,GADzBJ,cAAc;AAmBpB,eAAeA,cAAc;AAAC,IAAAI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}