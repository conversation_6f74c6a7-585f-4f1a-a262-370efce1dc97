from flask import Blueprint, jsonify, request
from datetime import datetime, timedelta
import random

dashboard_bp = Blueprint('dashboard', __name__)

@dashboard_bp.route('/api/admin/dashboard/stats', methods=['GET'])
def get_dashboard_stats():
    """Récupérer les statistiques générales du dashboard"""
    try:
        # Simuler des données réalistes
        stats = {
            'totalProducts': 156,
            'totalOrders': 89,
            'totalRevenue': 12450.75,
            'totalUsers': 234,
            'lowStockProducts': 8,
            'pendingOrders': 12,
            'monthlyRevenue': 8750.25,
            'weeklyOrders': 23
        }
        
        return jsonify({
            'success': True,
            'data': stats
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@dashboard_bp.route('/api/admin/dashboard/charts', methods=['GET'])
def get_chart_data():
    """Récupérer les données pour les graphiques"""
    try:
        period = request.args.get('period', '7days')
        
        # Données pour les graphiques
        chart_data = {
            'salesChart': {
                'labels': ['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'],
                'datasets': [{
                    'label': 'Ventes (MAD)',
                    'data': [1200, 1900, 800, 1500, 2000, 2400, 1800],
                    'borderColor': 'rgb(34, 197, 94)',
                    'backgroundColor': 'rgba(34, 197, 94, 0.1)',
                    'tension': 0.4
                }]
            },
            'ordersChart': {
                'labels': ['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'],
                'datasets': [{
                    'label': 'Commandes',
                    'data': [12, 19, 8, 15, 20, 24, 18],
                    'backgroundColor': [
                        '#ef4444', '#f97316', '#eab308', 
                        '#22c55e', '#3b82f6', '#8b5cf6', '#ec4899'
                    ]
                }]
            },
            'categoriesChart': {
                'labels': ['Produits Grillés', 'Salades', 'Fromages', 'Boissons', 'Desserts'],
                'datasets': [{
                    'data': [35, 25, 20, 15, 5],
                    'backgroundColor': [
                        '#ef4444', '#22c55e', '#eab308', '#3b82f6', '#8b5cf6'
                    ]
                }]
            }
        }
        
        return jsonify({
            'success': True,
            'data': chart_data
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@dashboard_bp.route('/api/admin/dashboard/activity', methods=['GET'])
def get_recent_activity():
    """Récupérer les activités récentes"""
    try:
        limit = int(request.args.get('limit', 10))
        
        activities = [
            {
                'id': 1,
                'type': 'order',
                'message': 'Nouvelle commande #1234',
                'user': 'Marie Dubois',
                'amount': 89.50,
                'time': '2 minutes',
                'status': 'pending'
            },
            {
                'id': 2,
                'type': 'product',
                'message': 'Stock faible: Poulet Grillé',
                'user': 'Système',
                'amount': None,
                'time': '15 minutes',
                'status': 'warning'
            },
            {
                'id': 3,
                'type': 'user',
                'message': 'Nouvel utilisateur inscrit',
                'user': 'Ahmed Benali',
                'amount': None,
                'time': '1 heure',
                'status': 'success'
            },
            {
                'id': 4,
                'type': 'order',
                'message': 'Commande #1233 livrée',
                'user': 'Fatima Zahra',
                'amount': 156.75,
                'time': '2 heures',
                'status': 'completed'
            },
            {
                'id': 5,
                'type': 'product',
                'message': 'Nouveau produit ajouté',
                'user': 'Admin',
                'amount': None,
                'time': '3 heures',
                'status': 'info'
            }
        ]
        
        return jsonify({
            'success': True,
            'data': activities[:limit]
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@dashboard_bp.route('/api/admin/dashboard/low-stock', methods=['GET'])
def get_low_stock_products():
    """Récupérer les produits en stock faible"""
    try:
        products = [
            {
                'id': 1,
                'name': 'Poulet Grillé Entier',
                'stock': 3,
                'min_stock': 10,
                'category': 'Produits Grillés',
                'price': 89.99
            },
            {
                'id': 2,
                'name': 'Brochettes de Bœuf',
                'stock': 2,
                'min_stock': 8,
                'category': 'Produits Grillés',
                'price': 65.50
            },
            {
                'id': 3,
                'name': 'Fromage de Chèvre',
                'stock': 1,
                'min_stock': 5,
                'category': 'Fromages',
                'price': 45.00
            }
        ]
        
        return jsonify({
            'success': True,
            'data': products
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@dashboard_bp.route('/api/admin/dashboard/recent-orders', methods=['GET'])
def get_recent_orders():
    """Récupérer les commandes récentes"""
    try:
        limit = int(request.args.get('limit', 5))
        
        orders = [
            {
                'id': 1234,
                'user': {
                    'name': 'Marie Dubois',
                    'email': '<EMAIL>'
                },
                'total': 89.50,
                'status': 'pending',
                'created_at': '2024-01-15T10:30:00Z',
                'items_count': 3
            },
            {
                'id': 1233,
                'user': {
                    'name': 'Ahmed Benali',
                    'email': '<EMAIL>'
                },
                'total': 156.75,
                'status': 'completed',
                'created_at': '2024-01-15T09:15:00Z',
                'items_count': 5
            },
            {
                'id': 1232,
                'user': {
                    'name': 'Fatima Zahra',
                    'email': '<EMAIL>'
                },
                'total': 234.25,
                'status': 'processing',
                'created_at': '2024-01-15T08:45:00Z',
                'items_count': 7
            }
        ]
        
        return jsonify({
            'success': True,
            'data': orders[:limit]
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# Route pour tester la connectivité API
@dashboard_bp.route('/api/admin/dashboard/health', methods=['GET'])
def health_check():
    """Vérifier l'état de l'API"""
    return jsonify({
        'success': True,
        'message': 'API Dashboard opérationnelle',
        'timestamp': datetime.now().isoformat()
    }), 200
