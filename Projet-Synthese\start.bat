@echo off
echo ========================================
echo    DEMARRAGE DU PROJET YUMMY
echo ========================================
echo.

echo [1/4] Verification des prerequis...
where php >nul 2>nul
if %errorlevel% neq 0 (
    echo ERREUR: PHP n'est pas installe ou pas dans le PATH
    echo Veuillez installer PHP et reessayer
    pause
    exit /b 1
)

where node >nul 2>nul
if %errorlevel% neq 0 (
    echo ERREUR: Node.js n'est pas installe ou pas dans le PATH
    echo Veuillez installer Node.js et reessayer
    pause
    exit /b 1
)

echo ✓ PHP detecte
echo ✓ Node.js detecte
echo.

echo [2/4] Demarrage du serveur backend Laravel...
cd backend
start "Backend Laravel" cmd /k "php artisan serve"
echo ✓ Serveur backend demarre sur http://localhost:8000
echo.

echo [3/4] Attente du demarrage du backend...
timeout /t 3 /nobreak >nul
echo.

echo [4/4] Demarrage du serveur frontend React...
cd ..\frontend
start "Frontend React" cmd /k "npm start"
echo ✓ Serveur frontend demarre sur http://localhost:3000
echo.

echo ========================================
echo    PROJET YUMMY DEMARRE AVEC SUCCES!
echo ========================================
echo.
echo Backend:  http://localhost:8000
echo Frontend: http://localhost:3000
echo.
echo Les deux serveurs sont maintenant en cours d'execution.
echo Fermez cette fenetre quand vous avez termine.
echo.
pause
