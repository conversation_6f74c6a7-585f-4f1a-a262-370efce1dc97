{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Projet-Synthese\\\\frontend\\\\src\\\\pages\\\\Admin\\\\Dashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { toast } from 'react-toastify';\nimport { FaUsers, FaShoppingCart, FaDollarSign, FaBox, FaExclamationTriangle, FaSync, FaChartLine, FaClock } from 'react-icons/fa';\nimport { dashboardService } from '../../services/dashboardService';\nimport { Line, Doughnut, Bar } from 'react-chartjs-2';\nimport { Chart as ChartJS, CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend, ArcElement, BarElement } from 'chart.js';\n\n// Enregistrer les composants Chart.js\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend, ArcElement, BarElement);\nconst Dashboard = () => {\n  _s();\n  var _stats$overview, _stats$overview2, _stats$overview3, _stats$overview4, _stats$overview5, _stats$overview6, _stats$overview7, _stats$overview8, _stats$overview9, _stats$overview10, _stats$charts, _stats$charts$monthly, _stats$charts2, _stats$charts2$monthl, _stats$charts3, _stats$charts3$popula, _stats$charts4, _stats$charts4$popula, _stats$overview11, _stats$overview12, _stats$overview13;\n  const [stats, setStats] = useState(null);\n  const [chartData, setChartData] = useState(null);\n  const [recentActivity, setRecentActivity] = useState([]);\n  const [lowStockProducts, setLowStockProducts] = useState([]);\n  const [recentOrders, setRecentOrders] = useState([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [lastUpdated, setLastUpdated] = useState(new Date());\n  useEffect(() => {\n    fetchDashboardData();\n  }, []);\n  const fetchDashboardData = async () => {\n    try {\n      setIsLoading(true);\n\n      // Simuler des données si l'API n'est pas disponible\n      const mockStats = {\n        overview: {\n          total_orders: 125,\n          total_revenue: 15420.50,\n          total_users: 89,\n          total_products: 45,\n          orders_last_30_days: 32,\n          revenue_last_30_days: 4250.00,\n          new_users_last_30_days: 15,\n          active_promotions: 3,\n          low_stock_products: 2\n        },\n        charts: {\n          monthly_revenue: [{\n            month: 'Jan',\n            revenue: 2100\n          }, {\n            month: 'Fév',\n            revenue: 2800\n          }, {\n            month: 'Mar',\n            revenue: 3200\n          }, {\n            month: 'Avr',\n            revenue: 2900\n          }, {\n            month: 'Mai',\n            revenue: 3800\n          }, {\n            month: 'Jun',\n            revenue: 4250\n          }],\n          popular_categories: [{\n            name: 'Produits Grillés',\n            total_sold: 145\n          }, {\n            name: 'Non Grillés',\n            total_sold: 98\n          }, {\n            name: 'Fromages',\n            total_sold: 67\n          }, {\n            name: 'Boissons',\n            total_sold: 89\n          }]\n        },\n        top_products: [{\n          name: 'Poulet Grillé Entier',\n          total_sold: 45\n        }, {\n          name: 'Salade César',\n          total_sold: 32\n        }, {\n          name: 'Plateau de Fromages',\n          total_sold: 28\n        }, {\n          name: 'Brochettes de Bœuf',\n          total_sold: 25\n        }, {\n          name: 'Jus d\\'Orange Frais',\n          total_sold: 22\n        }]\n      };\n\n      // En mode démonstration, utiliser directement les données mockées\n      // L'API sera automatiquement utilisée quand le backend sera disponible\n      setStats(mockStats);\n      setLastUpdated(new Date());\n    } catch (error) {\n      toast.error('Erreur lors du chargement des données du dashboard');\n      console.error('Erreur dashboard:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('fr-MA', {\n      style: 'currency',\n      currency: 'MAD'\n    }).format(amount);\n  };\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-center items-center h-64\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-gray-900\",\n          children: \"Tableau de bord\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-500\",\n          children: [\"Derni\\xE8re mise \\xE0 jour: \", lastUpdated.toLocaleString('fr-FR')]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: fetchDashboardData,\n        className: \"flex items-center space-x-2 px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors\",\n        disabled: isLoading,\n        children: [/*#__PURE__*/_jsxDEV(FaSync, {\n          className: `${isLoading ? 'animate-spin' : ''}`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Actualiser\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(StatsCard, {\n        title: \"Total Commandes\",\n        value: (stats === null || stats === void 0 ? void 0 : (_stats$overview = stats.overview) === null || _stats$overview === void 0 ? void 0 : _stats$overview.total_orders) || 0,\n        change: `+${(stats === null || stats === void 0 ? void 0 : (_stats$overview2 = stats.overview) === null || _stats$overview2 === void 0 ? void 0 : _stats$overview2.orders_last_30_days) || 0} ce mois`,\n        icon: /*#__PURE__*/_jsxDEV(FaShoppingCart, {\n          className: \"text-xl\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 17\n        }, this),\n        color: \"blue\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatsCard, {\n        title: \"Revenus Total\",\n        value: formatCurrency((stats === null || stats === void 0 ? void 0 : (_stats$overview3 = stats.overview) === null || _stats$overview3 === void 0 ? void 0 : _stats$overview3.total_revenue) || 0),\n        change: `+${formatCurrency((stats === null || stats === void 0 ? void 0 : (_stats$overview4 = stats.overview) === null || _stats$overview4 === void 0 ? void 0 : _stats$overview4.revenue_last_30_days) || 0)} ce mois`,\n        icon: /*#__PURE__*/_jsxDEV(FaDollarSign, {\n          className: \"text-xl\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 17\n        }, this),\n        color: \"green\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatsCard, {\n        title: \"Clients\",\n        value: (stats === null || stats === void 0 ? void 0 : (_stats$overview5 = stats.overview) === null || _stats$overview5 === void 0 ? void 0 : _stats$overview5.total_users) || 0,\n        change: `+${(stats === null || stats === void 0 ? void 0 : (_stats$overview6 = stats.overview) === null || _stats$overview6 === void 0 ? void 0 : _stats$overview6.new_users_last_30_days) || 0} nouveaux`,\n        icon: /*#__PURE__*/_jsxDEV(FaUsers, {\n          className: \"text-xl\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 17\n        }, this),\n        color: \"purple\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatsCard, {\n        title: \"Produits\",\n        value: (stats === null || stats === void 0 ? void 0 : (_stats$overview7 = stats.overview) === null || _stats$overview7 === void 0 ? void 0 : _stats$overview7.total_products) || 0,\n        change: (stats === null || stats === void 0 ? void 0 : (_stats$overview8 = stats.overview) === null || _stats$overview8 === void 0 ? void 0 : _stats$overview8.low_stock_products) > 0 ? `${stats.overview.low_stock_products} en rupture` : 'Stock suffisant',\n        icon: /*#__PURE__*/_jsxDEV(FaBox, {\n          className: \"text-xl\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 17\n        }, this),\n        color: (stats === null || stats === void 0 ? void 0 : (_stats$overview9 = stats.overview) === null || _stats$overview9 === void 0 ? void 0 : _stats$overview9.low_stock_products) > 0 ? 'red' : 'orange'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 146,\n      columnNumber: 7\n    }, this), (stats === null || stats === void 0 ? void 0 : (_stats$overview10 = stats.overview) === null || _stats$overview10 === void 0 ? void 0 : _stats$overview10.low_stock_products) > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(FaExclamationTriangle, {\n          className: \"text-red-500 mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-red-800\",\n          children: [\"Attention: \", stats.overview.low_stock_products, \" produit(s) en rupture de stock\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 185,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(DashboardCharts, {\n      salesData: {\n        labels: (stats === null || stats === void 0 ? void 0 : (_stats$charts = stats.charts) === null || _stats$charts === void 0 ? void 0 : (_stats$charts$monthly = _stats$charts.monthly_revenue) === null || _stats$charts$monthly === void 0 ? void 0 : _stats$charts$monthly.map(item => item.month)) || [],\n        data: (stats === null || stats === void 0 ? void 0 : (_stats$charts2 = stats.charts) === null || _stats$charts2 === void 0 ? void 0 : (_stats$charts2$monthl = _stats$charts2.monthly_revenue) === null || _stats$charts2$monthl === void 0 ? void 0 : _stats$charts2$monthl.map(item => item.revenue)) || []\n      },\n      categoryData: {\n        labels: (stats === null || stats === void 0 ? void 0 : (_stats$charts3 = stats.charts) === null || _stats$charts3 === void 0 ? void 0 : (_stats$charts3$popula = _stats$charts3.popular_categories) === null || _stats$charts3$popula === void 0 ? void 0 : _stats$charts3$popula.map(item => item.name)) || [],\n        data: (stats === null || stats === void 0 ? void 0 : (_stats$charts4 = stats.charts) === null || _stats$charts4 === void 0 ? void 0 : (_stats$charts4$popula = _stats$charts4.popular_categories) === null || _stats$charts4$popula === void 0 ? void 0 : _stats$charts4$popula.map(item => item.total_sold)) || []\n      },\n      topProducts: (stats === null || stats === void 0 ? void 0 : stats.top_products) || []\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 xl:grid-cols-3 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"xl:col-span-2\",\n        children: /*#__PURE__*/_jsxDEV(RecentActivity, {\n          activities: recentActivity\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white p-4 lg:p-6 rounded-lg shadow-sm border\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-900 mb-4\",\n            children: \"Promotions actives\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-3xl font-bold text-primary-600\",\n              children: (stats === null || stats === void 0 ? void 0 : (_stats$overview11 = stats.overview) === null || _stats$overview11 === void 0 ? void 0 : _stats$overview11.active_promotions) || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-500\",\n              children: \"promotions en cours\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white p-4 lg:p-6 rounded-lg shadow-sm border\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-900 mb-4\",\n            children: \"Performance\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Taux de conversion\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium\",\n                children: \"12.5%\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Panier moyen\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium\",\n                children: formatCurrency(((stats === null || stats === void 0 ? void 0 : (_stats$overview12 = stats.overview) === null || _stats$overview12 === void 0 ? void 0 : _stats$overview12.total_revenue) || 0) / ((stats === null || stats === void 0 ? void 0 : (_stats$overview13 = stats.overview) === null || _stats$overview13 === void 0 ? void 0 : _stats$overview13.total_orders) || 1))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Satisfaction client\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium\",\n                children: \"4.2/5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 209,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 126,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"18t/b74kzNMz6xcaDL3P7WHfDG0=\");\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "toast", "FaUsers", "FaShoppingCart", "FaDollarSign", "FaBox", "FaExclamationTriangle", "FaSync", "FaChartLine", "FaClock", "dashboardService", "Line", "Doughnut", "Bar", "Chart", "ChartJS", "CategoryScale", "LinearScale", "PointElement", "LineElement", "Title", "<PERSON><PERSON><PERSON>", "Legend", "ArcElement", "BarElement", "jsxDEV", "_jsxDEV", "register", "Dashboard", "_s", "_stats$overview", "_stats$overview2", "_stats$overview3", "_stats$overview4", "_stats$overview5", "_stats$overview6", "_stats$overview7", "_stats$overview8", "_stats$overview9", "_stats$overview10", "_stats$charts", "_stats$charts$monthly", "_stats$charts2", "_stats$charts2$monthl", "_stats$charts3", "_stats$charts3$popula", "_stats$charts4", "_stats$charts4$popula", "_stats$overview11", "_stats$overview12", "_stats$overview13", "stats", "setStats", "chartData", "setChartData", "recentActivity", "setRecentActivity", "lowStockProducts", "setLowStockProducts", "recentOrders", "setRecentOrders", "isLoading", "setIsLoading", "lastUpdated", "setLastUpdated", "Date", "fetchDashboardData", "mockStats", "overview", "total_orders", "total_revenue", "total_users", "total_products", "orders_last_30_days", "revenue_last_30_days", "new_users_last_30_days", "active_promotions", "low_stock_products", "charts", "monthly_revenue", "month", "revenue", "popular_categories", "name", "total_sold", "top_products", "error", "console", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "format", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "toLocaleString", "onClick", "disabled", "StatsCard", "title", "value", "change", "icon", "color", "DashboardCharts", "salesData", "labels", "map", "item", "data", "categoryData", "topProducts", "RecentActivity", "activities", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet-Synthese/frontend/src/pages/Admin/Dashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { toast } from 'react-toastify';\nimport {\n  FaUsers,\n  FaShoppingCart,\n  FaDollarSign,\n  FaBox,\n  FaExclamationTriangle,\n  FaSync,\n  FaChartLine,\n  FaClock\n} from 'react-icons/fa';\nimport { dashboardService } from '../../services/dashboardService';\nimport { Line, Doughnut, Bar } from 'react-chartjs-2';\nimport {\n  Chart as ChartJS,\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  Title,\n  Tooltip,\n  Legend,\n  ArcElement,\n  BarElement,\n} from 'chart.js';\n\n\n// Enregistrer les composants Chart.js\nChartJS.register(\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  Title,\n  Tooltip,\n  Legend,\n  ArcElement,\n  BarElement\n);\n\nconst Dashboard = () => {\n  const [stats, setStats] = useState(null);\n  const [chartData, setChartData] = useState(null);\n  const [recentActivity, setRecentActivity] = useState([]);\n  const [lowStockProducts, setLowStockProducts] = useState([]);\n  const [recentOrders, setRecentOrders] = useState([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [lastUpdated, setLastUpdated] = useState(new Date());\n\n  useEffect(() => {\n    fetchDashboardData();\n  }, []);\n\n  const fetchDashboardData = async () => {\n    try {\n      setIsLoading(true);\n\n      // Simuler des données si l'API n'est pas disponible\n      const mockStats = {\n        overview: {\n          total_orders: 125,\n          total_revenue: 15420.50,\n          total_users: 89,\n          total_products: 45,\n          orders_last_30_days: 32,\n          revenue_last_30_days: 4250.00,\n          new_users_last_30_days: 15,\n          active_promotions: 3,\n          low_stock_products: 2\n        },\n        charts: {\n          monthly_revenue: [\n            { month: 'Jan', revenue: 2100 },\n            { month: 'Fév', revenue: 2800 },\n            { month: 'Mar', revenue: 3200 },\n            { month: 'Avr', revenue: 2900 },\n            { month: 'Mai', revenue: 3800 },\n            { month: 'Jun', revenue: 4250 }\n          ],\n          popular_categories: [\n            { name: 'Produits Grillés', total_sold: 145 },\n            { name: 'Non Grillés', total_sold: 98 },\n            { name: 'Fromages', total_sold: 67 },\n            { name: 'Boissons', total_sold: 89 }\n          ]\n        },\n        top_products: [\n          { name: 'Poulet Grillé Entier', total_sold: 45 },\n          { name: 'Salade César', total_sold: 32 },\n          { name: 'Plateau de Fromages', total_sold: 28 },\n          { name: 'Brochettes de Bœuf', total_sold: 25 },\n          { name: 'Jus d\\'Orange Frais', total_sold: 22 }\n        ]\n      };\n\n      // En mode démonstration, utiliser directement les données mockées\n      // L'API sera automatiquement utilisée quand le backend sera disponible\n      setStats(mockStats);\n\n      setLastUpdated(new Date());\n    } catch (error) {\n      toast.error('Erreur lors du chargement des données du dashboard');\n      console.error('Erreur dashboard:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const formatCurrency = (amount) => {\n    return new Intl.NumberFormat('fr-MA', {\n      style: 'currency',\n      currency: 'MAD'\n    }).format(amount);\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"flex justify-center items-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* En-tête */}\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">Tableau de bord</h1>\n          <p className=\"text-sm text-gray-500\">\n            Dernière mise à jour: {lastUpdated.toLocaleString('fr-FR')}\n          </p>\n        </div>\n        <button\n          onClick={fetchDashboardData}\n          className=\"flex items-center space-x-2 px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors\"\n          disabled={isLoading}\n        >\n          <FaSync className={`${isLoading ? 'animate-spin' : ''}`} />\n          <span>Actualiser</span>\n        </button>\n      </div>\n\n      {/* Statistiques principales */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        <StatsCard\n          title=\"Total Commandes\"\n          value={stats?.overview?.total_orders || 0}\n          change={`+${stats?.overview?.orders_last_30_days || 0} ce mois`}\n          icon={<FaShoppingCart className=\"text-xl\" />}\n          color=\"blue\"\n        />\n\n        <StatsCard\n          title=\"Revenus Total\"\n          value={formatCurrency(stats?.overview?.total_revenue || 0)}\n          change={`+${formatCurrency(stats?.overview?.revenue_last_30_days || 0)} ce mois`}\n          icon={<FaDollarSign className=\"text-xl\" />}\n          color=\"green\"\n        />\n\n        <StatsCard\n          title=\"Clients\"\n          value={stats?.overview?.total_users || 0}\n          change={`+${stats?.overview?.new_users_last_30_days || 0} nouveaux`}\n          icon={<FaUsers className=\"text-xl\" />}\n          color=\"purple\"\n        />\n\n        <StatsCard\n          title=\"Produits\"\n          value={stats?.overview?.total_products || 0}\n          change={stats?.overview?.low_stock_products > 0 ?\n            `${stats.overview.low_stock_products} en rupture` :\n            'Stock suffisant'\n          }\n          icon={<FaBox className=\"text-xl\" />}\n          color={stats?.overview?.low_stock_products > 0 ? 'red' : 'orange'}\n        />\n      </div>\n\n      {/* Alertes */}\n      {stats?.overview?.low_stock_products > 0 && (\n        <div className=\"bg-red-50 border border-red-200 rounded-lg p-4\">\n          <div className=\"flex items-center\">\n            <FaExclamationTriangle className=\"text-red-500 mr-2\" />\n            <span className=\"text-red-800\">\n              Attention: {stats.overview.low_stock_products} produit(s) en rupture de stock\n            </span>\n          </div>\n        </div>\n      )}\n\n      {/* Graphiques */}\n      <DashboardCharts\n        salesData={{\n          labels: stats?.charts?.monthly_revenue?.map(item => item.month) || [],\n          data: stats?.charts?.monthly_revenue?.map(item => item.revenue) || []\n        }}\n        categoryData={{\n          labels: stats?.charts?.popular_categories?.map(item => item.name) || [],\n          data: stats?.charts?.popular_categories?.map(item => item.total_sold) || []\n        }}\n        topProducts={stats?.top_products || []}\n      />\n\n      {/* Section inférieure */}\n      <div className=\"grid grid-cols-1 xl:grid-cols-3 gap-6\">\n        {/* Activités récentes */}\n        <div className=\"xl:col-span-2\">\n          <RecentActivity activities={recentActivity} />\n        </div>\n\n        {/* Statistiques supplémentaires */}\n        <div className=\"space-y-6\">\n          <div className=\"bg-white p-4 lg:p-6 rounded-lg shadow-sm border\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Promotions actives</h3>\n            <div className=\"text-center\">\n              <p className=\"text-3xl font-bold text-primary-600\">\n                {stats?.overview?.active_promotions || 0}\n              </p>\n              <p className=\"text-sm text-gray-500\">promotions en cours</p>\n            </div>\n          </div>\n\n          <div className=\"bg-white p-4 lg:p-6 rounded-lg shadow-sm border\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Performance</h3>\n            <div className=\"space-y-3\">\n              <div className=\"flex justify-between\">\n                <span className=\"text-sm text-gray-600\">Taux de conversion</span>\n                <span className=\"text-sm font-medium\">12.5%</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-sm text-gray-600\">Panier moyen</span>\n                <span className=\"text-sm font-medium\">\n                  {formatCurrency((stats?.overview?.total_revenue || 0) / (stats?.overview?.total_orders || 1))}\n                </span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-sm text-gray-600\">Satisfaction client</span>\n                <span className=\"text-sm font-medium\">4.2/5</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Dashboard;\n\n\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SACEC,OAAO,EACPC,cAAc,EACdC,YAAY,EACZC,KAAK,EACLC,qBAAqB,EACrBC,MAAM,EACNC,WAAW,EACXC,OAAO,QACF,gBAAgB;AACvB,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,IAAI,EAAEC,QAAQ,EAAEC,GAAG,QAAQ,iBAAiB;AACrD,SACEC,KAAK,IAAIC,OAAO,EAChBC,aAAa,EACbC,WAAW,EACXC,YAAY,EACZC,WAAW,EACXC,KAAK,EACLC,OAAO,EACPC,MAAM,EACNC,UAAU,EACVC,UAAU,QACL,UAAU;;AAGjB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACAX,OAAO,CAACY,QAAQ,CACdX,aAAa,EACbC,WAAW,EACXC,YAAY,EACZC,WAAW,EACXC,KAAK,EACLC,OAAO,EACPC,MAAM,EACNC,UAAU,EACVC,UACF,CAAC;AAED,MAAMI,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,eAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,iBAAA,EAAAC,aAAA,EAAAC,qBAAA,EAAAC,cAAA,EAAAC,qBAAA,EAAAC,cAAA,EAAAC,qBAAA,EAAAC,cAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,iBAAA,EAAAC,iBAAA;EACtB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGrD,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACsD,SAAS,EAAEC,YAAY,CAAC,GAAGvD,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACwD,cAAc,EAAEC,iBAAiB,CAAC,GAAGzD,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC0D,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3D,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAAC4D,YAAY,EAAEC,eAAe,CAAC,GAAG7D,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC8D,SAAS,EAAEC,YAAY,CAAC,GAAG/D,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACgE,WAAW,EAAEC,cAAc,CAAC,GAAGjE,QAAQ,CAAC,IAAIkE,IAAI,CAAC,CAAC,CAAC;EAE1DjE,SAAS,CAAC,MAAM;IACdkE,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACFJ,YAAY,CAAC,IAAI,CAAC;;MAElB;MACA,MAAMK,SAAS,GAAG;QAChBC,QAAQ,EAAE;UACRC,YAAY,EAAE,GAAG;UACjBC,aAAa,EAAE,QAAQ;UACvBC,WAAW,EAAE,EAAE;UACfC,cAAc,EAAE,EAAE;UAClBC,mBAAmB,EAAE,EAAE;UACvBC,oBAAoB,EAAE,OAAO;UAC7BC,sBAAsB,EAAE,EAAE;UAC1BC,iBAAiB,EAAE,CAAC;UACpBC,kBAAkB,EAAE;QACtB,CAAC;QACDC,MAAM,EAAE;UACNC,eAAe,EAAE,CACf;YAAEC,KAAK,EAAE,KAAK;YAAEC,OAAO,EAAE;UAAK,CAAC,EAC/B;YAAED,KAAK,EAAE,KAAK;YAAEC,OAAO,EAAE;UAAK,CAAC,EAC/B;YAAED,KAAK,EAAE,KAAK;YAAEC,OAAO,EAAE;UAAK,CAAC,EAC/B;YAAED,KAAK,EAAE,KAAK;YAAEC,OAAO,EAAE;UAAK,CAAC,EAC/B;YAAED,KAAK,EAAE,KAAK;YAAEC,OAAO,EAAE;UAAK,CAAC,EAC/B;YAAED,KAAK,EAAE,KAAK;YAAEC,OAAO,EAAE;UAAK,CAAC,CAChC;UACDC,kBAAkB,EAAE,CAClB;YAAEC,IAAI,EAAE,kBAAkB;YAAEC,UAAU,EAAE;UAAI,CAAC,EAC7C;YAAED,IAAI,EAAE,aAAa;YAAEC,UAAU,EAAE;UAAG,CAAC,EACvC;YAAED,IAAI,EAAE,UAAU;YAAEC,UAAU,EAAE;UAAG,CAAC,EACpC;YAAED,IAAI,EAAE,UAAU;YAAEC,UAAU,EAAE;UAAG,CAAC;QAExC,CAAC;QACDC,YAAY,EAAE,CACZ;UAAEF,IAAI,EAAE,sBAAsB;UAAEC,UAAU,EAAE;QAAG,CAAC,EAChD;UAAED,IAAI,EAAE,cAAc;UAAEC,UAAU,EAAE;QAAG,CAAC,EACxC;UAAED,IAAI,EAAE,qBAAqB;UAAEC,UAAU,EAAE;QAAG,CAAC,EAC/C;UAAED,IAAI,EAAE,oBAAoB;UAAEC,UAAU,EAAE;QAAG,CAAC,EAC9C;UAAED,IAAI,EAAE,qBAAqB;UAAEC,UAAU,EAAE;QAAG,CAAC;MAEnD,CAAC;;MAED;MACA;MACAhC,QAAQ,CAACe,SAAS,CAAC;MAEnBH,cAAc,CAAC,IAAIC,IAAI,CAAC,CAAC,CAAC;IAC5B,CAAC,CAAC,OAAOqB,KAAK,EAAE;MACdrF,KAAK,CAACqF,KAAK,CAAC,oDAAoD,CAAC;MACjEC,OAAO,CAACD,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;IAC3C,CAAC,SAAS;MACRxB,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAM0B,cAAc,GAAIC,MAAM,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAACC,MAAM,CAACL,MAAM,CAAC;EACnB,CAAC;EAED,IAAI5B,SAAS,EAAE;IACb,oBACEnC,OAAA;MAAKqE,SAAS,EAAC,uCAAuC;MAAAC,QAAA,eACpDtE,OAAA;QAAKqE,SAAS,EAAC;MAAmE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtF,CAAC;EAEV;EAEA,oBACE1E,OAAA;IAAKqE,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBtE,OAAA;MAAKqE,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChDtE,OAAA;QAAAsE,QAAA,gBACEtE,OAAA;UAAIqE,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrE1E,OAAA;UAAGqE,SAAS,EAAC,uBAAuB;UAAAC,QAAA,GAAC,8BACb,EAACjC,WAAW,CAACsC,cAAc,CAAC,OAAO,CAAC;QAAA;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACN1E,OAAA;QACE4E,OAAO,EAAEpC,kBAAmB;QAC5B6B,SAAS,EAAC,mHAAmH;QAC7HQ,QAAQ,EAAE1C,SAAU;QAAAmC,QAAA,gBAEpBtE,OAAA,CAACnB,MAAM;UAACwF,SAAS,EAAE,GAAGlC,SAAS,GAAG,cAAc,GAAG,EAAE;QAAG;UAAAoC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3D1E,OAAA;UAAAsE,QAAA,EAAM;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGN1E,OAAA;MAAKqE,SAAS,EAAC,sDAAsD;MAAAC,QAAA,gBACnEtE,OAAA,CAAC8E,SAAS;QACRC,KAAK,EAAC,iBAAiB;QACvBC,KAAK,EAAE,CAAAvD,KAAK,aAALA,KAAK,wBAAArB,eAAA,GAALqB,KAAK,CAAEiB,QAAQ,cAAAtC,eAAA,uBAAfA,eAAA,CAAiBuC,YAAY,KAAI,CAAE;QAC1CsC,MAAM,EAAE,IAAI,CAAAxD,KAAK,aAALA,KAAK,wBAAApB,gBAAA,GAALoB,KAAK,CAAEiB,QAAQ,cAAArC,gBAAA,uBAAfA,gBAAA,CAAiB0C,mBAAmB,KAAI,CAAC,UAAW;QAChEmC,IAAI,eAAElF,OAAA,CAACvB,cAAc;UAAC4F,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC7CS,KAAK,EAAC;MAAM;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC,eAEF1E,OAAA,CAAC8E,SAAS;QACRC,KAAK,EAAC,eAAe;QACrBC,KAAK,EAAElB,cAAc,CAAC,CAAArC,KAAK,aAALA,KAAK,wBAAAnB,gBAAA,GAALmB,KAAK,CAAEiB,QAAQ,cAAApC,gBAAA,uBAAfA,gBAAA,CAAiBsC,aAAa,KAAI,CAAC,CAAE;QAC3DqC,MAAM,EAAE,IAAInB,cAAc,CAAC,CAAArC,KAAK,aAALA,KAAK,wBAAAlB,gBAAA,GAALkB,KAAK,CAAEiB,QAAQ,cAAAnC,gBAAA,uBAAfA,gBAAA,CAAiByC,oBAAoB,KAAI,CAAC,CAAC,UAAW;QACjFkC,IAAI,eAAElF,OAAA,CAACtB,YAAY;UAAC2F,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC3CS,KAAK,EAAC;MAAO;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC,eAEF1E,OAAA,CAAC8E,SAAS;QACRC,KAAK,EAAC,SAAS;QACfC,KAAK,EAAE,CAAAvD,KAAK,aAALA,KAAK,wBAAAjB,gBAAA,GAALiB,KAAK,CAAEiB,QAAQ,cAAAlC,gBAAA,uBAAfA,gBAAA,CAAiBqC,WAAW,KAAI,CAAE;QACzCoC,MAAM,EAAE,IAAI,CAAAxD,KAAK,aAALA,KAAK,wBAAAhB,gBAAA,GAALgB,KAAK,CAAEiB,QAAQ,cAAAjC,gBAAA,uBAAfA,gBAAA,CAAiBwC,sBAAsB,KAAI,CAAC,WAAY;QACpEiC,IAAI,eAAElF,OAAA,CAACxB,OAAO;UAAC6F,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACtCS,KAAK,EAAC;MAAQ;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC,eAEF1E,OAAA,CAAC8E,SAAS;QACRC,KAAK,EAAC,UAAU;QAChBC,KAAK,EAAE,CAAAvD,KAAK,aAALA,KAAK,wBAAAf,gBAAA,GAALe,KAAK,CAAEiB,QAAQ,cAAAhC,gBAAA,uBAAfA,gBAAA,CAAiBoC,cAAc,KAAI,CAAE;QAC5CmC,MAAM,EAAE,CAAAxD,KAAK,aAALA,KAAK,wBAAAd,gBAAA,GAALc,KAAK,CAAEiB,QAAQ,cAAA/B,gBAAA,uBAAfA,gBAAA,CAAiBwC,kBAAkB,IAAG,CAAC,GAC7C,GAAG1B,KAAK,CAACiB,QAAQ,CAACS,kBAAkB,aAAa,GACjD,iBACD;QACD+B,IAAI,eAAElF,OAAA,CAACrB,KAAK;UAAC0F,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACpCS,KAAK,EAAE,CAAA1D,KAAK,aAALA,KAAK,wBAAAb,gBAAA,GAALa,KAAK,CAAEiB,QAAQ,cAAA9B,gBAAA,uBAAfA,gBAAA,CAAiBuC,kBAAkB,IAAG,CAAC,GAAG,KAAK,GAAG;MAAS;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAGL,CAAAjD,KAAK,aAALA,KAAK,wBAAAZ,iBAAA,GAALY,KAAK,CAAEiB,QAAQ,cAAA7B,iBAAA,uBAAfA,iBAAA,CAAiBsC,kBAAkB,IAAG,CAAC,iBACtCnD,OAAA;MAAKqE,SAAS,EAAC,gDAAgD;MAAAC,QAAA,eAC7DtE,OAAA;QAAKqE,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCtE,OAAA,CAACpB,qBAAqB;UAACyF,SAAS,EAAC;QAAmB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACvD1E,OAAA;UAAMqE,SAAS,EAAC,cAAc;UAAAC,QAAA,GAAC,aAClB,EAAC7C,KAAK,CAACiB,QAAQ,CAACS,kBAAkB,EAAC,iCAChD;QAAA;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGD1E,OAAA,CAACoF,eAAe;MACdC,SAAS,EAAE;QACTC,MAAM,EAAE,CAAA7D,KAAK,aAALA,KAAK,wBAAAX,aAAA,GAALW,KAAK,CAAE2B,MAAM,cAAAtC,aAAA,wBAAAC,qBAAA,GAAbD,aAAA,CAAeuC,eAAe,cAAAtC,qBAAA,uBAA9BA,qBAAA,CAAgCwE,GAAG,CAACC,IAAI,IAAIA,IAAI,CAAClC,KAAK,CAAC,KAAI,EAAE;QACrEmC,IAAI,EAAE,CAAAhE,KAAK,aAALA,KAAK,wBAAAT,cAAA,GAALS,KAAK,CAAE2B,MAAM,cAAApC,cAAA,wBAAAC,qBAAA,GAAbD,cAAA,CAAeqC,eAAe,cAAApC,qBAAA,uBAA9BA,qBAAA,CAAgCsE,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACjC,OAAO,CAAC,KAAI;MACrE,CAAE;MACFmC,YAAY,EAAE;QACZJ,MAAM,EAAE,CAAA7D,KAAK,aAALA,KAAK,wBAAAP,cAAA,GAALO,KAAK,CAAE2B,MAAM,cAAAlC,cAAA,wBAAAC,qBAAA,GAAbD,cAAA,CAAesC,kBAAkB,cAAArC,qBAAA,uBAAjCA,qBAAA,CAAmCoE,GAAG,CAACC,IAAI,IAAIA,IAAI,CAAC/B,IAAI,CAAC,KAAI,EAAE;QACvEgC,IAAI,EAAE,CAAAhE,KAAK,aAALA,KAAK,wBAAAL,cAAA,GAALK,KAAK,CAAE2B,MAAM,cAAAhC,cAAA,wBAAAC,qBAAA,GAAbD,cAAA,CAAeoC,kBAAkB,cAAAnC,qBAAA,uBAAjCA,qBAAA,CAAmCkE,GAAG,CAACC,IAAI,IAAIA,IAAI,CAAC9B,UAAU,CAAC,KAAI;MAC3E,CAAE;MACFiC,WAAW,EAAE,CAAAlE,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEkC,YAAY,KAAI;IAAG;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxC,CAAC,eAGF1E,OAAA;MAAKqE,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBAEpDtE,OAAA;QAAKqE,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC5BtE,OAAA,CAAC4F,cAAc;UAACC,UAAU,EAAEhE;QAAe;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC,eAGN1E,OAAA;QAAKqE,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBtE,OAAA;UAAKqE,SAAS,EAAC,iDAAiD;UAAAC,QAAA,gBAC9DtE,OAAA;YAAIqE,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChF1E,OAAA;YAAKqE,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BtE,OAAA;cAAGqE,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAC/C,CAAA7C,KAAK,aAALA,KAAK,wBAAAH,iBAAA,GAALG,KAAK,CAAEiB,QAAQ,cAAApB,iBAAA,uBAAfA,iBAAA,CAAiB4B,iBAAiB,KAAI;YAAC;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC,eACJ1E,OAAA;cAAGqE,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN1E,OAAA;UAAKqE,SAAS,EAAC,iDAAiD;UAAAC,QAAA,gBAC9DtE,OAAA;YAAIqE,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzE1E,OAAA;YAAKqE,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBtE,OAAA;cAAKqE,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACnCtE,OAAA;gBAAMqE,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACjE1E,OAAA;gBAAMqE,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC,eACN1E,OAAA;cAAKqE,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACnCtE,OAAA;gBAAMqE,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC3D1E,OAAA;gBAAMqE,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAClCR,cAAc,CAAC,CAAC,CAAArC,KAAK,aAALA,KAAK,wBAAAF,iBAAA,GAALE,KAAK,CAAEiB,QAAQ,cAAAnB,iBAAA,uBAAfA,iBAAA,CAAiBqB,aAAa,KAAI,CAAC,KAAK,CAAAnB,KAAK,aAALA,KAAK,wBAAAD,iBAAA,GAALC,KAAK,CAAEiB,QAAQ,cAAAlB,iBAAA,uBAAfA,iBAAA,CAAiBmB,YAAY,KAAI,CAAC,CAAC;cAAC;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACN1E,OAAA;cAAKqE,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACnCtE,OAAA;gBAAMqE,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAClE1E,OAAA;gBAAMqE,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvE,EAAA,CAhNID,SAAS;AAAA4F,EAAA,GAAT5F,SAAS;AAkNf,eAAeA,SAAS;AAAC,IAAA4F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}