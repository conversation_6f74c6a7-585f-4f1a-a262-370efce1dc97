import axios from 'axios';

const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

// Service pour gérer les statistiques du dashboard
export const dashboardService = {
  // Récupérer les statistiques générales
  getStats: async (token) => {
    try {
      const response = await axios.get(`${API_URL}/api/dashboard/stats`, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des statistiques, utilisation des données locales:', error);

      // Retourner des données simulées en cas d'erreur
      return {
        status: 'success',
        data: {
          overview: {
            total_orders: 125,
            total_revenue: 15420.50,
            total_users: 89,
            total_products: 45,
            orders_last_30_days: 32,
            revenue_last_30_days: 4250.00,
            new_users_last_30_days: 15,
            active_promotions: 3,
            low_stock_products: 2
          },
          charts: {
            monthly_revenue: [
              { month: 'Jan', revenue: 2100 },
              { month: 'Fév', revenue: 2800 },
              { month: 'Mar', revenue: 3200 },
              { month: 'Avr', revenue: 2900 },
              { month: 'Mai', revenue: 3800 },
              { month: 'Jun', revenue: 4250 }
            ],
            popular_categories: [
              { name: 'Produits Grillés', total_sold: 145 },
              { name: 'Non Grillés', total_sold: 98 },
              { name: 'Fromages', total_sold: 67 },
              { name: 'Boissons', total_sold: 89 }
            ]
          },
          top_products: [
            { name: 'Poulet Grillé Entier', total_sold: 45 },
            { name: 'Salade César', total_sold: 32 },
            { name: 'Plateau de Fromages', total_sold: 28 },
            { name: 'Brochettes de Bœuf', total_sold: 25 },
            { name: 'Jus d\'Orange Frais', total_sold: 22 }
          ]
        }
      };
    }
  },

  // Récupérer les activités récentes
  getRecentActivity: async (token) => {
    try {
      const response = await axios.get(`${API_URL}/api/dashboard/activity`, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des activités récentes, utilisation des données locales:', error);

      // Retourner des activités simulées
      return {
        status: 'success',
        data: [
          {
            id: 1,
            type: 'order',
            user: 'Marie Dubois',
            amount: 45.99,
            created_at: new Date(Date.now() - 1000 * 60 * 15).toISOString()
          },
          {
            id: 2,
            type: 'user',
            name: 'Ahmed Hassan',
            email: '<EMAIL>',
            created_at: new Date(Date.now() - 1000 * 60 * 30).toISOString()
          },
          {
            id: 3,
            type: 'review',
            user: 'John Doe',
            product: 'Poulet Grillé Entier',
            rating: 5,
            created_at: new Date(Date.now() - 1000 * 60 * 45).toISOString()
          },
          {
            id: 4,
            type: 'order',
            user: 'Jane Smith',
            amount: 28.50,
            created_at: new Date(Date.now() - 1000 * 60 * 60).toISOString()
          }
        ]
      };
    }
  }
};

export default dashboardService;
