<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Lara<PERSON>\Sanctum\PersonalAccessToken;

class HandleAuth
{
    public function handle(Request $request, Closure $next)
    {
        if (!$request->bearerToken()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Token non fourni',
                'code' => 'TOKEN_NOT_PROVIDED'
            ], 401);
        }

        $token = PersonalAccessToken::findToken($request->bearerToken());

        if (!$token || !$token->tokenable) {
            return response()->json([
                'status' => 'error',
                'message' => 'Token invalide ou expiré',
                'code' => 'INVALID_TOKEN'
            ], 401);
        }

        // Vérifier si le token n'est pas expiré (si vous avez configuré une expiration)
        if (config('sanctum.expiration') && $token->created_at->lte(now()->subMinutes(config('sanctum.expiration')))) {
            $token->delete();
            return response()->json([
                'status' => 'error',
                'message' => 'Token expiré',
                'code' => 'TOKEN_EXPIRED'
            ], 401);
        }

        return $next($request);
    }
}

