<?php

// Test simple de l'API
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Données de test pour les produits
$products = [
    [
        'id' => 1,
        'name' => 'Poulet Grillé Entier',
        'description' => 'Poulet entier grillé aux herbes de Provence, tendre et savoureux',
        'short_description' => 'Poulet entier grillé aux herbes',
        'price' => 25.99,
        'sale_price' => null,
        'image' => '/images/products/poulet-grille.jpg',
        'category' => [
            'id' => 1,
            'name' => 'Produits Grillés'
        ],
        'is_featured' => true,
        'is_active' => true,
        'stock_quantity' => 50
    ],
    [
        'id' => 2,
        'name' => 'Brochettes de Bœuf',
        'description' => 'Brochettes de bœuf marinées et grillées, accompagnées de légumes',
        'short_description' => 'Brochettes de bœuf marinées',
        'price' => 18.50,
        'sale_price' => 16.99,
        'image' => '/images/products/brochettes-boeuf.jpg',
        'category' => [
            'id' => 1,
            'name' => 'Produits Grillés'
        ],
        'is_featured' => true,
        'is_active' => true,
        'stock_quantity' => 30
    ],
    [
        'id' => 3,
        'name' => 'Salade César',
        'description' => 'Salade César classique avec croûtons, parmesan et sauce maison',
        'short_description' => 'Salade César classique',
        'price' => 12.50,
        'sale_price' => null,
        'image' => '/images/products/salade-cesar.jpg',
        'category' => [
            'id' => 2,
            'name' => 'Salades'
        ],
        'is_featured' => true,
        'is_active' => true,
        'stock_quantity' => 40
    ],
    [
        'id' => 4,
        'name' => 'Plateau de Fromages',
        'description' => 'Sélection de fromages artisanaux avec confiture et noix',
        'short_description' => 'Plateau de fromages artisanaux',
        'price' => 19.90,
        'sale_price' => null,
        'image' => '/images/products/plateau-fromages.jpg',
        'category' => [
            'id' => 3,
            'name' => 'Fromages'
        ],
        'is_featured' => true,
        'is_active' => true,
        'stock_quantity' => 15
    ],
    [
        'id' => 5,
        'name' => 'Jus d\'Orange Frais',
        'description' => 'Jus d\'orange fraîchement pressé, 100% naturel',
        'short_description' => 'Jus d\'orange fraîchement pressé',
        'price' => 4.50,
        'sale_price' => null,
        'image' => '/images/products/jus-orange.jpg',
        'category' => [
            'id' => 4,
            'name' => 'Boissons'
        ],
        'is_featured' => false,
        'is_active' => true,
        'stock_quantity' => 100
    ],
    [
        'id' => 6,
        'name' => 'Tiramisu Maison',
        'description' => 'Tiramisu traditionnel fait maison avec mascarpone et café',
        'short_description' => 'Tiramisu traditionnel maison',
        'price' => 8.50,
        'sale_price' => null,
        'image' => '/images/products/tiramisu.jpg',
        'category' => [
            'id' => 5,
            'name' => 'Desserts'
        ],
        'is_featured' => true,
        'is_active' => true,
        'stock_quantity' => 25
    ]
];

// Réponse JSON
echo json_encode([
    'status' => 'success',
    'data' => $products,
    'message' => 'Produits récupérés avec succès'
]);
?>
