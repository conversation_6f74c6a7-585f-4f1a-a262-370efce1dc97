<?php

namespace Database\Seeders;

use App\Models\Promotion;
use Illuminate\Database\Seeder;
use Carbon\Carbon;

class PromotionSeeder extends Seeder
{
    public function run(): void
    {
        $promotions = [
            [
                'name' => 'Bienvenue 10%',
                'code' => 'BIENVENUE10',
                'description' => 'Réduction de 10% pour les nouveaux clients',
                'type' => Promotion::TYPE_PERCENTAGE,
                'value' => 10.00,
                'minimum_amount' => 50.00,
                'maximum_discount' => null,
                'usage_limit' => 100,
                'used_count' => 15,
                'start_date' => Carbon::now()->subDays(30),
                'end_date' => Carbon::now()->addDays(30),
                'is_active' => true
            ],
            [
                'name' => 'Été 2024',
                'code' => 'ETE2024',
                'description' => 'Promotion d\'été - 15% de réduction',
                'type' => Promotion::TYPE_PERCENTAGE,
                'value' => 15.00,
                'minimum_amount' => 75.00,
                'maximum_discount' => 50.00,
                'usage_limit' => 200,
                'used_count' => 45,
                'start_date' => Carbon::now()->subDays(15),
                'end_date' => Carbon::now()->addDays(45),
                'is_active' => true
            ],
            [
                'name' => 'Réduction fixe 20$',
                'code' => 'FIXE20',
                'description' => 'Réduction fixe de 20$ sur votre commande',
                'type' => Promotion::TYPE_FIXED,
                'value' => 20.00,
                'minimum_amount' => 100.00,
                'maximum_discount' => null,
                'usage_limit' => 50,
                'used_count' => 8,
                'start_date' => Carbon::now()->subDays(10),
                'end_date' => Carbon::now()->addDays(20),
                'is_active' => true
            ],
            [
                'name' => 'Livraison gratuite',
                'code' => 'LIVRAISON0',
                'description' => 'Livraison gratuite pour toute commande',
                'type' => Promotion::TYPE_FREE_SHIPPING,
                'value' => 0.00,
                'minimum_amount' => 60.00,
                'maximum_discount' => null,
                'usage_limit' => null, // Illimité
                'used_count' => 23,
                'start_date' => Carbon::now()->subDays(5),
                'end_date' => Carbon::now()->addDays(60),
                'is_active' => true
            ],
            [
                'name' => 'Black Friday',
                'code' => 'BLACKFRIDAY',
                'description' => 'Méga promotion Black Friday - 25% de réduction',
                'type' => Promotion::TYPE_PERCENTAGE,
                'value' => 25.00,
                'minimum_amount' => 150.00,
                'maximum_discount' => 100.00,
                'usage_limit' => 500,
                'used_count' => 0,
                'start_date' => Carbon::now()->addDays(60),
                'end_date' => Carbon::now()->addDays(67),
                'is_active' => false // Pas encore active
            ],
            [
                'name' => 'Promotion expirée',
                'code' => 'EXPIRE',
                'description' => 'Promotion test expirée',
                'type' => Promotion::TYPE_PERCENTAGE,
                'value' => 20.00,
                'minimum_amount' => 30.00,
                'maximum_discount' => null,
                'usage_limit' => 10,
                'used_count' => 10, // Limite atteinte
                'start_date' => Carbon::now()->subDays(60),
                'end_date' => Carbon::now()->subDays(30),
                'is_active' => false
            ]
        ];

        foreach ($promotions as $promotion) {
            Promotion::create($promotion);
        }
    }
}
