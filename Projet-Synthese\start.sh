#!/bin/bash

echo "========================================"
echo "    DÉMARRAGE DU PROJET YUMMY"
echo "========================================"
echo

echo "[1/4] Vérification des prérequis..."

# Vérifier PHP
if ! command -v php &> /dev/null; then
    echo "ERREUR: PHP n'est pas installé ou pas dans le PATH"
    echo "Veuillez installer PHP et réessayer"
    exit 1
fi

# Vérifier Node.js
if ! command -v node &> /dev/null; then
    echo "ERREUR: Node.js n'est pas installé ou pas dans le PATH"
    echo "Veuillez installer Node.js et réessayer"
    exit 1
fi

echo "✓ PHP détecté"
echo "✓ Node.js détecté"
echo

echo "[2/4] Démarrage du serveur backend Laravel..."
cd backend

# Démarrer le backend en arrière-plan
php artisan serve &
BACKEND_PID=$!

echo "✓ Serveur backend démarré sur http://localhost:8000 (PID: $BACKEND_PID)"
echo

echo "[3/4] Attente du démarrage du backend..."
sleep 3
echo

echo "[4/4] Démarrage du serveur frontend React..."
cd ../frontend

# Démarrer le frontend en arrière-plan
npm start &
FRONTEND_PID=$!

echo "✓ Serveur frontend démarré sur http://localhost:3000 (PID: $FRONTEND_PID)"
echo

echo "========================================"
echo "    PROJET YUMMY DÉMARRÉ AVEC SUCCÈS!"
echo "========================================"
echo
echo "Backend:  http://localhost:8000"
echo "Frontend: http://localhost:3000"
echo
echo "Les deux serveurs sont maintenant en cours d'exécution."
echo
echo "Pour arrêter les serveurs :"
echo "kill $BACKEND_PID $FRONTEND_PID"
echo
echo "Ou utilisez Ctrl+C pour arrêter ce script et les serveurs."

# Fonction pour nettoyer les processus à la sortie
cleanup() {
    echo
    echo "Arrêt des serveurs..."
    kill $BACKEND_PID $FRONTEND_PID 2>/dev/null
    echo "Serveurs arrêtés."
    exit 0
}

# Capturer Ctrl+C pour nettoyer
trap cleanup SIGINT SIGTERM

# Attendre indéfiniment
wait
