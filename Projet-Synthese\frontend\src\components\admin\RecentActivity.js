import React from 'react';
import { 
  FaShoppingCart, 
  FaUsers, 
  FaStar, 
  FaEye,
  FaArrowUp,
  FaArrowDown 
} from 'react-icons/fa';

const RecentActivity = ({ activities = [] }) => {
  const getActivityIcon = (type) => {
    switch (type) {
      case 'order':
        return <FaShoppingCart className="text-blue-500" />;
      case 'user':
        return <FaUsers className="text-green-500" />;
      case 'review':
        return <FaStar className="text-yellow-500" />;
      default:
        return <FaEye className="text-gray-500" />;
    }
  };

  const getActivityText = (activity) => {
    switch (activity.type) {
      case 'order':
        return `Nouvelle commande de ${activity.user} - ${formatCurrency(activity.amount)}`;
      case 'user':
        return `Nouvel utilisateur: ${activity.name}`;
      case 'review':
        return `${activity.user} a noté ${activity.product} (${activity.rating}/5)`;
      default:
        return 'Activité inconnue';
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('fr-MA', {
      style: 'currency',
      currency: 'MAD'
    }).format(amount);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Données de test si aucune activité n'est fournie
  const defaultActivities = [
    {
      id: 1,
      type: 'order',
      user: 'Marie Dubois',
      amount: 45.99,
      created_at: new Date(Date.now() - 1000 * 60 * 15).toISOString() // Il y a 15 minutes
    },
    {
      id: 2,
      type: 'user',
      name: 'Ahmed Hassan',
      email: '<EMAIL>',
      created_at: new Date(Date.now() - 1000 * 60 * 30).toISOString() // Il y a 30 minutes
    },
    {
      id: 3,
      type: 'review',
      user: 'John Doe',
      product: 'Poulet Grillé Entier',
      rating: 5,
      created_at: new Date(Date.now() - 1000 * 60 * 45).toISOString() // Il y a 45 minutes
    },
    {
      id: 4,
      type: 'order',
      user: 'Jane Smith',
      amount: 28.50,
      created_at: new Date(Date.now() - 1000 * 60 * 60).toISOString() // Il y a 1 heure
    },
    {
      id: 5,
      type: 'review',
      user: 'Marie Dubois',
      product: 'Salade César',
      rating: 4,
      created_at: new Date(Date.now() - 1000 * 60 * 90).toISOString() // Il y a 1h30
    }
  ];

  const displayActivities = activities.length > 0 ? activities : defaultActivities;

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm border">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">Activités récentes</h3>
        <button className="text-sm text-primary-600 hover:text-primary-700">
          Voir tout
        </button>
      </div>
      
      <div className="space-y-4 max-h-80 overflow-y-auto">
        {displayActivities.map((activity, index) => (
          <div key={activity.id || index} className="flex items-start space-x-3 p-3 hover:bg-gray-50 rounded-lg transition-colors">
            <div className="flex-shrink-0 mt-1">
              {getActivityIcon(activity.type)}
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm text-gray-900">
                {getActivityText(activity)}
              </p>
              <p className="text-xs text-gray-500 mt-1">
                {formatDate(activity.created_at)}
              </p>
            </div>
            <div className="flex-shrink-0">
              {activity.type === 'order' && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  <FaArrowUp className="mr-1" />
                  Vente
                </span>
              )}
              {activity.type === 'user' && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  Nouveau
                </span>
              )}
              {activity.type === 'review' && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                  {activity.rating}/5
                </span>
              )}
            </div>
          </div>
        ))}
      </div>

      {displayActivities.length === 0 && (
        <div className="text-center py-8">
          <FaEye className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <p className="text-gray-500">Aucune activité récente</p>
        </div>
      )}
    </div>
  );
};

export default RecentActivity;
