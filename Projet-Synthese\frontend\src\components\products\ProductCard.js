import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';

const ProductCard = ({ product }) => {
  // Adapter les noms de propriétés selon votre API
  const {
    id,
    name,
    slug,
    description,
    price,
    sale_price,
    image,
    category
  } = product;

  // Calculer le pourcentage de réduction si un prix de vente est défini
  const discountPercentage = sale_price && price > sale_price
    ? Math.round(((price - sale_price) / price) * 100)
    : null;

  // Construire l'URL de l'image complète
  const imageUrl = image 
    ? `${process.env.REACT_APP_API_URL}/storage/${image}`
    : 'https://via.placeholder.com/300';

  return (
    <motion.div
      className="card overflow-hidden hover:shadow-lg transition-shadow"
      whileHover={{ y: -5 }}
      transition={{ duration: 0.3 }}
    >
      <Link to={`/products/${slug}`} className="block relative">
        {discountPercentage && (
          <div className="absolute top-0 right-0 bg-accent-500 text-white text-sm font-bold p-2 rounded-bl-lg">
            -{discountPercentage}%
          </div>
        )}
        <img 
          src={imageUrl} 
          alt={name} 
          className="w-full aspect-square object-cover"
        />
      </Link>
      
      <div className="p-4">
        <div className="text-sm text-gray-500 mb-1">
          {category?.name || 'Catégorie'}
        </div>
        
        <Link to={`/products/${slug}`} className="block">
          <h3 className="text-lg font-semibold text-gray-900 hover:text-primary-600 transition-colors">
            {name}
          </h3>
        </Link>
        
        <p className="text-gray-600 text-sm mt-1 line-clamp-2">
          {description}
        </p>
        
        <div className="flex items-center mt-3">
          {sale_price ? (
            <>
              <span className="text-lg font-bold text-primary-600">
                {sale_price.toFixed(2)} DH
              </span>
              <span className="text-sm text-gray-500 line-through ml-2">
                {price.toFixed(2)} DH
              </span>
            </>
          ) : (
            <span className="text-lg font-bold text-gray-900">
              {price.toFixed(2)} DH
            </span>
          )}
        </div>
        
        <div className="mt-4">
          <Link 
            to={`/products/${slug}`} 
            className="btn-primary w-full text-center"
          >
            Voir le produit
          </Link>
        </div>
      </div>
    </motion.div>
  );
};

export default ProductCard;

