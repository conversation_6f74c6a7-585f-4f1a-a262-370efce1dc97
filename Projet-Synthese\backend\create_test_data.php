<?php

require_once 'vendor/autoload.php';

use Illuminate\Database\Capsule\Manager as Capsule;
use Carbon\Carbon;

// Configuration de la base de données
$capsule = new Capsule;
$capsule->addConnection([
    'driver' => 'mysql',
    'host' => 'localhost',
    'database' => 'yummy_db',
    'username' => 'root',
    'password' => '',
    'charset' => 'utf8mb4',
    'collation' => 'utf8mb4_unicode_ci',
    'prefix' => '',
]);

$capsule->setAsGlobal();
$capsule->bootEloquent();

try {
    // Créer un utilisateur admin
    $adminExists = Capsule::table('users')->where('email', '<EMAIL>')->exists();
    if (!$adminExists) {
        Capsule::table('users')->insert([
            'name' => 'Admin YUMMY',
            'email' => '<EMAIL>',
            'email_verified_at' => Carbon::now(),
            'password' => password_hash('password123', PASSWORD_DEFAULT),
            'role' => 'admin',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ]);
        echo "Utilisateur admin créé\n";
    }

    // Créer des catégories
    $categories = [
        [
            'name' => 'Produits Grillés',
            'slug' => 'produits-grilles',
            'description' => 'Délicieux produits grillés à la perfection',
            'is_active' => true,
            'sort_order' => 1,
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ],
        [
            'name' => 'Produits Non Grillés',
            'slug' => 'produits-non-grilles',
            'description' => 'Produits frais et savoureux non grillés',
            'is_active' => true,
            'sort_order' => 2,
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ],
        [
            'name' => 'Fromages',
            'slug' => 'fromages',
            'description' => 'Sélection de fromages artisanaux',
            'is_active' => true,
            'sort_order' => 3,
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ],
        [
            'name' => 'Boissons',
            'slug' => 'boissons',
            'description' => 'Boissons fraîches et rafraîchissantes',
            'is_active' => true,
            'sort_order' => 4,
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ]
    ];

    foreach ($categories as $category) {
        $exists = Capsule::table('categories')->where('slug', $category['slug'])->exists();
        if (!$exists) {
            Capsule::table('categories')->insert($category);
            echo "Catégorie {$category['name']} créée\n";
        }
    }

    // Récupérer les IDs des catégories
    $grillesId = Capsule::table('categories')->where('slug', 'produits-grilles')->value('id');
    $nonGrillesId = Capsule::table('categories')->where('slug', 'produits-non-grilles')->value('id');
    $fromagesId = Capsule::table('categories')->where('slug', 'fromages')->value('id');
    $boissonsId = Capsule::table('categories')->where('slug', 'boissons')->value('id');

    // Créer des produits
    $products = [
        [
            'name' => 'Poulet Grillé Entier',
            'slug' => 'poulet-grille-entier',
            'description' => 'Poulet entier grillé aux herbes de Provence, tendre et savoureux',
            'short_description' => 'Poulet entier grillé aux herbes',
            'price' => 25.99,
            'sku' => 'PGE001',
            'stock_quantity' => 50,
            'category_id' => $grillesId,
            'is_featured' => true,
            'is_active' => true,
            'weight' => 1.5,
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ],
        [
            'name' => 'Brochettes de Bœuf',
            'slug' => 'brochettes-boeuf',
            'description' => 'Brochettes de bœuf marinées et grillées, accompagnées de légumes',
            'short_description' => 'Brochettes de bœuf marinées',
            'price' => 18.50,
            'sale_price' => 16.99,
            'sku' => 'BBG001',
            'stock_quantity' => 30,
            'category_id' => $grillesId,
            'is_featured' => true,
            'is_active' => true,
            'weight' => 0.4,
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ],
        [
            'name' => 'Salade César',
            'slug' => 'salade-cesar',
            'description' => 'Salade César classique avec croûtons, parmesan et sauce maison',
            'short_description' => 'Salade César classique',
            'price' => 12.50,
            'sku' => 'SC001',
            'stock_quantity' => 40,
            'category_id' => $nonGrillesId,
            'is_featured' => true,
            'is_active' => true,
            'weight' => 0.3,
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ],
        [
            'name' => 'Plateau de Fromages',
            'slug' => 'plateau-fromages',
            'description' => 'Sélection de fromages artisanaux avec confiture et noix',
            'short_description' => 'Plateau de fromages artisanaux',
            'price' => 19.90,
            'sku' => 'PF001',
            'stock_quantity' => 15,
            'category_id' => $fromagesId,
            'is_featured' => true,
            'is_active' => true,
            'weight' => 0.5,
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ],
        [
            'name' => 'Jus d\'Orange Frais',
            'slug' => 'jus-orange-frais',
            'description' => 'Jus d\'orange fraîchement pressé, 100% naturel',
            'short_description' => 'Jus d\'orange fraîchement pressé',
            'price' => 4.50,
            'sku' => 'JOF001',
            'stock_quantity' => 100,
            'category_id' => $boissonsId,
            'is_featured' => false,
            'is_active' => true,
            'weight' => 0.5,
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ]
    ];

    foreach ($products as $product) {
        $exists = Capsule::table('products')->where('slug', $product['slug'])->exists();
        if (!$exists) {
            Capsule::table('products')->insert($product);
            echo "Produit {$product['name']} créé\n";
        }
    }

    // Créer des promotions
    $promotions = [
        [
            'name' => 'Bienvenue 10%',
            'code' => 'BIENVENUE10',
            'description' => 'Réduction de 10% pour les nouveaux clients',
            'type' => 'percentage',
            'value' => 10.00,
            'minimum_amount' => 50.00,
            'usage_limit' => 100,
            'used_count' => 0,
            'start_date' => Carbon::now(),
            'end_date' => Carbon::now()->addDays(30),
            'is_active' => true,
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ],
        [
            'name' => 'Livraison Gratuite',
            'code' => 'LIVRAISON',
            'description' => 'Livraison gratuite pour toute commande',
            'type' => 'free_shipping',
            'value' => 0.00,
            'minimum_amount' => 30.00,
            'usage_limit' => 50,
            'used_count' => 0,
            'start_date' => Carbon::now(),
            'end_date' => Carbon::now()->addDays(15),
            'is_active' => true,
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ]
    ];

    foreach ($promotions as $promotion) {
        $exists = Capsule::table('promotions')->where('code', $promotion['code'])->exists();
        if (!$exists) {
            Capsule::table('promotions')->insert($promotion);
            echo "Promotion {$promotion['name']} créée\n";
        }
    }

    echo "Données de test créées avec succès!\n";

} catch (Exception $e) {
    echo "Erreur: " . $e->getMessage() . "\n";
}
