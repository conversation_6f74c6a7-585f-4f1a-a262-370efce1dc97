<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Product extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'category_id',
        'name',
        'slug',
        'description',
        'price',
        'sale_price',
        'stock',
        'min_stock',
        'sku',
        'image',
        'additional_images',
        'is_grilled',
        'can_be_grilled',
        'weight',
        'is_featured',
        'is_active'
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'sale_price' => 'decimal:2',
        'weight' => 'decimal:2',
        'is_grilled' => 'boolean',
        'can_be_grilled' => 'boolean',
        'is_featured' => 'boolean',
        'is_active' => 'boolean',
        'additional_images' => 'array',
        'min_stock' => 'integer',
        'stock' => 'integer'
    ];

    // Accessors & Mutators
    protected function getDiscountPercentageAttribute(): ?float
    {
        if ($this->sale_price && $this->price > 0) {
            return round((($this->price - $this->sale_price) / $this->price) * 100, 2);
        }
        return null;
    }

    protected function getIsInStockAttribute(): bool
    {
        return $this->stock > 0;
    }

    protected function getIsLowStockAttribute(): bool
    {
        return $this->stock <= $this->min_stock;
    }

    // Relations
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    public function reviews(): HasMany
    {
        return $this->hasMany(Review::class);
    }

    public function orderItems(): HasMany
    {
        return $this->hasMany(OrderItem::class);
    }

    public function grillingOptions(): BelongsToMany
    {
        return $this->belongsToMany(GrillingOption::class, 'product_grilling_options')
                    ->withTimestamps();
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    public function scopeInStock($query)
    {
        return $query->where('stock', '>', 0);
    }

    public function scopeLowStock($query)
    {
        return $query->where('stock', '<=', 'min_stock');
    }

    public function scopeGrillable($query)
    {
        return $query->where('can_be_grilled', true);
    }
}


