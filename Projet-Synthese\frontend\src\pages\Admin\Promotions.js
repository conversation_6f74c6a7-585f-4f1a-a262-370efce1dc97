import React, { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import { FaPlus, FaEdit, FaTrash, FaEye, FaTag, FaPercent, FaDollarSign, FaShippingFast } from 'react-icons/fa';
import useSafeAuth from '../../hooks/useSafeAuth';
import { promotionService } from '../../services/api/promotionService';

const Promotions = () => {
  const { state: authState } = useSafeAuth();
  const [promotions, setPromotions] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [editingPromotion, setEditingPromotion] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    code: '',
    description: '',
    type: 'percentage',
    value: '',
    minimum_amount: '',
    maximum_discount: '',
    usage_limit: '',
    start_date: '',
    end_date: '',
    is_active: true
  });

  useEffect(() => {
    fetchPromotions();
  }, []);

  const fetchPromotions = async () => {
    try {
      setIsLoading(true);
      const data = await promotionService.getAllPromotions(authState?.token);
      setPromotions(data);
    } catch (error) {
      console.log('API non disponible, utilisation des données de test');

      // Données de test pour les promotions
      const testPromotions = [
        {
          id: 1,
          name: 'Bienvenue 10%',
          code: 'BIENVENUE10',
          description: 'Réduction de 10% pour les nouveaux clients sur leur première commande',
          type: 'percentage',
          value: 10,
          minimum_amount: 50,
          maximum_discount: null,
          usage_limit: 100,
          used_count: 15,
          start_date: new Date().toISOString(),
          end_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
          is_active: true
        },
        {
          id: 2,
          name: 'Livraison Gratuite',
          code: 'LIVRAISON',
          description: 'Livraison gratuite pour toute commande supérieure à 30€',
          type: 'free_shipping',
          value: 0,
          minimum_amount: 30,
          maximum_discount: null,
          usage_limit: 50,
          used_count: 8,
          start_date: new Date().toISOString(),
          end_date: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString(),
          is_active: true
        },
        {
          id: 3,
          name: 'Réduction 5€',
          code: 'REDUCTION5',
          description: '5€ de réduction immédiate sur votre commande',
          type: 'fixed',
          value: 5,
          minimum_amount: 25,
          maximum_discount: null,
          usage_limit: 30,
          used_count: 12,
          start_date: new Date().toISOString(),
          end_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
          is_active: true
        },
        {
          id: 4,
          name: 'Été 2024',
          code: 'ETE2024',
          description: '15% de réduction sur tous les produits grillés',
          type: 'percentage',
          value: 15,
          minimum_amount: 40,
          maximum_discount: 20,
          usage_limit: 200,
          used_count: 45,
          start_date: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
          end_date: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000).toISOString(),
          is_active: true
        },
        {
          id: 5,
          name: 'Black Friday',
          code: 'BLACKFRIDAY',
          description: '25% de réduction exceptionnelle',
          type: 'percentage',
          value: 25,
          minimum_amount: 100,
          maximum_discount: 50,
          usage_limit: 500,
          used_count: 500,
          start_date: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString(),
          end_date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
          is_active: false
        }
      ];

      setPromotions(testPromotions);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      const promotionData = {
        ...formData,
        value: parseFloat(formData.value),
        minimum_amount: formData.minimum_amount ? parseFloat(formData.minimum_amount) : null,
        maximum_discount: formData.maximum_discount ? parseFloat(formData.maximum_discount) : null,
        usage_limit: formData.usage_limit ? parseInt(formData.usage_limit) : null
      };

      if (editingPromotion) {
        try {
          await promotionService.updatePromotion(editingPromotion.id, promotionData, authState?.token);
          toast.success('Promotion mise à jour avec succès');
        } catch (apiError) {
          // Mode démonstration - simulation de la mise à jour
          const updatedPromotions = promotions.map(p =>
            p.id === editingPromotion.id
              ? { ...p, ...promotionData, id: editingPromotion.id, used_count: p.used_count }
              : p
          );
          setPromotions(updatedPromotions);
          toast.success('Promotion mise à jour avec succès (mode démonstration)');
        }
      } else {
        try {
          await promotionService.createPromotion(promotionData, authState?.token);
          toast.success('Promotion créée avec succès');
        } catch (apiError) {
          // Mode démonstration - simulation de la création
          const newPromotion = {
            ...promotionData,
            id: Math.max(...promotions.map(p => p.id)) + 1,
            used_count: 0
          };
          setPromotions([...promotions, newPromotion]);
          toast.success('Promotion créée avec succès (mode démonstration)');
        }
      }

      setShowModal(false);
      setEditingPromotion(null);
      resetForm();
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Erreur lors de la sauvegarde';
      toast.error(errorMessage);
    }
  };

  const handleEdit = (promotion) => {
    setEditingPromotion(promotion);
    setFormData({
      name: promotion.name,
      code: promotion.code,
      description: promotion.description || '',
      type: promotion.type,
      value: promotion.value.toString(),
      minimum_amount: promotion.minimum_amount?.toString() || '',
      maximum_discount: promotion.maximum_discount?.toString() || '',
      usage_limit: promotion.usage_limit?.toString() || '',
      start_date: promotion.start_date.split('T')[0],
      end_date: promotion.end_date.split('T')[0],
      is_active: promotion.is_active
    });
    setShowModal(true);
  };

  const handleDelete = async (id) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer cette promotion ?')) {
      try {
        await promotionService.deletePromotion(id, authState?.token);
        toast.success('Promotion supprimée avec succès');
        fetchPromotions();
      } catch (error) {
        // Mode démonstration - simulation de la suppression
        const updatedPromotions = promotions.filter(p => p.id !== id);
        setPromotions(updatedPromotions);
        toast.success('Promotion supprimée avec succès (mode démonstration)');
      }
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      code: '',
      description: '',
      type: 'percentage',
      value: '',
      minimum_amount: '',
      maximum_discount: '',
      usage_limit: '',
      start_date: '',
      end_date: '',
      is_active: true
    });
  };

  const getTypeIcon = (type) => {
    switch (type) {
      case 'percentage':
        return <FaPercent className="text-blue-500" />;
      case 'fixed':
        return <FaDollarSign className="text-green-500" />;
      case 'free_shipping':
        return <FaShippingFast className="text-purple-500" />;
      default:
        return <FaTag className="text-gray-500" />;
    }
  };

  const getTypeLabel = (type) => {
    switch (type) {
      case 'percentage':
        return 'Pourcentage';
      case 'fixed':
        return 'Montant fixe';
      case 'free_shipping':
        return 'Livraison gratuite';
      default:
        return type;
    }
  };

  const formatValue = (promotion) => {
    switch (promotion.type) {
      case 'percentage':
        return `${promotion.value}%`;
      case 'fixed':
        return `${promotion.value}$`;
      case 'free_shipping':
        return 'Gratuite';
      default:
        return promotion.value;
    }
  };

  const getStatusBadge = (promotion) => {
    const now = new Date();
    const startDate = new Date(promotion.start_date);
    const endDate = new Date(promotion.end_date);

    if (!promotion.is_active) {
      return <span className="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">Inactive</span>;
    }

    if (now < startDate) {
      return <span className="px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded-full">À venir</span>;
    }

    if (now > endDate) {
      return <span className="px-2 py-1 text-xs bg-red-100 text-red-800 rounded-full">Expirée</span>;
    }

    if (promotion.usage_limit && promotion.used_count >= promotion.usage_limit) {
      return <span className="px-2 py-1 text-xs bg-orange-100 text-orange-800 rounded-full">Épuisée</span>;
    }

    return <span className="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">Active</span>;
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">Gestion des Promotions</h1>
        <button
          onClick={() => {
            resetForm();
            setEditingPromotion(null);
            setShowModal(true);
          }}
          className="btn-primary flex items-center space-x-2"
        >
          <FaPlus />
          <span>Nouvelle promotion</span>
        </button>
      </div>

      {/* Modal pour créer/éditer une promotion */}
      {showModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                {editingPromotion ? 'Modifier la promotion' : 'Nouvelle promotion'}
              </h3>

              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Nom de la promotion *
                    </label>
                    <input
                      type="text"
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Code promo *
                    </label>
                    <input
                      type="text"
                      value={formData.code}
                      onChange={(e) => setFormData({ ...formData, code: e.target.value.toUpperCase() })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                      required
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    rows="3"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Type de promotion *
                    </label>
                    <select
                      value={formData.type}
                      onChange={(e) => setFormData({ ...formData, type: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                      required
                    >
                      <option value="percentage">Pourcentage</option>
                      <option value="fixed">Montant fixe</option>
                      <option value="free_shipping">Livraison gratuite</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Valeur *
                    </label>
                    <input
                      type="number"
                      step="0.01"
                      min="0"
                      max={formData.type === 'percentage' ? '100' : undefined}
                      value={formData.value}
                      onChange={(e) => setFormData({ ...formData, value: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                      required
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Montant minimum
                    </label>
                    <input
                      type="number"
                      step="0.01"
                      min="0"
                      value={formData.minimum_amount}
                      onChange={(e) => setFormData({ ...formData, minimum_amount: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Réduction maximum
                    </label>
                    <input
                      type="number"
                      step="0.01"
                      min="0"
                      value={formData.maximum_discount}
                      onChange={(e) => setFormData({ ...formData, maximum_discount: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                      disabled={formData.type !== 'percentage'}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Limite d'utilisation
                    </label>
                    <input
                      type="number"
                      min="1"
                      value={formData.usage_limit}
                      onChange={(e) => setFormData({ ...formData, usage_limit: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                      placeholder="Illimité"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Date de début *
                    </label>
                    <input
                      type="date"
                      value={formData.start_date}
                      onChange={(e) => setFormData({ ...formData, start_date: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Date de fin *
                    </label>
                    <input
                      type="date"
                      value={formData.end_date}
                      onChange={(e) => setFormData({ ...formData, end_date: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                      required
                    />
                  </div>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="is_active"
                    checked={formData.is_active}
                    onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                  <label htmlFor="is_active" className="ml-2 block text-sm text-gray-900">
                    Promotion active
                  </label>
                </div>

                <div className="flex justify-end space-x-3 pt-4">
                  <button
                    type="button"
                    onClick={() => {
                      setShowModal(false);
                      setEditingPromotion(null);
                      resetForm();
                    }}
                    className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                  >
                    Annuler
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700"
                  >
                    {editingPromotion ? 'Mettre à jour' : 'Créer'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Tableau des promotions */}
      <div className="bg-white shadow-sm rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Promotion
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Valeur
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Utilisation
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Période
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Statut
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {isLoading ? (
                <tr>
                  <td colSpan="7" className="px-6 py-4 text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
                  </td>
                </tr>
              ) : promotions.length === 0 ? (
                <tr>
                  <td colSpan="7" className="px-6 py-4 text-center text-gray-500">
                    Aucune promotion trouvée
                  </td>
                </tr>
              ) : (
                promotions.map((promotion) => (
                  <tr key={promotion.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{promotion.name}</div>
                        <div className="text-sm text-gray-500 font-mono">{promotion.code}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center space-x-2">
                        {getTypeIcon(promotion.type)}
                        <span className="text-sm text-gray-900">{getTypeLabel(promotion.type)}</span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="text-sm font-medium text-gray-900">
                        {formatValue(promotion)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {promotion.used_count} / {promotion.usage_limit || '∞'}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        <div>{new Date(promotion.start_date).toLocaleDateString('fr-FR')}</div>
                        <div className="text-gray-500">au {new Date(promotion.end_date).toLocaleDateString('fr-FR')}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getStatusBadge(promotion)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleEdit(promotion)}
                          className="text-primary-600 hover:text-primary-900"
                          title="Modifier"
                        >
                          <FaEdit />
                        </button>
                        <button
                          onClick={() => handleDelete(promotion.id)}
                          className="text-red-600 hover:text-red-900"
                          title="Supprimer"
                        >
                          <FaTrash />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default Promotions;
