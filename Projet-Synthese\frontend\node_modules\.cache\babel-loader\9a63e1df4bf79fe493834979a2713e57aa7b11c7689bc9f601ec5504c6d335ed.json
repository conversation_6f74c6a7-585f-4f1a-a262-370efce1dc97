{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Projet-Synthese\\\\frontend\\\\src\\\\pages\\\\Admin\\\\ProductsCRUD.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useData } from '../../context/DataContext';\nimport { productAPI } from '../../services/apiService';\nimport { toast } from 'react-toastify';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProductsCRUD = () => {\n  _s();\n  const {\n    state,\n    dispatch,\n    DataActions\n  } = useData();\n  const {\n    products,\n    categories,\n    loading\n  } = state;\n  const [error, setError] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const [currentProduct, setCurrentProduct] = useState(null);\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    price: '',\n    stock: '',\n    category_id: '',\n    is_grillable: false,\n    image: null\n  });\n  const [filteredProducts, setFilteredProducts] = useState([]);\n\n  // Filtrer les produits selon le terme de recherche\n  useEffect(() => {\n    if (searchTerm) {\n      const filtered = products.filter(product => product.name.toLowerCase().includes(searchTerm.toLowerCase()) || product.description.toLowerCase().includes(searchTerm.toLowerCase()));\n      setFilteredProducts(filtered);\n    } else {\n      setFilteredProducts(products);\n    }\n  }, [products, searchTerm]);\n\n  // Fonction pour rafraîchir les données depuis l'API\n  const refreshData = async () => {\n    dispatch({\n      type: DataActions.SET_LOADING,\n      payload: true\n    });\n    try {\n      console.log('🔄 Actualisation des produits depuis l\\'API...');\n      const result = await productAPI.getAll();\n      if (result.success) {\n        dispatch({\n          type: DataActions.SET_PRODUCTS,\n          payload: result.data\n        });\n        setError(null);\n        console.log('✅ Produits actualisés depuis l\\'API backend');\n        toast.success(`${result.data.length} produits chargés depuis l'API`);\n      } else {\n        setError('Mode démonstration - Données de test utilisées');\n        console.log('⚠️ API non disponible, données de test utilisées');\n        toast.info('Mode démonstration - Données locales utilisées');\n      }\n    } catch (err) {\n      console.log('❌ Erreur API, utilisation des données de test');\n      setError('Mode démonstration - Données de test utilisées');\n      toast.warning('Connexion API échouée - Mode démonstration activé');\n    } finally {\n      dispatch({\n        type: DataActions.SET_LOADING,\n        payload: false\n      });\n    }\n  };\n\n  // Gestion des changements dans le formulaire\n  const handleInputChange = e => {\n    const {\n      name,\n      value,\n      type,\n      checked,\n      files\n    } = e.target;\n    if (type === 'checkbox') {\n      setFormData(prev => ({\n        ...prev,\n        [name]: checked\n      }));\n    } else if (type === 'file') {\n      setFormData(prev => ({\n        ...prev,\n        [name]: files[0]\n      }));\n    } else {\n      setFormData(prev => ({\n        ...prev,\n        [name]: value\n      }));\n    }\n  };\n\n  // Ouvrir le modal pour ajouter/modifier un produit\n  const openModal = (product = null) => {\n    if (product) {\n      setCurrentProduct(product);\n      setFormData({\n        name: product.name,\n        description: product.description,\n        price: product.price,\n        stock: product.stock,\n        category_id: product.category_id,\n        is_grillable: product.is_grillable,\n        image: null\n      });\n    } else {\n      setCurrentProduct(null);\n      setFormData({\n        name: '',\n        description: '',\n        price: '',\n        stock: '',\n        category_id: '',\n        is_grillable: false,\n        image: null\n      });\n    }\n    setIsModalOpen(true);\n  };\n\n  // Fermer le modal\n  const closeModal = () => {\n    setIsModalOpen(false);\n    setCurrentProduct(null);\n    setFormData({\n      name: '',\n      description: '',\n      price: '',\n      stock: '',\n      category_id: '',\n      is_grillable: false,\n      image: null\n    });\n  };\n\n  // Soumettre le formulaire\n  const handleSubmit = async e => {\n    e.preventDefault();\n    dispatch({\n      type: DataActions.SET_LOADING,\n      payload: true\n    });\n    try {\n      const productData = {\n        ...formData,\n        price: parseFloat(formData.price),\n        stock: parseInt(formData.stock),\n        category_id: parseInt(formData.category_id)\n      };\n      if (currentProduct) {\n        // Mise à jour d'un produit existant\n        const result = await productAPI.update(currentProduct.id, productData);\n        if (result.success) {\n          // Mise à jour réussie via API\n          dispatch({\n            type: DataActions.UPDATE_PRODUCT,\n            payload: {\n              ...result.data,\n              id: currentProduct.id\n            }\n          });\n          console.log('Produit mis à jour via API');\n        } else {\n          // Fallback: mise à jour locale\n          dispatch({\n            type: DataActions.UPDATE_PRODUCT,\n            payload: {\n              ...productData,\n              id: currentProduct.id,\n              category: categories.find(cat => cat.id === productData.category_id)\n            }\n          });\n          console.log('Produit mis à jour localement (mode démonstration)');\n        }\n      } else {\n        // Création d'un nouveau produit\n        const result = await productAPI.create(productData);\n        if (result.success) {\n          // Création réussie via API\n          dispatch({\n            type: DataActions.ADD_PRODUCT,\n            payload: result.data\n          });\n          console.log('Produit créé via API');\n        } else {\n          // Fallback: création locale\n          const newProduct = {\n            ...productData,\n            id: Date.now(),\n            category: categories.find(cat => cat.id === productData.category_id),\n            image_url: 'https://images.unsplash.com/photo-1546069901-ba9599a7e63c?w=300',\n            slug: productData.name.toLowerCase().replace(/\\s+/g, '-')\n          };\n          dispatch({\n            type: DataActions.ADD_PRODUCT,\n            payload: newProduct\n          });\n          console.log('Produit créé localement (mode démonstration)');\n        }\n      }\n      closeModal();\n    } catch (err) {\n      console.error('Erreur lors de l\\'enregistrement du produit', err);\n      // En cas d'erreur, fallback vers le mode local\n      const productData = {\n        ...formData,\n        price: parseFloat(formData.price),\n        stock: parseInt(formData.stock),\n        category_id: parseInt(formData.category_id)\n      };\n      if (currentProduct) {\n        dispatch({\n          type: DataActions.UPDATE_PRODUCT,\n          payload: {\n            ...productData,\n            id: currentProduct.id,\n            category: categories.find(cat => cat.id === productData.category_id)\n          }\n        });\n      } else {\n        const newProduct = {\n          ...productData,\n          id: Date.now(),\n          category: categories.find(cat => cat.id === productData.category_id),\n          image_url: 'https://images.unsplash.com/photo-1546069901-ba9599a7e63c?w=300',\n          slug: productData.name.toLowerCase().replace(/\\s+/g, '-')\n        };\n        dispatch({\n          type: DataActions.ADD_PRODUCT,\n          payload: newProduct\n        });\n      }\n      closeModal();\n    } finally {\n      dispatch({\n        type: DataActions.SET_LOADING,\n        payload: false\n      });\n    }\n  };\n\n  // Supprimer un produit\n  const handleDelete = async id => {\n    if (window.confirm('Êtes-vous sûr de vouloir supprimer ce produit ?')) {\n      dispatch({\n        type: DataActions.SET_LOADING,\n        payload: true\n      });\n      try {\n        const result = await productAPI.delete(id);\n        if (result.success) {\n          // Suppression réussie via API\n          dispatch({\n            type: DataActions.DELETE_PRODUCT,\n            payload: id\n          });\n          console.log('Produit supprimé via API');\n        } else {\n          // Fallback: suppression locale\n          dispatch({\n            type: DataActions.DELETE_PRODUCT,\n            payload: id\n          });\n          console.log('Produit supprimé localement (mode démonstration)');\n        }\n      } catch (err) {\n        console.error('Erreur lors de la suppression du produit', err);\n        // Fallback: suppression locale en cas d'erreur\n        dispatch({\n          type: DataActions.DELETE_PRODUCT,\n          payload: id\n        });\n        console.log('Produit supprimé localement (erreur API)');\n      } finally {\n        dispatch({\n          type: DataActions.SET_LOADING,\n          payload: false\n        });\n      }\n    }\n  };\n\n  // Gestion de la recherche\n  const handleSearch = e => {\n    setSearchTerm(e.target.value);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-gray-900\",\n          children: \"Gestion des Produits\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-500\",\n          children: [filteredProducts.length, \" produit(s) \\u2022 CRUD complet activ\\xE9\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex space-x-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: refreshData,\n          disabled: loading,\n          className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50\",\n          children: loading ? 'Actualisation...' : 'Actualiser'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => openModal(),\n          className: \"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700\",\n          children: \"Ajouter un produit\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 243,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white p-4 rounded-lg shadow\",\n      children: /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        placeholder: \"Rechercher un produit...\",\n        value: searchTerm,\n        onChange: handleSearch,\n        className: \"w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 268,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative\",\n      role: \"alert\",\n      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n        className: \"font-bold\",\n        children: \"Info!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"block sm:inline\",\n        children: [\" \", error]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 281,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 279,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow overflow-hidden\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"overflow-x-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"min-w-full divide-y divide-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            className: \"bg-gray-50\",\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Image\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Nom\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Prix\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Stock\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Cat\\xE9gorie\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Grillable\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            className: \"bg-white divide-y divide-gray-200\",\n            children: filteredProducts.map(product => /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"h-10 w-10 rounded-full overflow-hidden bg-gray-100\",\n                  children: product.image_url ? /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: product.image_url,\n                    alt: product.name,\n                    className: \"h-full w-full object-cover\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 306,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"h-full w-full flex items-center justify-center text-gray-400\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      className: \"h-6 w-6\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      stroke: \"currentColor\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 314,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 313,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 312,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 304,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                children: product.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                children: [product.price, \" DH\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${product.stock > 10 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`,\n                  children: product.stock\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 323,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 322,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                children: product.category ? product.category.name : 'Non catégorisé'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 329,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                children: product.is_grillable ? /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-green-600\",\n                  children: \"Oui\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 334,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-600\",\n                  children: \"Non\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 336,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => openModal(product),\n                  className: \"text-indigo-600 hover:text-indigo-900 mr-3\",\n                  children: \"Modifier\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 340,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleDelete(product.id),\n                  className: \"text-red-600 hover:text-red-900\",\n                  children: \"Supprimer\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 346,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 19\n              }, this)]\n            }, product.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 302,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 288,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 287,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 286,\n      columnNumber: 7\n    }, this), isModalOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-lg w-full max-w-md mx-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-6 py-4 border-b\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold\",\n            children: currentProduct ? 'Modifier le produit' : 'Ajouter un produit'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 364,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"name\",\n                className: \"block text-gray-700 mb-2\",\n                children: \"Nom\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 372,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"name\",\n                name: \"name\",\n                value: formData.name,\n                onChange: handleInputChange,\n                className: \"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 373,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 371,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"description\",\n                className: \"block text-gray-700 mb-2\",\n                children: \"Description\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 385,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                id: \"description\",\n                name: \"description\",\n                value: formData.description,\n                onChange: handleInputChange,\n                className: \"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\",\n                rows: \"3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 386,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-2 gap-4 mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"price\",\n                  className: \"block text-gray-700 mb-2\",\n                  children: \"Prix (DH)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 398,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  id: \"price\",\n                  name: \"price\",\n                  value: formData.price,\n                  onChange: handleInputChange,\n                  className: \"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\",\n                  step: \"0.01\",\n                  min: \"0\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 399,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 397,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"stock\",\n                  className: \"block text-gray-700 mb-2\",\n                  children: \"Stock\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 413,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  id: \"stock\",\n                  name: \"stock\",\n                  value: formData.stock,\n                  onChange: handleInputChange,\n                  className: \"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\",\n                  min: \"0\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 414,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 412,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"category_id\",\n                className: \"block text-gray-700 mb-2\",\n                children: \"Cat\\xE9gorie\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 428,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                id: \"category_id\",\n                name: \"category_id\",\n                value: formData.category_id,\n                onChange: handleInputChange,\n                className: \"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\",\n                required: true,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"S\\xE9lectionner une cat\\xE9gorie\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 437,\n                  columnNumber: 21\n                }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: category.id,\n                  children: category.name\n                }, category.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 439,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 429,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 427,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"image\",\n                className: \"block text-gray-700 mb-2\",\n                children: \"Image\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 447,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"file\",\n                id: \"image\",\n                name: \"image\",\n                onChange: handleInputChange,\n                className: \"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\",\n                accept: \"image/*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 448,\n                columnNumber: 19\n              }, this), currentProduct && currentProduct.image_url && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-500\",\n                  children: \"Image actuelle:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 458,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"h-20 w-20 mt-1 rounded overflow-hidden bg-gray-100\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: currentProduct.image_url,\n                    alt: currentProduct.name,\n                    className: \"h-full w-full object-cover\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 460,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 459,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 457,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 446,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  id: \"is_grillable\",\n                  name: \"is_grillable\",\n                  checked: formData.is_grillable,\n                  onChange: handleInputChange,\n                  className: \"h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 472,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"is_grillable\",\n                  className: \"ml-2 block text-gray-700\",\n                  children: \"Produit grillable\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 480,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 471,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 470,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 370,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"px-6 py-4 bg-gray-50 border-t flex justify-end\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: closeModal,\n              className: \"px-4 py-2 border rounded-lg text-gray-700 mr-2 hover:bg-gray-100\",\n              children: \"Annuler\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 488,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              disabled: loading,\n              className: \"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50\",\n              children: loading ? 'Enregistrement...' : currentProduct ? 'Mettre à jour' : 'Ajouter'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 495,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 487,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 369,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 363,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 362,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 241,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductsCRUD, \"Cjuq0AMGyBtILmIzTnVAtgC6QTE=\", false, function () {\n  return [useData];\n});\n_c = ProductsCRUD;\nexport default ProductsCRUD;\nvar _c;\n$RefreshReg$(_c, \"ProductsCRUD\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useData", "productAPI", "toast", "jsxDEV", "_jsxDEV", "ProductsCRUD", "_s", "state", "dispatch", "DataActions", "products", "categories", "loading", "error", "setError", "searchTerm", "setSearchTerm", "isModalOpen", "setIsModalOpen", "currentProduct", "setCurrentProduct", "formData", "setFormData", "name", "description", "price", "stock", "category_id", "is_grillable", "image", "filteredProducts", "setFilteredProducts", "filtered", "filter", "product", "toLowerCase", "includes", "refreshData", "type", "SET_LOADING", "payload", "console", "log", "result", "getAll", "success", "SET_PRODUCTS", "data", "length", "info", "err", "warning", "handleInputChange", "e", "value", "checked", "files", "target", "prev", "openModal", "closeModal", "handleSubmit", "preventDefault", "productData", "parseFloat", "parseInt", "update", "id", "UPDATE_PRODUCT", "category", "find", "cat", "create", "ADD_PRODUCT", "newProduct", "Date", "now", "image_url", "slug", "replace", "handleDelete", "window", "confirm", "delete", "DELETE_PRODUCT", "handleSearch", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "placeholder", "onChange", "role", "map", "src", "alt", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "onSubmit", "htmlFor", "required", "rows", "step", "min", "accept", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet-Synthese/frontend/src/pages/Admin/ProductsCRUD.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useData } from '../../context/DataContext';\nimport { productAPI } from '../../services/apiService';\nimport { toast } from 'react-toastify';\n\nconst ProductsCRUD = () => {\n  const { state, dispatch, DataActions } = useData();\n  const { products, categories, loading } = state;\n  \n  const [error, setError] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const [currentProduct, setCurrentProduct] = useState(null);\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    price: '',\n    stock: '',\n    category_id: '',\n    is_grillable: false,\n    image: null\n  });\n  const [filteredProducts, setFilteredProducts] = useState([]);\n\n  // Filtrer les produits selon le terme de recherche\n  useEffect(() => {\n    if (searchTerm) {\n      const filtered = products.filter(product =>\n        product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        product.description.toLowerCase().includes(searchTerm.toLowerCase())\n      );\n      setFilteredProducts(filtered);\n    } else {\n      setFilteredProducts(products);\n    }\n  }, [products, searchTerm]);\n\n  // Fonction pour rafraîchir les données depuis l'API\n  const refreshData = async () => {\n    dispatch({ type: DataActions.SET_LOADING, payload: true });\n\n    try {\n      console.log('🔄 Actualisation des produits depuis l\\'API...');\n      const result = await productAPI.getAll();\n\n      if (result.success) {\n        dispatch({ type: DataActions.SET_PRODUCTS, payload: result.data });\n        setError(null);\n        console.log('✅ Produits actualisés depuis l\\'API backend');\n        toast.success(`${result.data.length} produits chargés depuis l'API`);\n      } else {\n        setError('Mode démonstration - Données de test utilisées');\n        console.log('⚠️ API non disponible, données de test utilisées');\n        toast.info('Mode démonstration - Données locales utilisées');\n      }\n    } catch (err) {\n      console.log('❌ Erreur API, utilisation des données de test');\n      setError('Mode démonstration - Données de test utilisées');\n      toast.warning('Connexion API échouée - Mode démonstration activé');\n    } finally {\n      dispatch({ type: DataActions.SET_LOADING, payload: false });\n    }\n  };\n\n  // Gestion des changements dans le formulaire\n  const handleInputChange = (e) => {\n    const { name, value, type, checked, files } = e.target;\n    \n    if (type === 'checkbox') {\n      setFormData(prev => ({ ...prev, [name]: checked }));\n    } else if (type === 'file') {\n      setFormData(prev => ({ ...prev, [name]: files[0] }));\n    } else {\n      setFormData(prev => ({ ...prev, [name]: value }));\n    }\n  };\n\n  // Ouvrir le modal pour ajouter/modifier un produit\n  const openModal = (product = null) => {\n    if (product) {\n      setCurrentProduct(product);\n      setFormData({\n        name: product.name,\n        description: product.description,\n        price: product.price,\n        stock: product.stock,\n        category_id: product.category_id,\n        is_grillable: product.is_grillable,\n        image: null\n      });\n    } else {\n      setCurrentProduct(null);\n      setFormData({\n        name: '',\n        description: '',\n        price: '',\n        stock: '',\n        category_id: '',\n        is_grillable: false,\n        image: null\n      });\n    }\n    setIsModalOpen(true);\n  };\n\n  // Fermer le modal\n  const closeModal = () => {\n    setIsModalOpen(false);\n    setCurrentProduct(null);\n    setFormData({\n      name: '',\n      description: '',\n      price: '',\n      stock: '',\n      category_id: '',\n      is_grillable: false,\n      image: null\n    });\n  };\n\n  // Soumettre le formulaire\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    dispatch({ type: DataActions.SET_LOADING, payload: true });\n\n    try {\n      const productData = {\n        ...formData,\n        price: parseFloat(formData.price),\n        stock: parseInt(formData.stock),\n        category_id: parseInt(formData.category_id)\n      };\n\n      if (currentProduct) {\n        // Mise à jour d'un produit existant\n        const result = await productAPI.update(currentProduct.id, productData);\n\n        if (result.success) {\n          // Mise à jour réussie via API\n          dispatch({\n            type: DataActions.UPDATE_PRODUCT,\n            payload: { ...result.data, id: currentProduct.id }\n          });\n          console.log('Produit mis à jour via API');\n        } else {\n          // Fallback: mise à jour locale\n          dispatch({\n            type: DataActions.UPDATE_PRODUCT,\n            payload: { ...productData, id: currentProduct.id, category: categories.find(cat => cat.id === productData.category_id) }\n          });\n          console.log('Produit mis à jour localement (mode démonstration)');\n        }\n      } else {\n        // Création d'un nouveau produit\n        const result = await productAPI.create(productData);\n\n        if (result.success) {\n          // Création réussie via API\n          dispatch({ type: DataActions.ADD_PRODUCT, payload: result.data });\n          console.log('Produit créé via API');\n        } else {\n          // Fallback: création locale\n          const newProduct = {\n            ...productData,\n            id: Date.now(),\n            category: categories.find(cat => cat.id === productData.category_id),\n            image_url: 'https://images.unsplash.com/photo-1546069901-ba9599a7e63c?w=300',\n            slug: productData.name.toLowerCase().replace(/\\s+/g, '-')\n          };\n          dispatch({ type: DataActions.ADD_PRODUCT, payload: newProduct });\n          console.log('Produit créé localement (mode démonstration)');\n        }\n      }\n\n      closeModal();\n    } catch (err) {\n      console.error('Erreur lors de l\\'enregistrement du produit', err);\n      // En cas d'erreur, fallback vers le mode local\n      const productData = {\n        ...formData,\n        price: parseFloat(formData.price),\n        stock: parseInt(formData.stock),\n        category_id: parseInt(formData.category_id)\n      };\n\n      if (currentProduct) {\n        dispatch({\n          type: DataActions.UPDATE_PRODUCT,\n          payload: { ...productData, id: currentProduct.id, category: categories.find(cat => cat.id === productData.category_id) }\n        });\n      } else {\n        const newProduct = {\n          ...productData,\n          id: Date.now(),\n          category: categories.find(cat => cat.id === productData.category_id),\n          image_url: 'https://images.unsplash.com/photo-1546069901-ba9599a7e63c?w=300',\n          slug: productData.name.toLowerCase().replace(/\\s+/g, '-')\n        };\n        dispatch({ type: DataActions.ADD_PRODUCT, payload: newProduct });\n      }\n      closeModal();\n    } finally {\n      dispatch({ type: DataActions.SET_LOADING, payload: false });\n    }\n  };\n\n  // Supprimer un produit\n  const handleDelete = async (id) => {\n    if (window.confirm('Êtes-vous sûr de vouloir supprimer ce produit ?')) {\n      dispatch({ type: DataActions.SET_LOADING, payload: true });\n\n      try {\n        const result = await productAPI.delete(id);\n\n        if (result.success) {\n          // Suppression réussie via API\n          dispatch({ type: DataActions.DELETE_PRODUCT, payload: id });\n          console.log('Produit supprimé via API');\n        } else {\n          // Fallback: suppression locale\n          dispatch({ type: DataActions.DELETE_PRODUCT, payload: id });\n          console.log('Produit supprimé localement (mode démonstration)');\n        }\n      } catch (err) {\n        console.error('Erreur lors de la suppression du produit', err);\n        // Fallback: suppression locale en cas d'erreur\n        dispatch({ type: DataActions.DELETE_PRODUCT, payload: id });\n        console.log('Produit supprimé localement (erreur API)');\n      } finally {\n        dispatch({ type: DataActions.SET_LOADING, payload: false });\n      }\n    }\n  };\n\n  // Gestion de la recherche\n  const handleSearch = (e) => {\n    setSearchTerm(e.target.value);\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* En-tête */}\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">Gestion des Produits</h1>\n          <p className=\"text-sm text-gray-500\">\n            {filteredProducts.length} produit(s) • CRUD complet activé\n          </p>\n        </div>\n        <div className=\"flex space-x-3\">\n          <button\n            onClick={refreshData}\n            disabled={loading}\n            className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50\"\n          >\n            {loading ? 'Actualisation...' : 'Actualiser'}\n          </button>\n          <button\n            onClick={() => openModal()}\n            className=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700\"\n          >\n            Ajouter un produit\n          </button>\n        </div>\n      </div>\n\n      {/* Barre de recherche */}\n      <div className=\"bg-white p-4 rounded-lg shadow\">\n        <input\n          type=\"text\"\n          placeholder=\"Rechercher un produit...\"\n          value={searchTerm}\n          onChange={handleSearch}\n          className=\"w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n        />\n      </div>\n\n      {error && (\n        <div className=\"bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative\" role=\"alert\">\n          <strong className=\"font-bold\">Info!</strong>\n          <span className=\"block sm:inline\"> {error}</span>\n        </div>\n      )}\n\n      {/* Tableau des produits */}\n      <div className=\"bg-white rounded-lg shadow overflow-hidden\">\n        <div className=\"overflow-x-auto\">\n          <table className=\"min-w-full divide-y divide-gray-200\">\n            <thead className=\"bg-gray-50\">\n              <tr>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Image</th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Nom</th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Prix</th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Stock</th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Catégorie</th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Grillable</th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Actions</th>\n              </tr>\n            </thead>\n            <tbody className=\"bg-white divide-y divide-gray-200\">\n              {filteredProducts.map((product) => (\n                <tr key={product.id}>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"h-10 w-10 rounded-full overflow-hidden bg-gray-100\">\n                      {product.image_url ? (\n                        <img\n                          src={product.image_url}\n                          alt={product.name}\n                          className=\"h-full w-full object-cover\"\n                        />\n                      ) : (\n                        <div className=\"h-full w-full flex items-center justify-center text-gray-400\">\n                          <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n                          </svg>\n                        </div>\n                      )}\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">{product.name}</td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">{product.price} DH</td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${\n                      product.stock > 10 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'\n                    }`}>\n                      {product.stock}\n                    </span>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                    {product.category ? product.category.name : 'Non catégorisé'}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                    {product.is_grillable ? (\n                      <span className=\"text-green-600\">Oui</span>\n                    ) : (\n                      <span className=\"text-red-600\">Non</span>\n                    )}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                    <button\n                      onClick={() => openModal(product)}\n                      className=\"text-indigo-600 hover:text-indigo-900 mr-3\"\n                    >\n                      Modifier\n                    </button>\n                    <button\n                      onClick={() => handleDelete(product.id)}\n                      className=\"text-red-600 hover:text-red-900\"\n                    >\n                      Supprimer\n                    </button>\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      </div>\n\n      {/* Modal pour ajouter/modifier un produit */}\n      {isModalOpen && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n          <div className=\"bg-white rounded-lg shadow-lg w-full max-w-md mx-4\">\n            <div className=\"px-6 py-4 border-b\">\n              <h3 className=\"text-lg font-semibold\">\n                {currentProduct ? 'Modifier le produit' : 'Ajouter un produit'}\n              </h3>\n            </div>\n            <form onSubmit={handleSubmit}>\n              <div className=\"p-6\">\n                <div className=\"mb-4\">\n                  <label htmlFor=\"name\" className=\"block text-gray-700 mb-2\">Nom</label>\n                  <input\n                    type=\"text\"\n                    id=\"name\"\n                    name=\"name\"\n                    value={formData.name}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n                    required\n                  />\n                </div>\n\n                <div className=\"mb-4\">\n                  <label htmlFor=\"description\" className=\"block text-gray-700 mb-2\">Description</label>\n                  <textarea\n                    id=\"description\"\n                    name=\"description\"\n                    value={formData.description}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n                    rows=\"3\"\n                  ></textarea>\n                </div>\n\n                <div className=\"grid grid-cols-2 gap-4 mb-4\">\n                  <div>\n                    <label htmlFor=\"price\" className=\"block text-gray-700 mb-2\">Prix (DH)</label>\n                    <input\n                      type=\"number\"\n                      id=\"price\"\n                      name=\"price\"\n                      value={formData.price}\n                      onChange={handleInputChange}\n                      className=\"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n                      step=\"0.01\"\n                      min=\"0\"\n                      required\n                    />\n                  </div>\n\n                  <div>\n                    <label htmlFor=\"stock\" className=\"block text-gray-700 mb-2\">Stock</label>\n                    <input\n                      type=\"number\"\n                      id=\"stock\"\n                      name=\"stock\"\n                      value={formData.stock}\n                      onChange={handleInputChange}\n                      className=\"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n                      min=\"0\"\n                      required\n                    />\n                  </div>\n                </div>\n\n                <div className=\"mb-4\">\n                  <label htmlFor=\"category_id\" className=\"block text-gray-700 mb-2\">Catégorie</label>\n                  <select\n                    id=\"category_id\"\n                    name=\"category_id\"\n                    value={formData.category_id}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n                    required\n                  >\n                    <option value=\"\">Sélectionner une catégorie</option>\n                    {categories.map(category => (\n                      <option key={category.id} value={category.id}>\n                        {category.name}\n                      </option>\n                    ))}\n                  </select>\n                </div>\n\n                <div className=\"mb-4\">\n                  <label htmlFor=\"image\" className=\"block text-gray-700 mb-2\">Image</label>\n                  <input\n                    type=\"file\"\n                    id=\"image\"\n                    name=\"image\"\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n                    accept=\"image/*\"\n                  />\n                  {currentProduct && currentProduct.image_url && (\n                    <div className=\"mt-2\">\n                      <p className=\"text-sm text-gray-500\">Image actuelle:</p>\n                      <div className=\"h-20 w-20 mt-1 rounded overflow-hidden bg-gray-100\">\n                        <img\n                          src={currentProduct.image_url}\n                          alt={currentProduct.name}\n                          className=\"h-full w-full object-cover\"\n                        />\n                      </div>\n                    </div>\n                  )}\n                </div>\n\n                <div className=\"mb-4\">\n                  <div className=\"flex items-center\">\n                    <input\n                      type=\"checkbox\"\n                      id=\"is_grillable\"\n                      name=\"is_grillable\"\n                      checked={formData.is_grillable}\n                      onChange={handleInputChange}\n                      className=\"h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded\"\n                    />\n                    <label htmlFor=\"is_grillable\" className=\"ml-2 block text-gray-700\">\n                      Produit grillable\n                    </label>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"px-6 py-4 bg-gray-50 border-t flex justify-end\">\n                <button\n                  type=\"button\"\n                  onClick={closeModal}\n                  className=\"px-4 py-2 border rounded-lg text-gray-700 mr-2 hover:bg-gray-100\"\n                >\n                  Annuler\n                </button>\n                <button\n                  type=\"submit\"\n                  disabled={loading}\n                  className=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50\"\n                >\n                  {loading ? 'Enregistrement...' : (currentProduct ? 'Mettre à jour' : 'Ajouter')}\n                </button>\n              </div>\n            </form>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default ProductsCRUD;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,2BAA2B;AACnD,SAASC,UAAU,QAAQ,2BAA2B;AACtD,SAASC,KAAK,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM;IAAEC,KAAK;IAAEC,QAAQ;IAAEC;EAAY,CAAC,GAAGT,OAAO,CAAC,CAAC;EAClD,MAAM;IAAEU,QAAQ;IAAEC,UAAU;IAAEC;EAAQ,CAAC,GAAGL,KAAK;EAE/C,MAAM,CAACM,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACiB,UAAU,EAAEC,aAAa,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACmB,WAAW,EAAEC,cAAc,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACqB,cAAc,EAAEC,iBAAiB,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACuB,QAAQ,EAAEC,WAAW,CAAC,GAAGxB,QAAQ,CAAC;IACvCyB,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,YAAY,EAAE,KAAK;IACnBC,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;;EAE5D;EACAC,SAAS,CAAC,MAAM;IACd,IAAIgB,UAAU,EAAE;MACd,MAAMiB,QAAQ,GAAGtB,QAAQ,CAACuB,MAAM,CAACC,OAAO,IACtCA,OAAO,CAACX,IAAI,CAACY,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACrB,UAAU,CAACoB,WAAW,CAAC,CAAC,CAAC,IAC7DD,OAAO,CAACV,WAAW,CAACW,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACrB,UAAU,CAACoB,WAAW,CAAC,CAAC,CACrE,CAAC;MACDJ,mBAAmB,CAACC,QAAQ,CAAC;IAC/B,CAAC,MAAM;MACLD,mBAAmB,CAACrB,QAAQ,CAAC;IAC/B;EACF,CAAC,EAAE,CAACA,QAAQ,EAAEK,UAAU,CAAC,CAAC;;EAE1B;EACA,MAAMsB,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B7B,QAAQ,CAAC;MAAE8B,IAAI,EAAE7B,WAAW,CAAC8B,WAAW;MAAEC,OAAO,EAAE;IAAK,CAAC,CAAC;IAE1D,IAAI;MACFC,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;MAC7D,MAAMC,MAAM,GAAG,MAAM1C,UAAU,CAAC2C,MAAM,CAAC,CAAC;MAExC,IAAID,MAAM,CAACE,OAAO,EAAE;QAClBrC,QAAQ,CAAC;UAAE8B,IAAI,EAAE7B,WAAW,CAACqC,YAAY;UAAEN,OAAO,EAAEG,MAAM,CAACI;QAAK,CAAC,CAAC;QAClEjC,QAAQ,CAAC,IAAI,CAAC;QACd2B,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;QAC1DxC,KAAK,CAAC2C,OAAO,CAAC,GAAGF,MAAM,CAACI,IAAI,CAACC,MAAM,gCAAgC,CAAC;MACtE,CAAC,MAAM;QACLlC,QAAQ,CAAC,gDAAgD,CAAC;QAC1D2B,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;QAC/DxC,KAAK,CAAC+C,IAAI,CAAC,gDAAgD,CAAC;MAC9D;IACF,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZT,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;MAC5D5B,QAAQ,CAAC,gDAAgD,CAAC;MAC1DZ,KAAK,CAACiD,OAAO,CAAC,mDAAmD,CAAC;IACpE,CAAC,SAAS;MACR3C,QAAQ,CAAC;QAAE8B,IAAI,EAAE7B,WAAW,CAAC8B,WAAW;QAAEC,OAAO,EAAE;MAAM,CAAC,CAAC;IAC7D;EACF,CAAC;;EAED;EACA,MAAMY,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAE9B,IAAI;MAAE+B,KAAK;MAAEhB,IAAI;MAAEiB,OAAO;MAAEC;IAAM,CAAC,GAAGH,CAAC,CAACI,MAAM;IAEtD,IAAInB,IAAI,KAAK,UAAU,EAAE;MACvBhB,WAAW,CAACoC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACnC,IAAI,GAAGgC;MAAQ,CAAC,CAAC,CAAC;IACrD,CAAC,MAAM,IAAIjB,IAAI,KAAK,MAAM,EAAE;MAC1BhB,WAAW,CAACoC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACnC,IAAI,GAAGiC,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,CAAC;IACtD,CAAC,MAAM;MACLlC,WAAW,CAACoC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACnC,IAAI,GAAG+B;MAAM,CAAC,CAAC,CAAC;IACnD;EACF,CAAC;;EAED;EACA,MAAMK,SAAS,GAAGA,CAACzB,OAAO,GAAG,IAAI,KAAK;IACpC,IAAIA,OAAO,EAAE;MACXd,iBAAiB,CAACc,OAAO,CAAC;MAC1BZ,WAAW,CAAC;QACVC,IAAI,EAAEW,OAAO,CAACX,IAAI;QAClBC,WAAW,EAAEU,OAAO,CAACV,WAAW;QAChCC,KAAK,EAAES,OAAO,CAACT,KAAK;QACpBC,KAAK,EAAEQ,OAAO,CAACR,KAAK;QACpBC,WAAW,EAAEO,OAAO,CAACP,WAAW;QAChCC,YAAY,EAAEM,OAAO,CAACN,YAAY;QAClCC,KAAK,EAAE;MACT,CAAC,CAAC;IACJ,CAAC,MAAM;MACLT,iBAAiB,CAAC,IAAI,CAAC;MACvBE,WAAW,CAAC;QACVC,IAAI,EAAE,EAAE;QACRC,WAAW,EAAE,EAAE;QACfC,KAAK,EAAE,EAAE;QACTC,KAAK,EAAE,EAAE;QACTC,WAAW,EAAE,EAAE;QACfC,YAAY,EAAE,KAAK;QACnBC,KAAK,EAAE;MACT,CAAC,CAAC;IACJ;IACAX,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;;EAED;EACA,MAAM0C,UAAU,GAAGA,CAAA,KAAM;IACvB1C,cAAc,CAAC,KAAK,CAAC;IACrBE,iBAAiB,CAAC,IAAI,CAAC;IACvBE,WAAW,CAAC;MACVC,IAAI,EAAE,EAAE;MACRC,WAAW,EAAE,EAAE;MACfC,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE,EAAE;MACTC,WAAW,EAAE,EAAE;MACfC,YAAY,EAAE,KAAK;MACnBC,KAAK,EAAE;IACT,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMgC,YAAY,GAAG,MAAOR,CAAC,IAAK;IAChCA,CAAC,CAACS,cAAc,CAAC,CAAC;IAClBtD,QAAQ,CAAC;MAAE8B,IAAI,EAAE7B,WAAW,CAAC8B,WAAW;MAAEC,OAAO,EAAE;IAAK,CAAC,CAAC;IAE1D,IAAI;MACF,MAAMuB,WAAW,GAAG;QAClB,GAAG1C,QAAQ;QACXI,KAAK,EAAEuC,UAAU,CAAC3C,QAAQ,CAACI,KAAK,CAAC;QACjCC,KAAK,EAAEuC,QAAQ,CAAC5C,QAAQ,CAACK,KAAK,CAAC;QAC/BC,WAAW,EAAEsC,QAAQ,CAAC5C,QAAQ,CAACM,WAAW;MAC5C,CAAC;MAED,IAAIR,cAAc,EAAE;QAClB;QACA,MAAMwB,MAAM,GAAG,MAAM1C,UAAU,CAACiE,MAAM,CAAC/C,cAAc,CAACgD,EAAE,EAAEJ,WAAW,CAAC;QAEtE,IAAIpB,MAAM,CAACE,OAAO,EAAE;UAClB;UACArC,QAAQ,CAAC;YACP8B,IAAI,EAAE7B,WAAW,CAAC2D,cAAc;YAChC5B,OAAO,EAAE;cAAE,GAAGG,MAAM,CAACI,IAAI;cAAEoB,EAAE,EAAEhD,cAAc,CAACgD;YAAG;UACnD,CAAC,CAAC;UACF1B,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;QAC3C,CAAC,MAAM;UACL;UACAlC,QAAQ,CAAC;YACP8B,IAAI,EAAE7B,WAAW,CAAC2D,cAAc;YAChC5B,OAAO,EAAE;cAAE,GAAGuB,WAAW;cAAEI,EAAE,EAAEhD,cAAc,CAACgD,EAAE;cAAEE,QAAQ,EAAE1D,UAAU,CAAC2D,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACJ,EAAE,KAAKJ,WAAW,CAACpC,WAAW;YAAE;UACzH,CAAC,CAAC;UACFc,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;QACnE;MACF,CAAC,MAAM;QACL;QACA,MAAMC,MAAM,GAAG,MAAM1C,UAAU,CAACuE,MAAM,CAACT,WAAW,CAAC;QAEnD,IAAIpB,MAAM,CAACE,OAAO,EAAE;UAClB;UACArC,QAAQ,CAAC;YAAE8B,IAAI,EAAE7B,WAAW,CAACgE,WAAW;YAAEjC,OAAO,EAAEG,MAAM,CAACI;UAAK,CAAC,CAAC;UACjEN,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;QACrC,CAAC,MAAM;UACL;UACA,MAAMgC,UAAU,GAAG;YACjB,GAAGX,WAAW;YACdI,EAAE,EAAEQ,IAAI,CAACC,GAAG,CAAC,CAAC;YACdP,QAAQ,EAAE1D,UAAU,CAAC2D,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACJ,EAAE,KAAKJ,WAAW,CAACpC,WAAW,CAAC;YACpEkD,SAAS,EAAE,iEAAiE;YAC5EC,IAAI,EAAEf,WAAW,CAACxC,IAAI,CAACY,WAAW,CAAC,CAAC,CAAC4C,OAAO,CAAC,MAAM,EAAE,GAAG;UAC1D,CAAC;UACDvE,QAAQ,CAAC;YAAE8B,IAAI,EAAE7B,WAAW,CAACgE,WAAW;YAAEjC,OAAO,EAAEkC;UAAW,CAAC,CAAC;UAChEjC,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;QAC7D;MACF;MAEAkB,UAAU,CAAC,CAAC;IACd,CAAC,CAAC,OAAOV,GAAG,EAAE;MACZT,OAAO,CAAC5B,KAAK,CAAC,6CAA6C,EAAEqC,GAAG,CAAC;MACjE;MACA,MAAMa,WAAW,GAAG;QAClB,GAAG1C,QAAQ;QACXI,KAAK,EAAEuC,UAAU,CAAC3C,QAAQ,CAACI,KAAK,CAAC;QACjCC,KAAK,EAAEuC,QAAQ,CAAC5C,QAAQ,CAACK,KAAK,CAAC;QAC/BC,WAAW,EAAEsC,QAAQ,CAAC5C,QAAQ,CAACM,WAAW;MAC5C,CAAC;MAED,IAAIR,cAAc,EAAE;QAClBX,QAAQ,CAAC;UACP8B,IAAI,EAAE7B,WAAW,CAAC2D,cAAc;UAChC5B,OAAO,EAAE;YAAE,GAAGuB,WAAW;YAAEI,EAAE,EAAEhD,cAAc,CAACgD,EAAE;YAAEE,QAAQ,EAAE1D,UAAU,CAAC2D,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACJ,EAAE,KAAKJ,WAAW,CAACpC,WAAW;UAAE;QACzH,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,MAAM+C,UAAU,GAAG;UACjB,GAAGX,WAAW;UACdI,EAAE,EAAEQ,IAAI,CAACC,GAAG,CAAC,CAAC;UACdP,QAAQ,EAAE1D,UAAU,CAAC2D,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACJ,EAAE,KAAKJ,WAAW,CAACpC,WAAW,CAAC;UACpEkD,SAAS,EAAE,iEAAiE;UAC5EC,IAAI,EAAEf,WAAW,CAACxC,IAAI,CAACY,WAAW,CAAC,CAAC,CAAC4C,OAAO,CAAC,MAAM,EAAE,GAAG;QAC1D,CAAC;QACDvE,QAAQ,CAAC;UAAE8B,IAAI,EAAE7B,WAAW,CAACgE,WAAW;UAAEjC,OAAO,EAAEkC;QAAW,CAAC,CAAC;MAClE;MACAd,UAAU,CAAC,CAAC;IACd,CAAC,SAAS;MACRpD,QAAQ,CAAC;QAAE8B,IAAI,EAAE7B,WAAW,CAAC8B,WAAW;QAAEC,OAAO,EAAE;MAAM,CAAC,CAAC;IAC7D;EACF,CAAC;;EAED;EACA,MAAMwC,YAAY,GAAG,MAAOb,EAAE,IAAK;IACjC,IAAIc,MAAM,CAACC,OAAO,CAAC,iDAAiD,CAAC,EAAE;MACrE1E,QAAQ,CAAC;QAAE8B,IAAI,EAAE7B,WAAW,CAAC8B,WAAW;QAAEC,OAAO,EAAE;MAAK,CAAC,CAAC;MAE1D,IAAI;QACF,MAAMG,MAAM,GAAG,MAAM1C,UAAU,CAACkF,MAAM,CAAChB,EAAE,CAAC;QAE1C,IAAIxB,MAAM,CAACE,OAAO,EAAE;UAClB;UACArC,QAAQ,CAAC;YAAE8B,IAAI,EAAE7B,WAAW,CAAC2E,cAAc;YAAE5C,OAAO,EAAE2B;UAAG,CAAC,CAAC;UAC3D1B,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;QACzC,CAAC,MAAM;UACL;UACAlC,QAAQ,CAAC;YAAE8B,IAAI,EAAE7B,WAAW,CAAC2E,cAAc;YAAE5C,OAAO,EAAE2B;UAAG,CAAC,CAAC;UAC3D1B,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;QACjE;MACF,CAAC,CAAC,OAAOQ,GAAG,EAAE;QACZT,OAAO,CAAC5B,KAAK,CAAC,0CAA0C,EAAEqC,GAAG,CAAC;QAC9D;QACA1C,QAAQ,CAAC;UAAE8B,IAAI,EAAE7B,WAAW,CAAC2E,cAAc;UAAE5C,OAAO,EAAE2B;QAAG,CAAC,CAAC;QAC3D1B,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;MACzD,CAAC,SAAS;QACRlC,QAAQ,CAAC;UAAE8B,IAAI,EAAE7B,WAAW,CAAC8B,WAAW;UAAEC,OAAO,EAAE;QAAM,CAAC,CAAC;MAC7D;IACF;EACF,CAAC;;EAED;EACA,MAAM6C,YAAY,GAAIhC,CAAC,IAAK;IAC1BrC,aAAa,CAACqC,CAAC,CAACI,MAAM,CAACH,KAAK,CAAC;EAC/B,CAAC;EAED,oBACElD,OAAA;IAAKkF,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBnF,OAAA;MAAKkF,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChDnF,OAAA;QAAAmF,QAAA,gBACEnF,OAAA;UAAIkF,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1EvF,OAAA;UAAGkF,SAAS,EAAC,uBAAuB;UAAAC,QAAA,GACjCzD,gBAAgB,CAACkB,MAAM,EAAC,2CAC3B;QAAA;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACNvF,OAAA;QAAKkF,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BnF,OAAA;UACEwF,OAAO,EAAEvD,WAAY;UACrBwD,QAAQ,EAAEjF,OAAQ;UAClB0E,SAAS,EAAC,mFAAmF;UAAAC,QAAA,EAE5F3E,OAAO,GAAG,kBAAkB,GAAG;QAAY;UAAA4E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC,eACTvF,OAAA;UACEwF,OAAO,EAAEA,CAAA,KAAMjC,SAAS,CAAC,CAAE;UAC3B2B,SAAS,EAAC,iEAAiE;UAAAC,QAAA,EAC5E;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNvF,OAAA;MAAKkF,SAAS,EAAC,gCAAgC;MAAAC,QAAA,eAC7CnF,OAAA;QACEkC,IAAI,EAAC,MAAM;QACXwD,WAAW,EAAC,0BAA0B;QACtCxC,KAAK,EAAEvC,UAAW;QAClBgF,QAAQ,EAAEV,YAAa;QACvBC,SAAS,EAAC;MAAyF;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAEL9E,KAAK,iBACJT,OAAA;MAAKkF,SAAS,EAAC,mFAAmF;MAACU,IAAI,EAAC,OAAO;MAAAT,QAAA,gBAC7GnF,OAAA;QAAQkF,SAAS,EAAC,WAAW;QAAAC,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC5CvF,OAAA;QAAMkF,SAAS,EAAC,iBAAiB;QAAAC,QAAA,GAAC,GAAC,EAAC1E,KAAK;MAAA;QAAA2E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9C,CACN,eAGDvF,OAAA;MAAKkF,SAAS,EAAC,4CAA4C;MAAAC,QAAA,eACzDnF,OAAA;QAAKkF,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9BnF,OAAA;UAAOkF,SAAS,EAAC,qCAAqC;UAAAC,QAAA,gBACpDnF,OAAA;YAAOkF,SAAS,EAAC,YAAY;YAAAC,QAAA,eAC3BnF,OAAA;cAAAmF,QAAA,gBACEnF,OAAA;gBAAIkF,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzGvF,OAAA;gBAAIkF,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvGvF,OAAA;gBAAIkF,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxGvF,OAAA;gBAAIkF,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzGvF,OAAA;gBAAIkF,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7GvF,OAAA;gBAAIkF,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7GvF,OAAA;gBAAIkF,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRvF,OAAA;YAAOkF,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EACjDzD,gBAAgB,CAACmE,GAAG,CAAE/D,OAAO,iBAC5B9B,OAAA;cAAAmF,QAAA,gBACEnF,OAAA;gBAAIkF,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,eACzCnF,OAAA;kBAAKkF,SAAS,EAAC,oDAAoD;kBAAAC,QAAA,EAChErD,OAAO,CAAC2C,SAAS,gBAChBzE,OAAA;oBACE8F,GAAG,EAAEhE,OAAO,CAAC2C,SAAU;oBACvBsB,GAAG,EAAEjE,OAAO,CAACX,IAAK;oBAClB+D,SAAS,EAAC;kBAA4B;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC,CAAC,gBAEFvF,OAAA;oBAAKkF,SAAS,EAAC,8DAA8D;oBAAAC,QAAA,eAC3EnF,OAAA;sBAAKgG,KAAK,EAAC,4BAA4B;sBAACd,SAAS,EAAC,SAAS;sBAACe,IAAI,EAAC,MAAM;sBAACC,OAAO,EAAC,WAAW;sBAACC,MAAM,EAAC,cAAc;sBAAAhB,QAAA,eAC/GnF,OAAA;wBAAMoG,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,WAAW,EAAE,CAAE;wBAACC,CAAC,EAAC;sBAA2J;wBAAAnB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLvF,OAAA;gBAAIkF,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,EAAErD,OAAO,CAACX;cAAI;gBAAAiE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACrFvF,OAAA;gBAAIkF,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,GAAErD,OAAO,CAACT,KAAK,EAAC,KAAG;cAAA;gBAAA+D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzFvF,OAAA;gBAAIkF,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,eAC/DnF,OAAA;kBAAMkF,SAAS,EAAE,iEACfpD,OAAO,CAACR,KAAK,GAAG,EAAE,GAAG,6BAA6B,GAAG,yBAAyB,EAC7E;kBAAA6D,QAAA,EACArD,OAAO,CAACR;gBAAK;kBAAA8D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACLvF,OAAA;gBAAIkF,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,EAC9DrD,OAAO,CAACmC,QAAQ,GAAGnC,OAAO,CAACmC,QAAQ,CAAC9C,IAAI,GAAG;cAAgB;gBAAAiE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC,eACLvF,OAAA;gBAAIkF,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,EAC9DrD,OAAO,CAACN,YAAY,gBACnBxB,OAAA;kBAAMkF,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,gBAE3CvF,OAAA;kBAAMkF,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cACzC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACLvF,OAAA;gBAAIkF,SAAS,EAAC,iDAAiD;gBAAAC,QAAA,gBAC7DnF,OAAA;kBACEwF,OAAO,EAAEA,CAAA,KAAMjC,SAAS,CAACzB,OAAO,CAAE;kBAClCoD,SAAS,EAAC,4CAA4C;kBAAAC,QAAA,EACvD;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTvF,OAAA;kBACEwF,OAAO,EAAEA,CAAA,KAAMZ,YAAY,CAAC9C,OAAO,CAACiC,EAAE,CAAE;kBACxCmB,SAAS,EAAC,iCAAiC;kBAAAC,QAAA,EAC5C;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC;YAAA,GAlDEzD,OAAO,CAACiC,EAAE;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAmDf,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL1E,WAAW,iBACVb,OAAA;MAAKkF,SAAS,EAAC,4EAA4E;MAAAC,QAAA,eACzFnF,OAAA;QAAKkF,SAAS,EAAC,oDAAoD;QAAAC,QAAA,gBACjEnF,OAAA;UAAKkF,SAAS,EAAC,oBAAoB;UAAAC,QAAA,eACjCnF,OAAA;YAAIkF,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAClCpE,cAAc,GAAG,qBAAqB,GAAG;UAAoB;YAAAqE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACNvF,OAAA;UAAMwG,QAAQ,EAAE/C,YAAa;UAAA0B,QAAA,gBAC3BnF,OAAA;YAAKkF,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAClBnF,OAAA;cAAKkF,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBnF,OAAA;gBAAOyG,OAAO,EAAC,MAAM;gBAACvB,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtEvF,OAAA;gBACEkC,IAAI,EAAC,MAAM;gBACX6B,EAAE,EAAC,MAAM;gBACT5C,IAAI,EAAC,MAAM;gBACX+B,KAAK,EAAEjC,QAAQ,CAACE,IAAK;gBACrBwE,QAAQ,EAAE3C,iBAAkB;gBAC5BkC,SAAS,EAAC,yFAAyF;gBACnGwB,QAAQ;cAAA;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENvF,OAAA;cAAKkF,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBnF,OAAA;gBAAOyG,OAAO,EAAC,aAAa;gBAACvB,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrFvF,OAAA;gBACE+D,EAAE,EAAC,aAAa;gBAChB5C,IAAI,EAAC,aAAa;gBAClB+B,KAAK,EAAEjC,QAAQ,CAACG,WAAY;gBAC5BuE,QAAQ,EAAE3C,iBAAkB;gBAC5BkC,SAAS,EAAC,yFAAyF;gBACnGyB,IAAI,EAAC;cAAG;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eAENvF,OAAA;cAAKkF,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1CnF,OAAA;gBAAAmF,QAAA,gBACEnF,OAAA;kBAAOyG,OAAO,EAAC,OAAO;kBAACvB,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC7EvF,OAAA;kBACEkC,IAAI,EAAC,QAAQ;kBACb6B,EAAE,EAAC,OAAO;kBACV5C,IAAI,EAAC,OAAO;kBACZ+B,KAAK,EAAEjC,QAAQ,CAACI,KAAM;kBACtBsE,QAAQ,EAAE3C,iBAAkB;kBAC5BkC,SAAS,EAAC,yFAAyF;kBACnG0B,IAAI,EAAC,MAAM;kBACXC,GAAG,EAAC,GAAG;kBACPH,QAAQ;gBAAA;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENvF,OAAA;gBAAAmF,QAAA,gBACEnF,OAAA;kBAAOyG,OAAO,EAAC,OAAO;kBAACvB,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACzEvF,OAAA;kBACEkC,IAAI,EAAC,QAAQ;kBACb6B,EAAE,EAAC,OAAO;kBACV5C,IAAI,EAAC,OAAO;kBACZ+B,KAAK,EAAEjC,QAAQ,CAACK,KAAM;kBACtBqE,QAAQ,EAAE3C,iBAAkB;kBAC5BkC,SAAS,EAAC,yFAAyF;kBACnG2B,GAAG,EAAC,GAAG;kBACPH,QAAQ;gBAAA;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENvF,OAAA;cAAKkF,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBnF,OAAA;gBAAOyG,OAAO,EAAC,aAAa;gBAACvB,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACnFvF,OAAA;gBACE+D,EAAE,EAAC,aAAa;gBAChB5C,IAAI,EAAC,aAAa;gBAClB+B,KAAK,EAAEjC,QAAQ,CAACM,WAAY;gBAC5BoE,QAAQ,EAAE3C,iBAAkB;gBAC5BkC,SAAS,EAAC,yFAAyF;gBACnGwB,QAAQ;gBAAAvB,QAAA,gBAERnF,OAAA;kBAAQkD,KAAK,EAAC,EAAE;kBAAAiC,QAAA,EAAC;gBAA0B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EACnDhF,UAAU,CAACsF,GAAG,CAAC5B,QAAQ,iBACtBjE,OAAA;kBAA0BkD,KAAK,EAAEe,QAAQ,CAACF,EAAG;kBAAAoB,QAAA,EAC1ClB,QAAQ,CAAC9C;gBAAI,GADH8C,QAAQ,CAACF,EAAE;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEhB,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENvF,OAAA;cAAKkF,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBnF,OAAA;gBAAOyG,OAAO,EAAC,OAAO;gBAACvB,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACzEvF,OAAA;gBACEkC,IAAI,EAAC,MAAM;gBACX6B,EAAE,EAAC,OAAO;gBACV5C,IAAI,EAAC,OAAO;gBACZwE,QAAQ,EAAE3C,iBAAkB;gBAC5BkC,SAAS,EAAC,yFAAyF;gBACnG4B,MAAM,EAAC;cAAS;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC,EACDxE,cAAc,IAAIA,cAAc,CAAC0D,SAAS,iBACzCzE,OAAA;gBAAKkF,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBnF,OAAA;kBAAGkF,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACxDvF,OAAA;kBAAKkF,SAAS,EAAC,oDAAoD;kBAAAC,QAAA,eACjEnF,OAAA;oBACE8F,GAAG,EAAE/E,cAAc,CAAC0D,SAAU;oBAC9BsB,GAAG,EAAEhF,cAAc,CAACI,IAAK;oBACzB+D,SAAS,EAAC;kBAA4B;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAENvF,OAAA;cAAKkF,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnBnF,OAAA;gBAAKkF,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChCnF,OAAA;kBACEkC,IAAI,EAAC,UAAU;kBACf6B,EAAE,EAAC,cAAc;kBACjB5C,IAAI,EAAC,cAAc;kBACnBgC,OAAO,EAAElC,QAAQ,CAACO,YAAa;kBAC/BmE,QAAQ,EAAE3C,iBAAkB;kBAC5BkC,SAAS,EAAC;gBAAqE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChF,CAAC,eACFvF,OAAA;kBAAOyG,OAAO,EAAC,cAAc;kBAACvB,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,EAAC;gBAEnE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENvF,OAAA;YAAKkF,SAAS,EAAC,gDAAgD;YAAAC,QAAA,gBAC7DnF,OAAA;cACEkC,IAAI,EAAC,QAAQ;cACbsD,OAAO,EAAEhC,UAAW;cACpB0B,SAAS,EAAC,kEAAkE;cAAAC,QAAA,EAC7E;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTvF,OAAA;cACEkC,IAAI,EAAC,QAAQ;cACbuD,QAAQ,EAAEjF,OAAQ;cAClB0E,SAAS,EAAC,qFAAqF;cAAAC,QAAA,EAE9F3E,OAAO,GAAG,mBAAmB,GAAIO,cAAc,GAAG,eAAe,GAAG;YAAU;cAAAqE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACrF,EAAA,CAvfID,YAAY;EAAA,QACyBL,OAAO;AAAA;AAAAmH,EAAA,GAD5C9G,YAAY;AAyflB,eAAeA,YAAY;AAAC,IAAA8G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}