{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Projet-Synthese\\\\frontend\\\\src\\\\components\\\\layout\\\\Header.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport { useCart } from '../../context/CartContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Header = () => {\n  _s();\n  var _authState$user, _authState$user2, _authState$user3;\n  const {\n    state: authState,\n    logout\n  } = useAuth();\n  const {\n    state: cartState\n  } = useCart();\n  const navigate = useNavigate();\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n  const cartItemCount = cartState !== null && cartState !== void 0 && cartState.items ? cartState.items.length : 0;\n  const handleLogout = () => {\n    logout();\n    navigate('/');\n  };\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    className: \"bg-white shadow-md\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 py-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center\",\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          className: \"text-2xl font-bold text-green-600\",\n          children: \"\\xC9picerie en ligne\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n          className: \"hidden md:flex items-center space-x-8\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            className: \"text-gray-700 hover:text-green-600 transition\",\n            children: \"Accueil\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 30,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/products\",\n            className: \"text-gray-700 hover:text-green-600 transition\",\n            children: \"Produits\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 33,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/contact\",\n            className: \"text-gray-700 hover:text-green-600 transition\",\n            children: \"Contact\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 36,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/about\",\n            className: \"text-gray-700 hover:text-green-600 transition\",\n            children: \"\\xC0 propos\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden md:flex items-center space-x-4\",\n          children: [authState && authState.isAuthenticated ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative group\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"flex items-center text-gray-700 hover:text-green-600 transition\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"mr-1\",\n                children: ((_authState$user = authState.user) === null || _authState$user === void 0 ? void 0 : _authState$user.name) || 'Mon compte'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 49,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-4 w-4\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M19 9l-7 7-7-7\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 51,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 50,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 48,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10 hidden group-hover:block\",\n              children: [((_authState$user2 = authState.user) === null || _authState$user2 === void 0 ? void 0 : _authState$user2.role) === 'admin' && /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/admin/dashboard\",\n                className: \"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                children: \"Administration\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 56,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/profile\",\n                className: \"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                children: \"Mon profil\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 60,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/orders\",\n                className: \"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                children: \"Mes commandes\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 63,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: handleLogout,\n                className: \"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                children: \"D\\xE9connexion\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 66,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/login\",\n              className: \"text-gray-700 hover:text-green-600 transition\",\n              children: \"Connexion\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/register\",\n              className: \"bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition\",\n              children: \"Inscription\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 79,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/cart\",\n            className: \"relative text-gray-700 hover:text-green-600 transition\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              className: \"h-6 w-6\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              stroke: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 87,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 15\n            }, this), cartItemCount > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"absolute -top-2 -right-2 bg-red-500 text-white text-xs w-5 h-5 flex items-center justify-center rounded-full\",\n              children: cartItemCount\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"md:hidden flex items-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/cart\",\n            className: \"relative text-gray-700\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              className: \"h-6 w-6\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              stroke: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 101,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 15\n            }, this), cartItemCount > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"absolute -top-2 -right-2 bg-red-500 text-white text-xs w-5 h-5 flex items-center justify-center rounded-full\",\n              children: cartItemCount\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setMobileMenuOpen(!mobileMenuOpen),\n            className: \"text-gray-700\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              className: \"h-6 w-6\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              stroke: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M4 6h16M4 12h16M4 18h16\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 114,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this), mobileMenuOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"md:hidden mt-4 pb-4\",\n        children: /*#__PURE__*/_jsxDEV(\"nav\", {\n          className: \"flex flex-col space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            className: \"text-gray-700 hover:text-green-600 transition\",\n            children: \"Accueil\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/products\",\n            className: \"text-gray-700 hover:text-green-600 transition\",\n            children: \"Produits\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/contact\",\n            className: \"text-gray-700 hover:text-green-600 transition\",\n            children: \"Contact\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/about\",\n            className: \"text-gray-700 hover:text-green-600 transition\",\n            children: \"\\xC0 propos\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 15\n          }, this), authState && authState.isAuthenticated ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"hr\", {\n              className: \"border-gray-200\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 19\n            }, this), ((_authState$user3 = authState.user) === null || _authState$user3 === void 0 ? void 0 : _authState$user3.role) === 'admin' && /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/admin\",\n              className: \"text-gray-700 hover:text-green-600 transition\",\n              children: \"Administration\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/profile\",\n              className: \"text-gray-700 hover:text-green-600 transition\",\n              children: \"Mon profil\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/orders\",\n              className: \"text-gray-700 hover:text-green-600 transition\",\n              children: \"Mes commandes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleLogout,\n              className: \"text-left text-gray-700 hover:text-green-600 transition\",\n              children: \"D\\xE9connexion\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"hr\", {\n              className: \"border-gray-200\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/login\",\n              className: \"text-gray-700 hover:text-green-600 transition\",\n              children: \"Connexion\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/register\",\n              className: \"text-gray-700 hover:text-green-600 transition\",\n              children: \"Inscription\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 20,\n    columnNumber: 5\n  }, this);\n};\n_s(Header, \"RiDdob9LDw84t1Bp3n5LyD0vxuQ=\", false, function () {\n  return [useAuth, useCart, useNavigate];\n});\n_c = Header;\nexport default Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");", "map": {"version": 3, "names": ["React", "useState", "Link", "useNavigate", "useAuth", "useCart", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Header", "_s", "_authState$user", "_authState$user2", "_authState$user3", "state", "authState", "logout", "cartState", "navigate", "mobileMenuOpen", "setMobileMenuOpen", "cartItemCount", "items", "length", "handleLogout", "className", "children", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "isAuthenticated", "user", "name", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "role", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet-Synthese/frontend/src/components/layout/Header.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport { useCart } from '../../context/CartContext';\n\nconst Header = () => {\n  const { state: authState, logout } = useAuth();\n  const { state: cartState } = useCart();\n  const navigate = useNavigate();\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n  \n  const cartItemCount = cartState?.items ? cartState.items.length : 0;\n  \n  const handleLogout = () => {\n    logout();\n    navigate('/');\n  };\n\n  return (\n    <header className=\"bg-white shadow-md\">\n      <div className=\"container mx-auto px-4 py-4\">\n        <div className=\"flex justify-between items-center\">\n          {/* Logo */}\n          <Link to=\"/\" className=\"text-2xl font-bold text-green-600\">\n            Épicerie en ligne\n          </Link>\n          \n          {/* Navigation - Desktop */}\n          <nav className=\"hidden md:flex items-center space-x-8\">\n            <Link to=\"/\" className=\"text-gray-700 hover:text-green-600 transition\">\n              Accueil\n            </Link>\n            <Link to=\"/products\" className=\"text-gray-700 hover:text-green-600 transition\">\n              Produits\n            </Link>\n            <Link to=\"/contact\" className=\"text-gray-700 hover:text-green-600 transition\">\n              Contact\n            </Link>\n            <Link to=\"/about\" className=\"text-gray-700 hover:text-green-600 transition\">\n              À propos\n            </Link>\n          </nav>\n          \n          {/* Actions - Desktop */}\n          <div className=\"hidden md:flex items-center space-x-4\">\n            {authState && authState.isAuthenticated ? (\n              <div className=\"relative group\">\n                <button className=\"flex items-center text-gray-700 hover:text-green-600 transition\">\n                  <span className=\"mr-1\">{authState.user?.name || 'Mon compte'}</span>\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n                  </svg>\n                </button>\n                <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10 hidden group-hover:block\">\n                  {authState.user?.role === 'admin' && (\n                    <Link to=\"/admin/dashboard\" className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\">\n                      Administration\n                    </Link>\n                  )}\n                  <Link to=\"/profile\" className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\">\n                    Mon profil\n                  </Link>\n                  <Link to=\"/orders\" className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\">\n                    Mes commandes\n                  </Link>\n                  <button \n                    onClick={handleLogout}\n                    className=\"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                  >\n                    Déconnexion\n                  </button>\n                </div>\n              </div>\n            ) : (\n              <>\n                <Link to=\"/login\" className=\"text-gray-700 hover:text-green-600 transition\">\n                  Connexion\n                </Link>\n                <Link to=\"/register\" className=\"bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition\">\n                  Inscription\n                </Link>\n              </>\n            )}\n            \n            <Link to=\"/cart\" className=\"relative text-gray-700 hover:text-green-600 transition\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z\" />\n              </svg>\n              {cartItemCount > 0 && (\n                <span className=\"absolute -top-2 -right-2 bg-red-500 text-white text-xs w-5 h-5 flex items-center justify-center rounded-full\">\n                  {cartItemCount}\n                </span>\n              )}\n            </Link>\n          </div>\n          \n          {/* Mobile menu button */}\n          <div className=\"md:hidden flex items-center space-x-4\">\n            <Link to=\"/cart\" className=\"relative text-gray-700\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z\" />\n              </svg>\n              {cartItemCount > 0 && (\n                <span className=\"absolute -top-2 -right-2 bg-red-500 text-white text-xs w-5 h-5 flex items-center justify-center rounded-full\">\n                  {cartItemCount}\n                </span>\n              )}\n            </Link>\n            <button \n              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}\n              className=\"text-gray-700\"\n            >\n              <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n              </svg>\n            </button>\n          </div>\n        </div>\n        \n        {/* Mobile menu */}\n        {mobileMenuOpen && (\n          <div className=\"md:hidden mt-4 pb-4\">\n            <nav className=\"flex flex-col space-y-4\">\n              <Link to=\"/\" className=\"text-gray-700 hover:text-green-600 transition\">\n                Accueil\n              </Link>\n              <Link to=\"/products\" className=\"text-gray-700 hover:text-green-600 transition\">\n                Produits\n              </Link>\n              <Link to=\"/contact\" className=\"text-gray-700 hover:text-green-600 transition\">\n                Contact\n              </Link>\n              <Link to=\"/about\" className=\"text-gray-700 hover:text-green-600 transition\">\n                À propos\n              </Link>\n              \n              {authState && authState.isAuthenticated ? (\n                <>\n                  <hr className=\"border-gray-200\" />\n                  {authState.user?.role === 'admin' && (\n                    <Link to=\"/admin\" className=\"text-gray-700 hover:text-green-600 transition\">\n                      Administration\n                    </Link>\n                  )}\n                  <Link to=\"/profile\" className=\"text-gray-700 hover:text-green-600 transition\">\n                    Mon profil\n                  </Link>\n                  <Link to=\"/orders\" className=\"text-gray-700 hover:text-green-600 transition\">\n                    Mes commandes\n                  </Link>\n                  <button \n                    onClick={handleLogout}\n                    className=\"text-left text-gray-700 hover:text-green-600 transition\"\n                  >\n                    Déconnexion\n                  </button>\n                </>\n              ) : (\n                <>\n                  <hr className=\"border-gray-200\" />\n                  <Link to=\"/login\" className=\"text-gray-700 hover:text-green-600 transition\">\n                    Connexion\n                  </Link>\n                  <Link to=\"/register\" className=\"text-gray-700 hover:text-green-600 transition\">\n                    Inscription\n                  </Link>\n                </>\n              )}\n            </nav>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,OAAO,QAAQ,2BAA2B;AACnD,SAASC,OAAO,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEpD,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,eAAA,EAAAC,gBAAA,EAAAC,gBAAA;EACnB,MAAM;IAAEC,KAAK,EAAEC,SAAS;IAAEC;EAAO,CAAC,GAAGb,OAAO,CAAC,CAAC;EAC9C,MAAM;IAAEW,KAAK,EAAEG;EAAU,CAAC,GAAGb,OAAO,CAAC,CAAC;EACtC,MAAMc,QAAQ,GAAGhB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACiB,cAAc,EAAEC,iBAAiB,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EAE3D,MAAMqB,aAAa,GAAGJ,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEK,KAAK,GAAGL,SAAS,CAACK,KAAK,CAACC,MAAM,GAAG,CAAC;EAEnE,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzBR,MAAM,CAAC,CAAC;IACRE,QAAQ,CAAC,GAAG,CAAC;EACf,CAAC;EAED,oBACEZ,OAAA;IAAQmB,SAAS,EAAC,oBAAoB;IAAAC,QAAA,eACpCpB,OAAA;MAAKmB,SAAS,EAAC,6BAA6B;MAAAC,QAAA,gBAC1CpB,OAAA;QAAKmB,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAEhDpB,OAAA,CAACL,IAAI;UAAC0B,EAAE,EAAC,GAAG;UAACF,SAAS,EAAC,mCAAmC;UAAAC,QAAA,EAAC;QAE3D;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAGPzB,OAAA;UAAKmB,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpDpB,OAAA,CAACL,IAAI;YAAC0B,EAAE,EAAC,GAAG;YAACF,SAAS,EAAC,+CAA+C;YAAAC,QAAA,EAAC;UAEvE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPzB,OAAA,CAACL,IAAI;YAAC0B,EAAE,EAAC,WAAW;YAACF,SAAS,EAAC,+CAA+C;YAAAC,QAAA,EAAC;UAE/E;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPzB,OAAA,CAACL,IAAI;YAAC0B,EAAE,EAAC,UAAU;YAACF,SAAS,EAAC,+CAA+C;YAAAC,QAAA,EAAC;UAE9E;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPzB,OAAA,CAACL,IAAI;YAAC0B,EAAE,EAAC,QAAQ;YAACF,SAAS,EAAC,+CAA+C;YAAAC,QAAA,EAAC;UAE5E;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGNzB,OAAA;UAAKmB,SAAS,EAAC,uCAAuC;UAAAC,QAAA,GACnDX,SAAS,IAAIA,SAAS,CAACiB,eAAe,gBACrC1B,OAAA;YAAKmB,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BpB,OAAA;cAAQmB,SAAS,EAAC,iEAAiE;cAAAC,QAAA,gBACjFpB,OAAA;gBAAMmB,SAAS,EAAC,MAAM;gBAAAC,QAAA,EAAE,EAAAf,eAAA,GAAAI,SAAS,CAACkB,IAAI,cAAAtB,eAAA,uBAAdA,eAAA,CAAgBuB,IAAI,KAAI;cAAY;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACpEzB,OAAA;gBAAK6B,KAAK,EAAC,4BAA4B;gBAACV,SAAS,EAAC,SAAS;gBAACW,IAAI,EAAC,MAAM;gBAACC,OAAO,EAAC,WAAW;gBAACC,MAAM,EAAC,cAAc;gBAAAZ,QAAA,eAC/GpB,OAAA;kBAAMiC,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAACC,CAAC,EAAC;gBAAgB;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACTzB,OAAA;cAAKmB,SAAS,EAAC,6FAA6F;cAAAC,QAAA,GACzG,EAAAd,gBAAA,GAAAG,SAAS,CAACkB,IAAI,cAAArB,gBAAA,uBAAdA,gBAAA,CAAgB+B,IAAI,MAAK,OAAO,iBAC/BrC,OAAA,CAACL,IAAI;gBAAC0B,EAAE,EAAC,kBAAkB;gBAACF,SAAS,EAAC,yDAAyD;gBAAAC,QAAA,EAAC;cAEhG;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACP,eACDzB,OAAA,CAACL,IAAI;gBAAC0B,EAAE,EAAC,UAAU;gBAACF,SAAS,EAAC,yDAAyD;gBAAAC,QAAA,EAAC;cAExF;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACPzB,OAAA,CAACL,IAAI;gBAAC0B,EAAE,EAAC,SAAS;gBAACF,SAAS,EAAC,yDAAyD;gBAAAC,QAAA,EAAC;cAEvF;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACPzB,OAAA;gBACEsC,OAAO,EAAEpB,YAAa;gBACtBC,SAAS,EAAC,0EAA0E;gBAAAC,QAAA,EACrF;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,gBAENzB,OAAA,CAAAE,SAAA;YAAAkB,QAAA,gBACEpB,OAAA,CAACL,IAAI;cAAC0B,EAAE,EAAC,QAAQ;cAACF,SAAS,EAAC,+CAA+C;cAAAC,QAAA,EAAC;YAE5E;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPzB,OAAA,CAACL,IAAI;cAAC0B,EAAE,EAAC,WAAW;cAACF,SAAS,EAAC,yEAAyE;cAAAC,QAAA,EAAC;YAEzG;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,eACP,CACH,eAEDzB,OAAA,CAACL,IAAI;YAAC0B,EAAE,EAAC,OAAO;YAACF,SAAS,EAAC,wDAAwD;YAAAC,QAAA,gBACjFpB,OAAA;cAAK6B,KAAK,EAAC,4BAA4B;cAACV,SAAS,EAAC,SAAS;cAACW,IAAI,EAAC,MAAM;cAACC,OAAO,EAAC,WAAW;cAACC,MAAM,EAAC,cAAc;cAAAZ,QAAA,eAC/GpB,OAAA;gBAAMiC,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAAsJ;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3N,CAAC,EACLV,aAAa,GAAG,CAAC,iBAChBf,OAAA;cAAMmB,SAAS,EAAC,8GAA8G;cAAAC,QAAA,EAC3HL;YAAa;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGNzB,OAAA;UAAKmB,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpDpB,OAAA,CAACL,IAAI;YAAC0B,EAAE,EAAC,OAAO;YAACF,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACjDpB,OAAA;cAAK6B,KAAK,EAAC,4BAA4B;cAACV,SAAS,EAAC,SAAS;cAACW,IAAI,EAAC,MAAM;cAACC,OAAO,EAAC,WAAW;cAACC,MAAM,EAAC,cAAc;cAAAZ,QAAA,eAC/GpB,OAAA;gBAAMiC,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAAsJ;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3N,CAAC,EACLV,aAAa,GAAG,CAAC,iBAChBf,OAAA;cAAMmB,SAAS,EAAC,8GAA8G;cAAAC,QAAA,EAC3HL;YAAa;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,eACPzB,OAAA;YACEsC,OAAO,EAAEA,CAAA,KAAMxB,iBAAiB,CAAC,CAACD,cAAc,CAAE;YAClDM,SAAS,EAAC,eAAe;YAAAC,QAAA,eAEzBpB,OAAA;cAAK6B,KAAK,EAAC,4BAA4B;cAACV,SAAS,EAAC,SAAS;cAACW,IAAI,EAAC,MAAM;cAACC,OAAO,EAAC,WAAW;cAACC,MAAM,EAAC,cAAc;cAAAZ,QAAA,eAC/GpB,OAAA;gBAAMiC,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAAyB;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9F;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLZ,cAAc,iBACbb,OAAA;QAAKmB,SAAS,EAAC,qBAAqB;QAAAC,QAAA,eAClCpB,OAAA;UAAKmB,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACtCpB,OAAA,CAACL,IAAI;YAAC0B,EAAE,EAAC,GAAG;YAACF,SAAS,EAAC,+CAA+C;YAAAC,QAAA,EAAC;UAEvE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPzB,OAAA,CAACL,IAAI;YAAC0B,EAAE,EAAC,WAAW;YAACF,SAAS,EAAC,+CAA+C;YAAAC,QAAA,EAAC;UAE/E;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPzB,OAAA,CAACL,IAAI;YAAC0B,EAAE,EAAC,UAAU;YAACF,SAAS,EAAC,+CAA+C;YAAAC,QAAA,EAAC;UAE9E;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPzB,OAAA,CAACL,IAAI;YAAC0B,EAAE,EAAC,QAAQ;YAACF,SAAS,EAAC,+CAA+C;YAAAC,QAAA,EAAC;UAE5E;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EAENhB,SAAS,IAAIA,SAAS,CAACiB,eAAe,gBACrC1B,OAAA,CAAAE,SAAA;YAAAkB,QAAA,gBACEpB,OAAA;cAAImB,SAAS,EAAC;YAAiB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EACjC,EAAAlB,gBAAA,GAAAE,SAAS,CAACkB,IAAI,cAAApB,gBAAA,uBAAdA,gBAAA,CAAgB8B,IAAI,MAAK,OAAO,iBAC/BrC,OAAA,CAACL,IAAI;cAAC0B,EAAE,EAAC,QAAQ;cAACF,SAAS,EAAC,+CAA+C;cAAAC,QAAA,EAAC;YAE5E;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CACP,eACDzB,OAAA,CAACL,IAAI;cAAC0B,EAAE,EAAC,UAAU;cAACF,SAAS,EAAC,+CAA+C;cAAAC,QAAA,EAAC;YAE9E;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPzB,OAAA,CAACL,IAAI;cAAC0B,EAAE,EAAC,SAAS;cAACF,SAAS,EAAC,+CAA+C;cAAAC,QAAA,EAAC;YAE7E;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPzB,OAAA;cACEsC,OAAO,EAAEpB,YAAa;cACtBC,SAAS,EAAC,yDAAyD;cAAAC,QAAA,EACpE;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA,eACT,CAAC,gBAEHzB,OAAA,CAAAE,SAAA;YAAAkB,QAAA,gBACEpB,OAAA;cAAImB,SAAS,EAAC;YAAiB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClCzB,OAAA,CAACL,IAAI;cAAC0B,EAAE,EAAC,QAAQ;cAACF,SAAS,EAAC,+CAA+C;cAAAC,QAAA,EAAC;YAE5E;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPzB,OAAA,CAACL,IAAI;cAAC0B,EAAE,EAAC,WAAW;cAACF,SAAS,EAAC,+CAA+C;cAAAC,QAAA,EAAC;YAE/E;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,eACP,CACH;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACrB,EAAA,CAzKID,MAAM;EAAA,QAC2BN,OAAO,EACfC,OAAO,EACnBF,WAAW;AAAA;AAAA2C,EAAA,GAHxBpC,MAAM;AA2KZ,eAAeA,MAAM;AAAC,IAAAoC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}