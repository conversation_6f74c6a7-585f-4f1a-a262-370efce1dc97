<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->foreignId('category_id')->constrained()->onDelete('cascade');
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description');
            $table->decimal('price', 10, 2);
            $table->decimal('sale_price', 10, 2)->nullable();
            $table->integer('stock')->default(0);
            $table->integer('min_stock')->default(5); // Alerte stock bas
            $table->string('sku')->unique();
            $table->string('image');
            $table->json('additional_images')->nullable();
            $table->boolean('is_grilled')->default(false);
            $table->boolean('can_be_grilled')->default(false);
            $table->decimal('weight', 8, 2)->nullable(); // en grammes
            $table->boolean('is_featured')->default(false);
            $table->boolean('is_active')->default(true);
            $table->dateTime('sale_start')->nullable(); // Début de la promotion
            $table->dateTime('sale_end')->nullable(); // Fin de la promotion
            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down()
    {
        Schema::dropIfExists('products');
    }
};





