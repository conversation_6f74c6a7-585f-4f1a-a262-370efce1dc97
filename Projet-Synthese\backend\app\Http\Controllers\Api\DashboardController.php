<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Product;
use App\Models\Order;
use App\Models\Category;
use App\Models\Review;
use App\Models\Promotion;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class DashboardController extends Controller
{

    public function getStats()
    {
        try {
            // Statistiques générales
            $totalUsers = User::where('role', 'client')->count();
            $totalProducts = Product::where('is_active', true)->count();
            $totalOrders = Order::count();
            $totalRevenue = Order::where('status', 'completed')->sum('total');

            // Statistiques des 30 derniers jours
            $last30Days = Carbon::now()->subDays(30);

            $newUsersLast30Days = User::where('role', 'client')
                ->where('created_at', '>=', $last30Days)
                ->count();

            $ordersLast30Days = Order::where('created_at', '>=', $last30Days)->count();

            $revenueLast30Days = Order::where('status', 'completed')
                ->where('created_at', '>=', $last30Days)
                ->sum('total');

            // Commandes par statut
            $ordersByStatus = Order::select('status', DB::raw('count(*) as count'))
                ->groupBy('status')
                ->get()
                ->pluck('count', 'status');

            // Produits les plus vendus (top 5)
            $topProducts = DB::table('order_items')
                ->join('products', 'order_items.product_id', '=', 'products.id')
                ->join('orders', 'order_items.order_id', '=', 'orders.id')
                ->where('orders.status', 'completed')
                ->select('products.name', DB::raw('SUM(order_items.quantity) as total_sold'))
                ->groupBy('products.id', 'products.name')
                ->orderBy('total_sold', 'desc')
                ->limit(5)
                ->get();

            // Revenus par mois (12 derniers mois)
            $monthlyRevenue = Order::where('status', 'completed')
                ->where('created_at', '>=', Carbon::now()->subMonths(12))
                ->select(
                    DB::raw('YEAR(created_at) as year'),
                    DB::raw('MONTH(created_at) as month'),
                    DB::raw('SUM(total) as revenue')
                )
                ->groupBy('year', 'month')
                ->orderBy('year', 'asc')
                ->orderBy('month', 'asc')
                ->get()
                ->map(function ($item) {
                    return [
                        'month' => Carbon::createFromDate($item->year, $item->month, 1)->format('M Y'),
                        'revenue' => $item->revenue
                    ];
                });

            // Commandes par jour (7 derniers jours)
            $dailyOrders = Order::where('created_at', '>=', Carbon::now()->subDays(7))
                ->select(
                    DB::raw('DATE(created_at) as date'),
                    DB::raw('COUNT(*) as count')
                )
                ->groupBy('date')
                ->orderBy('date', 'asc')
                ->get()
                ->map(function ($item) {
                    return [
                        'date' => Carbon::parse($item->date)->format('d/m'),
                        'count' => $item->count
                    ];
                });

            // Catégories les plus populaires
            $popularCategories = DB::table('order_items')
                ->join('products', 'order_items.product_id', '=', 'products.id')
                ->join('categories', 'products.category_id', '=', 'categories.id')
                ->join('orders', 'order_items.order_id', '=', 'orders.id')
                ->where('orders.status', 'completed')
                ->select('categories.name', DB::raw('SUM(order_items.quantity) as total_sold'))
                ->groupBy('categories.id', 'categories.name')
                ->orderBy('total_sold', 'desc')
                ->get();

            // Promotions actives
            $activePromotions = Promotion::where('is_active', true)
                ->where('start_date', '<=', now())
                ->where('end_date', '>=', now())
                ->count();

            // Avis récents
            $recentReviews = Review::with(['user', 'product'])
                ->latest()
                ->limit(5)
                ->get();

            // Produits en rupture de stock
            $lowStockProducts = Product::where('stock_quantity', '<=', 5)
                ->where('is_active', true)
                ->count();

            return response()->json([
                'status' => 'success',
                'data' => [
                    'overview' => [
                        'total_users' => $totalUsers,
                        'total_products' => $totalProducts,
                        'total_orders' => $totalOrders,
                        'total_revenue' => $totalRevenue,
                        'new_users_last_30_days' => $newUsersLast30Days,
                        'orders_last_30_days' => $ordersLast30Days,
                        'revenue_last_30_days' => $revenueLast30Days,
                        'active_promotions' => $activePromotions,
                        'low_stock_products' => $lowStockProducts
                    ],
                    'charts' => [
                        'orders_by_status' => $ordersByStatus,
                        'monthly_revenue' => $monthlyRevenue,
                        'daily_orders' => $dailyOrders,
                        'popular_categories' => $popularCategories
                    ],
                    'top_products' => $topProducts,
                    'recent_reviews' => $recentReviews
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Erreur lors de la récupération des statistiques',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function getRecentActivity()
    {
        try {
            // Activités récentes (commandes, inscriptions, avis)
            $recentOrders = Order::with(['user', 'items.product'])
                ->latest()
                ->limit(10)
                ->get()
                ->map(function ($order) {
                    return [
                        'type' => 'order',
                        'id' => $order->id,
                        'user' => $order->user->name,
                        'amount' => $order->total,
                        'status' => $order->status,
                        'created_at' => $order->created_at
                    ];
                });

            $recentUsers = User::where('role', 'client')
                ->latest()
                ->limit(5)
                ->get()
                ->map(function ($user) {
                    return [
                        'type' => 'user',
                        'id' => $user->id,
                        'name' => $user->name,
                        'email' => $user->email,
                        'created_at' => $user->created_at
                    ];
                });

            $recentReviews = Review::with(['user', 'product'])
                ->latest()
                ->limit(5)
                ->get()
                ->map(function ($review) {
                    return [
                        'type' => 'review',
                        'id' => $review->id,
                        'user' => $review->user->name,
                        'product' => $review->product->name,
                        'rating' => $review->rating,
                        'created_at' => $review->created_at
                    ];
                });

            // Combiner et trier par date
            $activities = collect()
                ->merge($recentOrders)
                ->merge($recentUsers)
                ->merge($recentReviews)
                ->sortByDesc('created_at')
                ->take(15)
                ->values();

            return response()->json([
                'status' => 'success',
                'data' => $activities
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Erreur lors de la récupération des activités récentes',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
