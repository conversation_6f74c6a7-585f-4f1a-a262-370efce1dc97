{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Projet-Synthese\\\\frontend\\\\src\\\\pages\\\\Admin\\\\Promotions.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { toast } from 'react-toastify';\nimport { FaPlus, FaEdit, FaTrash, FaEye, FaTag, FaPercent, FaDollarSign, FaShippingFast } from 'react-icons/fa';\nimport useSafeAuth from '../../hooks/useSafeAuth';\nimport { promotionService } from '../../services/api/promotionService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Promotions = () => {\n  _s();\n  const {\n    state: authState\n  } = useSafeAuth();\n  const [promotions, setPromotions] = useState([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [showModal, setShowModal] = useState(false);\n  const [editingPromotion, setEditingPromotion] = useState(null);\n  const [formData, setFormData] = useState({\n    name: '',\n    code: '',\n    description: '',\n    type: 'percentage',\n    value: '',\n    minimum_amount: '',\n    maximum_discount: '',\n    usage_limit: '',\n    start_date: '',\n    end_date: '',\n    is_active: true\n  });\n  useEffect(() => {\n    fetchPromotions();\n  }, []);\n  const fetchPromotions = async () => {\n    try {\n      setIsLoading(true);\n      const data = await promotionService.getAllPromotions(authState === null || authState === void 0 ? void 0 : authState.token);\n      setPromotions(data);\n    } catch (error) {\n      console.log('API non disponible, utilisation des données de test');\n\n      // Données de test pour les promotions\n      const testPromotions = [{\n        id: 1,\n        name: 'Bienvenue 10%',\n        code: 'BIENVENUE10',\n        description: 'Réduction de 10% pour les nouveaux clients sur leur première commande',\n        type: 'percentage',\n        value: 10,\n        minimum_amount: 50,\n        maximum_discount: null,\n        usage_limit: 100,\n        used_count: 15,\n        start_date: new Date().toISOString(),\n        end_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),\n        is_active: true\n      }, {\n        id: 2,\n        name: 'Livraison Gratuite',\n        code: 'LIVRAISON',\n        description: 'Livraison gratuite pour toute commande supérieure à 30€',\n        type: 'free_shipping',\n        value: 0,\n        minimum_amount: 30,\n        maximum_discount: null,\n        usage_limit: 50,\n        used_count: 8,\n        start_date: new Date().toISOString(),\n        end_date: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString(),\n        is_active: true\n      }, {\n        id: 3,\n        name: 'Réduction 5€',\n        code: 'REDUCTION5',\n        description: '5€ de réduction immédiate sur votre commande',\n        type: 'fixed',\n        value: 5,\n        minimum_amount: 25,\n        maximum_discount: null,\n        usage_limit: 30,\n        used_count: 12,\n        start_date: new Date().toISOString(),\n        end_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),\n        is_active: true\n      }, {\n        id: 4,\n        name: 'Été 2024',\n        code: 'ETE2024',\n        description: '15% de réduction sur tous les produits grillés',\n        type: 'percentage',\n        value: 15,\n        minimum_amount: 40,\n        maximum_discount: 20,\n        usage_limit: 200,\n        used_count: 45,\n        start_date: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),\n        end_date: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000).toISOString(),\n        is_active: true\n      }, {\n        id: 5,\n        name: 'Black Friday',\n        code: 'BLACKFRIDAY',\n        description: '25% de réduction exceptionnelle',\n        type: 'percentage',\n        value: 25,\n        minimum_amount: 100,\n        maximum_discount: 50,\n        usage_limit: 500,\n        used_count: 500,\n        start_date: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString(),\n        end_date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),\n        is_active: false\n      }];\n      setPromotions(testPromotions);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    try {\n      const promotionData = {\n        ...formData,\n        value: parseFloat(formData.value),\n        minimum_amount: formData.minimum_amount ? parseFloat(formData.minimum_amount) : null,\n        maximum_discount: formData.maximum_discount ? parseFloat(formData.maximum_discount) : null,\n        usage_limit: formData.usage_limit ? parseInt(formData.usage_limit) : null\n      };\n      if (editingPromotion) {\n        try {\n          await promotionService.updatePromotion(editingPromotion.id, promotionData, authState === null || authState === void 0 ? void 0 : authState.token);\n          toast.success('Promotion mise à jour avec succès');\n        } catch (apiError) {\n          // Mode démonstration - simulation de la mise à jour\n          const updatedPromotions = promotions.map(p => p.id === editingPromotion.id ? {\n            ...p,\n            ...promotionData,\n            id: editingPromotion.id,\n            used_count: p.used_count\n          } : p);\n          setPromotions(updatedPromotions);\n          toast.success('Promotion mise à jour avec succès (mode démonstration)');\n        }\n      } else {\n        try {\n          await promotionService.createPromotion(promotionData, authState === null || authState === void 0 ? void 0 : authState.token);\n          toast.success('Promotion créée avec succès');\n        } catch (apiError) {\n          // Mode démonstration - simulation de la création\n          const newPromotion = {\n            ...promotionData,\n            id: Math.max(...promotions.map(p => p.id)) + 1,\n            used_count: 0\n          };\n          setPromotions([...promotions, newPromotion]);\n          toast.success('Promotion créée avec succès (mode démonstration)');\n        }\n      }\n      setShowModal(false);\n      setEditingPromotion(null);\n      resetForm();\n    } catch (error) {\n      var _error$response, _error$response$data;\n      const errorMessage = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Erreur lors de la sauvegarde';\n      toast.error(errorMessage);\n    }\n  };\n  const handleEdit = promotion => {\n    var _promotion$minimum_am, _promotion$maximum_di, _promotion$usage_limi;\n    setEditingPromotion(promotion);\n    setFormData({\n      name: promotion.name,\n      code: promotion.code,\n      description: promotion.description || '',\n      type: promotion.type,\n      value: promotion.value.toString(),\n      minimum_amount: ((_promotion$minimum_am = promotion.minimum_amount) === null || _promotion$minimum_am === void 0 ? void 0 : _promotion$minimum_am.toString()) || '',\n      maximum_discount: ((_promotion$maximum_di = promotion.maximum_discount) === null || _promotion$maximum_di === void 0 ? void 0 : _promotion$maximum_di.toString()) || '',\n      usage_limit: ((_promotion$usage_limi = promotion.usage_limit) === null || _promotion$usage_limi === void 0 ? void 0 : _promotion$usage_limi.toString()) || '',\n      start_date: promotion.start_date.split('T')[0],\n      end_date: promotion.end_date.split('T')[0],\n      is_active: promotion.is_active\n    });\n    setShowModal(true);\n  };\n  const handleDelete = async id => {\n    if (window.confirm('Êtes-vous sûr de vouloir supprimer cette promotion ?')) {\n      try {\n        await promotionService.deletePromotion(id, authState === null || authState === void 0 ? void 0 : authState.token);\n        toast.success('Promotion supprimée avec succès');\n        fetchPromotions();\n      } catch (error) {\n        // Mode démonstration - simulation de la suppression\n        const updatedPromotions = promotions.filter(p => p.id !== id);\n        setPromotions(updatedPromotions);\n        toast.success('Promotion supprimée avec succès (mode démonstration)');\n      }\n    }\n  };\n  const resetForm = () => {\n    setFormData({\n      name: '',\n      code: '',\n      description: '',\n      type: 'percentage',\n      value: '',\n      minimum_amount: '',\n      maximum_discount: '',\n      usage_limit: '',\n      start_date: '',\n      end_date: '',\n      is_active: true\n    });\n  };\n  const getTypeIcon = type => {\n    switch (type) {\n      case 'percentage':\n        return /*#__PURE__*/_jsxDEV(FaPercent, {\n          className: \"text-blue-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 16\n        }, this);\n      case 'fixed':\n        return /*#__PURE__*/_jsxDEV(FaDollarSign, {\n          className: \"text-green-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 16\n        }, this);\n      case 'free_shipping':\n        return /*#__PURE__*/_jsxDEV(FaShippingFast, {\n          className: \"text-purple-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(FaTag, {\n          className: \"text-gray-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  const getTypeLabel = type => {\n    switch (type) {\n      case 'percentage':\n        return 'Pourcentage';\n      case 'fixed':\n        return 'Montant fixe';\n      case 'free_shipping':\n        return 'Livraison gratuite';\n      default:\n        return type;\n    }\n  };\n  const formatValue = promotion => {\n    switch (promotion.type) {\n      case 'percentage':\n        return `${promotion.value}%`;\n      case 'fixed':\n        return `${promotion.value}$`;\n      case 'free_shipping':\n        return 'Gratuite';\n      default:\n        return promotion.value;\n    }\n  };\n  const getStatusBadge = promotion => {\n    const now = new Date();\n    const startDate = new Date(promotion.start_date);\n    const endDate = new Date(promotion.end_date);\n    if (!promotion.is_active) {\n      return /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full\",\n        children: \"Inactive\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 14\n      }, this);\n    }\n    if (now < startDate) {\n      return /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded-full\",\n        children: \"\\xC0 venir\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 273,\n        columnNumber: 14\n      }, this);\n    }\n    if (now > endDate) {\n      return /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"px-2 py-1 text-xs bg-red-100 text-red-800 rounded-full\",\n        children: \"Expir\\xE9e\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 277,\n        columnNumber: 14\n      }, this);\n    }\n    if (promotion.usage_limit && promotion.used_count >= promotion.usage_limit) {\n      return /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"px-2 py-1 text-xs bg-orange-100 text-orange-800 rounded-full\",\n        children: \"\\xC9puis\\xE9e\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 281,\n        columnNumber: 14\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(\"span\", {\n      className: \"px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full\",\n      children: \"Active\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 284,\n      columnNumber: 12\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-2xl font-bold text-gray-900\",\n        children: \"Gestion des Promotions\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 290,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => {\n          resetForm();\n          setEditingPromotion(null);\n          setShowModal(true);\n        },\n        className: \"btn-primary flex items-center space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(FaPlus, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Nouvelle promotion\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 291,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 289,\n      columnNumber: 7\n    }, this), showModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-gray-900 mb-4\",\n            children: editingPromotion ? 'Modifier la promotion' : 'Nouvelle promotion'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleSubmit,\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-1\",\n                  children: \"Nom de la promotion *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 316,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: formData.name,\n                  onChange: e => setFormData({\n                    ...formData,\n                    name: e.target.value\n                  }),\n                  className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 319,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-1\",\n                  children: \"Code promo *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 329,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: formData.code,\n                  onChange: e => setFormData({\n                    ...formData,\n                    code: e.target.value.toUpperCase()\n                  }),\n                  className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 332,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 328,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Description\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 343,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                value: formData.description,\n                onChange: e => setFormData({\n                  ...formData,\n                  description: e.target.value\n                }),\n                rows: \"3\",\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 346,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 342,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-1\",\n                  children: \"Type de promotion *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 356,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  value: formData.type,\n                  onChange: e => setFormData({\n                    ...formData,\n                    type: e.target.value\n                  }),\n                  className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500\",\n                  required: true,\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"percentage\",\n                    children: \"Pourcentage\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 365,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"fixed\",\n                    children: \"Montant fixe\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 366,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"free_shipping\",\n                    children: \"Livraison gratuite\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 367,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 359,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 355,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-1\",\n                  children: \"Valeur *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 372,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  step: \"0.01\",\n                  min: \"0\",\n                  max: formData.type === 'percentage' ? '100' : undefined,\n                  value: formData.value,\n                  onChange: e => setFormData({\n                    ...formData,\n                    value: e.target.value\n                  }),\n                  className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 375,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 371,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 354,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-1\",\n                  children: \"Montant minimum\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 390,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  step: \"0.01\",\n                  min: \"0\",\n                  value: formData.minimum_amount,\n                  onChange: e => setFormData({\n                    ...formData,\n                    minimum_amount: e.target.value\n                  }),\n                  className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 393,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 389,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-1\",\n                  children: \"R\\xE9duction maximum\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 404,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  step: \"0.01\",\n                  min: \"0\",\n                  value: formData.maximum_discount,\n                  onChange: e => setFormData({\n                    ...formData,\n                    maximum_discount: e.target.value\n                  }),\n                  className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500\",\n                  disabled: formData.type !== 'percentage'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 407,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 403,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-1\",\n                  children: \"Limite d'utilisation\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 419,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  min: \"1\",\n                  value: formData.usage_limit,\n                  onChange: e => setFormData({\n                    ...formData,\n                    usage_limit: e.target.value\n                  }),\n                  className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500\",\n                  placeholder: \"Illimit\\xE9\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 422,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 418,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 388,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-1\",\n                  children: \"Date de d\\xE9but *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 435,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  value: formData.start_date,\n                  onChange: e => setFormData({\n                    ...formData,\n                    start_date: e.target.value\n                  }),\n                  className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 438,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 434,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-1\",\n                  children: \"Date de fin *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 448,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  value: formData.end_date,\n                  onChange: e => setFormData({\n                    ...formData,\n                    end_date: e.target.value\n                  }),\n                  className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 451,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 447,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 433,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                id: \"is_active\",\n                checked: formData.is_active,\n                onChange: e => setFormData({\n                  ...formData,\n                  is_active: e.target.checked\n                }),\n                className: \"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 462,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"is_active\",\n                className: \"ml-2 block text-sm text-gray-900\",\n                children: \"Promotion active\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 469,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 461,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-end space-x-3 pt-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: () => {\n                  setShowModal(false);\n                  setEditingPromotion(null);\n                  resetForm();\n                },\n                className: \"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\",\n                children: \"Annuler\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 475,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                className: \"px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700\",\n                children: editingPromotion ? 'Mettre à jour' : 'Créer'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 486,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 474,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 307,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 306,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow-sm rounded-lg overflow-hidden\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"overflow-x-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"min-w-full divide-y divide-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            className: \"bg-gray-50\",\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Promotion\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 505,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 508,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Valeur\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 511,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Utilisation\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 514,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"P\\xE9riode\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 517,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Statut\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 520,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 523,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 504,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 503,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            className: \"bg-white divide-y divide-gray-200\",\n            children: isLoading ? /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: /*#__PURE__*/_jsxDEV(\"td\", {\n                colSpan: \"7\",\n                className: \"px-6 py-4 text-center\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 532,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 531,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 530,\n              columnNumber: 17\n            }, this) : promotions.length === 0 ? /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: /*#__PURE__*/_jsxDEV(\"td\", {\n                colSpan: \"7\",\n                className: \"px-6 py-4 text-center text-gray-500\",\n                children: \"Aucune promotion trouv\\xE9e\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 537,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 536,\n              columnNumber: 17\n            }, this) : promotions.map(promotion => /*#__PURE__*/_jsxDEV(\"tr\", {\n              className: \"hover:bg-gray-50\",\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm font-medium text-gray-900\",\n                    children: promotion.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 546,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-gray-500 font-mono\",\n                    children: promotion.code\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 547,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 545,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 544,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [getTypeIcon(promotion.type), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm text-gray-900\",\n                    children: getTypeLabel(promotion.type)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 553,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 551,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 550,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm font-medium text-gray-900\",\n                  children: formatValue(promotion)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 557,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 556,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-900\",\n                  children: [promotion.used_count, \" / \", promotion.usage_limit || '∞']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 562,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 561,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-900\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: new Date(promotion.start_date).toLocaleDateString('fr-FR')\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 568,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-gray-500\",\n                    children: [\"au \", new Date(promotion.end_date).toLocaleDateString('fr-FR')]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 569,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 567,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 566,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap\",\n                children: getStatusBadge(promotion)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 572,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleEdit(promotion),\n                    className: \"text-primary-600 hover:text-primary-900\",\n                    title: \"Modifier\",\n                    children: /*#__PURE__*/_jsxDEV(FaEdit, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 582,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 577,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleDelete(promotion.id),\n                    className: \"text-red-600 hover:text-red-900\",\n                    title: \"Supprimer\",\n                    children: /*#__PURE__*/_jsxDEV(FaTrash, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 589,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 584,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 576,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 575,\n                columnNumber: 21\n              }, this)]\n            }, promotion.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 543,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 528,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 502,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 501,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 500,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 288,\n    columnNumber: 5\n  }, this);\n};\n_s(Promotions, \"gTQVhNJb3hkU92kdjILILX/DATw=\", false, function () {\n  return [useSafeAuth];\n});\n_c = Promotions;\nexport default Promotions;\nvar _c;\n$RefreshReg$(_c, \"Promotions\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "toast", "FaPlus", "FaEdit", "FaTrash", "FaEye", "FaTag", "FaPercent", "FaDollarSign", "FaShippingFast", "useSafeAuth", "promotionService", "jsxDEV", "_jsxDEV", "Promotions", "_s", "state", "authState", "promotions", "setPromotions", "isLoading", "setIsLoading", "showModal", "setShowModal", "editingPromotion", "setEditingPromotion", "formData", "setFormData", "name", "code", "description", "type", "value", "minimum_amount", "maximum_discount", "usage_limit", "start_date", "end_date", "is_active", "fetchPromotions", "data", "getAllPromotions", "token", "error", "console", "log", "testPromotions", "id", "used_count", "Date", "toISOString", "now", "handleSubmit", "e", "preventDefault", "promotionData", "parseFloat", "parseInt", "updatePromotion", "success", "apiError", "updatedPromotions", "map", "p", "createPromotion", "newPromotion", "Math", "max", "resetForm", "_error$response", "_error$response$data", "errorMessage", "response", "message", "handleEdit", "promotion", "_promotion$minimum_am", "_promotion$maximum_di", "_promotion$usage_limi", "toString", "split", "handleDelete", "window", "confirm", "deletePromotion", "filter", "getTypeIcon", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getTypeLabel", "formatValue", "getStatusBadge", "startDate", "endDate", "children", "onClick", "onSubmit", "onChange", "target", "required", "toUpperCase", "rows", "step", "min", "undefined", "disabled", "placeholder", "checked", "htmlFor", "colSpan", "length", "toLocaleDateString", "title", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet-Synthese/frontend/src/pages/Admin/Promotions.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { toast } from 'react-toastify';\nimport { FaPlus, FaEdit, FaTrash, FaEye, FaTag, FaPercent, FaDollarSign, FaShippingFast } from 'react-icons/fa';\nimport useSafeAuth from '../../hooks/useSafeAuth';\nimport { promotionService } from '../../services/api/promotionService';\n\nconst Promotions = () => {\n  const { state: authState } = useSafeAuth();\n  const [promotions, setPromotions] = useState([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [showModal, setShowModal] = useState(false);\n  const [editingPromotion, setEditingPromotion] = useState(null);\n  const [formData, setFormData] = useState({\n    name: '',\n    code: '',\n    description: '',\n    type: 'percentage',\n    value: '',\n    minimum_amount: '',\n    maximum_discount: '',\n    usage_limit: '',\n    start_date: '',\n    end_date: '',\n    is_active: true\n  });\n\n  useEffect(() => {\n    fetchPromotions();\n  }, []);\n\n  const fetchPromotions = async () => {\n    try {\n      setIsLoading(true);\n      const data = await promotionService.getAllPromotions(authState?.token);\n      setPromotions(data);\n    } catch (error) {\n      console.log('API non disponible, utilisation des données de test');\n\n      // Données de test pour les promotions\n      const testPromotions = [\n        {\n          id: 1,\n          name: 'Bienvenue 10%',\n          code: 'BIENVENUE10',\n          description: 'Réduction de 10% pour les nouveaux clients sur leur première commande',\n          type: 'percentage',\n          value: 10,\n          minimum_amount: 50,\n          maximum_discount: null,\n          usage_limit: 100,\n          used_count: 15,\n          start_date: new Date().toISOString(),\n          end_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),\n          is_active: true\n        },\n        {\n          id: 2,\n          name: 'Livraison Gratuite',\n          code: 'LIVRAISON',\n          description: 'Livraison gratuite pour toute commande supérieure à 30€',\n          type: 'free_shipping',\n          value: 0,\n          minimum_amount: 30,\n          maximum_discount: null,\n          usage_limit: 50,\n          used_count: 8,\n          start_date: new Date().toISOString(),\n          end_date: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString(),\n          is_active: true\n        },\n        {\n          id: 3,\n          name: 'Réduction 5€',\n          code: 'REDUCTION5',\n          description: '5€ de réduction immédiate sur votre commande',\n          type: 'fixed',\n          value: 5,\n          minimum_amount: 25,\n          maximum_discount: null,\n          usage_limit: 30,\n          used_count: 12,\n          start_date: new Date().toISOString(),\n          end_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),\n          is_active: true\n        },\n        {\n          id: 4,\n          name: 'Été 2024',\n          code: 'ETE2024',\n          description: '15% de réduction sur tous les produits grillés',\n          type: 'percentage',\n          value: 15,\n          minimum_amount: 40,\n          maximum_discount: 20,\n          usage_limit: 200,\n          used_count: 45,\n          start_date: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),\n          end_date: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000).toISOString(),\n          is_active: true\n        },\n        {\n          id: 5,\n          name: 'Black Friday',\n          code: 'BLACKFRIDAY',\n          description: '25% de réduction exceptionnelle',\n          type: 'percentage',\n          value: 25,\n          minimum_amount: 100,\n          maximum_discount: 50,\n          usage_limit: 500,\n          used_count: 500,\n          start_date: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString(),\n          end_date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),\n          is_active: false\n        }\n      ];\n\n      setPromotions(testPromotions);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n\n    try {\n      const promotionData = {\n        ...formData,\n        value: parseFloat(formData.value),\n        minimum_amount: formData.minimum_amount ? parseFloat(formData.minimum_amount) : null,\n        maximum_discount: formData.maximum_discount ? parseFloat(formData.maximum_discount) : null,\n        usage_limit: formData.usage_limit ? parseInt(formData.usage_limit) : null\n      };\n\n      if (editingPromotion) {\n        try {\n          await promotionService.updatePromotion(editingPromotion.id, promotionData, authState?.token);\n          toast.success('Promotion mise à jour avec succès');\n        } catch (apiError) {\n          // Mode démonstration - simulation de la mise à jour\n          const updatedPromotions = promotions.map(p =>\n            p.id === editingPromotion.id\n              ? { ...p, ...promotionData, id: editingPromotion.id, used_count: p.used_count }\n              : p\n          );\n          setPromotions(updatedPromotions);\n          toast.success('Promotion mise à jour avec succès (mode démonstration)');\n        }\n      } else {\n        try {\n          await promotionService.createPromotion(promotionData, authState?.token);\n          toast.success('Promotion créée avec succès');\n        } catch (apiError) {\n          // Mode démonstration - simulation de la création\n          const newPromotion = {\n            ...promotionData,\n            id: Math.max(...promotions.map(p => p.id)) + 1,\n            used_count: 0\n          };\n          setPromotions([...promotions, newPromotion]);\n          toast.success('Promotion créée avec succès (mode démonstration)');\n        }\n      }\n\n      setShowModal(false);\n      setEditingPromotion(null);\n      resetForm();\n    } catch (error) {\n      const errorMessage = error.response?.data?.message || 'Erreur lors de la sauvegarde';\n      toast.error(errorMessage);\n    }\n  };\n\n  const handleEdit = (promotion) => {\n    setEditingPromotion(promotion);\n    setFormData({\n      name: promotion.name,\n      code: promotion.code,\n      description: promotion.description || '',\n      type: promotion.type,\n      value: promotion.value.toString(),\n      minimum_amount: promotion.minimum_amount?.toString() || '',\n      maximum_discount: promotion.maximum_discount?.toString() || '',\n      usage_limit: promotion.usage_limit?.toString() || '',\n      start_date: promotion.start_date.split('T')[0],\n      end_date: promotion.end_date.split('T')[0],\n      is_active: promotion.is_active\n    });\n    setShowModal(true);\n  };\n\n  const handleDelete = async (id) => {\n    if (window.confirm('Êtes-vous sûr de vouloir supprimer cette promotion ?')) {\n      try {\n        await promotionService.deletePromotion(id, authState?.token);\n        toast.success('Promotion supprimée avec succès');\n        fetchPromotions();\n      } catch (error) {\n        // Mode démonstration - simulation de la suppression\n        const updatedPromotions = promotions.filter(p => p.id !== id);\n        setPromotions(updatedPromotions);\n        toast.success('Promotion supprimée avec succès (mode démonstration)');\n      }\n    }\n  };\n\n  const resetForm = () => {\n    setFormData({\n      name: '',\n      code: '',\n      description: '',\n      type: 'percentage',\n      value: '',\n      minimum_amount: '',\n      maximum_discount: '',\n      usage_limit: '',\n      start_date: '',\n      end_date: '',\n      is_active: true\n    });\n  };\n\n  const getTypeIcon = (type) => {\n    switch (type) {\n      case 'percentage':\n        return <FaPercent className=\"text-blue-500\" />;\n      case 'fixed':\n        return <FaDollarSign className=\"text-green-500\" />;\n      case 'free_shipping':\n        return <FaShippingFast className=\"text-purple-500\" />;\n      default:\n        return <FaTag className=\"text-gray-500\" />;\n    }\n  };\n\n  const getTypeLabel = (type) => {\n    switch (type) {\n      case 'percentage':\n        return 'Pourcentage';\n      case 'fixed':\n        return 'Montant fixe';\n      case 'free_shipping':\n        return 'Livraison gratuite';\n      default:\n        return type;\n    }\n  };\n\n  const formatValue = (promotion) => {\n    switch (promotion.type) {\n      case 'percentage':\n        return `${promotion.value}%`;\n      case 'fixed':\n        return `${promotion.value}$`;\n      case 'free_shipping':\n        return 'Gratuite';\n      default:\n        return promotion.value;\n    }\n  };\n\n  const getStatusBadge = (promotion) => {\n    const now = new Date();\n    const startDate = new Date(promotion.start_date);\n    const endDate = new Date(promotion.end_date);\n\n    if (!promotion.is_active) {\n      return <span className=\"px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full\">Inactive</span>;\n    }\n\n    if (now < startDate) {\n      return <span className=\"px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded-full\">À venir</span>;\n    }\n\n    if (now > endDate) {\n      return <span className=\"px-2 py-1 text-xs bg-red-100 text-red-800 rounded-full\">Expirée</span>;\n    }\n\n    if (promotion.usage_limit && promotion.used_count >= promotion.usage_limit) {\n      return <span className=\"px-2 py-1 text-xs bg-orange-100 text-orange-800 rounded-full\">Épuisée</span>;\n    }\n\n    return <span className=\"px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full\">Active</span>;\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"flex justify-between items-center\">\n        <h1 className=\"text-2xl font-bold text-gray-900\">Gestion des Promotions</h1>\n        <button\n          onClick={() => {\n            resetForm();\n            setEditingPromotion(null);\n            setShowModal(true);\n          }}\n          className=\"btn-primary flex items-center space-x-2\"\n        >\n          <FaPlus />\n          <span>Nouvelle promotion</span>\n        </button>\n      </div>\n\n      {/* Modal pour créer/éditer une promotion */}\n      {showModal && (\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\n          <div className=\"relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white\">\n            <div className=\"mt-3\">\n              <h3 className=\"text-lg font-medium text-gray-900 mb-4\">\n                {editingPromotion ? 'Modifier la promotion' : 'Nouvelle promotion'}\n              </h3>\n\n              <form onSubmit={handleSubmit} className=\"space-y-4\">\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Nom de la promotion *\n                    </label>\n                    <input\n                      type=\"text\"\n                      value={formData.name}\n                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500\"\n                      required\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Code promo *\n                    </label>\n                    <input\n                      type=\"text\"\n                      value={formData.code}\n                      onChange={(e) => setFormData({ ...formData, code: e.target.value.toUpperCase() })}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500\"\n                      required\n                    />\n                  </div>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Description\n                  </label>\n                  <textarea\n                    value={formData.description}\n                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}\n                    rows=\"3\"\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500\"\n                  />\n                </div>\n\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Type de promotion *\n                    </label>\n                    <select\n                      value={formData.type}\n                      onChange={(e) => setFormData({ ...formData, type: e.target.value })}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500\"\n                      required\n                    >\n                      <option value=\"percentage\">Pourcentage</option>\n                      <option value=\"fixed\">Montant fixe</option>\n                      <option value=\"free_shipping\">Livraison gratuite</option>\n                    </select>\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Valeur *\n                    </label>\n                    <input\n                      type=\"number\"\n                      step=\"0.01\"\n                      min=\"0\"\n                      max={formData.type === 'percentage' ? '100' : undefined}\n                      value={formData.value}\n                      onChange={(e) => setFormData({ ...formData, value: e.target.value })}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500\"\n                      required\n                    />\n                  </div>\n                </div>\n\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Montant minimum\n                    </label>\n                    <input\n                      type=\"number\"\n                      step=\"0.01\"\n                      min=\"0\"\n                      value={formData.minimum_amount}\n                      onChange={(e) => setFormData({ ...formData, minimum_amount: e.target.value })}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500\"\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Réduction maximum\n                    </label>\n                    <input\n                      type=\"number\"\n                      step=\"0.01\"\n                      min=\"0\"\n                      value={formData.maximum_discount}\n                      onChange={(e) => setFormData({ ...formData, maximum_discount: e.target.value })}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500\"\n                      disabled={formData.type !== 'percentage'}\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Limite d'utilisation\n                    </label>\n                    <input\n                      type=\"number\"\n                      min=\"1\"\n                      value={formData.usage_limit}\n                      onChange={(e) => setFormData({ ...formData, usage_limit: e.target.value })}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500\"\n                      placeholder=\"Illimité\"\n                    />\n                  </div>\n                </div>\n\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Date de début *\n                    </label>\n                    <input\n                      type=\"date\"\n                      value={formData.start_date}\n                      onChange={(e) => setFormData({ ...formData, start_date: e.target.value })}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500\"\n                      required\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Date de fin *\n                    </label>\n                    <input\n                      type=\"date\"\n                      value={formData.end_date}\n                      onChange={(e) => setFormData({ ...formData, end_date: e.target.value })}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500\"\n                      required\n                    />\n                  </div>\n                </div>\n\n                <div className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    id=\"is_active\"\n                    checked={formData.is_active}\n                    onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}\n                    className=\"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n                  />\n                  <label htmlFor=\"is_active\" className=\"ml-2 block text-sm text-gray-900\">\n                    Promotion active\n                  </label>\n                </div>\n\n                <div className=\"flex justify-end space-x-3 pt-4\">\n                  <button\n                    type=\"button\"\n                    onClick={() => {\n                      setShowModal(false);\n                      setEditingPromotion(null);\n                      resetForm();\n                    }}\n                    className=\"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\"\n                  >\n                    Annuler\n                  </button>\n                  <button\n                    type=\"submit\"\n                    className=\"px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700\"\n                  >\n                    {editingPromotion ? 'Mettre à jour' : 'Créer'}\n                  </button>\n                </div>\n              </form>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Tableau des promotions */}\n      <div className=\"bg-white shadow-sm rounded-lg overflow-hidden\">\n        <div className=\"overflow-x-auto\">\n          <table className=\"min-w-full divide-y divide-gray-200\">\n            <thead className=\"bg-gray-50\">\n              <tr>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Promotion\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Type\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Valeur\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Utilisation\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Période\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Statut\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Actions\n                </th>\n              </tr>\n            </thead>\n            <tbody className=\"bg-white divide-y divide-gray-200\">\n              {isLoading ? (\n                <tr>\n                  <td colSpan=\"7\" className=\"px-6 py-4 text-center\">\n                    <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto\"></div>\n                  </td>\n                </tr>\n              ) : promotions.length === 0 ? (\n                <tr>\n                  <td colSpan=\"7\" className=\"px-6 py-4 text-center text-gray-500\">\n                    Aucune promotion trouvée\n                  </td>\n                </tr>\n              ) : (\n                promotions.map((promotion) => (\n                  <tr key={promotion.id} className=\"hover:bg-gray-50\">\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div>\n                        <div className=\"text-sm font-medium text-gray-900\">{promotion.name}</div>\n                        <div className=\"text-sm text-gray-500 font-mono\">{promotion.code}</div>\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div className=\"flex items-center space-x-2\">\n                        {getTypeIcon(promotion.type)}\n                        <span className=\"text-sm text-gray-900\">{getTypeLabel(promotion.type)}</span>\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <span className=\"text-sm font-medium text-gray-900\">\n                        {formatValue(promotion)}\n                      </span>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div className=\"text-sm text-gray-900\">\n                        {promotion.used_count} / {promotion.usage_limit || '∞'}\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div className=\"text-sm text-gray-900\">\n                        <div>{new Date(promotion.start_date).toLocaleDateString('fr-FR')}</div>\n                        <div className=\"text-gray-500\">au {new Date(promotion.end_date).toLocaleDateString('fr-FR')}</div>\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      {getStatusBadge(promotion)}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                      <div className=\"flex space-x-2\">\n                        <button\n                          onClick={() => handleEdit(promotion)}\n                          className=\"text-primary-600 hover:text-primary-900\"\n                          title=\"Modifier\"\n                        >\n                          <FaEdit />\n                        </button>\n                        <button\n                          onClick={() => handleDelete(promotion.id)}\n                          className=\"text-red-600 hover:text-red-900\"\n                          title=\"Supprimer\"\n                        >\n                          <FaTrash />\n                        </button>\n                      </div>\n                    </td>\n                  </tr>\n                ))\n              )}\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Promotions;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,YAAY,EAAEC,cAAc,QAAQ,gBAAgB;AAC/G,OAAOC,WAAW,MAAM,yBAAyB;AACjD,SAASC,gBAAgB,QAAQ,qCAAqC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvE,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAM;IAAEC,KAAK,EAAEC;EAAU,CAAC,GAAGP,WAAW,CAAC,CAAC;EAC1C,MAAM,CAACQ,UAAU,EAAEC,aAAa,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACqB,SAAS,EAAEC,YAAY,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACuB,SAAS,EAAEC,YAAY,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACyB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAAC2B,QAAQ,EAAEC,WAAW,CAAC,GAAG5B,QAAQ,CAAC;IACvC6B,IAAI,EAAE,EAAE;IACRC,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,EAAE;IACTC,cAAc,EAAE,EAAE;IAClBC,gBAAgB,EAAE,EAAE;IACpBC,WAAW,EAAE,EAAE;IACfC,UAAU,EAAE,EAAE;IACdC,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE;EACb,CAAC,CAAC;EAEFtC,SAAS,CAAC,MAAM;IACduC,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACFlB,YAAY,CAAC,IAAI,CAAC;MAClB,MAAMmB,IAAI,GAAG,MAAM7B,gBAAgB,CAAC8B,gBAAgB,CAACxB,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEyB,KAAK,CAAC;MACtEvB,aAAa,CAACqB,IAAI,CAAC;IACrB,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;;MAElE;MACA,MAAMC,cAAc,GAAG,CACrB;QACEC,EAAE,EAAE,CAAC;QACLnB,IAAI,EAAE,eAAe;QACrBC,IAAI,EAAE,aAAa;QACnBC,WAAW,EAAE,uEAAuE;QACpFC,IAAI,EAAE,YAAY;QAClBC,KAAK,EAAE,EAAE;QACTC,cAAc,EAAE,EAAE;QAClBC,gBAAgB,EAAE,IAAI;QACtBC,WAAW,EAAE,GAAG;QAChBa,UAAU,EAAE,EAAE;QACdZ,UAAU,EAAE,IAAIa,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACpCb,QAAQ,EAAE,IAAIY,IAAI,CAACA,IAAI,CAACE,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACD,WAAW,CAAC,CAAC;QACvEZ,SAAS,EAAE;MACb,CAAC,EACD;QACES,EAAE,EAAE,CAAC;QACLnB,IAAI,EAAE,oBAAoB;QAC1BC,IAAI,EAAE,WAAW;QACjBC,WAAW,EAAE,yDAAyD;QACtEC,IAAI,EAAE,eAAe;QACrBC,KAAK,EAAE,CAAC;QACRC,cAAc,EAAE,EAAE;QAClBC,gBAAgB,EAAE,IAAI;QACtBC,WAAW,EAAE,EAAE;QACfa,UAAU,EAAE,CAAC;QACbZ,UAAU,EAAE,IAAIa,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACpCb,QAAQ,EAAE,IAAIY,IAAI,CAACA,IAAI,CAACE,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACD,WAAW,CAAC,CAAC;QACvEZ,SAAS,EAAE;MACb,CAAC,EACD;QACES,EAAE,EAAE,CAAC;QACLnB,IAAI,EAAE,cAAc;QACpBC,IAAI,EAAE,YAAY;QAClBC,WAAW,EAAE,8CAA8C;QAC3DC,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE,CAAC;QACRC,cAAc,EAAE,EAAE;QAClBC,gBAAgB,EAAE,IAAI;QACtBC,WAAW,EAAE,EAAE;QACfa,UAAU,EAAE,EAAE;QACdZ,UAAU,EAAE,IAAIa,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACpCb,QAAQ,EAAE,IAAIY,IAAI,CAACA,IAAI,CAACE,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACD,WAAW,CAAC,CAAC;QACtEZ,SAAS,EAAE;MACb,CAAC,EACD;QACES,EAAE,EAAE,CAAC;QACLnB,IAAI,EAAE,UAAU;QAChBC,IAAI,EAAE,SAAS;QACfC,WAAW,EAAE,gDAAgD;QAC7DC,IAAI,EAAE,YAAY;QAClBC,KAAK,EAAE,EAAE;QACTC,cAAc,EAAE,EAAE;QAClBC,gBAAgB,EAAE,EAAE;QACpBC,WAAW,EAAE,GAAG;QAChBa,UAAU,EAAE,EAAE;QACdZ,UAAU,EAAE,IAAIa,IAAI,CAACA,IAAI,CAACE,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACD,WAAW,CAAC,CAAC;QACzEb,QAAQ,EAAE,IAAIY,IAAI,CAACA,IAAI,CAACE,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACD,WAAW,CAAC,CAAC;QACvEZ,SAAS,EAAE;MACb,CAAC,EACD;QACES,EAAE,EAAE,CAAC;QACLnB,IAAI,EAAE,cAAc;QACpBC,IAAI,EAAE,aAAa;QACnBC,WAAW,EAAE,iCAAiC;QAC9CC,IAAI,EAAE,YAAY;QAClBC,KAAK,EAAE,EAAE;QACTC,cAAc,EAAE,GAAG;QACnBC,gBAAgB,EAAE,EAAE;QACpBC,WAAW,EAAE,GAAG;QAChBa,UAAU,EAAE,GAAG;QACfZ,UAAU,EAAE,IAAIa,IAAI,CAACA,IAAI,CAACE,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACD,WAAW,CAAC,CAAC;QACzEb,QAAQ,EAAE,IAAIY,IAAI,CAACA,IAAI,CAACE,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACD,WAAW,CAAC,CAAC;QACvEZ,SAAS,EAAE;MACb,CAAC,CACF;MAEDnB,aAAa,CAAC2B,cAAc,CAAC;IAC/B,CAAC,SAAS;MACRzB,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAM+B,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,IAAI;MACF,MAAMC,aAAa,GAAG;QACpB,GAAG7B,QAAQ;QACXM,KAAK,EAAEwB,UAAU,CAAC9B,QAAQ,CAACM,KAAK,CAAC;QACjCC,cAAc,EAAEP,QAAQ,CAACO,cAAc,GAAGuB,UAAU,CAAC9B,QAAQ,CAACO,cAAc,CAAC,GAAG,IAAI;QACpFC,gBAAgB,EAAER,QAAQ,CAACQ,gBAAgB,GAAGsB,UAAU,CAAC9B,QAAQ,CAACQ,gBAAgB,CAAC,GAAG,IAAI;QAC1FC,WAAW,EAAET,QAAQ,CAACS,WAAW,GAAGsB,QAAQ,CAAC/B,QAAQ,CAACS,WAAW,CAAC,GAAG;MACvE,CAAC;MAED,IAAIX,gBAAgB,EAAE;QACpB,IAAI;UACF,MAAMb,gBAAgB,CAAC+C,eAAe,CAAClC,gBAAgB,CAACuB,EAAE,EAAEQ,aAAa,EAAEtC,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEyB,KAAK,CAAC;UAC5FzC,KAAK,CAAC0D,OAAO,CAAC,mCAAmC,CAAC;QACpD,CAAC,CAAC,OAAOC,QAAQ,EAAE;UACjB;UACA,MAAMC,iBAAiB,GAAG3C,UAAU,CAAC4C,GAAG,CAACC,CAAC,IACxCA,CAAC,CAAChB,EAAE,KAAKvB,gBAAgB,CAACuB,EAAE,GACxB;YAAE,GAAGgB,CAAC;YAAE,GAAGR,aAAa;YAAER,EAAE,EAAEvB,gBAAgB,CAACuB,EAAE;YAAEC,UAAU,EAAEe,CAAC,CAACf;UAAW,CAAC,GAC7Ee,CACN,CAAC;UACD5C,aAAa,CAAC0C,iBAAiB,CAAC;UAChC5D,KAAK,CAAC0D,OAAO,CAAC,wDAAwD,CAAC;QACzE;MACF,CAAC,MAAM;QACL,IAAI;UACF,MAAMhD,gBAAgB,CAACqD,eAAe,CAACT,aAAa,EAAEtC,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEyB,KAAK,CAAC;UACvEzC,KAAK,CAAC0D,OAAO,CAAC,6BAA6B,CAAC;QAC9C,CAAC,CAAC,OAAOC,QAAQ,EAAE;UACjB;UACA,MAAMK,YAAY,GAAG;YACnB,GAAGV,aAAa;YAChBR,EAAE,EAAEmB,IAAI,CAACC,GAAG,CAAC,GAAGjD,UAAU,CAAC4C,GAAG,CAACC,CAAC,IAAIA,CAAC,CAAChB,EAAE,CAAC,CAAC,GAAG,CAAC;YAC9CC,UAAU,EAAE;UACd,CAAC;UACD7B,aAAa,CAAC,CAAC,GAAGD,UAAU,EAAE+C,YAAY,CAAC,CAAC;UAC5ChE,KAAK,CAAC0D,OAAO,CAAC,kDAAkD,CAAC;QACnE;MACF;MAEApC,YAAY,CAAC,KAAK,CAAC;MACnBE,mBAAmB,CAAC,IAAI,CAAC;MACzB2C,SAAS,CAAC,CAAC;IACb,CAAC,CAAC,OAAOzB,KAAK,EAAE;MAAA,IAAA0B,eAAA,EAAAC,oBAAA;MACd,MAAMC,YAAY,GAAG,EAAAF,eAAA,GAAA1B,KAAK,CAAC6B,QAAQ,cAAAH,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgB7B,IAAI,cAAA8B,oBAAA,uBAApBA,oBAAA,CAAsBG,OAAO,KAAI,8BAA8B;MACpFxE,KAAK,CAAC0C,KAAK,CAAC4B,YAAY,CAAC;IAC3B;EACF,CAAC;EAED,MAAMG,UAAU,GAAIC,SAAS,IAAK;IAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;IAChCrD,mBAAmB,CAACkD,SAAS,CAAC;IAC9BhD,WAAW,CAAC;MACVC,IAAI,EAAE+C,SAAS,CAAC/C,IAAI;MACpBC,IAAI,EAAE8C,SAAS,CAAC9C,IAAI;MACpBC,WAAW,EAAE6C,SAAS,CAAC7C,WAAW,IAAI,EAAE;MACxCC,IAAI,EAAE4C,SAAS,CAAC5C,IAAI;MACpBC,KAAK,EAAE2C,SAAS,CAAC3C,KAAK,CAAC+C,QAAQ,CAAC,CAAC;MACjC9C,cAAc,EAAE,EAAA2C,qBAAA,GAAAD,SAAS,CAAC1C,cAAc,cAAA2C,qBAAA,uBAAxBA,qBAAA,CAA0BG,QAAQ,CAAC,CAAC,KAAI,EAAE;MAC1D7C,gBAAgB,EAAE,EAAA2C,qBAAA,GAAAF,SAAS,CAACzC,gBAAgB,cAAA2C,qBAAA,uBAA1BA,qBAAA,CAA4BE,QAAQ,CAAC,CAAC,KAAI,EAAE;MAC9D5C,WAAW,EAAE,EAAA2C,qBAAA,GAAAH,SAAS,CAACxC,WAAW,cAAA2C,qBAAA,uBAArBA,qBAAA,CAAuBC,QAAQ,CAAC,CAAC,KAAI,EAAE;MACpD3C,UAAU,EAAEuC,SAAS,CAACvC,UAAU,CAAC4C,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAC9C3C,QAAQ,EAAEsC,SAAS,CAACtC,QAAQ,CAAC2C,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAC1C1C,SAAS,EAAEqC,SAAS,CAACrC;IACvB,CAAC,CAAC;IACFf,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAM0D,YAAY,GAAG,MAAOlC,EAAE,IAAK;IACjC,IAAImC,MAAM,CAACC,OAAO,CAAC,sDAAsD,CAAC,EAAE;MAC1E,IAAI;QACF,MAAMxE,gBAAgB,CAACyE,eAAe,CAACrC,EAAE,EAAE9B,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEyB,KAAK,CAAC;QAC5DzC,KAAK,CAAC0D,OAAO,CAAC,iCAAiC,CAAC;QAChDpB,eAAe,CAAC,CAAC;MACnB,CAAC,CAAC,OAAOI,KAAK,EAAE;QACd;QACA,MAAMkB,iBAAiB,GAAG3C,UAAU,CAACmE,MAAM,CAACtB,CAAC,IAAIA,CAAC,CAAChB,EAAE,KAAKA,EAAE,CAAC;QAC7D5B,aAAa,CAAC0C,iBAAiB,CAAC;QAChC5D,KAAK,CAAC0D,OAAO,CAAC,sDAAsD,CAAC;MACvE;IACF;EACF,CAAC;EAED,MAAMS,SAAS,GAAGA,CAAA,KAAM;IACtBzC,WAAW,CAAC;MACVC,IAAI,EAAE,EAAE;MACRC,IAAI,EAAE,EAAE;MACRC,WAAW,EAAE,EAAE;MACfC,IAAI,EAAE,YAAY;MAClBC,KAAK,EAAE,EAAE;MACTC,cAAc,EAAE,EAAE;MAClBC,gBAAgB,EAAE,EAAE;MACpBC,WAAW,EAAE,EAAE;MACfC,UAAU,EAAE,EAAE;MACdC,QAAQ,EAAE,EAAE;MACZC,SAAS,EAAE;IACb,CAAC,CAAC;EACJ,CAAC;EAED,MAAMgD,WAAW,GAAIvD,IAAI,IAAK;IAC5B,QAAQA,IAAI;MACV,KAAK,YAAY;QACf,oBAAOlB,OAAA,CAACN,SAAS;UAACgF,SAAS,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAChD,KAAK,OAAO;QACV,oBAAO9E,OAAA,CAACL,YAAY;UAAC+E,SAAS,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACpD,KAAK,eAAe;QAClB,oBAAO9E,OAAA,CAACJ,cAAc;UAAC8E,SAAS,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACvD;QACE,oBAAO9E,OAAA,CAACP,KAAK;UAACiF,SAAS,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAC9C;EACF,CAAC;EAED,MAAMC,YAAY,GAAI7D,IAAI,IAAK;IAC7B,QAAQA,IAAI;MACV,KAAK,YAAY;QACf,OAAO,aAAa;MACtB,KAAK,OAAO;QACV,OAAO,cAAc;MACvB,KAAK,eAAe;QAClB,OAAO,oBAAoB;MAC7B;QACE,OAAOA,IAAI;IACf;EACF,CAAC;EAED,MAAM8D,WAAW,GAAIlB,SAAS,IAAK;IACjC,QAAQA,SAAS,CAAC5C,IAAI;MACpB,KAAK,YAAY;QACf,OAAO,GAAG4C,SAAS,CAAC3C,KAAK,GAAG;MAC9B,KAAK,OAAO;QACV,OAAO,GAAG2C,SAAS,CAAC3C,KAAK,GAAG;MAC9B,KAAK,eAAe;QAClB,OAAO,UAAU;MACnB;QACE,OAAO2C,SAAS,CAAC3C,KAAK;IAC1B;EACF,CAAC;EAED,MAAM8D,cAAc,GAAInB,SAAS,IAAK;IACpC,MAAMxB,GAAG,GAAG,IAAIF,IAAI,CAAC,CAAC;IACtB,MAAM8C,SAAS,GAAG,IAAI9C,IAAI,CAAC0B,SAAS,CAACvC,UAAU,CAAC;IAChD,MAAM4D,OAAO,GAAG,IAAI/C,IAAI,CAAC0B,SAAS,CAACtC,QAAQ,CAAC;IAE5C,IAAI,CAACsC,SAAS,CAACrC,SAAS,EAAE;MACxB,oBAAOzB,OAAA;QAAM0E,SAAS,EAAC,0DAA0D;QAAAU,QAAA,EAAC;MAAQ;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IACnG;IAEA,IAAIxC,GAAG,GAAG4C,SAAS,EAAE;MACnB,oBAAOlF,OAAA;QAAM0E,SAAS,EAAC,8DAA8D;QAAAU,QAAA,EAAC;MAAO;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IACtG;IAEA,IAAIxC,GAAG,GAAG6C,OAAO,EAAE;MACjB,oBAAOnF,OAAA;QAAM0E,SAAS,EAAC,wDAAwD;QAAAU,QAAA,EAAC;MAAO;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAChG;IAEA,IAAIhB,SAAS,CAACxC,WAAW,IAAIwC,SAAS,CAAC3B,UAAU,IAAI2B,SAAS,CAACxC,WAAW,EAAE;MAC1E,oBAAOtB,OAAA;QAAM0E,SAAS,EAAC,8DAA8D;QAAAU,QAAA,EAAC;MAAO;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IACtG;IAEA,oBAAO9E,OAAA;MAAM0E,SAAS,EAAC,4DAA4D;MAAAU,QAAA,EAAC;IAAM;MAAAT,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EACnG,CAAC;EAED,oBACE9E,OAAA;IAAK0E,SAAS,EAAC,WAAW;IAAAU,QAAA,gBACxBpF,OAAA;MAAK0E,SAAS,EAAC,mCAAmC;MAAAU,QAAA,gBAChDpF,OAAA;QAAI0E,SAAS,EAAC,kCAAkC;QAAAU,QAAA,EAAC;MAAsB;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5E9E,OAAA;QACEqF,OAAO,EAAEA,CAAA,KAAM;UACb9B,SAAS,CAAC,CAAC;UACX3C,mBAAmB,CAAC,IAAI,CAAC;UACzBF,YAAY,CAAC,IAAI,CAAC;QACpB,CAAE;QACFgE,SAAS,EAAC,yCAAyC;QAAAU,QAAA,gBAEnDpF,OAAA,CAACX,MAAM;UAAAsF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACV9E,OAAA;UAAAoF,QAAA,EAAM;QAAkB;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAGLrE,SAAS,iBACRT,OAAA;MAAK0E,SAAS,EAAC,4EAA4E;MAAAU,QAAA,eACzFpF,OAAA;QAAK0E,SAAS,EAAC,4FAA4F;QAAAU,QAAA,eACzGpF,OAAA;UAAK0E,SAAS,EAAC,MAAM;UAAAU,QAAA,gBACnBpF,OAAA;YAAI0E,SAAS,EAAC,wCAAwC;YAAAU,QAAA,EACnDzE,gBAAgB,GAAG,uBAAuB,GAAG;UAAoB;YAAAgE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC,eAEL9E,OAAA;YAAMsF,QAAQ,EAAE/C,YAAa;YAACmC,SAAS,EAAC,WAAW;YAAAU,QAAA,gBACjDpF,OAAA;cAAK0E,SAAS,EAAC,uCAAuC;cAAAU,QAAA,gBACpDpF,OAAA;gBAAAoF,QAAA,gBACEpF,OAAA;kBAAO0E,SAAS,EAAC,8CAA8C;kBAAAU,QAAA,EAAC;gBAEhE;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR9E,OAAA;kBACEkB,IAAI,EAAC,MAAM;kBACXC,KAAK,EAAEN,QAAQ,CAACE,IAAK;kBACrBwE,QAAQ,EAAG/C,CAAC,IAAK1B,WAAW,CAAC;oBAAE,GAAGD,QAAQ;oBAAEE,IAAI,EAAEyB,CAAC,CAACgD,MAAM,CAACrE;kBAAM,CAAC,CAAE;kBACpEuD,SAAS,EAAC,2GAA2G;kBACrHe,QAAQ;gBAAA;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN9E,OAAA;gBAAAoF,QAAA,gBACEpF,OAAA;kBAAO0E,SAAS,EAAC,8CAA8C;kBAAAU,QAAA,EAAC;gBAEhE;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR9E,OAAA;kBACEkB,IAAI,EAAC,MAAM;kBACXC,KAAK,EAAEN,QAAQ,CAACG,IAAK;kBACrBuE,QAAQ,EAAG/C,CAAC,IAAK1B,WAAW,CAAC;oBAAE,GAAGD,QAAQ;oBAAEG,IAAI,EAAEwB,CAAC,CAACgD,MAAM,CAACrE,KAAK,CAACuE,WAAW,CAAC;kBAAE,CAAC,CAAE;kBAClFhB,SAAS,EAAC,2GAA2G;kBACrHe,QAAQ;gBAAA;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN9E,OAAA;cAAAoF,QAAA,gBACEpF,OAAA;gBAAO0E,SAAS,EAAC,8CAA8C;gBAAAU,QAAA,EAAC;cAEhE;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR9E,OAAA;gBACEmB,KAAK,EAAEN,QAAQ,CAACI,WAAY;gBAC5BsE,QAAQ,EAAG/C,CAAC,IAAK1B,WAAW,CAAC;kBAAE,GAAGD,QAAQ;kBAAEI,WAAW,EAAEuB,CAAC,CAACgD,MAAM,CAACrE;gBAAM,CAAC,CAAE;gBAC3EwE,IAAI,EAAC,GAAG;gBACRjB,SAAS,EAAC;cAA2G;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN9E,OAAA;cAAK0E,SAAS,EAAC,uCAAuC;cAAAU,QAAA,gBACpDpF,OAAA;gBAAAoF,QAAA,gBACEpF,OAAA;kBAAO0E,SAAS,EAAC,8CAA8C;kBAAAU,QAAA,EAAC;gBAEhE;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR9E,OAAA;kBACEmB,KAAK,EAAEN,QAAQ,CAACK,IAAK;kBACrBqE,QAAQ,EAAG/C,CAAC,IAAK1B,WAAW,CAAC;oBAAE,GAAGD,QAAQ;oBAAEK,IAAI,EAAEsB,CAAC,CAACgD,MAAM,CAACrE;kBAAM,CAAC,CAAE;kBACpEuD,SAAS,EAAC,2GAA2G;kBACrHe,QAAQ;kBAAAL,QAAA,gBAERpF,OAAA;oBAAQmB,KAAK,EAAC,YAAY;oBAAAiE,QAAA,EAAC;kBAAW;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC/C9E,OAAA;oBAAQmB,KAAK,EAAC,OAAO;oBAAAiE,QAAA,EAAC;kBAAY;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC3C9E,OAAA;oBAAQmB,KAAK,EAAC,eAAe;oBAAAiE,QAAA,EAAC;kBAAkB;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAEN9E,OAAA;gBAAAoF,QAAA,gBACEpF,OAAA;kBAAO0E,SAAS,EAAC,8CAA8C;kBAAAU,QAAA,EAAC;gBAEhE;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR9E,OAAA;kBACEkB,IAAI,EAAC,QAAQ;kBACb0E,IAAI,EAAC,MAAM;kBACXC,GAAG,EAAC,GAAG;kBACPvC,GAAG,EAAEzC,QAAQ,CAACK,IAAI,KAAK,YAAY,GAAG,KAAK,GAAG4E,SAAU;kBACxD3E,KAAK,EAAEN,QAAQ,CAACM,KAAM;kBACtBoE,QAAQ,EAAG/C,CAAC,IAAK1B,WAAW,CAAC;oBAAE,GAAGD,QAAQ;oBAAEM,KAAK,EAAEqB,CAAC,CAACgD,MAAM,CAACrE;kBAAM,CAAC,CAAE;kBACrEuD,SAAS,EAAC,2GAA2G;kBACrHe,QAAQ;gBAAA;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN9E,OAAA;cAAK0E,SAAS,EAAC,uCAAuC;cAAAU,QAAA,gBACpDpF,OAAA;gBAAAoF,QAAA,gBACEpF,OAAA;kBAAO0E,SAAS,EAAC,8CAA8C;kBAAAU,QAAA,EAAC;gBAEhE;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR9E,OAAA;kBACEkB,IAAI,EAAC,QAAQ;kBACb0E,IAAI,EAAC,MAAM;kBACXC,GAAG,EAAC,GAAG;kBACP1E,KAAK,EAAEN,QAAQ,CAACO,cAAe;kBAC/BmE,QAAQ,EAAG/C,CAAC,IAAK1B,WAAW,CAAC;oBAAE,GAAGD,QAAQ;oBAAEO,cAAc,EAAEoB,CAAC,CAACgD,MAAM,CAACrE;kBAAM,CAAC,CAAE;kBAC9EuD,SAAS,EAAC;gBAA2G;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN9E,OAAA;gBAAAoF,QAAA,gBACEpF,OAAA;kBAAO0E,SAAS,EAAC,8CAA8C;kBAAAU,QAAA,EAAC;gBAEhE;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR9E,OAAA;kBACEkB,IAAI,EAAC,QAAQ;kBACb0E,IAAI,EAAC,MAAM;kBACXC,GAAG,EAAC,GAAG;kBACP1E,KAAK,EAAEN,QAAQ,CAACQ,gBAAiB;kBACjCkE,QAAQ,EAAG/C,CAAC,IAAK1B,WAAW,CAAC;oBAAE,GAAGD,QAAQ;oBAAEQ,gBAAgB,EAAEmB,CAAC,CAACgD,MAAM,CAACrE;kBAAM,CAAC,CAAE;kBAChFuD,SAAS,EAAC,2GAA2G;kBACrHqB,QAAQ,EAAElF,QAAQ,CAACK,IAAI,KAAK;gBAAa;kBAAAyD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN9E,OAAA;gBAAAoF,QAAA,gBACEpF,OAAA;kBAAO0E,SAAS,EAAC,8CAA8C;kBAAAU,QAAA,EAAC;gBAEhE;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR9E,OAAA;kBACEkB,IAAI,EAAC,QAAQ;kBACb2E,GAAG,EAAC,GAAG;kBACP1E,KAAK,EAAEN,QAAQ,CAACS,WAAY;kBAC5BiE,QAAQ,EAAG/C,CAAC,IAAK1B,WAAW,CAAC;oBAAE,GAAGD,QAAQ;oBAAES,WAAW,EAAEkB,CAAC,CAACgD,MAAM,CAACrE;kBAAM,CAAC,CAAE;kBAC3EuD,SAAS,EAAC,2GAA2G;kBACrHsB,WAAW,EAAC;gBAAU;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN9E,OAAA;cAAK0E,SAAS,EAAC,uCAAuC;cAAAU,QAAA,gBACpDpF,OAAA;gBAAAoF,QAAA,gBACEpF,OAAA;kBAAO0E,SAAS,EAAC,8CAA8C;kBAAAU,QAAA,EAAC;gBAEhE;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR9E,OAAA;kBACEkB,IAAI,EAAC,MAAM;kBACXC,KAAK,EAAEN,QAAQ,CAACU,UAAW;kBAC3BgE,QAAQ,EAAG/C,CAAC,IAAK1B,WAAW,CAAC;oBAAE,GAAGD,QAAQ;oBAAEU,UAAU,EAAEiB,CAAC,CAACgD,MAAM,CAACrE;kBAAM,CAAC,CAAE;kBAC1EuD,SAAS,EAAC,2GAA2G;kBACrHe,QAAQ;gBAAA;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN9E,OAAA;gBAAAoF,QAAA,gBACEpF,OAAA;kBAAO0E,SAAS,EAAC,8CAA8C;kBAAAU,QAAA,EAAC;gBAEhE;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR9E,OAAA;kBACEkB,IAAI,EAAC,MAAM;kBACXC,KAAK,EAAEN,QAAQ,CAACW,QAAS;kBACzB+D,QAAQ,EAAG/C,CAAC,IAAK1B,WAAW,CAAC;oBAAE,GAAGD,QAAQ;oBAAEW,QAAQ,EAAEgB,CAAC,CAACgD,MAAM,CAACrE;kBAAM,CAAC,CAAE;kBACxEuD,SAAS,EAAC,2GAA2G;kBACrHe,QAAQ;gBAAA;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN9E,OAAA;cAAK0E,SAAS,EAAC,mBAAmB;cAAAU,QAAA,gBAChCpF,OAAA;gBACEkB,IAAI,EAAC,UAAU;gBACfgB,EAAE,EAAC,WAAW;gBACd+D,OAAO,EAAEpF,QAAQ,CAACY,SAAU;gBAC5B8D,QAAQ,EAAG/C,CAAC,IAAK1B,WAAW,CAAC;kBAAE,GAAGD,QAAQ;kBAAEY,SAAS,EAAEe,CAAC,CAACgD,MAAM,CAACS;gBAAQ,CAAC,CAAE;gBAC3EvB,SAAS,EAAC;cAAyE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpF,CAAC,eACF9E,OAAA;gBAAOkG,OAAO,EAAC,WAAW;gBAACxB,SAAS,EAAC,kCAAkC;gBAAAU,QAAA,EAAC;cAExE;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAEN9E,OAAA;cAAK0E,SAAS,EAAC,iCAAiC;cAAAU,QAAA,gBAC9CpF,OAAA;gBACEkB,IAAI,EAAC,QAAQ;gBACbmE,OAAO,EAAEA,CAAA,KAAM;kBACb3E,YAAY,CAAC,KAAK,CAAC;kBACnBE,mBAAmB,CAAC,IAAI,CAAC;kBACzB2C,SAAS,CAAC,CAAC;gBACb,CAAE;gBACFmB,SAAS,EAAC,4EAA4E;gBAAAU,QAAA,EACvF;cAED;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT9E,OAAA;gBACEkB,IAAI,EAAC,QAAQ;gBACbwD,SAAS,EAAC,qEAAqE;gBAAAU,QAAA,EAE9EzE,gBAAgB,GAAG,eAAe,GAAG;cAAO;gBAAAgE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGD9E,OAAA;MAAK0E,SAAS,EAAC,+CAA+C;MAAAU,QAAA,eAC5DpF,OAAA;QAAK0E,SAAS,EAAC,iBAAiB;QAAAU,QAAA,eAC9BpF,OAAA;UAAO0E,SAAS,EAAC,qCAAqC;UAAAU,QAAA,gBACpDpF,OAAA;YAAO0E,SAAS,EAAC,YAAY;YAAAU,QAAA,eAC3BpF,OAAA;cAAAoF,QAAA,gBACEpF,OAAA;gBAAI0E,SAAS,EAAC,gFAAgF;gBAAAU,QAAA,EAAC;cAE/F;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL9E,OAAA;gBAAI0E,SAAS,EAAC,gFAAgF;gBAAAU,QAAA,EAAC;cAE/F;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL9E,OAAA;gBAAI0E,SAAS,EAAC,gFAAgF;gBAAAU,QAAA,EAAC;cAE/F;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL9E,OAAA;gBAAI0E,SAAS,EAAC,gFAAgF;gBAAAU,QAAA,EAAC;cAE/F;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL9E,OAAA;gBAAI0E,SAAS,EAAC,gFAAgF;gBAAAU,QAAA,EAAC;cAE/F;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL9E,OAAA;gBAAI0E,SAAS,EAAC,gFAAgF;gBAAAU,QAAA,EAAC;cAE/F;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL9E,OAAA;gBAAI0E,SAAS,EAAC,gFAAgF;gBAAAU,QAAA,EAAC;cAE/F;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACR9E,OAAA;YAAO0E,SAAS,EAAC,mCAAmC;YAAAU,QAAA,EACjD7E,SAAS,gBACRP,OAAA;cAAAoF,QAAA,eACEpF,OAAA;gBAAImG,OAAO,EAAC,GAAG;gBAACzB,SAAS,EAAC,uBAAuB;gBAAAU,QAAA,eAC/CpF,OAAA;kBAAK0E,SAAS,EAAC;gBAAyE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7F;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,GACHzE,UAAU,CAAC+F,MAAM,KAAK,CAAC,gBACzBpG,OAAA;cAAAoF,QAAA,eACEpF,OAAA;gBAAImG,OAAO,EAAC,GAAG;gBAACzB,SAAS,EAAC,qCAAqC;gBAAAU,QAAA,EAAC;cAEhE;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,GAELzE,UAAU,CAAC4C,GAAG,CAAEa,SAAS,iBACvB9D,OAAA;cAAuB0E,SAAS,EAAC,kBAAkB;cAAAU,QAAA,gBACjDpF,OAAA;gBAAI0E,SAAS,EAAC,6BAA6B;gBAAAU,QAAA,eACzCpF,OAAA;kBAAAoF,QAAA,gBACEpF,OAAA;oBAAK0E,SAAS,EAAC,mCAAmC;oBAAAU,QAAA,EAAEtB,SAAS,CAAC/C;kBAAI;oBAAA4D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACzE9E,OAAA;oBAAK0E,SAAS,EAAC,iCAAiC;oBAAAU,QAAA,EAAEtB,SAAS,CAAC9C;kBAAI;oBAAA2D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACL9E,OAAA;gBAAI0E,SAAS,EAAC,6BAA6B;gBAAAU,QAAA,eACzCpF,OAAA;kBAAK0E,SAAS,EAAC,6BAA6B;kBAAAU,QAAA,GACzCX,WAAW,CAACX,SAAS,CAAC5C,IAAI,CAAC,eAC5BlB,OAAA;oBAAM0E,SAAS,EAAC,uBAAuB;oBAAAU,QAAA,EAAEL,YAAY,CAACjB,SAAS,CAAC5C,IAAI;kBAAC;oBAAAyD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1E;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACL9E,OAAA;gBAAI0E,SAAS,EAAC,6BAA6B;gBAAAU,QAAA,eACzCpF,OAAA;kBAAM0E,SAAS,EAAC,mCAAmC;kBAAAU,QAAA,EAChDJ,WAAW,CAAClB,SAAS;gBAAC;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACL9E,OAAA;gBAAI0E,SAAS,EAAC,6BAA6B;gBAAAU,QAAA,eACzCpF,OAAA;kBAAK0E,SAAS,EAAC,uBAAuB;kBAAAU,QAAA,GACnCtB,SAAS,CAAC3B,UAAU,EAAC,KAAG,EAAC2B,SAAS,CAACxC,WAAW,IAAI,GAAG;gBAAA;kBAAAqD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACL9E,OAAA;gBAAI0E,SAAS,EAAC,6BAA6B;gBAAAU,QAAA,eACzCpF,OAAA;kBAAK0E,SAAS,EAAC,uBAAuB;kBAAAU,QAAA,gBACpCpF,OAAA;oBAAAoF,QAAA,EAAM,IAAIhD,IAAI,CAAC0B,SAAS,CAACvC,UAAU,CAAC,CAAC8E,kBAAkB,CAAC,OAAO;kBAAC;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACvE9E,OAAA;oBAAK0E,SAAS,EAAC,eAAe;oBAAAU,QAAA,GAAC,KAAG,EAAC,IAAIhD,IAAI,CAAC0B,SAAS,CAACtC,QAAQ,CAAC,CAAC6E,kBAAkB,CAAC,OAAO,CAAC;kBAAA;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/F;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACL9E,OAAA;gBAAI0E,SAAS,EAAC,6BAA6B;gBAAAU,QAAA,EACxCH,cAAc,CAACnB,SAAS;cAAC;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC,eACL9E,OAAA;gBAAI0E,SAAS,EAAC,iDAAiD;gBAAAU,QAAA,eAC7DpF,OAAA;kBAAK0E,SAAS,EAAC,gBAAgB;kBAAAU,QAAA,gBAC7BpF,OAAA;oBACEqF,OAAO,EAAEA,CAAA,KAAMxB,UAAU,CAACC,SAAS,CAAE;oBACrCY,SAAS,EAAC,yCAAyC;oBACnD4B,KAAK,EAAC,UAAU;oBAAAlB,QAAA,eAEhBpF,OAAA,CAACV,MAAM;sBAAAqF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACT9E,OAAA;oBACEqF,OAAO,EAAEA,CAAA,KAAMjB,YAAY,CAACN,SAAS,CAAC5B,EAAE,CAAE;oBAC1CwC,SAAS,EAAC,iCAAiC;oBAC3C4B,KAAK,EAAC,WAAW;oBAAAlB,QAAA,eAEjBpF,OAAA,CAACT,OAAO;sBAAAoF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,GAjDEhB,SAAS,CAAC5B,EAAE;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAkDjB,CACL;UACF;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC5E,EAAA,CAnlBID,UAAU;EAAA,QACeJ,WAAW;AAAA;AAAA0G,EAAA,GADpCtG,UAAU;AAqlBhB,eAAeA,UAAU;AAAC,IAAAsG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}