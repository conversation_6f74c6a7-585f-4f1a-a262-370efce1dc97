<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class GrillingOption extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'description',
        'additional_cost',
        'cooking_time',
        'is_active'
    ];

    protected $casts = [
        'additional_cost' => 'decimal:2',
        'cooking_time' => 'integer',
        'is_active' => 'boolean'
    ];

    // Relations
    public function products(): BelongsToMany
    {
        return $this->belongsToMany(Product::class, 'product_grilling_options')
                    ->withTimestamps();
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    // Accessors
    public function getFormattedCookingTimeAttribute(): string
    {
        return $this->cooking_time . ' minutes';
    }

    public function getFormattedAdditionalCostAttribute(): string
    {
        return number_format($this->additional_cost, 2) . ' $';
    }
}
