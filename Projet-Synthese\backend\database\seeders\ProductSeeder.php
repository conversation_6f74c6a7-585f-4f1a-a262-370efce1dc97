<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Product;
use App\Models\Category;

class ProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Récupérer les catégories
        $grillesCategory = Category::where('slug', 'produits-grilles')->first();
        $nonGrillesCategory = Category::where('slug', 'produits-non-grilles')->first();
        $fromagesCategory = Category::where('slug', 'fromages')->first();
        $boissonsCategory = Category::where('slug', 'boissons')->first();
        $dessertsCategory = Category::where('slug', 'desserts')->first();
        $saladesCategory = Category::where('slug', 'salades')->first();

        $products = [
            // Produits Grillés
            [
                'name' => 'Poulet Grillé Entier',
                'slug' => 'poulet-grille-entier',
                'description' => 'Poulet entier grillé aux herbes de Provence, tendre et savoureux',
                'short_description' => 'Poulet entier grillé aux herbes',
                'price' => 25.99,
                'sale_price' => null,
                'sku' => 'PGE001',
                'stock_quantity' => 50,
                'category_id' => $grillesCategory?->id,
                'image' => '/images/products/poulet-grille.jpg',
                'gallery' => json_encode(['/images/products/poulet-grille-1.jpg', '/images/products/poulet-grille-2.jpg']),
                'is_featured' => true,
                'is_active' => true,
                'weight' => 1.5,
                'dimensions' => null,
                'meta_title' => 'Poulet Grillé Entier - YUMMY',
                'meta_description' => 'Délicieux poulet entier grillé aux herbes de Provence'
            ],
            [
                'name' => 'Brochettes de Bœuf',
                'slug' => 'brochettes-boeuf',
                'description' => 'Brochettes de bœuf marinées et grillées, accompagnées de légumes',
                'short_description' => 'Brochettes de bœuf marinées',
                'price' => 18.50,
                'sale_price' => 16.99,
                'sku' => 'BBG001',
                'stock_quantity' => 30,
                'category_id' => $grillesCategory?->id,
                'image' => '/images/products/brochettes-boeuf.jpg',
                'gallery' => json_encode(['/images/products/brochettes-boeuf-1.jpg']),
                'is_featured' => true,
                'is_active' => true,
                'weight' => 0.4,
                'dimensions' => null,
                'meta_title' => 'Brochettes de Bœuf Grillées - YUMMY',
                'meta_description' => 'Savoureuses brochettes de bœuf marinées et grillées'
            ],
            [
                'name' => 'Saumon Grillé',
                'slug' => 'saumon-grille',
                'description' => 'Filet de saumon grillé avec une marinade au citron et aneth',
                'short_description' => 'Filet de saumon grillé au citron',
                'price' => 22.00,
                'sale_price' => null,
                'sku' => 'SG001',
                'stock_quantity' => 25,
                'category_id' => $grillesCategory?->id,
                'image' => '/images/products/saumon-grille.jpg',
                'gallery' => json_encode(['/images/products/saumon-grille-1.jpg']),
                'is_featured' => false,
                'is_active' => true,
                'weight' => 0.3,
                'dimensions' => null,
                'meta_title' => 'Saumon Grillé au Citron - YUMMY',
                'meta_description' => 'Filet de saumon grillé avec marinade citron et aneth'
            ],

            // Produits Non Grillés
            [
                'name' => 'Salade César',
                'slug' => 'salade-cesar',
                'description' => 'Salade César classique avec croûtons, parmesan et sauce maison',
                'short_description' => 'Salade César classique',
                'price' => 12.50,
                'sale_price' => null,
                'sku' => 'SC001',
                'stock_quantity' => 40,
                'category_id' => $saladesCategory?->id,
                'image' => '/images/products/salade-cesar.jpg',
                'gallery' => json_encode(['/images/products/salade-cesar-1.jpg']),
                'is_featured' => true,
                'is_active' => true,
                'weight' => 0.3,
                'dimensions' => null,
                'meta_title' => 'Salade César Fraîche - YUMMY',
                'meta_description' => 'Délicieuse salade César avec croûtons et parmesan'
            ],
            [
                'name' => 'Carpaccio de Bœuf',
                'slug' => 'carpaccio-boeuf',
                'description' => 'Fines tranches de bœuf cru avec roquette, parmesan et huile d\'olive',
                'short_description' => 'Carpaccio de bœuf à la roquette',
                'price' => 16.00,
                'sale_price' => null,
                'sku' => 'CB001',
                'stock_quantity' => 20,
                'category_id' => $nonGrillesCategory?->id,
                'image' => '/images/products/carpaccio-boeuf.jpg',
                'gallery' => json_encode(['/images/products/carpaccio-boeuf-1.jpg']),
                'is_featured' => false,
                'is_active' => true,
                'weight' => 0.2,
                'dimensions' => null,
                'meta_title' => 'Carpaccio de Bœuf - YUMMY',
                'meta_description' => 'Carpaccio de bœuf frais avec roquette et parmesan'
            ],

            // Fromages
            [
                'name' => 'Plateau de Fromages',
                'slug' => 'plateau-fromages',
                'description' => 'Sélection de fromages artisanaux avec confiture et noix',
                'short_description' => 'Plateau de fromages artisanaux',
                'price' => 19.90,
                'sale_price' => null,
                'sku' => 'PF001',
                'stock_quantity' => 15,
                'category_id' => $fromagesCategory?->id,
                'image' => '/images/products/plateau-fromages.jpg',
                'gallery' => json_encode(['/images/products/plateau-fromages-1.jpg']),
                'is_featured' => true,
                'is_active' => true,
                'weight' => 0.5,
                'dimensions' => null,
                'meta_title' => 'Plateau de Fromages Artisanaux - YUMMY',
                'meta_description' => 'Sélection de fromages artisanaux avec accompagnements'
            ],

            // Boissons
            [
                'name' => 'Jus d\'Orange Frais',
                'slug' => 'jus-orange-frais',
                'description' => 'Jus d\'orange fraîchement pressé, 100% naturel',
                'short_description' => 'Jus d\'orange fraîchement pressé',
                'price' => 4.50,
                'sale_price' => null,
                'sku' => 'JOF001',
                'stock_quantity' => 100,
                'category_id' => $boissonsCategory?->id,
                'image' => '/images/products/jus-orange.jpg',
                'gallery' => json_encode(['/images/products/jus-orange-1.jpg']),
                'is_featured' => false,
                'is_active' => true,
                'weight' => 0.5,
                'dimensions' => null,
                'meta_title' => 'Jus d\'Orange Frais - YUMMY',
                'meta_description' => 'Jus d\'orange fraîchement pressé, 100% naturel'
            ],
            [
                'name' => 'Eau Minérale',
                'slug' => 'eau-minerale',
                'description' => 'Eau minérale naturelle, bouteille de 50cl',
                'short_description' => 'Eau minérale naturelle 50cl',
                'price' => 2.00,
                'sale_price' => null,
                'sku' => 'EM001',
                'stock_quantity' => 200,
                'category_id' => $boissonsCategory?->id,
                'image' => '/images/products/eau-minerale.jpg',
                'gallery' => json_encode([]),
                'is_featured' => false,
                'is_active' => true,
                'weight' => 0.5,
                'dimensions' => null,
                'meta_title' => 'Eau Minérale Naturelle - YUMMY',
                'meta_description' => 'Eau minérale naturelle en bouteille de 50cl'
            ],

            // Desserts
            [
                'name' => 'Tiramisu Maison',
                'slug' => 'tiramisu-maison',
                'description' => 'Tiramisu traditionnel fait maison avec mascarpone et café',
                'short_description' => 'Tiramisu traditionnel maison',
                'price' => 8.50,
                'sale_price' => null,
                'sku' => 'TM001',
                'stock_quantity' => 25,
                'category_id' => $dessertsCategory?->id,
                'image' => '/images/products/tiramisu.jpg',
                'gallery' => json_encode(['/images/products/tiramisu-1.jpg']),
                'is_featured' => true,
                'is_active' => true,
                'weight' => 0.2,
                'dimensions' => null,
                'meta_title' => 'Tiramisu Maison - YUMMY',
                'meta_description' => 'Délicieux tiramisu traditionnel fait maison'
            ]
        ];

        foreach ($products as $product) {
            Product::create($product);
        }
    }
}
