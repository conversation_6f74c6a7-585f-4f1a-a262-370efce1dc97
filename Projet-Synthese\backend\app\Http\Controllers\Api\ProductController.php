<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\ValidationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class ProductController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:sanctum')->except(['index', 'show']);
    }

    public function index(Request $request)
    {
        try {
            $query = Product::with(['category', 'grillingOptions'])
                ->when($request->filled('is_active'), function ($query) use ($request) {
                    return $query->where('is_active', $request->boolean('is_active'));
                })
                ->when($request->filled('category_id'), function ($query) use ($request) {
                    return $query->where('category_id', $request->category_id);
                })
                ->when($request->filled('can_be_grilled'), function ($query) use ($request) {
                    return $query->where('can_be_grilled', $request->boolean('can_be_grilled'));
                })
                ->when($request->filled('featured'), function ($query) {
                    return $query->where('is_featured', true);
                })
                ->when($request->filled('search'), function ($query) use ($request) {
                    return $query->where('name', 'like', '%' . $request->search . '%')
                        ->orWhere('description', 'like', '%' . $request->search . '%');
                });

            // Tri
            $sortField = in_array($request->get('sort_by'), [
                'name', 'price', 'created_at'
            ]) ? $request->get('sort_by') : 'created_at';
            
            $sortDirection = in_array($request->get('sort_direction'), [
                'asc', 'desc'
            ]) ? $request->get('sort_direction') : 'desc';

            $query->orderBy($sortField, $sortDirection);

            $perPage = min(max($request->get('per_page', 12), 1), 100);
            $products = $query->paginate($perPage);

            return response()->json([
                'status' => 'success',
                'data' => $products
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Une erreur est survenue lors de la récupération des produits',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function store(Request $request)
    {
        try {
            $validated = $request->validate([
                'category_id' => 'required|exists:categories,id',
                'name' => 'required|string|max:255',
                'description' => 'required|string',
                'price' => 'required|numeric|min:0',
                'sale_price' => 'nullable|numeric|min:0|lt:price',
                'stock' => 'required|integer|min:0',
                'sku' => 'required|string|unique:products',
                'image' => 'required|image|max:2048|mimes:jpeg,png,jpg',
                'additional_images.*' => 'nullable|image|max:2048|mimes:jpeg,png,jpg',
                'can_be_grilled' => 'required|boolean',
                'weight' => 'nullable|numeric|min:0',
                'is_featured' => 'nullable|boolean',
                'grilling_options' => 'nullable|array',
                'grilling_options.*' => 'exists:grilling_options,id'
            ]);

            \DB::beginTransaction();

            try {
                $validated['slug'] = Str::slug($validated['name']);
                
                if ($request->hasFile('image')) {
                    $validated['image'] = $request->file('image')->store('products', 'public');
                }

                if ($request->hasFile('additional_images')) {
                    $additionalImages = [];
                    foreach ($request->file('additional_images') as $image) {
                        $additionalImages[] = $image->store('products', 'public');
                    }
                    $validated['additional_images'] = $additionalImages;
                }

                $product = Product::create($validated);

                if (!empty($validated['grilling_options'])) {
                    $product->grillingOptions()->attach($validated['grilling_options']);
                }

                \DB::commit();

                return response()->json([
                    'status' => 'success',
                    'message' => 'Produit créé avec succès',
                    'data' => $product->load(['category', 'grillingOptions'])
                ], 201);

            } catch (\Exception $e) {
                \DB::rollBack();
                throw $e;
            }

        } catch (ValidationException $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Erreur de validation',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Une erreur est survenue lors de la création du produit',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function show(Product $product)
    {
        try {
            return response()->json([
                'status' => 'success',
                'data' => $product->load(['category', 'grillingOptions'])
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Une erreur est survenue lors de la récupération du produit',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function update(Request $request, Product $product)
    {
        try {
            $validated = $request->validate([
                'category_id' => 'sometimes|required|exists:categories,id',
                'name' => 'sometimes|required|string|max:255',
                'description' => 'sometimes|required|string',
                'price' => 'sometimes|required|numeric|min:0',
                'sale_price' => 'nullable|numeric|min:0|lt:price',
                'stock' => 'sometimes|required|integer|min:0',
                'sku' => 'sometimes|required|string|unique:products,sku,' . $product->id,
                'image' => 'nullable|image|max:2048|mimes:jpeg,png,jpg',
                'additional_images.*' => 'nullable|image|max:2048|mimes:jpeg,png,jpg',
                'can_be_grilled' => 'sometimes|required|boolean',
                'weight' => 'nullable|numeric|min:0',
                'is_featured' => 'nullable|boolean',
                'is_active' => 'nullable|boolean',
                'grilling_options' => 'nullable|array',
                'grilling_options.*' => 'exists:grilling_options,id'
            ]);

            if ($request->has('name')) {
                $validated['slug'] = Str::slug($validated['name']);
            }

            // Gestion de l'image principale
            if ($request->hasFile('image')) {
                // Supprimer l'ancienne image
                if ($product->image) {
                    Storage::disk('public')->delete($product->image);
                }
                $validated['image'] = $request->file('image')->store('products', 'public');
            }

            // Gestion des images additionnelles
            if ($request->hasFile('additional_images')) {
                // Supprimer les anciennes images additionnelles
                if ($product->additional_images) {
                    foreach ($product->additional_images as $image) {
                        Storage::disk('public')->delete($image);
                    }
                }
                
                $additionalImages = [];
                foreach ($request->file('additional_images') as $image) {
                    $additionalImages[] = $image->store('products', 'public');
                }
                $validated['additional_images'] = $additionalImages;
            }

            $product->update($validated);

            // Mettre à jour les options de grillage si présentes
            if (isset($validated['grilling_options'])) {
                $product->grillingOptions()->sync($validated['grilling_options']);
            }

            return response()->json([
                'status' => 'success',
                'message' => 'Produit mis à jour avec succès',
                'data' => $product->load(['category', 'grillingOptions'])
            ]);

        } catch (ValidationException $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Erreur de validation',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Une erreur est survenue lors de la mise à jour du produit',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function destroy(Product $product)
    {
        try {
            // Supprimer les images
            if ($product->image) {
                Storage::disk('public')->delete($product->image);
            }
            
            if ($product->additional_images) {
                foreach ($product->additional_images as $image) {
                    Storage::disk('public')->delete($image);
                }
            }

            $product->delete();

            return response()->json([
                'status' => 'success',
                'message' => 'Produit supprimé avec succès'
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Une erreur est survenue lors de la suppression du produit',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}

