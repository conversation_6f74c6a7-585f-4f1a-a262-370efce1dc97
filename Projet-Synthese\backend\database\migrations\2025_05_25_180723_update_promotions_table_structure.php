<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('promotions', function (Blueprint $table) {
            // Vérifier et ajouter les colonnes manquantes si elles n'existent pas
            if (!Schema::hasColumn('promotions', 'name')) {
                $table->string('name')->after('id');
            }
            if (!Schema::hasColumn('promotions', 'code')) {
                $table->string('code')->unique()->after('name');
            }
            if (!Schema::hasColumn('promotions', 'description')) {
                $table->text('description')->nullable()->after('code');
            }
            if (!Schema::hasColumn('promotions', 'type')) {
                $table->enum('type', ['percentage', 'fixed', 'free_shipping'])->after('description');
            }
            if (!Schema::hasColumn('promotions', 'value')) {
                $table->decimal('value', 10, 2)->after('type');
            }
            if (!Schema::hasColumn('promotions', 'minimum_amount')) {
                $table->decimal('minimum_amount', 10, 2)->nullable()->after('value');
            }
            if (!Schema::hasColumn('promotions', 'maximum_discount')) {
                $table->decimal('maximum_discount', 10, 2)->nullable()->after('minimum_amount');
            }
            if (!Schema::hasColumn('promotions', 'usage_limit')) {
                $table->integer('usage_limit')->nullable()->after('maximum_discount');
            }
            if (!Schema::hasColumn('promotions', 'used_count')) {
                $table->integer('used_count')->default(0)->after('usage_limit');
            }
            if (!Schema::hasColumn('promotions', 'start_date')) {
                $table->datetime('start_date')->after('used_count');
            }
            if (!Schema::hasColumn('promotions', 'end_date')) {
                $table->datetime('end_date')->after('start_date');
            }
            if (!Schema::hasColumn('promotions', 'is_active')) {
                $table->boolean('is_active')->default(true)->after('end_date');
            }
            if (!Schema::hasColumn('promotions', 'deleted_at')) {
                $table->softDeletes();
            }
        });

        // Ajouter les index
        Schema::table('promotions', function (Blueprint $table) {
            if (!Schema::hasIndex('promotions', ['code', 'is_active'])) {
                $table->index(['code', 'is_active']);
            }
            if (!Schema::hasIndex('promotions', ['start_date', 'end_date'])) {
                $table->index(['start_date', 'end_date']);
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('promotions', function (Blueprint $table) {
            $table->dropIndex(['code', 'is_active']);
            $table->dropIndex(['start_date', 'end_date']);
            $table->dropColumn([
                'name', 'code', 'description', 'type', 'value',
                'minimum_amount', 'maximum_discount', 'usage_limit',
                'used_count', 'start_date', 'end_date', 'is_active'
            ]);
            $table->dropSoftDeletes();
        });
    }
};
