import React, { useState } from 'react';
import { useData } from '../../context/DataContext';
import { categoryAPI } from '../../services/apiService';

const CategoriesCRUD = () => {
  const { state, dispatch, DataActions } = useData();
  const { categories, loading } = state;
  
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentCategory, setCurrentCategory] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    color: 'bg-blue-100',
    textColor: 'text-blue-800',
    status: 'active'
  });

  // Couleurs disponibles pour les catégories
  const colorOptions = [
    { bg: 'bg-red-100', text: 'text-red-800', label: 'Rouge' },
    { bg: 'bg-green-100', text: 'text-green-800', label: 'Vert' },
    { bg: 'bg-blue-100', text: 'text-blue-800', label: 'Bleu' },
    { bg: 'bg-yellow-100', text: 'text-yellow-800', label: 'Jaune' },
    { bg: 'bg-purple-100', text: 'text-purple-800', label: 'Violet' },
    { bg: 'bg-gray-100', text: 'text-gray-800', label: 'Gris' },
    { bg: 'bg-orange-100', text: 'text-orange-800', label: 'Orange' },
    { bg: 'bg-pink-100', text: 'text-pink-800', label: 'Rose' }
  ];

  // Gestion des changements dans le formulaire
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Gestion du changement de couleur
  const handleColorChange = (colorOption) => {
    setFormData(prev => ({
      ...prev,
      color: colorOption.bg,
      textColor: colorOption.text
    }));
  };

  // Ouvrir le modal pour ajouter/modifier une catégorie
  const openModal = (category = null) => {
    if (category) {
      setCurrentCategory(category);
      setFormData({
        name: category.name,
        description: category.description,
        color: category.color,
        textColor: category.textColor,
        status: category.status
      });
    } else {
      setCurrentCategory(null);
      setFormData({
        name: '',
        description: '',
        color: 'bg-blue-100',
        textColor: 'text-blue-800',
        status: 'active'
      });
    }
    setIsModalOpen(true);
  };

  // Fermer le modal
  const closeModal = () => {
    setIsModalOpen(false);
    setCurrentCategory(null);
    setFormData({
      name: '',
      description: '',
      color: 'bg-blue-100',
      textColor: 'text-blue-800',
      status: 'active'
    });
  };

  // Soumettre le formulaire
  const handleSubmit = async (e) => {
    e.preventDefault();
    dispatch({ type: DataActions.SET_LOADING, payload: true });

    try {
      const categoryData = {
        ...formData,
        slug: formData.name.toLowerCase().replace(/\s+/g, '-')
      };

      if (currentCategory) {
        // Mise à jour d'une catégorie existante
        const result = await categoryAPI.update(currentCategory.id, categoryData);

        if (result.success) {
          dispatch({
            type: DataActions.UPDATE_CATEGORY,
            payload: { ...result.data, id: currentCategory.id }
          });
          console.log('Catégorie mise à jour via API');
        } else {
          // Fallback: mise à jour locale
          dispatch({
            type: DataActions.UPDATE_CATEGORY,
            payload: { ...categoryData, id: currentCategory.id }
          });
          console.log('Catégorie mise à jour localement (mode démonstration)');
        }
      } else {
        // Création d'une nouvelle catégorie
        const result = await categoryAPI.create(categoryData);

        if (result.success) {
          dispatch({ type: DataActions.ADD_CATEGORY, payload: result.data });
          console.log('Catégorie créée via API');
        } else {
          // Fallback: création locale
          const newCategory = {
            ...categoryData,
            id: Date.now()
          };
          dispatch({ type: DataActions.ADD_CATEGORY, payload: newCategory });
          console.log('Catégorie créée localement (mode démonstration)');
        }
      }

      closeModal();
    } catch (err) {
      console.error('Erreur lors de l\'enregistrement de la catégorie', err);
      // Fallback en cas d'erreur
      const categoryData = {
        ...formData,
        slug: formData.name.toLowerCase().replace(/\s+/g, '-')
      };

      if (currentCategory) {
        dispatch({
          type: DataActions.UPDATE_CATEGORY,
          payload: { ...categoryData, id: currentCategory.id }
        });
      } else {
        const newCategory = {
          ...categoryData,
          id: Date.now()
        };
        dispatch({ type: DataActions.ADD_CATEGORY, payload: newCategory });
      }
      closeModal();
    } finally {
      dispatch({ type: DataActions.SET_LOADING, payload: false });
    }
  };

  // Supprimer une catégorie
  const handleDelete = (id) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer cette catégorie ?')) {
      dispatch({ type: DataActions.SET_LOADING, payload: true });

      try {
        dispatch({ type: DataActions.DELETE_CATEGORY, payload: id });
        console.log('Catégorie supprimée avec succès');
      } catch (err) {
        console.error('Erreur lors de la suppression de la catégorie', err);
      } finally {
        dispatch({ type: DataActions.SET_LOADING, payload: false });
      }
    }
  };

  // Basculer le statut d'une catégorie
  const toggleStatus = (id, currentStatus) => {
    const newStatus = currentStatus === 'active' ? 'inactive' : 'active';
    dispatch({ type: DataActions.SET_LOADING, payload: true });

    try {
      dispatch({
        type: DataActions.UPDATE_CATEGORY,
        payload: { id, status: newStatus }
      });
      console.log(`Catégorie ${newStatus === 'active' ? 'activée' : 'désactivée'}`);
    } catch (err) {
      console.error('Erreur lors du changement de statut', err);
    } finally {
      dispatch({ type: DataActions.SET_LOADING, payload: false });
    }
  };

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Gestion des Catégories</h1>
          <p className="text-sm text-gray-500">
            {categories.length} catégorie(s) • CRUD complet activé
          </p>
        </div>
        <button
          onClick={() => openModal()}
          className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
        >
          Ajouter une catégorie
        </button>
      </div>

      {/* Grille des catégories */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {categories.map((category) => (
          <div key={category.id} className="bg-white rounded-lg shadow-sm border overflow-hidden">
            <div className={`p-4 ${category.color}`}>
              <div className="flex justify-between items-start">
                <h3 className={`text-lg font-semibold ${category.textColor}`}>
                  {category.name}
                </h3>
                <span className={`px-2 py-1 text-xs rounded-full ${
                  category.status === 'active' 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  {category.status === 'active' ? 'Actif' : 'Inactif'}
                </span>
              </div>
            </div>
            
            <div className="p-4">
              <p className="text-gray-600 text-sm mb-4">
                {category.description}
              </p>
              
              <div className="flex justify-between items-center">
                <div className="flex space-x-2">
                  <button
                    onClick={() => openModal(category)}
                    className="text-indigo-600 hover:text-indigo-900 text-sm"
                  >
                    Modifier
                  </button>
                  <button
                    onClick={() => toggleStatus(category.id, category.status)}
                    className={`text-sm ${
                      category.status === 'active' 
                        ? 'text-orange-600 hover:text-orange-900' 
                        : 'text-green-600 hover:text-green-900'
                    }`}
                  >
                    {category.status === 'active' ? 'Désactiver' : 'Activer'}
                  </button>
                  <button
                    onClick={() => handleDelete(category.id)}
                    className="text-red-600 hover:text-red-900 text-sm"
                  >
                    Supprimer
                  </button>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Modal pour ajouter/modifier une catégorie */}
      {isModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg w-full max-w-md mx-4">
            <div className="px-6 py-4 border-b">
              <h3 className="text-lg font-semibold">
                {currentCategory ? 'Modifier la catégorie' : 'Ajouter une catégorie'}
              </h3>
            </div>
            <form onSubmit={handleSubmit}>
              <div className="p-6">
                <div className="mb-4">
                  <label htmlFor="name" className="block text-gray-700 mb-2">Nom</label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                    required
                  />
                </div>
                
                <div className="mb-4">
                  <label htmlFor="description" className="block text-gray-700 mb-2">Description</label>
                  <textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                    rows="3"
                  ></textarea>
                </div>
                
                <div className="mb-4">
                  <label className="block text-gray-700 mb-2">Couleur</label>
                  <div className="grid grid-cols-4 gap-2">
                    {colorOptions.map((colorOption, index) => (
                      <button
                        key={index}
                        type="button"
                        onClick={() => handleColorChange(colorOption)}
                        className={`p-3 rounded-lg border-2 ${colorOption.bg} ${colorOption.text} ${
                          formData.color === colorOption.bg ? 'border-gray-800' : 'border-gray-300'
                        }`}
                      >
                        {colorOption.label}
                      </button>
                    ))}
                  </div>
                </div>
                
                <div className="mb-4">
                  <label htmlFor="status" className="block text-gray-700 mb-2">Statut</label>
                  <select
                    id="status"
                    name="status"
                    value={formData.status}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                  >
                    <option value="active">Actif</option>
                    <option value="inactive">Inactif</option>
                  </select>
                </div>
              </div>
              
              <div className="px-6 py-4 bg-gray-50 border-t flex justify-end">
                <button
                  type="button"
                  onClick={closeModal}
                  className="px-4 py-2 border rounded-lg text-gray-700 mr-2 hover:bg-gray-100"
                >
                  Annuler
                </button>
                <button
                  type="submit"
                  disabled={loading}
                  className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50"
                >
                  {loading ? 'Enregistrement...' : (currentCategory ? 'Mettre à jour' : 'Ajouter')}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default CategoriesCRUD;
