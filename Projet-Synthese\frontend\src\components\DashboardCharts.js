import React from 'react';
import { FaChartLine, FaChartPie } from 'react-icons/fa';

const DashboardCharts = ({ salesData, categoryData }) => {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-8">
      {/* Graphique des ventes */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Revenus Mensuels</h3>
          <FaChartLine className="text-blue-500" />
        </div>
        <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
          <div className="text-center">
            <FaChartLine className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <p className="text-gray-500">Graphique des revenus</p>
            <p className="text-sm text-gray-400 mt-2">
              {salesData?.labels?.length || 0} mois de données
            </p>
          </div>
        </div>
      </div>

      {/* Graphique des catégories */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Catégories Populaires</h3>
          <FaChartPie className="text-green-500" />
        </div>
        <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
          <div className="text-center">
            <FaChartPie className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <p className="text-gray-500">Répartition par catégories</p>
            <p className="text-sm text-gray-400 mt-2">
              {categoryData?.labels?.length || 0} catégories
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardCharts;
