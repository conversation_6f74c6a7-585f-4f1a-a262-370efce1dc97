import axios from 'axios';

// Configuration de base pour axios
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

// Instance axios configurée
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// Intercepteur pour ajouter le token d'authentification
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Intercepteur pour gérer les réponses et erreurs
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Token expiré ou invalide
      localStorage.removeItem('auth_token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Service API pour les produits
export const productAPI = {
  // Récupérer tous les produits (public)
  getAll: async (params = {}) => {
    try {
      const response = await apiClient.get('/api/products', { params });
      return {
        success: true,
        data: response.data.data || response.data,
        meta: response.data.meta || null
      };
    } catch (error) {
      console.error('Erreur API produits:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Erreur de connexion'
      };
    }
  },

  // Récupérer un produit par ID
  getById: async (id) => {
    try {
      const response = await apiClient.get(`/api/products/${id}`);
      return {
        success: true,
        data: response.data.data || response.data
      };
    } catch (error) {
      console.error('Erreur API produit:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Erreur de connexion'
      };
    }
  },

  // Rechercher des produits
  search: async (query, filters = {}) => {
    try {
      const params = { search: query, ...filters };
      const response = await apiClient.get('/api/products/search', { params });
      return {
        success: true,
        data: response.data.data || response.data,
        meta: response.data.meta || null
      };
    } catch (error) {
      console.error('Erreur recherche produits:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Erreur de connexion'
      };
    }
  },

  // Récupérer les produits par catégorie
  getByCategory: async (categoryId, params = {}) => {
    try {
      const response = await apiClient.get(`/api/products/category/${categoryId}`, { params });
      return {
        success: true,
        data: response.data.data || response.data,
        meta: response.data.meta || null
      };
    } catch (error) {
      console.error('Erreur API produits par catégorie:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Erreur de connexion'
      };
    }
  },

  // Admin: Créer un produit
  create: async (productData) => {
    try {
      const response = await apiClient.post('/api/admin/products', productData, {
        headers: {
          'Content-Type': productData instanceof FormData ? 'multipart/form-data' : 'application/json'
        }
      });
      return {
        success: true,
        data: response.data.data || response.data
      };
    } catch (error) {
      console.error('Erreur création produit:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Erreur de connexion'
      };
    }
  },

  // Admin: Mettre à jour un produit
  update: async (id, productData) => {
    try {
      const response = await apiClient.put(`/api/admin/products/${id}`, productData, {
        headers: {
          'Content-Type': productData instanceof FormData ? 'multipart/form-data' : 'application/json'
        }
      });
      return {
        success: true,
        data: response.data.data || response.data
      };
    } catch (error) {
      console.error('Erreur mise à jour produit:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Erreur de connexion'
      };
    }
  },

  // Admin: Supprimer un produit
  delete: async (id) => {
    try {
      await apiClient.delete(`/api/admin/products/${id}`);
      return {
        success: true,
        message: 'Produit supprimé avec succès'
      };
    } catch (error) {
      console.error('Erreur suppression produit:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Erreur de connexion'
      };
    }
  }
};

// Service API pour les catégories
export const categoryAPI = {
  // Récupérer toutes les catégories (public)
  getAll: async (params = {}) => {
    try {
      const response = await apiClient.get('/api/categories', { params });
      return {
        success: true,
        data: response.data.data || response.data,
        meta: response.data.meta || null
      };
    } catch (error) {
      console.error('Erreur API catégories:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Erreur de connexion'
      };
    }
  },

  // Récupérer une catégorie par ID
  getById: async (id) => {
    try {
      const response = await apiClient.get(`/api/categories/${id}`);
      return {
        success: true,
        data: response.data.data || response.data
      };
    } catch (error) {
      console.error('Erreur API catégorie:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Erreur de connexion'
      };
    }
  },

  // Récupérer une catégorie par slug
  getBySlug: async (slug) => {
    try {
      const response = await apiClient.get(`/api/categories/slug/${slug}`);
      return {
        success: true,
        data: response.data.data || response.data
      };
    } catch (error) {
      console.error('Erreur API catégorie par slug:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Erreur de connexion'
      };
    }
  },

  // Admin: Créer une catégorie
  create: async (categoryData) => {
    try {
      const response = await apiClient.post('/api/admin/categories', categoryData);
      return {
        success: true,
        data: response.data.data || response.data
      };
    } catch (error) {
      console.error('Erreur création catégorie:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Erreur de connexion'
      };
    }
  },

  // Admin: Mettre à jour une catégorie
  update: async (id, categoryData) => {
    try {
      const response = await apiClient.put(`/api/admin/categories/${id}`, categoryData);
      return {
        success: true,
        data: response.data.data || response.data
      };
    } catch (error) {
      console.error('Erreur mise à jour catégorie:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Erreur de connexion'
      };
    }
  },

  // Admin: Supprimer une catégorie
  delete: async (id) => {
    try {
      await apiClient.delete(`/api/admin/categories/${id}`);
      return {
        success: true,
        message: 'Catégorie supprimée avec succès'
      };
    } catch (error) {
      console.error('Erreur suppression catégorie:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Erreur de connexion'
      };
    }
  }
};

// Service API pour les commandes
export const orderAPI = {
  // Récupérer toutes les commandes (admin)
  getAll: async (params = {}) => {
    try {
      const response = await apiClient.get('/api/admin/orders', { params });
      return {
        success: true,
        data: response.data.data || response.data,
        meta: response.data.meta || null
      };
    } catch (error) {
      console.error('Erreur API commandes:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Erreur de connexion'
      };
    }
  },

  // Créer une commande (client)
  create: async (orderData) => {
    try {
      const response = await apiClient.post('/api/orders', orderData);
      return {
        success: true,
        data: response.data.data || response.data
      };
    } catch (error) {
      console.error('Erreur création commande:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Erreur de connexion'
      };
    }
  },

  // Mettre à jour le statut d'une commande (admin)
  updateStatus: async (id, status) => {
    try {
      const response = await apiClient.patch(`/api/admin/orders/${id}/status`, { status });
      return {
        success: true,
        data: response.data.data || response.data
      };
    } catch (error) {
      console.error('Erreur mise à jour statut commande:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Erreur de connexion'
      };
    }
  }
};

// Export de l'instance axios pour usage direct si nécessaire
export default apiClient;
