{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Projet-Synthese\\\\frontend\\\\src\\\\components\\\\EmptyStateMessage.js\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst EmptyStateMessage = ({\n  title = \"Aucun contenu disponible\",\n  message = \"L'administrateur n'a pas encore ajouté de contenu.\",\n  actionText = \"Aller à l'administration\",\n  actionLink = \"/admin\",\n  icon = null\n}) => {\n  const DefaultIcon = () => /*#__PURE__*/_jsxDEV(\"svg\", {\n    className: \"mx-auto h-16 w-16\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    stroke: \"currentColor\",\n    children: /*#__PURE__*/_jsxDEV(\"path\", {\n      strokeLinecap: \"round\",\n      strokeLinejoin: \"round\",\n      strokeWidth: 1,\n      d: \"M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8V4a1 1 0 00-1-1H7a1 1 0 00-1 1v1m8 0V4.5\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"text-center py-12\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-50 rounded-lg p-8 max-w-2xl mx-auto\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-gray-400 mb-4\",\n        children: icon || /*#__PURE__*/_jsxDEV(DefaultIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 20\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-xl font-semibold text-gray-900 mb-2\",\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600 mb-4\",\n        children: message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-sm text-gray-500 mb-6\",\n        children: \"\\uD83D\\uDD17 Toutes les donn\\xE9es sont g\\xE9r\\xE9es via l'interface d'administration Laravel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: actionLink,\n        className: \"inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors\",\n        children: actionText\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 18,\n    columnNumber: 5\n  }, this);\n};\n_c = EmptyStateMessage;\nexport default EmptyStateMessage;\nvar _c;\n$RefreshReg$(_c, \"EmptyStateMessage\");", "map": {"version": 3, "names": ["React", "Link", "jsxDEV", "_jsxDEV", "EmptyStateMessage", "title", "message", "actionText", "actionLink", "icon", "DefaultIcon", "className", "fill", "viewBox", "stroke", "children", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet-Synthese/frontend/src/components/EmptyStateMessage.js"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\n\nconst EmptyStateMessage = ({ \n  title = \"Aucun contenu disponible\",\n  message = \"L'administrateur n'a pas encore ajouté de contenu.\",\n  actionText = \"Aller à l'administration\",\n  actionLink = \"/admin\",\n  icon = null\n}) => {\n  const DefaultIcon = () => (\n    <svg className=\"mx-auto h-16 w-16\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1} d=\"M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8V4a1 1 0 00-1-1H7a1 1 0 00-1 1v1m8 0V4.5\" />\n    </svg>\n  );\n\n  return (\n    <div className=\"text-center py-12\">\n      <div className=\"bg-gray-50 rounded-lg p-8 max-w-2xl mx-auto\">\n        <div className=\"text-gray-400 mb-4\">\n          {icon || <DefaultIcon />}\n        </div>\n        <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">{title}</h3>\n        <p className=\"text-gray-600 mb-4\">{message}</p>\n        <p className=\"text-sm text-gray-500 mb-6\">\n          🔗 Toutes les données sont gérées via l'interface d'administration Laravel\n        </p>\n        <Link\n          to={actionLink}\n          className=\"inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors\"\n        >\n          {actionText}\n        </Link>\n      </div>\n    </div>\n  );\n};\n\nexport default EmptyStateMessage;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,iBAAiB,GAAGA,CAAC;EACzBC,KAAK,GAAG,0BAA0B;EAClCC,OAAO,GAAG,oDAAoD;EAC9DC,UAAU,GAAG,0BAA0B;EACvCC,UAAU,GAAG,QAAQ;EACrBC,IAAI,GAAG;AACT,CAAC,KAAK;EACJ,MAAMC,WAAW,GAAGA,CAAA,kBAClBP,OAAA;IAAKQ,SAAS,EAAC,mBAAmB;IAACC,IAAI,EAAC,MAAM;IAACC,OAAO,EAAC,WAAW;IAACC,MAAM,EAAC,cAAc;IAAAC,QAAA,eACtFZ,OAAA;MAAMa,aAAa,EAAC,OAAO;MAACC,cAAc,EAAC,OAAO;MAACC,WAAW,EAAE,CAAE;MAACC,CAAC,EAAC;IAA0I;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC/M,CACN;EAED,oBACEpB,OAAA;IAAKQ,SAAS,EAAC,mBAAmB;IAAAI,QAAA,eAChCZ,OAAA;MAAKQ,SAAS,EAAC,6CAA6C;MAAAI,QAAA,gBAC1DZ,OAAA;QAAKQ,SAAS,EAAC,oBAAoB;QAAAI,QAAA,EAChCN,IAAI,iBAAIN,OAAA,CAACO,WAAW;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CAAC,eACNpB,OAAA;QAAIQ,SAAS,EAAC,0CAA0C;QAAAI,QAAA,EAAEV;MAAK;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACrEpB,OAAA;QAAGQ,SAAS,EAAC,oBAAoB;QAAAI,QAAA,EAAET;MAAO;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC/CpB,OAAA;QAAGQ,SAAS,EAAC,4BAA4B;QAAAI,QAAA,EAAC;MAE1C;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJpB,OAAA,CAACF,IAAI;QACHuB,EAAE,EAAEhB,UAAW;QACfG,SAAS,EAAC,gHAAgH;QAAAI,QAAA,EAEzHR;MAAU;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACE,EAAA,GAjCIrB,iBAAiB;AAmCvB,eAAeA,iBAAiB;AAAC,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}