import { useAuth } from '../context/AuthContext';

/**
 * Hook personnalisé pour utiliser l'authentification de manière sécurisée
 * Retourne des valeurs par défaut si le contexte n'est pas disponible
 */
const useSafeAuth = () => {
  try {
    return useAuth();
  } catch (error) {
    // Si le contexte n'est pas disponible, retourner des valeurs par défaut
    console.warn('AuthContext non disponible, utilisation des valeurs par défaut');
    return {
      state: {
        isAuthenticated: false,
        user: null,
        loading: false,
        error: null,
        token: null
      },
      login: () => Promise.reject(new Error('Authentification non disponible')),
      register: () => Promise.reject(new Error('Authentification non disponible')),
      logout: () => {
        console.log('Déconnexion en mode démonstration');
        window.location.href = '/';
      },
      clearError: () => {}
    };
  }
};

export default useSafeAuth;
