import React, { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import {
  FaUsers,
  FaShoppingCart,
  FaDollarSign,
  FaBox,
  FaExclamationTriangle,
  FaSync,
  FaChartLine,
  FaClock
} from 'react-icons/fa';
import { dashboardService } from '../../services/dashboardService';
import { Line, Doughnut, Bar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  BarElement,
} from 'chart.js';


// Enregistrer les composants Chart.js
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  BarElement
);

const Dashboard = () => {
  const [stats, setStats] = useState(null);
  const [chartData, setChartData] = useState(null);
  const [recentActivity, setRecentActivity] = useState([]);
  const [lowStockProducts, setLowStockProducts] = useState([]);
  const [recentOrders, setRecentOrders] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState(new Date());

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setIsLoading(true);

      // Charger toutes les données en parallèle depuis les APIs
      const [
        statsResult,
        chartResult,
        activityResult,
        lowStockResult,
        ordersResult
      ] = await Promise.all([
        dashboardService.getStats(),
        dashboardService.getChartData(),
        dashboardService.getRecentActivity(),
        dashboardService.getLowStockProducts(),
        dashboardService.getRecentOrders()
      ]);

      // Traiter les statistiques
      if (statsResult.success) {
        console.log('✅ Données stats chargées depuis l\'API');
        setStats({
          overview: {
            total_orders: statsResult.data.totalOrders,
            total_revenue: statsResult.data.totalRevenue,
            total_users: statsResult.data.totalUsers,
            total_products: statsResult.data.totalProducts,
            orders_last_30_days: statsResult.data.weeklyOrders,
            revenue_last_30_days: statsResult.data.monthlyRevenue,
            new_users_last_30_days: 15,
            active_promotions: 3,
            low_stock_products: statsResult.data.lowStockProducts
          }
        });
      } else {
        console.log('⚠️ Mode démonstration - Stats');
        setStats({
          overview: {
            total_orders: statsResult.data.totalOrders,
            total_revenue: statsResult.data.totalRevenue,
            total_users: statsResult.data.totalUsers,
            total_products: statsResult.data.totalProducts,
            orders_last_30_days: statsResult.data.weeklyOrders,
            revenue_last_30_days: statsResult.data.monthlyRevenue,
            new_users_last_30_days: 15,
            active_promotions: 3,
            low_stock_products: statsResult.data.lowStockProducts
          }
        });
      }

      // Traiter les données de graphiques
      if (chartResult.success) {
        console.log('✅ Données charts chargées depuis l\'API');
        setChartData(chartResult.data);
      } else {
        console.log('⚠️ Mode démonstration - Charts');
        setChartData(chartResult.data);
      }

      // Traiter les activités récentes
      if (activityResult.success) {
        console.log('✅ Activités chargées depuis l\'API');
        setRecentActivity(activityResult.data);
      } else {
        console.log('⚠️ Mode démonstration - Activity');
        setRecentActivity(activityResult.data);
      }

      // Traiter les produits en stock faible
      if (lowStockResult.success) {
        console.log('✅ Stock faible chargé depuis l\'API');
        setLowStockProducts(lowStockResult.data);
      } else {
        console.log('⚠️ Mode démonstration - Low Stock');
        setLowStockProducts(lowStockResult.data);
      }

      // Traiter les commandes récentes
      if (ordersResult.success) {
        console.log('✅ Commandes récentes chargées depuis l\'API');
        setRecentOrders(ordersResult.data);
      } else {
        console.log('⚠️ Mode démonstration - Orders');
        setRecentOrders(ordersResult.data);
      }

      setLastUpdated(new Date());
    } catch (error) {
      toast.error('Erreur lors du chargement des données du dashboard');
      console.error('Erreur dashboard:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('fr-MA', {
      style: 'currency',
      currency: 'MAD'
    }).format(amount);
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Tableau de bord</h1>
          <p className="text-sm text-gray-500">
            Dernière mise à jour: {lastUpdated.toLocaleString('fr-FR')}
          </p>
        </div>
        <button
          onClick={fetchDashboardData}
          className="flex items-center space-x-2 px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
          disabled={isLoading}
        >
          <FaSync className={`${isLoading ? 'animate-spin' : ''}`} />
          <span>Actualiser</span>
        </button>
      </div>

      {/* Statistiques principales */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title="Total Commandes"
          value={stats?.overview?.total_orders || 0}
          change={`+${stats?.overview?.orders_last_30_days || 0} ce mois`}
          icon={<FaShoppingCart className="text-xl" />}
          color="blue"
        />

        <StatsCard
          title="Revenus Total"
          value={formatCurrency(stats?.overview?.total_revenue || 0)}
          change={`+${formatCurrency(stats?.overview?.revenue_last_30_days || 0)} ce mois`}
          icon={<FaDollarSign className="text-xl" />}
          color="green"
        />

        <StatsCard
          title="Clients"
          value={stats?.overview?.total_users || 0}
          change={`+${stats?.overview?.new_users_last_30_days || 0} nouveaux`}
          icon={<FaUsers className="text-xl" />}
          color="purple"
        />

        <StatsCard
          title="Produits"
          value={stats?.overview?.total_products || 0}
          change={stats?.overview?.low_stock_products > 0 ?
            `${stats.overview.low_stock_products} en rupture` :
            'Stock suffisant'
          }
          icon={<FaBox className="text-xl" />}
          color={stats?.overview?.low_stock_products > 0 ? 'red' : 'orange'}
        />
      </div>

      {/* Alertes */}
      {stats?.overview?.low_stock_products > 0 && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <FaExclamationTriangle className="text-red-500 mr-2" />
            <span className="text-red-800">
              Attention: {stats.overview.low_stock_products} produit(s) en rupture de stock
            </span>
          </div>
        </div>
      )}

      {/* Graphiques */}
      <DashboardCharts
        salesData={{
          labels: stats?.charts?.monthly_revenue?.map(item => item.month) || [],
          data: stats?.charts?.monthly_revenue?.map(item => item.revenue) || []
        }}
        categoryData={{
          labels: stats?.charts?.popular_categories?.map(item => item.name) || [],
          data: stats?.charts?.popular_categories?.map(item => item.total_sold) || []
        }}
        topProducts={stats?.top_products || []}
      />

      {/* Section inférieure */}
      <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
        {/* Activités récentes */}
        <div className="xl:col-span-2">
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Activités Récentes</h3>
            <div className="space-y-4">
              {recentActivity.map((activity) => (
                <div key={activity.id} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                  <div className={`w-2 h-2 rounded-full ${
                    activity.status === 'success' ? 'bg-green-500' :
                    activity.status === 'warning' ? 'bg-yellow-500' :
                    activity.status === 'pending' ? 'bg-blue-500' :
                    activity.status === 'completed' ? 'bg-green-600' :
                    'bg-gray-500'
                  }`}></div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">{activity.message}</p>
                    <p className="text-xs text-gray-500">
                      {activity.user} • {activity.time}
                      {activity.amount && (
                        <span className="ml-2 font-medium text-green-600">
                          {formatCurrency(activity.amount)}
                        </span>
                      )}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Statistiques supplémentaires */}
        <div className="space-y-6">
          <div className="bg-white p-4 lg:p-6 rounded-lg shadow-sm border">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Promotions actives</h3>
            <div className="text-center">
              <p className="text-3xl font-bold text-primary-600">
                {stats?.overview?.active_promotions || 0}
              </p>
              <p className="text-sm text-gray-500">promotions en cours</p>
            </div>
          </div>

          <div className="bg-white p-4 lg:p-6 rounded-lg shadow-sm border">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Performance</h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Taux de conversion</span>
                <span className="text-sm font-medium">12.5%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Panier moyen</span>
                <span className="text-sm font-medium">
                  {formatCurrency((stats?.overview?.total_revenue || 0) / (stats?.overview?.total_orders || 1))}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Satisfaction client</span>
                <span className="text-sm font-medium">4.2/5</span>
              </div>
            </div>
          </div>

          {/* Produits en stock faible */}
          <div className="bg-white p-4 lg:p-6 rounded-lg shadow-sm border">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <FaExclamationTriangle className="mr-2 text-yellow-500" />
              Stock Faible
            </h3>
            <div className="space-y-2">
              {lowStockProducts.slice(0, 3).map((product) => (
                <div key={product.id} className="flex items-center justify-between p-2 bg-red-50 rounded border border-red-200">
                  <div>
                    <p className="text-sm font-medium text-gray-900">{product.name}</p>
                    <p className="text-xs text-gray-600">{product.category}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-bold text-red-600">{product.stock}</p>
                    <p className="text-xs text-gray-500">Min: {product.min_stock}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Commandes récentes */}
          <div className="bg-white p-4 lg:p-6 rounded-lg shadow-sm border">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <FaShoppingCart className="mr-2 text-blue-500" />
              Commandes Récentes
            </h3>
            <div className="space-y-2">
              {recentOrders.slice(0, 3).map((order) => (
                <div key={order.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                  <div>
                    <p className="text-sm font-medium text-gray-900">#{order.id}</p>
                    <p className="text-xs text-gray-600">{order.user.name}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-bold text-green-600">{formatCurrency(order.total)}</p>
                    <p className={`text-xs px-2 py-1 rounded-full ${
                      order.status === 'completed' ? 'bg-green-100 text-green-800' :
                      order.status === 'processing' ? 'bg-blue-100 text-blue-800' :
                      order.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {order.status === 'completed' ? 'Terminée' :
                       order.status === 'processing' ? 'En cours' :
                       order.status === 'pending' ? 'En attente' : order.status}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Section graphiques avec données API */}
      {chartData && (
        <div className="mt-8 grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Graphique des ventes */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Ventes de la Semaine</h3>
            <div className="h-64">
              <Line
                data={chartData.salesChart}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  plugins: {
                    legend: {
                      position: 'top',
                    },
                  },
                  scales: {
                    y: {
                      beginAtZero: true,
                    },
                  },
                }}
              />
            </div>
          </div>

          {/* Graphique des catégories */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Ventes par Catégorie</h3>
            <div className="h-64">
              <Doughnut
                data={chartData.categoriesChart}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  plugins: {
                    legend: {
                      position: 'bottom',
                    },
                  },
                }}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Dashboard;


