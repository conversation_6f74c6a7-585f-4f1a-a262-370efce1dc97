import React, { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import {
  FaUsers,
  FaShoppingCart,
  FaDollarSign,
  FaBox,
  FaExclamationTriangle,
  FaSync
} from 'react-icons/fa';
import { dashboardService } from '../../services/dashboardService';
import StatsCard from '../../components/StatsCard';
import DashboardCharts from '../../components/DashboardCharts';
import 'react-toastify/dist/ReactToastify.css';


// Dashboard principal

const Dashboard = () => {
  const [stats, setStats] = useState(null);
  const [recentActivity, setRecentActivity] = useState([]);
  const [lowStockProducts, setLowStockProducts] = useState([]);
  const [recentOrders, setRecentOrders] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState(new Date());
  const [apiConnected, setApiConnected] = useState(false);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setIsLoading(true);
      console.log('🔄 Chargement des données du dashboard...');

      // Charger toutes les données en parallèle
      const [
        statsResult,
        activityResult,
        lowStockResult,
        ordersResult
      ] = await Promise.all([
        dashboardService.getStats(),
        dashboardService.getRecentActivity(),
        dashboardService.getLowStockProducts(),
        dashboardService.getRecentOrders()
      ]);

      // Traiter les statistiques
      if (statsResult.success) {
        console.log('✅ Statistiques chargées depuis l\'API backend');
        setApiConnected(true);
        setStats({
          overview: {
            total_orders: statsResult.data.totalOrders || 0,
            total_revenue: statsResult.data.totalRevenue || 0,
            total_users: statsResult.data.totalUsers || 0,
            total_products: statsResult.data.totalProducts || 0,
            orders_last_30_days: statsResult.data.weeklyOrders || 0,
            revenue_last_30_days: statsResult.data.monthlyRevenue || 0,
            new_users_last_30_days: 15,
            active_promotions: 3,
            low_stock_products: statsResult.data.lowStockProducts || 0
          }
        });
        toast.success('Données chargées depuis l\'API');
      } else {
        console.log('⚠️ Mode démonstration - API non disponible');
        setApiConnected(false);
        setStats({
          overview: {
            total_orders: statsResult.data.totalOrders || 89,
            total_revenue: statsResult.data.totalRevenue || 12450.75,
            total_users: statsResult.data.totalUsers || 234,
            total_products: statsResult.data.totalProducts || 156,
            orders_last_30_days: statsResult.data.weeklyOrders || 23,
            revenue_last_30_days: statsResult.data.monthlyRevenue || 8750.25,
            new_users_last_30_days: 15,
            active_promotions: 3,
            low_stock_products: statsResult.data.lowStockProducts || 8
          }
        });
        toast.info('Mode démonstration - Données locales');
      }

      // Traiter les autres données
      setRecentActivity(activityResult.data || []);
      setLowStockProducts(lowStockResult.data || []);
      setRecentOrders(ordersResult.data || []);

      setLastUpdated(new Date());
    } catch (error) {
      console.error('❌ Erreur dashboard:', error);
      toast.error('Erreur lors du chargement des données');

      // Pas de données de fallback - forcer l'utilisation de Laravel
      setStats({
        overview: {
          total_orders: 0,
          total_revenue: 0,
          total_users: 0,
          total_products: 0,
          orders_last_30_days: 0,
          revenue_last_30_days: 0,
          new_users_last_30_days: 0,
          active_promotions: 0,
          low_stock_products: 0
        }
      });
    } finally {
      setIsLoading(false);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('fr-MA', {
      style: 'currency',
      currency: 'MAD'
    }).format(amount);
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  // Vérifier si l'application est vide (pas de données)
  const isEmpty = stats?.overview?.total_products === 0 &&
                  stats?.overview?.total_orders === 0 &&
                  stats?.overview?.total_users === 0;

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Tableau de bord</h1>
          <div className="flex items-center space-x-4 mt-1">
            <p className="text-sm text-gray-500">
              Dernière mise à jour: {lastUpdated.toLocaleString('fr-FR')}
            </p>
            <div className={`flex items-center space-x-2 px-3 py-1 rounded-full text-xs ${
              apiConnected
                ? 'bg-green-100 text-green-800'
                : 'bg-yellow-100 text-yellow-800'
            }`}>
              <div className={`w-2 h-2 rounded-full ${
                apiConnected ? 'bg-green-500' : 'bg-yellow-500'
              }`}></div>
              <span>{apiConnected ? 'API Connectée' : 'Mode Démonstration'}</span>
            </div>
          </div>
        </div>
        <button
          onClick={fetchDashboardData}
          className="flex items-center space-x-2 px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
          disabled={isLoading}
        >
          <FaSync className={`${isLoading ? 'animate-spin' : ''}`} />
          <span>Actualiser</span>
        </button>
      </div>

      {/* Message d'information si l'application est vide */}
      {isEmpty && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <FaBox className="h-8 w-8 text-blue-400" />
            </div>
            <div className="ml-3">
              <h3 className="text-lg font-medium text-blue-800">
                🎯 Application vide - Prête pour la gestion admin
              </h3>
              <div className="mt-2 text-sm text-blue-700">
                <p>✅ Toutes les données de test ont été supprimées.</p>
                <p className="mt-1">
                  <strong>Vous devez maintenant :</strong>
                </p>
                <ul className="mt-2 list-disc list-inside space-y-1">
                  <li>📁 Créer des catégories dans la section "Catégories"</li>
                  <li>📦 Ajouter des produits dans la section "Produits"</li>
                  <li>🎛️ Gérer votre application via l'interface admin</li>
                  <li>🔗 Toutes les données seront connectées à Laravel</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Statistiques principales */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title="Total Commandes"
          value={stats?.overview?.total_orders || 0}
          change={`+${stats?.overview?.orders_last_30_days || 0} ce mois`}
          icon={<FaShoppingCart className="text-xl" />}
          color="blue"
        />

        <StatsCard
          title="Revenus Total"
          value={formatCurrency(stats?.overview?.total_revenue || 0)}
          change={`+${formatCurrency(stats?.overview?.revenue_last_30_days || 0)} ce mois`}
          icon={<FaDollarSign className="text-xl" />}
          color="green"
        />

        <StatsCard
          title="Clients"
          value={stats?.overview?.total_users || 0}
          change={`+${stats?.overview?.new_users_last_30_days || 0} nouveaux`}
          icon={<FaUsers className="text-xl" />}
          color="purple"
        />

        <StatsCard
          title="Produits"
          value={stats?.overview?.total_products || 0}
          change={stats?.overview?.low_stock_products > 0 ?
            `${stats.overview.low_stock_products} en rupture` :
            'Stock suffisant'
          }
          icon={<FaBox className="text-xl" />}
          color={stats?.overview?.low_stock_products > 0 ? 'red' : 'orange'}
        />
      </div>

      {/* Alertes */}
      {stats?.overview?.low_stock_products > 0 && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <FaExclamationTriangle className="text-red-500 mr-2" />
            <span className="text-red-800">
              Attention: {stats.overview.low_stock_products} produit(s) en rupture de stock
            </span>
          </div>
        </div>
      )}



      {/* Section inférieure */}
      <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
        {/* Activités récentes */}
        <div className="xl:col-span-2">
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Activités Récentes</h3>
            <div className="space-y-4">
              {recentActivity.map((activity) => (
                <div key={activity.id} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                  <div className={`w-2 h-2 rounded-full ${
                    activity.status === 'success' ? 'bg-green-500' :
                    activity.status === 'warning' ? 'bg-yellow-500' :
                    activity.status === 'pending' ? 'bg-blue-500' :
                    activity.status === 'completed' ? 'bg-green-600' :
                    'bg-gray-500'
                  }`}></div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">{activity.message}</p>
                    <p className="text-xs text-gray-500">
                      {activity.user} • {activity.time}
                      {activity.amount && (
                        <span className="ml-2 font-medium text-green-600">
                          {formatCurrency(activity.amount)}
                        </span>
                      )}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Statistiques supplémentaires */}
        <div className="space-y-6">
          <div className="bg-white p-4 lg:p-6 rounded-lg shadow-sm border">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Promotions actives</h3>
            <div className="text-center">
              <p className="text-3xl font-bold text-primary-600">
                {stats?.overview?.active_promotions || 0}
              </p>
              <p className="text-sm text-gray-500">promotions en cours</p>
            </div>
          </div>

          <div className="bg-white p-4 lg:p-6 rounded-lg shadow-sm border">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Performance</h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Taux de conversion</span>
                <span className="text-sm font-medium">12.5%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Panier moyen</span>
                <span className="text-sm font-medium">
                  {formatCurrency((stats?.overview?.total_revenue || 0) / (stats?.overview?.total_orders || 1))}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Satisfaction client</span>
                <span className="text-sm font-medium">4.2/5</span>
              </div>
            </div>
          </div>

          {/* Produits en stock faible */}
          <div className="bg-white p-4 lg:p-6 rounded-lg shadow-sm border">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <FaExclamationTriangle className="mr-2 text-yellow-500" />
              Stock Faible
            </h3>
            <div className="space-y-2">
              {lowStockProducts.slice(0, 3).map((product) => (
                <div key={product.id} className="flex items-center justify-between p-2 bg-red-50 rounded border border-red-200">
                  <div>
                    <p className="text-sm font-medium text-gray-900">{product.name}</p>
                    <p className="text-xs text-gray-600">{product.category}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-bold text-red-600">{product.stock}</p>
                    <p className="text-xs text-gray-500">Min: {product.min_stock}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Commandes récentes */}
          <div className="bg-white p-4 lg:p-6 rounded-lg shadow-sm border">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <FaShoppingCart className="mr-2 text-blue-500" />
              Commandes Récentes
            </h3>
            <div className="space-y-2">
              {recentOrders.slice(0, 3).map((order) => (
                <div key={order.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                  <div>
                    <p className="text-sm font-medium text-gray-900">#{order.id}</p>
                    <p className="text-xs text-gray-600">{order.user.name}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-bold text-green-600">{formatCurrency(order.total)}</p>
                    <p className={`text-xs px-2 py-1 rounded-full ${
                      order.status === 'completed' ? 'bg-green-100 text-green-800' :
                      order.status === 'processing' ? 'bg-blue-100 text-blue-800' :
                      order.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {order.status === 'completed' ? 'Terminée' :
                       order.status === 'processing' ? 'En cours' :
                       order.status === 'pending' ? 'En attente' : order.status}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Section graphiques */}
      <DashboardCharts
        salesData={{
          labels: stats?.charts?.monthly_revenue?.map(item => item.month) || [],
          data: stats?.charts?.monthly_revenue?.map(item => item.revenue) || []
        }}
        categoryData={{
          labels: stats?.charts?.popular_categories?.map(item => item.name) || [],
          data: stats?.charts?.popular_categories?.map(item => item.total_sold) || []
        }}
      />
    </div>
  );
};

export default Dashboard;


