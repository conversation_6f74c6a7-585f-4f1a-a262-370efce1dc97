import React, { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import {
  FaUsers,
  FaShoppingCart,
  FaDollarSign,
  FaBox,
  FaExclamationTriangle,
  FaSync
} from 'react-icons/fa';
import { dashboardService } from '../../services/api/dashboardService';
import StatsCard from '../../components/admin/StatsCard';
import DashboardCharts from '../../components/admin/DashboardCharts';
import RecentActivity from '../../components/admin/RecentActivity';


const Dashboard = () => {
  // Mode démonstration - pas besoin d'authentification
  const [stats, setStats] = useState(null);
  const [recentActivity, setRecentActivity] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState(new Date());

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setIsLoading(true);

      // Simuler des données si l'API n'est pas disponible
      const mockStats = {
        overview: {
          total_orders: 125,
          total_revenue: 15420.50,
          total_users: 89,
          total_products: 45,
          orders_last_30_days: 32,
          revenue_last_30_days: 4250.00,
          new_users_last_30_days: 15,
          active_promotions: 3,
          low_stock_products: 2
        },
        charts: {
          monthly_revenue: [
            { month: 'Jan', revenue: 2100 },
            { month: 'Fév', revenue: 2800 },
            { month: 'Mar', revenue: 3200 },
            { month: 'Avr', revenue: 2900 },
            { month: 'Mai', revenue: 3800 },
            { month: 'Jun', revenue: 4250 }
          ],
          popular_categories: [
            { name: 'Produits Grillés', total_sold: 145 },
            { name: 'Non Grillés', total_sold: 98 },
            { name: 'Fromages', total_sold: 67 },
            { name: 'Boissons', total_sold: 89 }
          ]
        },
        top_products: [
          { name: 'Poulet Grillé Entier', total_sold: 45 },
          { name: 'Salade César', total_sold: 32 },
          { name: 'Plateau de Fromages', total_sold: 28 },
          { name: 'Brochettes de Bœuf', total_sold: 25 },
          { name: 'Jus d\'Orange Frais', total_sold: 22 }
        ]
      };

      // En mode démonstration, utiliser directement les données mockées
      // L'API sera automatiquement utilisée quand le backend sera disponible
      setStats(mockStats);

      setLastUpdated(new Date());
    } catch (error) {
      toast.error('Erreur lors du chargement des données du dashboard');
      console.error('Erreur dashboard:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('fr-MA', {
      style: 'currency',
      currency: 'MAD'
    }).format(amount);
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Tableau de bord</h1>
          <p className="text-sm text-gray-500">
            Dernière mise à jour: {lastUpdated.toLocaleString('fr-FR')}
          </p>
        </div>
        <button
          onClick={fetchDashboardData}
          className="flex items-center space-x-2 px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
          disabled={isLoading}
        >
          <FaSync className={`${isLoading ? 'animate-spin' : ''}`} />
          <span>Actualiser</span>
        </button>
      </div>

      {/* Statistiques principales */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title="Total Commandes"
          value={stats?.overview?.total_orders || 0}
          change={`+${stats?.overview?.orders_last_30_days || 0} ce mois`}
          icon={<FaShoppingCart className="text-xl" />}
          color="blue"
        />

        <StatsCard
          title="Revenus Total"
          value={formatCurrency(stats?.overview?.total_revenue || 0)}
          change={`+${formatCurrency(stats?.overview?.revenue_last_30_days || 0)} ce mois`}
          icon={<FaDollarSign className="text-xl" />}
          color="green"
        />

        <StatsCard
          title="Clients"
          value={stats?.overview?.total_users || 0}
          change={`+${stats?.overview?.new_users_last_30_days || 0} nouveaux`}
          icon={<FaUsers className="text-xl" />}
          color="purple"
        />

        <StatsCard
          title="Produits"
          value={stats?.overview?.total_products || 0}
          change={stats?.overview?.low_stock_products > 0 ?
            `${stats.overview.low_stock_products} en rupture` :
            'Stock suffisant'
          }
          icon={<FaBox className="text-xl" />}
          color={stats?.overview?.low_stock_products > 0 ? 'red' : 'orange'}
        />
      </div>

      {/* Alertes */}
      {stats?.overview?.low_stock_products > 0 && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <FaExclamationTriangle className="text-red-500 mr-2" />
            <span className="text-red-800">
              Attention: {stats.overview.low_stock_products} produit(s) en rupture de stock
            </span>
          </div>
        </div>
      )}

      {/* Graphiques */}
      <DashboardCharts
        salesData={{
          labels: stats?.charts?.monthly_revenue?.map(item => item.month) || [],
          data: stats?.charts?.monthly_revenue?.map(item => item.revenue) || []
        }}
        categoryData={{
          labels: stats?.charts?.popular_categories?.map(item => item.name) || [],
          data: stats?.charts?.popular_categories?.map(item => item.total_sold) || []
        }}
        topProducts={stats?.top_products || []}
      />

      {/* Section inférieure */}
      <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
        {/* Activités récentes */}
        <div className="xl:col-span-2">
          <RecentActivity activities={recentActivity} />
        </div>

        {/* Statistiques supplémentaires */}
        <div className="space-y-6">
          <div className="bg-white p-4 lg:p-6 rounded-lg shadow-sm border">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Promotions actives</h3>
            <div className="text-center">
              <p className="text-3xl font-bold text-primary-600">
                {stats?.overview?.active_promotions || 0}
              </p>
              <p className="text-sm text-gray-500">promotions en cours</p>
            </div>
          </div>

          <div className="bg-white p-4 lg:p-6 rounded-lg shadow-sm border">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Performance</h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Taux de conversion</span>
                <span className="text-sm font-medium">12.5%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Panier moyen</span>
                <span className="text-sm font-medium">
                  {formatCurrency((stats?.overview?.total_revenue || 0) / (stats?.overview?.total_orders || 1))}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Satisfaction client</span>
                <span className="text-sm font-medium">4.2/5</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;


