[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\services\\api.js": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\context\\AppContext.js": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\services\\api\\productService.js": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\services\\api\\config.js": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Checkout\\components\\AddressForm.jsx": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Checkout\\components\\PaymentForm.jsx": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Checkout\\components\\OrderSummary.jsx": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\services\\api\\authService.js": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\api\\orderService.js": "12", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\api\\axios.js": "13", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\CartPage.js": "14", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\context\\CartContext.js": "15", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\cart\\CartItem.js": "16", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\cart\\EmptyCart.js": "17", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\cart\\CartSummary.js": "18", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\HomePage.js": "19", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\categories\\CategoryList.js": "20", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\icons\\CategoryIcons.js": "21", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\home\\HeroSection.js": "22", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\home\\PromoSection.js": "23", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\ContactPage.js": "24", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\products\\FeaturedProducts.js": "25", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\products\\ProductCard.js": "26", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\ProductDetailPage.js": "27", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\products\\RelatedProducts.js": "28", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\layouts\\MainLayout.js": "29", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\layouts\\AdminLayout.js": "30", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\layout\\Header.js": "31", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\layout\\Footer.js": "32", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\admin\\AdminSidebar.js": "33", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\admin\\AdminHeader.js": "34", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\auth\\ProtectedRoute.js": "35", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\ProductsPage.js": "36", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\CategoryPage.js": "37", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\OrderConfirmationPage.js": "38", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\AboutPage.js": "39", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\LoginPage.js": "40", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\RegisterPage.js": "41", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\NotFoundPage.js": "42", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Admin\\Dashboard.js": "43", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Admin\\Products.js": "44", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Admin\\Categories.js": "45", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Admin\\Orders.js": "46", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\context\\AuthContext.js": "47", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Admin\\Promotions.js": "48", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\services\\api\\promotionService.js": "49", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\cart\\PromoCodeInput.js": "50", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\promotions\\ActivePromotions.js": "51", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Admin\\Users.js": "52", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Checkout\\index.js": "53", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\hooks\\useForm.js": "54", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\services\\api\\dashboardService.js": "55", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\admin\\StatsCard.js": "56", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\admin\\DashboardCharts.js": "57", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\admin\\RecentActivity.js": "58"}, {"size": 326, "mtime": 1747142994132, "results": "59", "hashOfConfig": "60"}, {"size": 3141, "mtime": 1749067718564, "results": "61", "hashOfConfig": "60"}, {"size": 363, "mtime": 1736863717815, "results": "62", "hashOfConfig": "60"}, {"size": 7689, "mtime": 1746560718828, "results": "63", "hashOfConfig": "60"}, {"size": 1521, "mtime": 1746211425729, "results": "64", "hashOfConfig": "60"}, {"size": 802, "mtime": 1745521007503, "results": "65", "hashOfConfig": "60"}, {"size": 885, "mtime": 1746211930285, "results": "66", "hashOfConfig": "60"}, {"size": 6253, "mtime": 1748254133452, "results": "67", "hashOfConfig": "60"}, {"size": 5290, "mtime": 1748254154005, "results": "68", "hashOfConfig": "60"}, {"size": 2005, "mtime": 1747137811947, "results": "69", "hashOfConfig": "60"}, {"size": 1383, "mtime": 1746561639967, "results": "70", "hashOfConfig": "60"}, {"size": 1179, "mtime": 1746211973790, "results": "71", "hashOfConfig": "60"}, {"size": 953, "mtime": 1746211963091, "results": "72", "hashOfConfig": "60"}, {"size": 3154, "mtime": 1747139445897, "results": "73", "hashOfConfig": "60"}, {"size": 3702, "mtime": 1747143163391, "results": "74", "hashOfConfig": "60"}, {"size": 4195, "mtime": 1747139473447, "results": "75", "hashOfConfig": "60"}, {"size": 1037, "mtime": 1747138769044, "results": "76", "hashOfConfig": "60"}, {"size": 3968, "mtime": 1748196287842, "results": "77", "hashOfConfig": "60"}, {"size": 14691, "mtime": 1749074788371, "results": "78", "hashOfConfig": "60"}, {"size": 2413, "mtime": 1747139088166, "results": "79", "hashOfConfig": "60"}, {"size": 4727, "mtime": 1747139080279, "results": "80", "hashOfConfig": "60"}, {"size": 2162, "mtime": 1747139107880, "results": "81", "hashOfConfig": "60"}, {"size": 2280, "mtime": 1747139120251, "results": "82", "hashOfConfig": "60"}, {"size": 7370, "mtime": 1747139301836, "results": "83", "hashOfConfig": "60"}, {"size": 1538, "mtime": 1747139308791, "results": "84", "hashOfConfig": "60"}, {"size": 2603, "mtime": 1747174196644, "results": "85", "hashOfConfig": "60"}, {"size": 10006, "mtime": 1747139423441, "results": "86", "hashOfConfig": "60"}, {"size": 1657, "mtime": 1747139427583, "results": "87", "hashOfConfig": "60"}, {"size": 792, "mtime": 1747143709897, "results": "88", "hashOfConfig": "60"}, {"size": 567, "mtime": 1747139661661, "results": "89", "hashOfConfig": "60"}, {"size": 7934, "mtime": 1747143142234, "results": "90", "hashOfConfig": "60"}, {"size": 5341, "mtime": 1747143741468, "results": "91", "hashOfConfig": "60"}, {"size": 2672, "mtime": 1748196187194, "results": "92", "hashOfConfig": "60"}, {"size": 1650, "mtime": 1747139877949, "results": "93", "hashOfConfig": "60"}, {"size": 724, "mtime": 1747139883357, "results": "94", "hashOfConfig": "60"}, {"size": 1465, "mtime": 1747139888784, "results": "95", "hashOfConfig": "60"}, {"size": 2135, "mtime": 1747139897465, "results": "96", "hashOfConfig": "60"}, {"size": 3120, "mtime": 1747139902647, "results": "97", "hashOfConfig": "60"}, {"size": 7055, "mtime": 1747140045909, "results": "98", "hashOfConfig": "60"}, {"size": 3707, "mtime": 1747140090497, "results": "99", "hashOfConfig": "60"}, {"size": 4957, "mtime": 1747140281729, "results": "100", "hashOfConfig": "60"}, {"size": 1462, "mtime": 1747140282755, "results": "101", "hashOfConfig": "60"}, {"size": 8306, "mtime": 1748255185586, "results": "102", "hashOfConfig": "60"}, {"size": 17170, "mtime": 1747140414773, "results": "103", "hashOfConfig": "60"}, {"size": 12996, "mtime": 1747140585147, "results": "104", "hashOfConfig": "60"}, {"size": 18107, "mtime": 1747140758027, "results": "105", "hashOfConfig": "60"}, {"size": 3591, "mtime": 1747143130292, "results": "106", "hashOfConfig": "60"}, {"size": 19960, "mtime": 1748196044049, "results": "107", "hashOfConfig": "60"}, {"size": 3114, "mtime": 1748195902455, "results": "108", "hashOfConfig": "60"}, {"size": 5284, "mtime": 1748195928560, "results": "109", "hashOfConfig": "60"}, {"size": 7290, "mtime": 1749074732679, "results": "110", "hashOfConfig": "60"}, {"size": 6990, "mtime": 1748253843291, "results": "111", "hashOfConfig": "60"}, {"size": 5168, "mtime": 1747137777119, "results": "112", "hashOfConfig": "60"}, {"size": 5101, "mtime": 1748254114464, "results": "113", "hashOfConfig": "60"}, {"size": 1055, "mtime": 1748254493763, "results": "114", "hashOfConfig": "60"}, {"size": 1200, "mtime": 1748254789320, "results": "115", "hashOfConfig": "60"}, {"size": 4948, "mtime": 1748254818981, "results": "116", "hashOfConfig": "60"}, {"size": 4745, "mtime": 1748254847572, "results": "117", "hashOfConfig": "60"}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1rke8h4", {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\services\\api.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\context\\AppContext.js", ["292", "293", "294"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\services\\api\\productService.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\services\\api\\config.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Checkout\\components\\AddressForm.jsx", ["295"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Checkout\\components\\PaymentForm.jsx", ["296"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Checkout\\components\\OrderSummary.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\services\\api\\authService.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\api\\orderService.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\api\\axios.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\CartPage.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\context\\CartContext.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\cart\\CartItem.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\cart\\EmptyCart.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\cart\\CartSummary.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\HomePage.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\categories\\CategoryList.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\icons\\CategoryIcons.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\home\\HeroSection.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\home\\PromoSection.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\ContactPage.js", ["297"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\products\\FeaturedProducts.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\products\\ProductCard.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\ProductDetailPage.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\products\\RelatedProducts.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\layouts\\MainLayout.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\layouts\\AdminLayout.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\layout\\Header.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\layout\\Footer.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\admin\\AdminSidebar.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\admin\\AdminHeader.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\auth\\ProtectedRoute.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\ProductsPage.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\CategoryPage.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\OrderConfirmationPage.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\AboutPage.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\LoginPage.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\RegisterPage.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\NotFoundPage.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Admin\\Dashboard.js", ["298", "299"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Admin\\Products.js", ["300"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Admin\\Categories.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Admin\\Orders.js", ["301"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\context\\AuthContext.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Admin\\Promotions.js", ["302", "303"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\services\\api\\promotionService.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\cart\\PromoCodeInput.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\promotions\\ActivePromotions.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Admin\\Users.js", ["304"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Checkout\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\hooks\\useForm.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\services\\api\\dashboardService.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\admin\\StatsCard.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\admin\\DashboardCharts.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\admin\\RecentActivity.js", ["305"], [], {"ruleId": "306", "severity": 1, "message": "307", "line": 35, "column": 1, "nodeType": "308", "messageId": "309", "endLine": 35, "endColumn": 20}, {"ruleId": "306", "severity": 1, "message": "307", "line": 37, "column": 1, "nodeType": "308", "messageId": "309", "endLine": 38, "endColumn": 38}, {"ruleId": "310", "severity": 1, "message": "311", "line": 37, "column": 1, "nodeType": "308", "messageId": "312", "endLine": 38, "endColumn": 38}, {"ruleId": "313", "severity": 1, "message": "314", "line": 10, "column": 41, "nodeType": "315", "messageId": "316", "endLine": 10, "endColumn": 53}, {"ruleId": "313", "severity": 1, "message": "314", "line": 8, "column": 41, "nodeType": "315", "messageId": "316", "endLine": 8, "endColumn": 53}, {"ruleId": "313", "severity": 1, "message": "317", "line": 33, "column": 13, "nodeType": "315", "messageId": "316", "endLine": 33, "endColumn": 21}, {"ruleId": "313", "severity": 1, "message": "318", "line": 21, "column": 26, "nodeType": "315", "messageId": "316", "endLine": 21, "endColumn": 43}, {"ruleId": "319", "severity": 1, "message": "320", "line": 27, "column": 6, "nodeType": "321", "endLine": 27, "endColumn": 8, "suggestions": "322"}, {"ruleId": "319", "severity": 1, "message": "323", "line": 27, "column": 6, "nodeType": "321", "endLine": 27, "endColumn": 31, "suggestions": "324"}, {"ruleId": "319", "severity": 1, "message": "325", "line": 17, "column": 6, "nodeType": "321", "endLine": 17, "endColumn": 45, "suggestions": "326"}, {"ruleId": "313", "severity": 1, "message": "327", "line": 3, "column": 35, "nodeType": "315", "messageId": "316", "endLine": 3, "endColumn": 40}, {"ruleId": "319", "severity": 1, "message": "328", "line": 29, "column": 6, "nodeType": "321", "endLine": 29, "endColumn": 8, "suggestions": "329"}, {"ruleId": "313", "severity": 1, "message": "330", "line": 7, "column": 18, "nodeType": "315", "messageId": "316", "endLine": 7, "endColumn": 27}, {"ruleId": "313", "severity": 1, "message": "331", "line": 8, "column": 3, "nodeType": "315", "messageId": "316", "endLine": 8, "endColumn": 14}, "no-duplicate-case", "Duplicate case label.", "SwitchCase", "unexpected", "no-fallthrough", "Expected a 'break' statement before 'case'.", "case", "no-unused-vars", "'handleSubmit' is assigned a value but never used.", "Identifier", "unusedVar", "'response' is assigned a value but never used.", "'setRecentActivity' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchDashboardData'. Either include it or remove the dependency array.", "ArrayExpression", ["332"], "React Hook useEffect has a missing dependency: 'fetchProducts'. Either include it or remove the dependency array.", ["333"], "React Hook useEffect has a missing dependency: 'fetchOrders'. Either include it or remove the dependency array.", ["334"], "'FaEye' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchPromotions'. Either include it or remove the dependency array.", ["335"], "'authState' is assigned a value but never used.", "'FaArrowDown' is defined but never used.", {"desc": "336", "fix": "337"}, {"desc": "338", "fix": "339"}, {"desc": "340", "fix": "341"}, {"desc": "342", "fix": "343"}, "Update the dependencies array to be: [fetchDashboardData]", {"range": "344", "text": "345"}, "Update the dependencies array to be: [currentPage, fetchProducts, searchTerm]", {"range": "346", "text": "347"}, "Update the dependencies array to be: [currentPage, statusFilter, searchTerm, fetchOrders]", {"range": "348", "text": "349"}, "Update the dependencies array to be: [fetchPromotions]", {"range": "350", "text": "351"}, [875, 877], "[fetchDashboardData]", [820, 845], "[currentPage, fetchProducts, searchTerm]", [614, 653], "[currentPage, statusFilter, searchTerm, fetchOrders]", [940, 942], "[fetchPromotions]"]