[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\services\\api.js": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\context\\AppContext.js": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\services\\api\\productService.js": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\services\\api\\config.js": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Checkout\\components\\AddressForm.jsx": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Checkout\\components\\PaymentForm.jsx": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Checkout\\components\\OrderSummary.jsx": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\services\\api\\authService.js": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\api\\orderService.js": "12", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\api\\axios.js": "13", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\CartPage.js": "14", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\context\\CartContext.js": "15", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\cart\\CartItem.js": "16", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\cart\\EmptyCart.js": "17", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\cart\\CartSummary.js": "18", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\HomePage.js": "19", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\categories\\CategoryList.js": "20", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\icons\\CategoryIcons.js": "21", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\home\\HeroSection.js": "22", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\home\\PromoSection.js": "23", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\ContactPage.js": "24", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\products\\FeaturedProducts.js": "25", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\products\\ProductCard.js": "26", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\ProductDetailPage.js": "27", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\products\\RelatedProducts.js": "28", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\layouts\\MainLayout.js": "29", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\layouts\\AdminLayout.js": "30", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\layout\\Header.js": "31", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\layout\\Footer.js": "32", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\admin\\AdminSidebar.js": "33", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\admin\\AdminHeader.js": "34", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\auth\\ProtectedRoute.js": "35", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\ProductsPage.js": "36", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\OrderConfirmationPage.js": "37", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\AboutPage.js": "38", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\LoginPage.js": "39", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\RegisterPage.js": "40", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\NotFoundPage.js": "41", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Admin\\Dashboard.js": "42", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Admin\\Categories.js": "43", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Admin\\Orders.js": "44", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\context\\AuthContext.js": "45", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Admin\\Promotions.js": "46", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\services\\api\\promotionService.js": "47", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\cart\\PromoCodeInput.js": "48", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\promotions\\ActivePromotions.js": "49", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Admin\\Users.js": "50", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Checkout\\index.js": "51", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\hooks\\useForm.js": "52", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\services\\api\\dashboardService.js": "53", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\admin\\StatsCard.js": "54", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\admin\\DashboardCharts.js": "55", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\admin\\RecentActivity.js": "56", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\hooks\\useSafeAuth.js": "57", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\context\\DataContext.js": "58", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\services\\crudService.js": "59", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Admin\\ProductsCRUD.js": "60", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Admin\\CategoriesCRUD.js": "61", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\services\\apiService.js": "62", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\CategoryPage.js": "63", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\services\\dashboardService.js": "64", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\StatsCard.js": "65", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\DashboardCharts.js": "66"}, {"size": 326, "mtime": 1747142994132, "results": "67", "hashOfConfig": "68"}, {"size": 3358, "mtime": 1749078563608, "results": "69", "hashOfConfig": "68"}, {"size": 363, "mtime": 1736863717815, "results": "70", "hashOfConfig": "68"}, {"size": 7689, "mtime": 1746560718828, "results": "71", "hashOfConfig": "68"}, {"size": 1521, "mtime": 1746211425729, "results": "72", "hashOfConfig": "68"}, {"size": 802, "mtime": 1745521007503, "results": "73", "hashOfConfig": "68"}, {"size": 885, "mtime": 1746211930285, "results": "74", "hashOfConfig": "68"}, {"size": 6253, "mtime": 1748254133452, "results": "75", "hashOfConfig": "68"}, {"size": 5290, "mtime": 1748254154005, "results": "76", "hashOfConfig": "68"}, {"size": 2005, "mtime": 1747137811947, "results": "77", "hashOfConfig": "68"}, {"size": 1383, "mtime": 1746561639967, "results": "78", "hashOfConfig": "68"}, {"size": 1179, "mtime": 1746211973790, "results": "79", "hashOfConfig": "68"}, {"size": 953, "mtime": 1746211963091, "results": "80", "hashOfConfig": "68"}, {"size": 3154, "mtime": 1747139445897, "results": "81", "hashOfConfig": "68"}, {"size": 3702, "mtime": 1747143163391, "results": "82", "hashOfConfig": "68"}, {"size": 4195, "mtime": 1747139473447, "results": "83", "hashOfConfig": "68"}, {"size": 1037, "mtime": 1747138769044, "results": "84", "hashOfConfig": "68"}, {"size": 3968, "mtime": 1748196287842, "results": "85", "hashOfConfig": "68"}, {"size": 14963, "mtime": 1749079845174, "results": "86", "hashOfConfig": "68"}, {"size": 2413, "mtime": 1747139088166, "results": "87", "hashOfConfig": "68"}, {"size": 4727, "mtime": 1747139080279, "results": "88", "hashOfConfig": "68"}, {"size": 2162, "mtime": 1747139107880, "results": "89", "hashOfConfig": "68"}, {"size": 2280, "mtime": 1747139120251, "results": "90", "hashOfConfig": "68"}, {"size": 7370, "mtime": 1747139301836, "results": "91", "hashOfConfig": "68"}, {"size": 1538, "mtime": 1747139308791, "results": "92", "hashOfConfig": "68"}, {"size": 2600, "mtime": 1749077183251, "results": "93", "hashOfConfig": "68"}, {"size": 10006, "mtime": 1747139423441, "results": "94", "hashOfConfig": "68"}, {"size": 1657, "mtime": 1747139427583, "results": "95", "hashOfConfig": "68"}, {"size": 792, "mtime": 1747143709897, "results": "96", "hashOfConfig": "68"}, {"size": 644, "mtime": 1749077517136, "results": "97", "hashOfConfig": "68"}, {"size": 7956, "mtime": 1749076096615, "results": "98", "hashOfConfig": "68"}, {"size": 5341, "mtime": 1747143741468, "results": "99", "hashOfConfig": "68"}, {"size": 2913, "mtime": 1749077589896, "results": "100", "hashOfConfig": "68"}, {"size": 1921, "mtime": 1749075854054, "results": "101", "hashOfConfig": "68"}, {"size": 616, "mtime": 1749076069152, "results": "102", "hashOfConfig": "68"}, {"size": 1465, "mtime": 1747139888784, "results": "103", "hashOfConfig": "68"}, {"size": 3120, "mtime": 1747139902647, "results": "104", "hashOfConfig": "68"}, {"size": 7055, "mtime": 1747140045909, "results": "105", "hashOfConfig": "68"}, {"size": 3707, "mtime": 1747140090497, "results": "106", "hashOfConfig": "68"}, {"size": 4957, "mtime": 1747140281729, "results": "107", "hashOfConfig": "68"}, {"size": 1462, "mtime": 1747140282755, "results": "108", "hashOfConfig": "68"}, {"size": 13829, "mtime": 1749127082381, "results": "109", "hashOfConfig": "68"}, {"size": 15392, "mtime": 1749076529993, "results": "110", "hashOfConfig": "68"}, {"size": 23817, "mtime": 1749077136499, "results": "111", "hashOfConfig": "68"}, {"size": 3591, "mtime": 1747143130292, "results": "112", "hashOfConfig": "68"}, {"size": 23690, "mtime": 1749076732549, "results": "113", "hashOfConfig": "68"}, {"size": 3114, "mtime": 1748195902455, "results": "114", "hashOfConfig": "68"}, {"size": 5284, "mtime": 1748195928560, "results": "115", "hashOfConfig": "68"}, {"size": 7290, "mtime": 1749074732679, "results": "116", "hashOfConfig": "68"}, {"size": 8610, "mtime": 1749076802115, "results": "117", "hashOfConfig": "68"}, {"size": 5168, "mtime": 1747137777119, "results": "118", "hashOfConfig": "68"}, {"size": 5101, "mtime": 1748254114464, "results": "119", "hashOfConfig": "68"}, {"size": 3543, "mtime": 1749075335396, "results": "120", "hashOfConfig": "68"}, {"size": 1200, "mtime": 1748254789320, "results": "121", "hashOfConfig": "68"}, {"size": 4948, "mtime": 1748254818981, "results": "122", "hashOfConfig": "68"}, {"size": 4745, "mtime": 1749077003934, "results": "123", "hashOfConfig": "68"}, {"size": 967, "mtime": 1749076040176, "results": "124", "hashOfConfig": "68"}, {"size": 10464, "mtime": 1749078875974, "results": "125", "hashOfConfig": "68"}, {"size": 6583, "mtime": 1749077850886, "results": "126", "hashOfConfig": "68"}, {"size": 20315, "mtime": 1749126797860, "results": "127", "hashOfConfig": "68"}, {"size": 12813, "mtime": 1749078989384, "results": "128", "hashOfConfig": "68"}, {"size": 9479, "mtime": 1749126239757, "results": "129", "hashOfConfig": "68"}, {"size": 6169, "mtime": 1749079485577, "results": "130", "hashOfConfig": "68"}, {"size": 7619, "mtime": 1749125610004, "results": "131", "hashOfConfig": "68"}, {"size": 978, "mtime": 1749126740353, "results": "132", "hashOfConfig": "68"}, {"size": 1823, "mtime": 1749126785854, "results": "133", "hashOfConfig": "68"}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1rke8h4", {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\services\\api.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\context\\AppContext.js", ["332", "333", "334"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\services\\api\\productService.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\services\\api\\config.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Checkout\\components\\AddressForm.jsx", ["335"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Checkout\\components\\PaymentForm.jsx", ["336"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Checkout\\components\\OrderSummary.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\services\\api\\authService.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\api\\orderService.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\api\\axios.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\CartPage.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\context\\CartContext.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\cart\\CartItem.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\cart\\EmptyCart.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\cart\\CartSummary.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\HomePage.js", ["337", "338", "339"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\categories\\CategoryList.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\icons\\CategoryIcons.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\home\\HeroSection.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\home\\PromoSection.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\ContactPage.js", ["340"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\products\\FeaturedProducts.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\products\\ProductCard.js", ["341"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\ProductDetailPage.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\products\\RelatedProducts.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\layouts\\MainLayout.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\layouts\\AdminLayout.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\layout\\Header.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\layout\\Footer.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\admin\\AdminSidebar.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\admin\\AdminHeader.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\auth\\ProtectedRoute.js", ["342", "343", "344"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\ProductsPage.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\OrderConfirmationPage.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\AboutPage.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\LoginPage.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\RegisterPage.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\NotFoundPage.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Admin\\Dashboard.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Admin\\Categories.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Admin\\Orders.js", ["345"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\context\\AuthContext.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Admin\\Promotions.js", ["346", "347"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\services\\api\\promotionService.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\cart\\PromoCodeInput.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\promotions\\ActivePromotions.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Admin\\Users.js", ["348"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Checkout\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\hooks\\useForm.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\services\\api\\dashboardService.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\admin\\StatsCard.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\admin\\DashboardCharts.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\admin\\RecentActivity.js", ["349"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\hooks\\useSafeAuth.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\context\\DataContext.js", ["350"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\services\\crudService.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Admin\\ProductsCRUD.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Admin\\CategoriesCRUD.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\services\\apiService.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\CategoryPage.js", ["351"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\services\\dashboardService.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\StatsCard.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\DashboardCharts.js", [], [], {"ruleId": "352", "severity": 1, "message": "353", "line": 35, "column": 1, "nodeType": "354", "messageId": "355", "endLine": 35, "endColumn": 20}, {"ruleId": "352", "severity": 1, "message": "353", "line": 37, "column": 1, "nodeType": "354", "messageId": "355", "endLine": 38, "endColumn": 38}, {"ruleId": "356", "severity": 1, "message": "357", "line": 37, "column": 1, "nodeType": "354", "messageId": "358", "endLine": 38, "endColumn": 38}, {"ruleId": "359", "severity": 1, "message": "360", "line": 10, "column": 41, "nodeType": "361", "messageId": "362", "endLine": 10, "endColumn": 53}, {"ruleId": "359", "severity": 1, "message": "360", "line": 8, "column": 41, "nodeType": "361", "messageId": "362", "endLine": 8, "endColumn": 53}, {"ruleId": "359", "severity": 1, "message": "363", "line": 11, "column": 46, "nodeType": "361", "messageId": "362", "endLine": 11, "endColumn": 59}, {"ruleId": "359", "severity": 1, "message": "364", "line": 11, "column": 70, "nodeType": "361", "messageId": "362", "endLine": 11, "endColumn": 81}, {"ruleId": "359", "severity": 1, "message": "365", "line": 16, "column": 10, "nodeType": "361", "messageId": "362", "endLine": 16, "endColumn": 25}, {"ruleId": "359", "severity": 1, "message": "366", "line": 33, "column": 13, "nodeType": "361", "messageId": "362", "endLine": 33, "endColumn": 21}, {"ruleId": "359", "severity": 1, "message": "367", "line": 8, "column": 5, "nodeType": "361", "messageId": "362", "endLine": 8, "endColumn": 7}, {"ruleId": "359", "severity": 1, "message": "368", "line": 1, "column": 8, "nodeType": "361", "messageId": "362", "endLine": 1, "endColumn": 13}, {"ruleId": "359", "severity": 1, "message": "369", "line": 2, "column": 10, "nodeType": "361", "messageId": "362", "endLine": 2, "endColumn": 18}, {"ruleId": "359", "severity": 1, "message": "370", "line": 6, "column": 11, "nodeType": "361", "messageId": "362", "endLine": 6, "endColumn": 16}, {"ruleId": "371", "severity": 1, "message": "372", "line": 17, "column": 6, "nodeType": "373", "endLine": 17, "endColumn": 45, "suggestions": "374"}, {"ruleId": "359", "severity": 1, "message": "375", "line": 3, "column": 35, "nodeType": "361", "messageId": "362", "endLine": 3, "endColumn": 40}, {"ruleId": "371", "severity": 1, "message": "376", "line": 29, "column": 6, "nodeType": "373", "endLine": 29, "endColumn": 8, "suggestions": "377"}, {"ruleId": "359", "severity": 1, "message": "378", "line": 7, "column": 18, "nodeType": "361", "messageId": "362", "endLine": 7, "endColumn": 27}, {"ruleId": "359", "severity": 1, "message": "379", "line": 8, "column": 3, "nodeType": "361", "messageId": "362", "endLine": 8, "endColumn": 14}, {"ruleId": "371", "severity": 1, "message": "380", "line": 263, "column": 6, "nodeType": "373", "endLine": 263, "endColumn": 8, "suggestions": "381"}, {"ruleId": "371", "severity": 1, "message": "382", "line": 19, "column": 6, "nodeType": "373", "endLine": 19, "endColumn": 34, "suggestions": "383"}, "no-duplicate-case", "Duplicate case label.", "SwitchCase", "unexpected", "no-fallthrough", "Expected a 'break' statement before 'case'.", "case", "no-unused-vars", "'handleSubmit' is assigned a value but never used.", "Identifier", "unusedVar", "'allCategories' is assigned a value but never used.", "'dataLoading' is assigned a value but never used.", "'isUsingTestData' is assigned a value but never used.", "'response' is assigned a value but never used.", "'id' is assigned a value but never used.", "'React' is defined but never used.", "'Navigate' is defined but never used.", "'state' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchOrders'. Either include it or remove the dependency array.", "ArrayExpression", ["384"], "'FaEye' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchPromotions'. Either include it or remove the dependency array.", ["385"], "'authState' is assigned a value but never used.", "'FaArrowDown' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadDataFromAPI'. Either include it or remove the dependency array.", ["386"], "React Hook useEffect has a missing dependency: 'loadCategoryAndProducts'. Either include it or remove the dependency array.", ["387"], {"desc": "388", "fix": "389"}, {"desc": "390", "fix": "391"}, {"desc": "392", "fix": "393"}, {"desc": "394", "fix": "395"}, "Update the dependencies array to be: [currentPage, statusFilter, searchTerm, fetchOrders]", {"range": "396", "text": "397"}, "Update the dependencies array to be: [fetchPromotions]", {"range": "398", "text": "399"}, "Update the dependencies array to be: [loadDataFromAPI]", {"range": "400", "text": "401"}, "Update the dependencies array to be: [slug, categories, products, loadCategoryAndProducts]", {"range": "402", "text": "403"}, [614, 653], "[currentPage, statusFilter, searchTerm, fetchOrders]", [942, 944], "[fetchPromotions]", [10056, 10058], "[loadDataFromAPI]", [674, 702], "[slug, categories, products, loadCategoryAndProducts]"]