[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\services\\api.js": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\context\\AppContext.js": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\services\\api\\productService.js": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\services\\api\\config.js": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Checkout\\components\\AddressForm.jsx": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Checkout\\components\\PaymentForm.jsx": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Checkout\\components\\OrderSummary.jsx": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\services\\api\\authService.js": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\api\\orderService.js": "12", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\api\\axios.js": "13", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\CartPage.js": "14", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\context\\CartContext.js": "15", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\cart\\CartItem.js": "16", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\cart\\EmptyCart.js": "17", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\cart\\CartSummary.js": "18", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\HomePage.js": "19", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\categories\\CategoryList.js": "20", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\icons\\CategoryIcons.js": "21", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\home\\HeroSection.js": "22", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\home\\PromoSection.js": "23", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\ContactPage.js": "24", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\products\\FeaturedProducts.js": "25", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\products\\ProductCard.js": "26", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\ProductDetailPage.js": "27", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\products\\RelatedProducts.js": "28", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\layouts\\MainLayout.js": "29", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\layouts\\AdminLayout.js": "30", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\layout\\Header.js": "31", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\layout\\Footer.js": "32", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\admin\\AdminSidebar.js": "33", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\admin\\AdminHeader.js": "34", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\auth\\ProtectedRoute.js": "35", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\ProductsPage.js": "36", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\CategoryPage.js": "37", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\OrderConfirmationPage.js": "38", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\AboutPage.js": "39", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\LoginPage.js": "40", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\RegisterPage.js": "41", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\NotFoundPage.js": "42", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Admin\\Dashboard.js": "43", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Admin\\Products.js": "44", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Admin\\Categories.js": "45", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Admin\\Orders.js": "46", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\context\\AuthContext.js": "47", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Admin\\Promotions.js": "48", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\services\\api\\promotionService.js": "49", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\cart\\PromoCodeInput.js": "50", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\promotions\\ActivePromotions.js": "51", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Admin\\Users.js": "52", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Checkout\\index.js": "53", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\hooks\\useForm.js": "54", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\services\\api\\dashboardService.js": "55", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\admin\\StatsCard.js": "56", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\admin\\DashboardCharts.js": "57", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\admin\\RecentActivity.js": "58", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\hooks\\useSafeAuth.js": "59"}, {"size": 326, "mtime": 1747142994132, "results": "60", "hashOfConfig": "61"}, {"size": 3245, "mtime": 1749075579951, "results": "62", "hashOfConfig": "61"}, {"size": 363, "mtime": 1736863717815, "results": "63", "hashOfConfig": "61"}, {"size": 7689, "mtime": 1746560718828, "results": "64", "hashOfConfig": "61"}, {"size": 1521, "mtime": 1746211425729, "results": "65", "hashOfConfig": "61"}, {"size": 802, "mtime": 1745521007503, "results": "66", "hashOfConfig": "61"}, {"size": 885, "mtime": 1746211930285, "results": "67", "hashOfConfig": "61"}, {"size": 6253, "mtime": 1748254133452, "results": "68", "hashOfConfig": "61"}, {"size": 5290, "mtime": 1748254154005, "results": "69", "hashOfConfig": "61"}, {"size": 2005, "mtime": 1747137811947, "results": "70", "hashOfConfig": "61"}, {"size": 1383, "mtime": 1746561639967, "results": "71", "hashOfConfig": "61"}, {"size": 1179, "mtime": 1746211973790, "results": "72", "hashOfConfig": "61"}, {"size": 953, "mtime": 1746211963091, "results": "73", "hashOfConfig": "61"}, {"size": 3154, "mtime": 1747139445897, "results": "74", "hashOfConfig": "61"}, {"size": 3702, "mtime": 1747143163391, "results": "75", "hashOfConfig": "61"}, {"size": 4195, "mtime": 1747139473447, "results": "76", "hashOfConfig": "61"}, {"size": 1037, "mtime": 1747138769044, "results": "77", "hashOfConfig": "61"}, {"size": 3968, "mtime": 1748196287842, "results": "78", "hashOfConfig": "61"}, {"size": 14828, "mtime": 1749075640651, "results": "79", "hashOfConfig": "61"}, {"size": 2413, "mtime": 1747139088166, "results": "80", "hashOfConfig": "61"}, {"size": 4727, "mtime": 1747139080279, "results": "81", "hashOfConfig": "61"}, {"size": 2162, "mtime": 1747139107880, "results": "82", "hashOfConfig": "61"}, {"size": 2280, "mtime": 1747139120251, "results": "83", "hashOfConfig": "61"}, {"size": 7370, "mtime": 1747139301836, "results": "84", "hashOfConfig": "61"}, {"size": 1538, "mtime": 1747139308791, "results": "85", "hashOfConfig": "61"}, {"size": 2603, "mtime": 1747174196644, "results": "86", "hashOfConfig": "61"}, {"size": 10006, "mtime": 1747139423441, "results": "87", "hashOfConfig": "61"}, {"size": 1657, "mtime": 1747139427583, "results": "88", "hashOfConfig": "61"}, {"size": 792, "mtime": 1747143709897, "results": "89", "hashOfConfig": "61"}, {"size": 567, "mtime": 1747139661661, "results": "90", "hashOfConfig": "61"}, {"size": 7956, "mtime": 1749076096615, "results": "91", "hashOfConfig": "61"}, {"size": 5341, "mtime": 1747143741468, "results": "92", "hashOfConfig": "61"}, {"size": 2893, "mtime": 1749075537781, "results": "93", "hashOfConfig": "61"}, {"size": 1921, "mtime": 1749075854054, "results": "94", "hashOfConfig": "61"}, {"size": 616, "mtime": 1749076069152, "results": "95", "hashOfConfig": "61"}, {"size": 1465, "mtime": 1747139888784, "results": "96", "hashOfConfig": "61"}, {"size": 2135, "mtime": 1747139897465, "results": "97", "hashOfConfig": "61"}, {"size": 3120, "mtime": 1747139902647, "results": "98", "hashOfConfig": "61"}, {"size": 7055, "mtime": 1747140045909, "results": "99", "hashOfConfig": "61"}, {"size": 3707, "mtime": 1747140090497, "results": "100", "hashOfConfig": "61"}, {"size": 4957, "mtime": 1747140281729, "results": "101", "hashOfConfig": "61"}, {"size": 1462, "mtime": 1747140282755, "results": "102", "hashOfConfig": "61"}, {"size": 8016, "mtime": 1749076022319, "results": "103", "hashOfConfig": "61"}, {"size": 21290, "mtime": 1749076495208, "results": "104", "hashOfConfig": "61"}, {"size": 15392, "mtime": 1749076529993, "results": "105", "hashOfConfig": "61"}, {"size": 23824, "mtime": 1749076572511, "results": "106", "hashOfConfig": "61"}, {"size": 3591, "mtime": 1747143130292, "results": "107", "hashOfConfig": "61"}, {"size": 23690, "mtime": 1749076732549, "results": "108", "hashOfConfig": "61"}, {"size": 3114, "mtime": 1748195902455, "results": "109", "hashOfConfig": "61"}, {"size": 5284, "mtime": 1748195928560, "results": "110", "hashOfConfig": "61"}, {"size": 7290, "mtime": 1749074732679, "results": "111", "hashOfConfig": "61"}, {"size": 8610, "mtime": 1749076802115, "results": "112", "hashOfConfig": "61"}, {"size": 5168, "mtime": 1747137777119, "results": "113", "hashOfConfig": "61"}, {"size": 5101, "mtime": 1748254114464, "results": "114", "hashOfConfig": "61"}, {"size": 3543, "mtime": 1749075335396, "results": "115", "hashOfConfig": "61"}, {"size": 1200, "mtime": 1748254789320, "results": "116", "hashOfConfig": "61"}, {"size": 4948, "mtime": 1748254818981, "results": "117", "hashOfConfig": "61"}, {"size": 4745, "mtime": 1748254847572, "results": "118", "hashOfConfig": "61"}, {"size": 967, "mtime": 1749076040176, "results": "119", "hashOfConfig": "61"}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1rke8h4", {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\services\\api.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\context\\AppContext.js", ["297", "298", "299"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\services\\api\\productService.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\services\\api\\config.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Checkout\\components\\AddressForm.jsx", ["300"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Checkout\\components\\PaymentForm.jsx", ["301"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Checkout\\components\\OrderSummary.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\services\\api\\authService.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\api\\orderService.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\api\\axios.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\CartPage.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\context\\CartContext.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\cart\\CartItem.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\cart\\EmptyCart.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\cart\\CartSummary.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\HomePage.js", ["302"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\categories\\CategoryList.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\icons\\CategoryIcons.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\home\\HeroSection.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\home\\PromoSection.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\ContactPage.js", ["303"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\products\\FeaturedProducts.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\products\\ProductCard.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\ProductDetailPage.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\products\\RelatedProducts.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\layouts\\MainLayout.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\layouts\\AdminLayout.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\layout\\Header.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\layout\\Footer.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\admin\\AdminSidebar.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\admin\\AdminHeader.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\auth\\ProtectedRoute.js", ["304", "305", "306"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\ProductsPage.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\CategoryPage.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\OrderConfirmationPage.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\AboutPage.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\LoginPage.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\RegisterPage.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\NotFoundPage.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Admin\\Dashboard.js", ["307", "308"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Admin\\Products.js", ["309"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Admin\\Categories.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Admin\\Orders.js", ["310"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\context\\AuthContext.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Admin\\Promotions.js", ["311", "312"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\services\\api\\promotionService.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\cart\\PromoCodeInput.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\promotions\\ActivePromotions.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Admin\\Users.js", ["313"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Checkout\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\hooks\\useForm.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\services\\api\\dashboardService.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\admin\\StatsCard.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\admin\\DashboardCharts.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\admin\\RecentActivity.js", ["314"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\hooks\\useSafeAuth.js", [], [], {"ruleId": "315", "severity": 1, "message": "316", "line": 35, "column": 1, "nodeType": "317", "messageId": "318", "endLine": 35, "endColumn": 20}, {"ruleId": "315", "severity": 1, "message": "316", "line": 37, "column": 1, "nodeType": "317", "messageId": "318", "endLine": 38, "endColumn": 38}, {"ruleId": "319", "severity": 1, "message": "320", "line": 37, "column": 1, "nodeType": "317", "messageId": "321", "endLine": 38, "endColumn": 38}, {"ruleId": "322", "severity": 1, "message": "323", "line": 10, "column": 41, "nodeType": "324", "messageId": "325", "endLine": 10, "endColumn": 53}, {"ruleId": "322", "severity": 1, "message": "323", "line": 8, "column": 41, "nodeType": "324", "messageId": "325", "endLine": 8, "endColumn": 53}, {"ruleId": "322", "severity": 1, "message": "326", "line": 13, "column": 10, "nodeType": "324", "messageId": "325", "endLine": 13, "endColumn": 25}, {"ruleId": "322", "severity": 1, "message": "327", "line": 33, "column": 13, "nodeType": "324", "messageId": "325", "endLine": 33, "endColumn": 21}, {"ruleId": "322", "severity": 1, "message": "328", "line": 1, "column": 8, "nodeType": "324", "messageId": "325", "endLine": 1, "endColumn": 13}, {"ruleId": "322", "severity": 1, "message": "329", "line": 2, "column": 10, "nodeType": "324", "messageId": "325", "endLine": 2, "endColumn": 18}, {"ruleId": "322", "severity": 1, "message": "330", "line": 6, "column": 11, "nodeType": "324", "messageId": "325", "endLine": 6, "endColumn": 16}, {"ruleId": "322", "severity": 1, "message": "331", "line": 11, "column": 10, "nodeType": "324", "messageId": "325", "endLine": 11, "endColumn": 26}, {"ruleId": "322", "severity": 1, "message": "332", "line": 20, "column": 26, "nodeType": "324", "messageId": "325", "endLine": 20, "endColumn": 43}, {"ruleId": "333", "severity": 1, "message": "334", "line": 27, "column": 6, "nodeType": "335", "endLine": 27, "endColumn": 31, "suggestions": "336"}, {"ruleId": "333", "severity": 1, "message": "337", "line": 17, "column": 6, "nodeType": "335", "endLine": 17, "endColumn": 45, "suggestions": "338"}, {"ruleId": "322", "severity": 1, "message": "339", "line": 3, "column": 35, "nodeType": "324", "messageId": "325", "endLine": 3, "endColumn": 40}, {"ruleId": "333", "severity": 1, "message": "340", "line": 29, "column": 6, "nodeType": "335", "endLine": 29, "endColumn": 8, "suggestions": "341"}, {"ruleId": "322", "severity": 1, "message": "342", "line": 7, "column": 18, "nodeType": "324", "messageId": "325", "endLine": 7, "endColumn": 27}, {"ruleId": "322", "severity": 1, "message": "343", "line": 8, "column": 3, "nodeType": "324", "messageId": "325", "endLine": 8, "endColumn": 14}, "no-duplicate-case", "Duplicate case label.", "SwitchCase", "unexpected", "no-fallthrough", "Expected a 'break' statement before 'case'.", "case", "no-unused-vars", "'handleSubmit' is assigned a value but never used.", "Identifier", "unusedVar", "'isUsingTestData' is assigned a value but never used.", "'response' is assigned a value but never used.", "'React' is defined but never used.", "'Navigate' is defined but never used.", "'state' is assigned a value but never used.", "'dashboardService' is defined but never used.", "'setRecentActivity' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchProducts'. Either include it or remove the dependency array.", "ArrayExpression", ["344"], "React Hook useEffect has a missing dependency: 'fetchOrders'. Either include it or remove the dependency array.", ["345"], "'FaEye' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchPromotions'. Either include it or remove the dependency array.", ["346"], "'authState' is assigned a value but never used.", "'FaArrowDown' is defined but never used.", {"desc": "347", "fix": "348"}, {"desc": "349", "fix": "350"}, {"desc": "351", "fix": "352"}, "Update the dependencies array to be: [currentPage, fetchProducts, searchTerm]", {"range": "353", "text": "354"}, "Update the dependencies array to be: [currentPage, statusFilter, searchTerm, fetchOrders]", {"range": "355", "text": "356"}, "Update the dependencies array to be: [fetchPromotions]", {"range": "357", "text": "358"}, [820, 845], "[currentPage, fetchProducts, searchTerm]", [614, 653], "[currentPage, statusFilter, searchTerm, fetchOrders]", [942, 944], "[fetchPromotions]"]