[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\services\\api.js": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\context\\AppContext.js": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\services\\api\\productService.js": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\services\\api\\config.js": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Checkout\\components\\AddressForm.jsx": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Checkout\\components\\PaymentForm.jsx": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Checkout\\components\\OrderSummary.jsx": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\services\\api\\authService.js": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\api\\orderService.js": "12", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\api\\axios.js": "13", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\CartPage.js": "14", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\context\\CartContext.js": "15", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\cart\\CartItem.js": "16", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\cart\\EmptyCart.js": "17", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\cart\\CartSummary.js": "18", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\HomePage.js": "19", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\categories\\CategoryList.js": "20", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\icons\\CategoryIcons.js": "21", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\home\\HeroSection.js": "22", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\home\\PromoSection.js": "23", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\ContactPage.js": "24", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\products\\FeaturedProducts.js": "25", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\products\\ProductCard.js": "26", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\ProductDetailPage.js": "27", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\products\\RelatedProducts.js": "28", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\layouts\\MainLayout.js": "29", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\layouts\\AdminLayout.js": "30", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\layout\\Header.js": "31", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\layout\\Footer.js": "32", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\admin\\AdminSidebar.js": "33", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\admin\\AdminHeader.js": "34", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\auth\\ProtectedRoute.js": "35", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\ProductsPage.js": "36", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\OrderConfirmationPage.js": "37", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\AboutPage.js": "38", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\LoginPage.js": "39", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\RegisterPage.js": "40", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\NotFoundPage.js": "41", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Admin\\Dashboard.js": "42", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Admin\\Categories.js": "43", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Admin\\Orders.js": "44", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\context\\AuthContext.js": "45", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Admin\\Promotions.js": "46", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\services\\api\\promotionService.js": "47", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\cart\\PromoCodeInput.js": "48", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\promotions\\ActivePromotions.js": "49", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Admin\\Users.js": "50", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Checkout\\index.js": "51", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\hooks\\useForm.js": "52", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\services\\api\\dashboardService.js": "53", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\admin\\StatsCard.js": "54", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\admin\\DashboardCharts.js": "55", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\admin\\RecentActivity.js": "56", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\hooks\\useSafeAuth.js": "57", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\context\\DataContext.js": "58", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\services\\crudService.js": "59", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Admin\\ProductsCRUD.js": "60", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Admin\\CategoriesCRUD.js": "61", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\services\\apiService.js": "62", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\CategoryPage.js": "63", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\services\\dashboardService.js": "64", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\StatsCard.js": "65", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\DashboardCharts.js": "66", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\EmptyStateMessage.js": "67"}, {"size": 326, "mtime": 1747142994132, "results": "68", "hashOfConfig": "69"}, {"size": 3838, "mtime": 1749130770265, "results": "70", "hashOfConfig": "69"}, {"size": 363, "mtime": 1736863717815, "results": "71", "hashOfConfig": "69"}, {"size": 7689, "mtime": 1746560718828, "results": "72", "hashOfConfig": "69"}, {"size": 1521, "mtime": 1746211425729, "results": "73", "hashOfConfig": "69"}, {"size": 802, "mtime": 1745521007503, "results": "74", "hashOfConfig": "69"}, {"size": 885, "mtime": 1746211930285, "results": "75", "hashOfConfig": "69"}, {"size": 6253, "mtime": 1748254133452, "results": "76", "hashOfConfig": "69"}, {"size": 5290, "mtime": 1748254154005, "results": "77", "hashOfConfig": "69"}, {"size": 2005, "mtime": 1747137811947, "results": "78", "hashOfConfig": "69"}, {"size": 1383, "mtime": 1746561639967, "results": "79", "hashOfConfig": "69"}, {"size": 1179, "mtime": 1746211973790, "results": "80", "hashOfConfig": "69"}, {"size": 953, "mtime": 1746211963091, "results": "81", "hashOfConfig": "69"}, {"size": 3154, "mtime": 1747139445897, "results": "82", "hashOfConfig": "69"}, {"size": 3702, "mtime": 1747143163391, "results": "83", "hashOfConfig": "69"}, {"size": 4195, "mtime": 1747139473447, "results": "84", "hashOfConfig": "69"}, {"size": 1037, "mtime": 1747138769044, "results": "85", "hashOfConfig": "69"}, {"size": 3968, "mtime": 1748196287842, "results": "86", "hashOfConfig": "69"}, {"size": 13163, "mtime": 1749131111265, "results": "87", "hashOfConfig": "69"}, {"size": 2413, "mtime": 1747139088166, "results": "88", "hashOfConfig": "69"}, {"size": 4727, "mtime": 1747139080279, "results": "89", "hashOfConfig": "69"}, {"size": 2162, "mtime": 1747139107880, "results": "90", "hashOfConfig": "69"}, {"size": 2280, "mtime": 1747139120251, "results": "91", "hashOfConfig": "69"}, {"size": 7370, "mtime": 1747139301836, "results": "92", "hashOfConfig": "69"}, {"size": 1538, "mtime": 1747139308791, "results": "93", "hashOfConfig": "69"}, {"size": 2455, "mtime": 1749130245015, "results": "94", "hashOfConfig": "69"}, {"size": 10006, "mtime": 1747139423441, "results": "95", "hashOfConfig": "69"}, {"size": 1657, "mtime": 1747139427583, "results": "96", "hashOfConfig": "69"}, {"size": 792, "mtime": 1747143709897, "results": "97", "hashOfConfig": "69"}, {"size": 644, "mtime": 1749077517136, "results": "98", "hashOfConfig": "69"}, {"size": 7956, "mtime": 1749076096615, "results": "99", "hashOfConfig": "69"}, {"size": 5341, "mtime": 1747143741468, "results": "100", "hashOfConfig": "69"}, {"size": 2913, "mtime": 1749077589896, "results": "101", "hashOfConfig": "69"}, {"size": 1921, "mtime": 1749075854054, "results": "102", "hashOfConfig": "69"}, {"size": 616, "mtime": 1749076069152, "results": "103", "hashOfConfig": "69"}, {"size": 7495, "mtime": 1749130948959, "results": "104", "hashOfConfig": "69"}, {"size": 3120, "mtime": 1747139902647, "results": "105", "hashOfConfig": "69"}, {"size": 7055, "mtime": 1747140045909, "results": "106", "hashOfConfig": "69"}, {"size": 3707, "mtime": 1747140090497, "results": "107", "hashOfConfig": "69"}, {"size": 4957, "mtime": 1747140281729, "results": "108", "hashOfConfig": "69"}, {"size": 1462, "mtime": 1747140282755, "results": "109", "hashOfConfig": "69"}, {"size": 15339, "mtime": 1749127973184, "results": "110", "hashOfConfig": "69"}, {"size": 15392, "mtime": 1749076529993, "results": "111", "hashOfConfig": "69"}, {"size": 23817, "mtime": 1749077136499, "results": "112", "hashOfConfig": "69"}, {"size": 3591, "mtime": 1747143130292, "results": "113", "hashOfConfig": "69"}, {"size": 23690, "mtime": 1749076732549, "results": "114", "hashOfConfig": "69"}, {"size": 3114, "mtime": 1748195902455, "results": "115", "hashOfConfig": "69"}, {"size": 5284, "mtime": 1748195928560, "results": "116", "hashOfConfig": "69"}, {"size": 7290, "mtime": 1749074732679, "results": "117", "hashOfConfig": "69"}, {"size": 8610, "mtime": 1749076802115, "results": "118", "hashOfConfig": "69"}, {"size": 5168, "mtime": 1747137777119, "results": "119", "hashOfConfig": "69"}, {"size": 5101, "mtime": 1748254114464, "results": "120", "hashOfConfig": "69"}, {"size": 3543, "mtime": 1749075335396, "results": "121", "hashOfConfig": "69"}, {"size": 1200, "mtime": 1748254789320, "results": "122", "hashOfConfig": "69"}, {"size": 4948, "mtime": 1748254818981, "results": "123", "hashOfConfig": "69"}, {"size": 4745, "mtime": 1749077003934, "results": "124", "hashOfConfig": "69"}, {"size": 967, "mtime": 1749076040176, "results": "125", "hashOfConfig": "69"}, {"size": 7593, "mtime": 1749131014540, "results": "126", "hashOfConfig": "69"}, {"size": 6583, "mtime": 1749077850886, "results": "127", "hashOfConfig": "69"}, {"size": 20315, "mtime": 1749126797860, "results": "128", "hashOfConfig": "69"}, {"size": 12813, "mtime": 1749078989384, "results": "129", "hashOfConfig": "69"}, {"size": 9479, "mtime": 1749126239757, "results": "130", "hashOfConfig": "69"}, {"size": 6650, "mtime": 1749130815109, "results": "131", "hashOfConfig": "69"}, {"size": 4958, "mtime": 1749127580615, "results": "132", "hashOfConfig": "69"}, {"size": 978, "mtime": 1749126740353, "results": "133", "hashOfConfig": "69"}, {"size": 1823, "mtime": 1749126785854, "results": "134", "hashOfConfig": "69"}, {"size": 1445, "mtime": 1749131074835, "results": "135", "hashOfConfig": "69"}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1rke8h4", {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\services\\api.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\context\\AppContext.js", ["337", "338", "339"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\services\\api\\productService.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\services\\api\\config.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Checkout\\components\\AddressForm.jsx", ["340"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Checkout\\components\\PaymentForm.jsx", ["341"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Checkout\\components\\OrderSummary.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\services\\api\\authService.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\api\\orderService.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\api\\axios.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\CartPage.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\context\\CartContext.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\cart\\CartItem.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\cart\\EmptyCart.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\cart\\CartSummary.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\HomePage.js", ["342", "343", "344"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\categories\\CategoryList.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\icons\\CategoryIcons.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\home\\HeroSection.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\home\\PromoSection.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\ContactPage.js", ["345"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\products\\FeaturedProducts.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\products\\ProductCard.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\ProductDetailPage.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\products\\RelatedProducts.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\layouts\\MainLayout.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\layouts\\AdminLayout.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\layout\\Header.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\layout\\Footer.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\admin\\AdminSidebar.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\admin\\AdminHeader.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\auth\\ProtectedRoute.js", ["346", "347", "348"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\ProductsPage.js", ["349", "350"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\OrderConfirmationPage.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\AboutPage.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\LoginPage.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\RegisterPage.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\NotFoundPage.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Admin\\Dashboard.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Admin\\Categories.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Admin\\Orders.js", ["351"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\context\\AuthContext.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Admin\\Promotions.js", ["352", "353"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\services\\api\\promotionService.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\cart\\PromoCodeInput.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\promotions\\ActivePromotions.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Admin\\Users.js", ["354"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Checkout\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\hooks\\useForm.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\services\\api\\dashboardService.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\admin\\StatsCard.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\admin\\DashboardCharts.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\admin\\RecentActivity.js", ["355"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\hooks\\useSafeAuth.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\context\\DataContext.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\services\\crudService.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Admin\\ProductsCRUD.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\Admin\\CategoriesCRUD.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\services\\apiService.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\pages\\CategoryPage.js", ["356"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\services\\dashboardService.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\StatsCard.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\DashboardCharts.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projet-Synthese\\frontend\\src\\components\\EmptyStateMessage.js", [], [], {"ruleId": "357", "severity": 1, "message": "358", "line": 35, "column": 1, "nodeType": "359", "messageId": "360", "endLine": 35, "endColumn": 20}, {"ruleId": "357", "severity": 1, "message": "358", "line": 37, "column": 1, "nodeType": "359", "messageId": "360", "endLine": 38, "endColumn": 38}, {"ruleId": "361", "severity": 1, "message": "362", "line": 37, "column": 1, "nodeType": "359", "messageId": "363", "endLine": 38, "endColumn": 38}, {"ruleId": "364", "severity": 1, "message": "365", "line": 10, "column": 41, "nodeType": "366", "messageId": "367", "endLine": 10, "endColumn": 53}, {"ruleId": "364", "severity": 1, "message": "365", "line": 8, "column": 41, "nodeType": "366", "messageId": "367", "endLine": 8, "endColumn": 53}, {"ruleId": "364", "severity": 1, "message": "368", "line": 12, "column": 46, "nodeType": "366", "messageId": "367", "endLine": 12, "endColumn": 59}, {"ruleId": "364", "severity": 1, "message": "369", "line": 12, "column": 70, "nodeType": "366", "messageId": "367", "endLine": 12, "endColumn": 81}, {"ruleId": "364", "severity": 1, "message": "370", "line": 17, "column": 10, "nodeType": "366", "messageId": "367", "endLine": 17, "endColumn": 25}, {"ruleId": "364", "severity": 1, "message": "371", "line": 33, "column": 13, "nodeType": "366", "messageId": "367", "endLine": 33, "endColumn": 21}, {"ruleId": "364", "severity": 1, "message": "372", "line": 1, "column": 8, "nodeType": "366", "messageId": "367", "endLine": 1, "endColumn": 13}, {"ruleId": "364", "severity": 1, "message": "373", "line": 2, "column": 10, "nodeType": "366", "messageId": "367", "endLine": 2, "endColumn": 18}, {"ruleId": "364", "severity": 1, "message": "374", "line": 6, "column": 11, "nodeType": "366", "messageId": "367", "endLine": 6, "endColumn": 16}, {"ruleId": "375", "severity": 1, "message": "376", "line": 18, "column": 6, "nodeType": "377", "endLine": 18, "endColumn": 41, "suggestions": "378"}, {"ruleId": "379", "severity": 1, "message": "380", "line": 30, "column": 72, "nodeType": "381", "messageId": "360", "endLine": 30, "endColumn": 74}, {"ruleId": "375", "severity": 1, "message": "382", "line": 17, "column": 6, "nodeType": "377", "endLine": 17, "endColumn": 45, "suggestions": "383"}, {"ruleId": "364", "severity": 1, "message": "384", "line": 3, "column": 35, "nodeType": "366", "messageId": "367", "endLine": 3, "endColumn": 40}, {"ruleId": "375", "severity": 1, "message": "385", "line": 29, "column": 6, "nodeType": "377", "endLine": 29, "endColumn": 8, "suggestions": "386"}, {"ruleId": "364", "severity": 1, "message": "387", "line": 7, "column": 18, "nodeType": "366", "messageId": "367", "endLine": 7, "endColumn": 27}, {"ruleId": "364", "severity": 1, "message": "388", "line": 8, "column": 3, "nodeType": "366", "messageId": "367", "endLine": 8, "endColumn": 14}, {"ruleId": "375", "severity": 1, "message": "389", "line": 23, "column": 6, "nodeType": "377", "endLine": 23, "endColumn": 42, "suggestions": "390"}, "no-duplicate-case", "Duplicate case label.", "SwitchCase", "unexpected", "no-fallthrough", "Expected a 'break' statement before 'case'.", "case", "no-unused-vars", "'handleSubmit' is assigned a value but never used.", "Identifier", "unusedVar", "'allCategories' is assigned a value but never used.", "'dataLoading' is assigned a value but never used.", "'isUsingTestData' is assigned a value but never used.", "'response' is assigned a value but never used.", "'React' is defined but never used.", "'Navigate' is defined but never used.", "'state' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchProducts'. Either include it or remove the dependency array.", "ArrayExpression", ["391"], "eqeqeq", "Expected '===' and instead saw '=='.", "BinaryExpression", "React Hook useEffect has a missing dependency: 'fetchOrders'. Either include it or remove the dependency array.", ["392"], "'FaEye' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchPromotions'. Either include it or remove the dependency array.", ["393"], "'authState' is assigned a value but never used.", "'FaArrowDown' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadCategoryAndProducts'. Either include it or remove the dependency array.", ["394"], {"desc": "395", "fix": "396"}, {"desc": "397", "fix": "398"}, {"desc": "399", "fix": "400"}, {"desc": "401", "fix": "402"}, "Update the dependencies array to be: [contextProducts, fetchProducts, selectedCategory]", {"range": "403", "text": "404"}, "Update the dependencies array to be: [currentPage, statusFilter, searchTerm, fetchOrders]", {"range": "405", "text": "406"}, "Update the dependencies array to be: [fetchPromotions]", {"range": "407", "text": "408"}, "Update the dependencies array to be: [categorySlug, categories, products, loadCategoryAndProducts]", {"range": "409", "text": "410"}, [641, 676], "[contextProducts, fetchProducts, selectedCategory]", [614, 653], "[currentPage, statusFilter, searchTerm, fetchOrders]", [942, 944], "[fetchPromotions]", [842, 878], "[categorySlug, categories, products, loadCategoryAndProducts]"]