import axios from 'axios';

// Configuration de base pour les appels API
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

// Instance axios configurée
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// Intercepteur pour ajouter le token d'authentification
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Service Dashboard API
export const dashboardService = {
  // Récupérer les statistiques générales
  getStats: async () => {
    try {
      const response = await apiClient.get('/api/admin/dashboard/stats');
      return {
        success: true,
        data: response.data.data || response.data
      };
    } catch (error) {
      console.error('Erreur API stats:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Erreur de connexion',
        // Données de fallback pour la démonstration
        data: {
          totalProducts: 156,
          totalOrders: 89,
          totalRevenue: 12450.75,
          totalUsers: 234,
          lowStockProducts: 8,
          pendingOrders: 12,
          monthlyRevenue: 8750.25,
          weeklyOrders: 23
        }
      };
    }
  },

  // Récupérer les données pour les graphiques
  getChartData: async (period = '7days') => {
    try {
      const response = await apiClient.get(`/api/admin/dashboard/charts?period=${period}`);
      return {
        success: true,
        data: response.data.data || response.data
      };
    } catch (error) {
      console.error('Erreur API charts:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Erreur de connexion',
        // Données de fallback pour la démonstration
        data: {
          salesChart: {
            labels: ['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'],
            datasets: [{
              label: 'Ventes (MAD)',
              data: [1200, 1900, 800, 1500, 2000, 2400, 1800],
              borderColor: 'rgb(34, 197, 94)',
              backgroundColor: 'rgba(34, 197, 94, 0.1)',
              tension: 0.4
            }]
          },
          ordersChart: {
            labels: ['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'],
            datasets: [{
              label: 'Commandes',
              data: [12, 19, 8, 15, 20, 24, 18],
              backgroundColor: [
                '#ef4444', '#f97316', '#eab308', 
                '#22c55e', '#3b82f6', '#8b5cf6', '#ec4899'
              ]
            }]
          },
          categoriesChart: {
            labels: ['Produits Grillés', 'Salades', 'Fromages', 'Boissons', 'Desserts'],
            datasets: [{
              data: [35, 25, 20, 15, 5],
              backgroundColor: [
                '#ef4444', '#22c55e', '#eab308', '#3b82f6', '#8b5cf6'
              ]
            }]
          }
        }
      };
    }
  },

  // Récupérer les activités récentes
  getRecentActivity: async (limit = 10) => {
    try {
      const response = await apiClient.get(`/api/admin/dashboard/activity?limit=${limit}`);
      return {
        success: true,
        data: response.data.data || response.data
      };
    } catch (error) {
      console.error('Erreur API activity:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Erreur de connexion',
        // Données de fallback pour la démonstration
        data: [
          {
            id: 1,
            type: 'order',
            message: 'Nouvelle commande #1234',
            user: 'Marie Dubois',
            amount: 89.50,
            time: '2 minutes',
            status: 'pending'
          },
          {
            id: 2,
            type: 'product',
            message: 'Stock faible: Poulet Grillé',
            user: 'Système',
            amount: null,
            time: '15 minutes',
            status: 'warning'
          },
          {
            id: 3,
            type: 'user',
            message: 'Nouvel utilisateur inscrit',
            user: 'Ahmed Benali',
            amount: null,
            time: '1 heure',
            status: 'success'
          },
          {
            id: 4,
            type: 'order',
            message: 'Commande #1233 livrée',
            user: 'Fatima Zahra',
            amount: 156.75,
            time: '2 heures',
            status: 'completed'
          },
          {
            id: 5,
            type: 'product',
            message: 'Nouveau produit ajouté',
            user: 'Admin',
            amount: null,
            time: '3 heures',
            status: 'info'
          }
        ]
      };
    }
  },

  // Récupérer les produits en stock faible
  getLowStockProducts: async () => {
    try {
      const response = await apiClient.get('/api/admin/dashboard/low-stock');
      return {
        success: true,
        data: response.data.data || response.data
      };
    } catch (error) {
      console.error('Erreur API low-stock:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Erreur de connexion',
        // Données de fallback pour la démonstration
        data: [
          {
            id: 1,
            name: 'Poulet Grillé Entier',
            stock: 3,
            min_stock: 10,
            category: 'Produits Grillés',
            price: 89.99
          },
          {
            id: 2,
            name: 'Brochettes de Bœuf',
            stock: 2,
            min_stock: 8,
            category: 'Produits Grillés',
            price: 65.50
          },
          {
            id: 3,
            name: 'Fromage de Chèvre',
            stock: 1,
            min_stock: 5,
            category: 'Fromages',
            price: 45.00
          }
        ]
      };
    }
  },

  // Récupérer les commandes récentes
  getRecentOrders: async (limit = 5) => {
    try {
      const response = await apiClient.get(`/api/admin/dashboard/recent-orders?limit=${limit}`);
      return {
        success: true,
        data: response.data.data || response.data
      };
    } catch (error) {
      console.error('Erreur API recent-orders:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Erreur de connexion',
        // Données de fallback pour la démonstration
        data: [
          {
            id: 1234,
            user: {
              name: 'Marie Dubois',
              email: '<EMAIL>'
            },
            total: 89.50,
            status: 'pending',
            created_at: '2024-01-15T10:30:00Z',
            items_count: 3
          },
          {
            id: 1233,
            user: {
              name: 'Ahmed Benali',
              email: '<EMAIL>'
            },
            total: 156.75,
            status: 'completed',
            created_at: '2024-01-15T09:15:00Z',
            items_count: 5
          },
          {
            id: 1232,
            user: {
              name: 'Fatima Zahra',
              email: '<EMAIL>'
            },
            total: 234.25,
            status: 'processing',
            created_at: '2024-01-15T08:45:00Z',
            items_count: 7
          }
        ]
      };
    }
  }
};

export default dashboardService;
