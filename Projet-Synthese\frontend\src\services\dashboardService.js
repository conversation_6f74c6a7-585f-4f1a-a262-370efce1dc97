import axios from 'axios';

// Configuration de base pour les appels API
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

// Instance axios configurée
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// Intercepteur pour ajouter le token d'authentification
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Service Dashboard API
export const dashboardService = {
  // Récupérer les statistiques générales
  getStats: async () => {
    try {
      const response = await apiClient.get('/api/admin/dashboard/stats');
      return {
        success: true,
        data: response.data.data || response.data
      };
    } catch (error) {
      console.error('Erreur API stats:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Erreur de connexion',
        // Pas de données de fallback - forcer l'utilisation de Laravel
        data: {
          totalProducts: 0,
          totalOrders: 0,
          totalRevenue: 0,
          totalUsers: 0,
          lowStockProducts: 0,
          pendingOrders: 0,
          monthlyRevenue: 0,
          weeklyOrders: 0
        }
      };
    }
  },

  // Récupérer les données pour les graphiques
  getChartData: async (period = '7days') => {
    try {
      const response = await apiClient.get(`/api/admin/dashboard/charts?period=${period}`);
      return {
        success: true,
        data: response.data.data || response.data
      };
    } catch (error) {
      console.error('Erreur API charts:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Erreur de connexion',
        // Données de fallback pour la démonstration
        data: {
          salesChart: {
            labels: ['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'],
            datasets: [{
              label: 'Ventes (MAD)',
              data: [1200, 1900, 800, 1500, 2000, 2400, 1800],
              borderColor: 'rgb(34, 197, 94)',
              backgroundColor: 'rgba(34, 197, 94, 0.1)',
              tension: 0.4
            }]
          },
          ordersChart: {
            labels: ['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'],
            datasets: [{
              label: 'Commandes',
              data: [12, 19, 8, 15, 20, 24, 18],
              backgroundColor: [
                '#ef4444', '#f97316', '#eab308', 
                '#22c55e', '#3b82f6', '#8b5cf6', '#ec4899'
              ]
            }]
          },
          categoriesChart: {
            labels: ['Produits Grillés', 'Salades', 'Fromages', 'Boissons', 'Desserts'],
            datasets: [{
              data: [35, 25, 20, 15, 5],
              backgroundColor: [
                '#ef4444', '#22c55e', '#eab308', '#3b82f6', '#8b5cf6'
              ]
            }]
          }
        }
      };
    }
  },

  // Récupérer les activités récentes
  getRecentActivity: async (limit = 10) => {
    try {
      const response = await apiClient.get(`/api/admin/dashboard/activity?limit=${limit}`);
      return {
        success: true,
        data: response.data.data || response.data
      };
    } catch (error) {
      console.error('Erreur API activity:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Erreur de connexion',
        // Pas de données de fallback - forcer l'utilisation de Laravel
        data: []
      };
    }
  },

  // Récupérer les produits en stock faible
  getLowStockProducts: async () => {
    try {
      const response = await apiClient.get('/api/admin/dashboard/low-stock');
      return {
        success: true,
        data: response.data.data || response.data
      };
    } catch (error) {
      console.error('Erreur API low-stock:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Erreur de connexion',
        // Pas de données de fallback - forcer l'utilisation de Laravel
        data: []
      };
    }
  },

  // Récupérer les commandes récentes
  getRecentOrders: async (limit = 5) => {
    try {
      const response = await apiClient.get(`/api/admin/dashboard/recent-orders?limit=${limit}`);
      return {
        success: true,
        data: response.data.data || response.data
      };
    } catch (error) {
      console.error('Erreur API recent-orders:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Erreur de connexion',
        // Pas de données de fallback - forcer l'utilisation de Laravel
        data: []
      };
    }
  }
};

export default dashboardService;
