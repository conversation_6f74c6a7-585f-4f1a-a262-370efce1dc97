{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Projet-Synthese\\\\frontend\\\\src\\\\components\\\\promotions\\\\ActivePromotions.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { FaTag, FaPercent, FaDollarSign, FaShippingFast, FaClock } from 'react-icons/fa';\nimport { promotionService } from '../../services/api/promotionService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ActivePromotions = () => {\n  _s();\n  const [promotions, setPromotions] = useState([]);\n  const [isLoading, setIsLoading] = useState(true);\n  useEffect(() => {\n    const fetchPromotions = async () => {\n      try {\n        const data = await promotionService.getActivePromotions();\n        setPromotions(data);\n      } catch (error) {\n        console.error('Erreur lors du chargement des promotions:', error);\n\n        // Données de test en cas d'erreur de connexion\n        const testPromotions = [{\n          id: 1,\n          name: 'Bienvenue 10%',\n          code: 'BIENVENUE10',\n          description: 'Réduction de 10% pour les nouveaux clients sur leur première commande',\n          type: 'percentage',\n          value: 10,\n          minimum_amount: 50,\n          end_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),\n          // 30 jours\n          is_active: true\n        }, {\n          id: 2,\n          name: 'Livraison Gratuite',\n          code: 'LIVRAISON',\n          description: 'Livraison gratuite pour toute commande supérieure à 30€',\n          type: 'free_shipping',\n          value: 0,\n          minimum_amount: 30,\n          end_date: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString(),\n          // 15 jours\n          is_active: true\n        }, {\n          id: 3,\n          name: 'Réduction 5€',\n          code: 'REDUCTION5',\n          description: '5€ de réduction immédiate sur votre commande',\n          type: 'fixed',\n          value: 5,\n          minimum_amount: 25,\n          end_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),\n          // 7 jours\n          is_active: true\n        }];\n        setPromotions(testPromotions);\n        console.log('Utilisation des données de test pour les promotions - Backend non disponible');\n      } finally {\n        setIsLoading(false);\n      }\n    };\n    fetchPromotions();\n  }, []);\n  const getPromotionIcon = type => {\n    switch (type) {\n      case 'percentage':\n        return /*#__PURE__*/_jsxDEV(FaPercent, {\n          className: \"text-xl\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 16\n        }, this);\n      case 'fixed':\n        return /*#__PURE__*/_jsxDEV(FaDollarSign, {\n          className: \"text-xl\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 16\n        }, this);\n      case 'free_shipping':\n        return /*#__PURE__*/_jsxDEV(FaShippingFast, {\n          className: \"text-xl\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(FaTag, {\n          className: \"text-xl\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  const getPromotionColor = type => {\n    switch (type) {\n      case 'percentage':\n        return 'from-blue-500 to-blue-600';\n      case 'fixed':\n        return 'from-green-500 to-green-600';\n      case 'free_shipping':\n        return 'from-purple-500 to-purple-600';\n      default:\n        return 'from-gray-500 to-gray-600';\n    }\n  };\n  const formatValue = promotion => {\n    switch (promotion.type) {\n      case 'percentage':\n        return `${promotion.value}%`;\n      case 'fixed':\n        return `${promotion.value}$`;\n      case 'free_shipping':\n        return 'Gratuite';\n      default:\n        return promotion.value;\n    }\n  };\n  const formatEndDate = endDate => {\n    const date = new Date(endDate);\n    const now = new Date();\n    const diffTime = date - now;\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    if (diffDays <= 0) {\n      return 'Expire aujourd\\'hui';\n    } else if (diffDays === 1) {\n      return 'Expire demain';\n    } else if (diffDays <= 7) {\n      return `Expire dans ${diffDays} jours`;\n    } else {\n      return `Valide jusqu'au ${date.toLocaleDateString('fr-FR')}`;\n    }\n  };\n  const copyToClipboard = code => {\n    navigator.clipboard.writeText(code);\n    // Vous pouvez ajouter une notification toast ici\n  };\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n      children: Array(3).fill(0).map((_, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-200 animate-pulse rounded-lg h-32\"\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 7\n    }, this);\n  }\n  if (promotions.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-8\",\n      children: [/*#__PURE__*/_jsxDEV(FaTag, {\n        className: \"mx-auto text-4xl text-gray-400 mb-4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600\",\n        children: \"Aucune promotion active pour le moment\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 138,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n    children: promotions.map((promotion, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        duration: 0.5,\n        delay: index * 0.1\n      },\n      className: `relative overflow-hidden rounded-lg bg-gradient-to-r ${getPromotionColor(promotion.type)} text-white shadow-lg hover:shadow-xl transition-shadow cursor-pointer`,\n      onClick: () => copyToClipboard(promotion.code),\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-start justify-between mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white bg-opacity-20 p-3 rounded-full\",\n            children: getPromotionIcon(promotion.type)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-right\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-2xl font-bold\",\n              children: formatValue(promotion)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm opacity-90\",\n              children: [promotion.type === 'percentage' && 'de réduction', promotion.type === 'fixed' && 'de réduction', promotion.type === 'free_shipping' && 'livraison']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"font-semibold text-lg mb-1\",\n            children: promotion.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm opacity-90 line-clamp-2\",\n            children: promotion.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm opacity-90\",\n              children: \"Code:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-mono font-bold bg-white bg-opacity-20 px-2 py-1 rounded\",\n              children: promotion.code\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 15\n          }, this), promotion.minimum_amount && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm opacity-90\",\n              children: \"Minimum:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium\",\n              children: [promotion.minimum_amount, \"$\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(FaClock, {\n              className: \"text-sm opacity-75\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xs opacity-90\",\n              children: formatEndDate(promotion.end_date)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-0 hover:opacity-10 transform -skew-x-12 -translate-x-full hover:translate-x-full transition-all duration-700\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 11\n      }, this)]\n    }, promotion.id, true, {\n      fileName: _jsxFileName,\n      lineNumber: 148,\n      columnNumber: 9\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 146,\n    columnNumber: 5\n  }, this);\n};\n_s(ActivePromotions, \"oD+evbb9koHPfJt9JqXkLs/qnNU=\");\n_c = ActivePromotions;\nexport default ActivePromotions;\nvar _c;\n$RefreshReg$(_c, \"ActivePromotions\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "FaTag", "FaPercent", "FaDollarSign", "FaShippingFast", "FaClock", "promotionService", "jsxDEV", "_jsxDEV", "ActivePromotions", "_s", "promotions", "setPromotions", "isLoading", "setIsLoading", "fetchPromotions", "data", "getActivePromotions", "error", "console", "testPromotions", "id", "name", "code", "description", "type", "value", "minimum_amount", "end_date", "Date", "now", "toISOString", "is_active", "log", "getPromotionIcon", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getPromotionColor", "formatValue", "promotion", "formatEndDate", "endDate", "date", "diffTime", "diffDays", "Math", "ceil", "toLocaleDateString", "copyToClipboard", "navigator", "clipboard", "writeText", "children", "Array", "fill", "map", "_", "index", "length", "div", "initial", "opacity", "y", "animate", "transition", "duration", "delay", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet-Synthese/frontend/src/components/promotions/ActivePromotions.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { FaTag, FaPercent, FaDollarSign, FaShippingFast, FaClock } from 'react-icons/fa';\nimport { promotionService } from '../../services/api/promotionService';\n\nconst ActivePromotions = () => {\n  const [promotions, setPromotions] = useState([]);\n  const [isLoading, setIsLoading] = useState(true);\n\n  useEffect(() => {\n    const fetchPromotions = async () => {\n      try {\n        const data = await promotionService.getActivePromotions();\n        setPromotions(data);\n      } catch (error) {\n        console.error('Erreur lors du chargement des promotions:', error);\n\n        // Données de test en cas d'erreur de connexion\n        const testPromotions = [\n          {\n            id: 1,\n            name: 'Bienvenue 10%',\n            code: 'BIENVENUE10',\n            description: 'Réduction de 10% pour les nouveaux clients sur leur première commande',\n            type: 'percentage',\n            value: 10,\n            minimum_amount: 50,\n            end_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 jours\n            is_active: true\n          },\n          {\n            id: 2,\n            name: 'Livraison Gratuite',\n            code: 'LIVRAISON',\n            description: 'Livraison gratuite pour toute commande supérieure à 30€',\n            type: 'free_shipping',\n            value: 0,\n            minimum_amount: 30,\n            end_date: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString(), // 15 jours\n            is_active: true\n          },\n          {\n            id: 3,\n            name: 'Réduction 5€',\n            code: 'REDUCTION5',\n            description: '5€ de réduction immédiate sur votre commande',\n            type: 'fixed',\n            value: 5,\n            minimum_amount: 25,\n            end_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 jours\n            is_active: true\n          }\n        ];\n\n        setPromotions(testPromotions);\n        console.log('Utilisation des données de test pour les promotions - Backend non disponible');\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    fetchPromotions();\n  }, []);\n\n  const getPromotionIcon = (type) => {\n    switch (type) {\n      case 'percentage':\n        return <FaPercent className=\"text-xl\" />;\n      case 'fixed':\n        return <FaDollarSign className=\"text-xl\" />;\n      case 'free_shipping':\n        return <FaShippingFast className=\"text-xl\" />;\n      default:\n        return <FaTag className=\"text-xl\" />;\n    }\n  };\n\n  const getPromotionColor = (type) => {\n    switch (type) {\n      case 'percentage':\n        return 'from-blue-500 to-blue-600';\n      case 'fixed':\n        return 'from-green-500 to-green-600';\n      case 'free_shipping':\n        return 'from-purple-500 to-purple-600';\n      default:\n        return 'from-gray-500 to-gray-600';\n    }\n  };\n\n  const formatValue = (promotion) => {\n    switch (promotion.type) {\n      case 'percentage':\n        return `${promotion.value}%`;\n      case 'fixed':\n        return `${promotion.value}$`;\n      case 'free_shipping':\n        return 'Gratuite';\n      default:\n        return promotion.value;\n    }\n  };\n\n  const formatEndDate = (endDate) => {\n    const date = new Date(endDate);\n    const now = new Date();\n    const diffTime = date - now;\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n\n    if (diffDays <= 0) {\n      return 'Expire aujourd\\'hui';\n    } else if (diffDays === 1) {\n      return 'Expire demain';\n    } else if (diffDays <= 7) {\n      return `Expire dans ${diffDays} jours`;\n    } else {\n      return `Valide jusqu'au ${date.toLocaleDateString('fr-FR')}`;\n    }\n  };\n\n  const copyToClipboard = (code) => {\n    navigator.clipboard.writeText(code);\n    // Vous pouvez ajouter une notification toast ici\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n        {Array(3).fill(0).map((_, index) => (\n          <div key={index} className=\"bg-gray-200 animate-pulse rounded-lg h-32\"></div>\n        ))}\n      </div>\n    );\n  }\n\n  if (promotions.length === 0) {\n    return (\n      <div className=\"text-center py-8\">\n        <FaTag className=\"mx-auto text-4xl text-gray-400 mb-4\" />\n        <p className=\"text-gray-600\">Aucune promotion active pour le moment</p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n      {promotions.map((promotion, index) => (\n        <motion.div\n          key={promotion.id}\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5, delay: index * 0.1 }}\n          className={`relative overflow-hidden rounded-lg bg-gradient-to-r ${getPromotionColor(promotion.type)} text-white shadow-lg hover:shadow-xl transition-shadow cursor-pointer`}\n          onClick={() => copyToClipboard(promotion.code)}\n        >\n          <div className=\"p-6\">\n            <div className=\"flex items-start justify-between mb-4\">\n              <div className=\"bg-white bg-opacity-20 p-3 rounded-full\">\n                {getPromotionIcon(promotion.type)}\n              </div>\n              <div className=\"text-right\">\n                <div className=\"text-2xl font-bold\">\n                  {formatValue(promotion)}\n                </div>\n                <div className=\"text-sm opacity-90\">\n                  {promotion.type === 'percentage' && 'de réduction'}\n                  {promotion.type === 'fixed' && 'de réduction'}\n                  {promotion.type === 'free_shipping' && 'livraison'}\n                </div>\n              </div>\n            </div>\n\n            <div className=\"mb-4\">\n              <h3 className=\"font-semibold text-lg mb-1\">{promotion.name}</h3>\n              <p className=\"text-sm opacity-90 line-clamp-2\">{promotion.description}</p>\n            </div>\n\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm opacity-90\">Code:</span>\n                <span className=\"font-mono font-bold bg-white bg-opacity-20 px-2 py-1 rounded\">\n                  {promotion.code}\n                </span>\n              </div>\n\n              {promotion.minimum_amount && (\n                <div className=\"flex items-center justify-between\">\n                  <span className=\"text-sm opacity-90\">Minimum:</span>\n                  <span className=\"text-sm font-medium\">{promotion.minimum_amount}$</span>\n                </div>\n              )}\n\n              <div className=\"flex items-center justify-between\">\n                <FaClock className=\"text-sm opacity-75\" />\n                <span className=\"text-xs opacity-90\">\n                  {formatEndDate(promotion.end_date)}\n                </span>\n              </div>\n            </div>\n          </div>\n\n          {/* Effet de brillance au survol */}\n          <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-0 hover:opacity-10 transform -skew-x-12 -translate-x-full hover:translate-x-full transition-all duration-700\"></div>\n        </motion.div>\n      ))}\n    </div>\n  );\n};\n\nexport default ActivePromotions;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,KAAK,EAAEC,SAAS,EAAEC,YAAY,EAAEC,cAAc,EAAEC,OAAO,QAAQ,gBAAgB;AACxF,SAASC,gBAAgB,QAAQ,qCAAqC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvE,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACe,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAEhDC,SAAS,CAAC,MAAM;IACd,MAAMgB,eAAe,GAAG,MAAAA,CAAA,KAAY;MAClC,IAAI;QACF,MAAMC,IAAI,GAAG,MAAMV,gBAAgB,CAACW,mBAAmB,CAAC,CAAC;QACzDL,aAAa,CAACI,IAAI,CAAC;MACrB,CAAC,CAAC,OAAOE,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;;QAEjE;QACA,MAAME,cAAc,GAAG,CACrB;UACEC,EAAE,EAAE,CAAC;UACLC,IAAI,EAAE,eAAe;UACrBC,IAAI,EAAE,aAAa;UACnBC,WAAW,EAAE,uEAAuE;UACpFC,IAAI,EAAE,YAAY;UAClBC,KAAK,EAAE,EAAE;UACTC,cAAc,EAAE,EAAE;UAClBC,QAAQ,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,CAAC,CAAC;UAAE;UACzEC,SAAS,EAAE;QACb,CAAC,EACD;UACEX,EAAE,EAAE,CAAC;UACLC,IAAI,EAAE,oBAAoB;UAC1BC,IAAI,EAAE,WAAW;UACjBC,WAAW,EAAE,yDAAyD;UACtEC,IAAI,EAAE,eAAe;UACrBC,KAAK,EAAE,CAAC;UACRC,cAAc,EAAE,EAAE;UAClBC,QAAQ,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,CAAC,CAAC;UAAE;UACzEC,SAAS,EAAE;QACb,CAAC,EACD;UACEX,EAAE,EAAE,CAAC;UACLC,IAAI,EAAE,cAAc;UACpBC,IAAI,EAAE,YAAY;UAClBC,WAAW,EAAE,8CAA8C;UAC3DC,IAAI,EAAE,OAAO;UACbC,KAAK,EAAE,CAAC;UACRC,cAAc,EAAE,EAAE;UAClBC,QAAQ,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,CAAC,CAAC;UAAE;UACxEC,SAAS,EAAE;QACb,CAAC,CACF;QAEDpB,aAAa,CAACQ,cAAc,CAAC;QAC7BD,OAAO,CAACc,GAAG,CAAC,8EAA8E,CAAC;MAC7F,CAAC,SAAS;QACRnB,YAAY,CAAC,KAAK,CAAC;MACrB;IACF,CAAC;IAEDC,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMmB,gBAAgB,GAAIT,IAAI,IAAK;IACjC,QAAQA,IAAI;MACV,KAAK,YAAY;QACf,oBAAOjB,OAAA,CAACN,SAAS;UAACiC,SAAS,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC1C,KAAK,OAAO;QACV,oBAAO/B,OAAA,CAACL,YAAY;UAACgC,SAAS,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC7C,KAAK,eAAe;QAClB,oBAAO/B,OAAA,CAACJ,cAAc;UAAC+B,SAAS,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC/C;QACE,oBAAO/B,OAAA,CAACP,KAAK;UAACkC,SAAS,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IACxC;EACF,CAAC;EAED,MAAMC,iBAAiB,GAAIf,IAAI,IAAK;IAClC,QAAQA,IAAI;MACV,KAAK,YAAY;QACf,OAAO,2BAA2B;MACpC,KAAK,OAAO;QACV,OAAO,6BAA6B;MACtC,KAAK,eAAe;QAClB,OAAO,+BAA+B;MACxC;QACE,OAAO,2BAA2B;IACtC;EACF,CAAC;EAED,MAAMgB,WAAW,GAAIC,SAAS,IAAK;IACjC,QAAQA,SAAS,CAACjB,IAAI;MACpB,KAAK,YAAY;QACf,OAAO,GAAGiB,SAAS,CAAChB,KAAK,GAAG;MAC9B,KAAK,OAAO;QACV,OAAO,GAAGgB,SAAS,CAAChB,KAAK,GAAG;MAC9B,KAAK,eAAe;QAClB,OAAO,UAAU;MACnB;QACE,OAAOgB,SAAS,CAAChB,KAAK;IAC1B;EACF,CAAC;EAED,MAAMiB,aAAa,GAAIC,OAAO,IAAK;IACjC,MAAMC,IAAI,GAAG,IAAIhB,IAAI,CAACe,OAAO,CAAC;IAC9B,MAAMd,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;IACtB,MAAMiB,QAAQ,GAAGD,IAAI,GAAGf,GAAG;IAC3B,MAAMiB,QAAQ,GAAGC,IAAI,CAACC,IAAI,CAACH,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAE5D,IAAIC,QAAQ,IAAI,CAAC,EAAE;MACjB,OAAO,qBAAqB;IAC9B,CAAC,MAAM,IAAIA,QAAQ,KAAK,CAAC,EAAE;MACzB,OAAO,eAAe;IACxB,CAAC,MAAM,IAAIA,QAAQ,IAAI,CAAC,EAAE;MACxB,OAAO,eAAeA,QAAQ,QAAQ;IACxC,CAAC,MAAM;MACL,OAAO,mBAAmBF,IAAI,CAACK,kBAAkB,CAAC,OAAO,CAAC,EAAE;IAC9D;EACF,CAAC;EAED,MAAMC,eAAe,GAAI5B,IAAI,IAAK;IAChC6B,SAAS,CAACC,SAAS,CAACC,SAAS,CAAC/B,IAAI,CAAC;IACnC;EACF,CAAC;EAED,IAAIV,SAAS,EAAE;IACb,oBACEL,OAAA;MAAK2B,SAAS,EAAC,sDAAsD;MAAAoB,QAAA,EAClEC,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,kBAC7BpD,OAAA;QAAiB2B,SAAS,EAAC;MAA2C,GAA5DyB,KAAK;QAAAxB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAA6D,CAC7E;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEV;EAEA,IAAI5B,UAAU,CAACkD,MAAM,KAAK,CAAC,EAAE;IAC3B,oBACErD,OAAA;MAAK2B,SAAS,EAAC,kBAAkB;MAAAoB,QAAA,gBAC/B/C,OAAA,CAACP,KAAK;QAACkC,SAAS,EAAC;MAAqC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACzD/B,OAAA;QAAG2B,SAAS,EAAC,eAAe;QAAAoB,QAAA,EAAC;MAAsC;QAAAnB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpE,CAAC;EAEV;EAEA,oBACE/B,OAAA;IAAK2B,SAAS,EAAC,sDAAsD;IAAAoB,QAAA,EAClE5C,UAAU,CAAC+C,GAAG,CAAC,CAAChB,SAAS,EAAEkB,KAAK,kBAC/BpD,OAAA,CAACR,MAAM,CAAC8D,GAAG;MAETC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BE,UAAU,EAAE;QAAEC,QAAQ,EAAE,GAAG;QAAEC,KAAK,EAAET,KAAK,GAAG;MAAI,CAAE;MAClDzB,SAAS,EAAE,wDAAwDK,iBAAiB,CAACE,SAAS,CAACjB,IAAI,CAAC,wEAAyE;MAC7K6C,OAAO,EAAEA,CAAA,KAAMnB,eAAe,CAACT,SAAS,CAACnB,IAAI,CAAE;MAAAgC,QAAA,gBAE/C/C,OAAA;QAAK2B,SAAS,EAAC,KAAK;QAAAoB,QAAA,gBAClB/C,OAAA;UAAK2B,SAAS,EAAC,uCAAuC;UAAAoB,QAAA,gBACpD/C,OAAA;YAAK2B,SAAS,EAAC,yCAAyC;YAAAoB,QAAA,EACrDrB,gBAAgB,CAACQ,SAAS,CAACjB,IAAI;UAAC;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,eACN/B,OAAA;YAAK2B,SAAS,EAAC,YAAY;YAAAoB,QAAA,gBACzB/C,OAAA;cAAK2B,SAAS,EAAC,oBAAoB;cAAAoB,QAAA,EAChCd,WAAW,CAACC,SAAS;YAAC;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC,eACN/B,OAAA;cAAK2B,SAAS,EAAC,oBAAoB;cAAAoB,QAAA,GAChCb,SAAS,CAACjB,IAAI,KAAK,YAAY,IAAI,cAAc,EACjDiB,SAAS,CAACjB,IAAI,KAAK,OAAO,IAAI,cAAc,EAC5CiB,SAAS,CAACjB,IAAI,KAAK,eAAe,IAAI,WAAW;YAAA;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN/B,OAAA;UAAK2B,SAAS,EAAC,MAAM;UAAAoB,QAAA,gBACnB/C,OAAA;YAAI2B,SAAS,EAAC,4BAA4B;YAAAoB,QAAA,EAAEb,SAAS,CAACpB;UAAI;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAChE/B,OAAA;YAAG2B,SAAS,EAAC,iCAAiC;YAAAoB,QAAA,EAAEb,SAAS,CAAClB;UAAW;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvE,CAAC,eAEN/B,OAAA;UAAK2B,SAAS,EAAC,WAAW;UAAAoB,QAAA,gBACxB/C,OAAA;YAAK2B,SAAS,EAAC,mCAAmC;YAAAoB,QAAA,gBAChD/C,OAAA;cAAM2B,SAAS,EAAC,oBAAoB;cAAAoB,QAAA,EAAC;YAAK;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACjD/B,OAAA;cAAM2B,SAAS,EAAC,8DAA8D;cAAAoB,QAAA,EAC3Eb,SAAS,CAACnB;YAAI;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,EAELG,SAAS,CAACf,cAAc,iBACvBnB,OAAA;YAAK2B,SAAS,EAAC,mCAAmC;YAAAoB,QAAA,gBAChD/C,OAAA;cAAM2B,SAAS,EAAC,oBAAoB;cAAAoB,QAAA,EAAC;YAAQ;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpD/B,OAAA;cAAM2B,SAAS,EAAC,qBAAqB;cAAAoB,QAAA,GAAEb,SAAS,CAACf,cAAc,EAAC,GAAC;YAAA;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE,CACN,eAED/B,OAAA;YAAK2B,SAAS,EAAC,mCAAmC;YAAAoB,QAAA,gBAChD/C,OAAA,CAACH,OAAO;cAAC8B,SAAS,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1C/B,OAAA;cAAM2B,SAAS,EAAC,oBAAoB;cAAAoB,QAAA,EACjCZ,aAAa,CAACD,SAAS,CAACd,QAAQ;YAAC;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN/B,OAAA;QAAK2B,SAAS,EAAC;MAAkM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA,GAtDnNG,SAAS,CAACrB,EAAE;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAuDP,CACb;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC7B,EAAA,CA1MID,gBAAgB;AAAA8D,EAAA,GAAhB9D,gBAAgB;AA4MtB,eAAeA,gBAAgB;AAAC,IAAA8D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}