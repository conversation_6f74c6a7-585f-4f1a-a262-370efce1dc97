{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Projet-Synthese\\\\frontend\\\\src\\\\pages\\\\HomePage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { useData } from '../context/DataContext';\nimport ProductCard from '../components/products/ProductCard';\nimport ActivePromotions from '../components/promotions/ActivePromotions';\nimport { FaArrowRight, FaUtensils, FaPizzaSlice, FaCheese, FaCocktail } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HomePage = () => {\n  _s();\n  const {\n    state\n  } = useData();\n  const {\n    products: allProducts,\n    categories: allCategories,\n    loading: dataLoading\n  } = state;\n  const [featuredProducts, setFeaturedProducts] = useState([]);\n  const [newProducts, setNewProducts] = useState([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [isUsingTestData, setIsUsingTestData] = useState(false);\n  useEffect(() => {\n    const fetchProducts = async () => {\n      try {\n        // Utiliser les données du contexte global en priorité\n        if (allProducts && allProducts.length > 0) {\n          setFeaturedProducts(allProducts.slice(0, 4));\n          setNewProducts(allProducts.slice(-4));\n          setIsLoading(false);\n          return;\n        }\n\n        // Fallback: essayer l'API (mais sans axios direct)\n        console.log('Tentative de chargement depuis l\\'API...');\n\n        // Cette section ne sera plus utilisée car on utilise le contexte\n        setIsLoading(false);\n      } catch (error) {\n        console.error('Erreur lors du chargement des produits:', error);\n\n        // Données de test en cas d'erreur de connexion\n        const testProducts = [{\n          id: 1,\n          name: 'Poulet Grillé Entier',\n          description: 'Poulet entier grillé aux herbes de Provence, tendre et savoureux',\n          short_description: 'Poulet entier grillé aux herbes',\n          price: 25.99,\n          sale_price: null,\n          image: 'https://images.unsplash.com/photo-1598103442097-8b74394b95c6?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',\n          category: {\n            name: 'Produits Grillés'\n          },\n          is_featured: true,\n          is_active: true,\n          stock_quantity: 50\n        }, {\n          id: 2,\n          name: 'Brochettes de Bœuf',\n          description: 'Brochettes de bœuf marinées et grillées, accompagnées de légumes',\n          short_description: 'Brochettes de bœuf marinées',\n          price: 18.50,\n          sale_price: 16.99,\n          image: 'https://images.unsplash.com/photo-1529692236671-f1f6cf9683ba?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',\n          category: {\n            name: 'Produits Grillés'\n          },\n          is_featured: true,\n          is_active: true,\n          stock_quantity: 30\n        }, {\n          id: 3,\n          name: 'Salade César',\n          description: 'Salade César classique avec croûtons, parmesan et sauce maison',\n          short_description: 'Salade César classique',\n          price: 12.50,\n          sale_price: null,\n          image: 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',\n          category: {\n            name: 'Salades'\n          },\n          is_featured: true,\n          is_active: true,\n          stock_quantity: 40\n        }, {\n          id: 4,\n          name: 'Plateau de Fromages',\n          description: 'Sélection de fromages artisanaux avec confiture et noix',\n          short_description: 'Plateau de fromages artisanaux',\n          price: 19.90,\n          sale_price: null,\n          image: 'https://images.unsplash.com/photo-1486297678162-eb2a19b0a32d?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',\n          category: {\n            name: 'Fromages'\n          },\n          is_featured: true,\n          is_active: true,\n          stock_quantity: 15\n        }];\n        setFeaturedProducts(testProducts);\n        setNewProducts(testProducts);\n        setIsUsingTestData(false); // Masquer le bandeau pour la démonstration\n        console.log('Utilisation des données de test - Mode démonstration');\n        setIsLoading(false);\n      }\n    };\n\n    // Vérifier d'abord si on a des données du contexte\n    if (allProducts && allProducts.length > 0) {\n      setFeaturedProducts(allProducts.slice(0, 4));\n      setNewProducts(allProducts.slice(-4));\n      setIsLoading(false);\n      console.log('Utilisation des données du contexte global');\n    } else {\n      fetchProducts();\n    }\n  }, [allProducts]);\n  const categories = [{\n    id: 1,\n    name: \"Produits à Griller\",\n    description: \"Viandes, saucisses et brochettes prêtes à griller\",\n    icon: /*#__PURE__*/_jsxDEV(FaUtensils, {\n      className: \"text-4xl text-yummy-grilled\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 13\n    }, this),\n    color: \"bg-yummy-grilled/10 border-yummy-grilled/20\",\n    textColor: \"text-yummy-grilled\",\n    path: \"/produits-grilles\"\n  }, {\n    id: 2,\n    name: \"Produits Non-Grillés\",\n    description: \"Salades, sandwichs et plats préparés\",\n    icon: /*#__PURE__*/_jsxDEV(FaPizzaSlice, {\n      className: \"text-4xl text-yummy-nongrilled\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 13\n    }, this),\n    color: \"bg-yummy-nongrilled/10 border-yummy-nongrilled/20\",\n    textColor: \"text-yummy-nongrilled\",\n    path: \"/produits-non-grilles\"\n  }, {\n    id: 3,\n    name: \"Fromages\",\n    description: \"Fromages locaux et importés de qualité\",\n    icon: /*#__PURE__*/_jsxDEV(FaCheese, {\n      className: \"text-4xl text-yummy-cheese\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 13\n    }, this),\n    color: \"bg-yummy-cheese/10 border-yummy-cheese/20\",\n    textColor: \"text-yummy-cheese\",\n    path: \"/fromages\"\n  }, {\n    id: 4,\n    name: \"Boissons\",\n    description: \"Boissons fraîches, chaudes et alcoolisées\",\n    icon: /*#__PURE__*/_jsxDEV(FaCocktail, {\n      className: \"text-4xl text-yummy-drinks\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 13\n    }, this),\n    color: \"bg-yummy-drinks/10 border-yummy-drinks/20\",\n    textColor: \"text-yummy-drinks\",\n    path: \"/boissons\"\n  }];\n\n  // Composant Hero Section\n  const HeroSection = () => /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"relative bg-gradient-to-r from-green-50 to-green-100 py-16 md:py-24\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 gap-8 items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(motion.h1, {\n            className: \"text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900\",\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.5\n            },\n            children: [\"Des produits frais pour \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-primary-600\",\n              children: \"tous vos repas\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 39\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n            className: \"text-lg text-gray-700\",\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.5,\n              delay: 0.1\n            },\n            children: \"D\\xE9couvrez notre s\\xE9lection de produits \\xE0 griller, non-grill\\xE9s, fromages et boissons de qualit\\xE9 pour satisfaire toutes vos envies gourmandes.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"flex flex-wrap gap-4\",\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.5,\n              delay: 0.2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/products\",\n              className: \"btn-primary\",\n              children: \"D\\xE9couvrir nos produits\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/about\",\n              className: \"btn-outline\",\n              children: \"En savoir plus\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"relative\",\n          initial: {\n            opacity: 0,\n            scale: 0.9\n          },\n          animate: {\n            opacity: 1,\n            scale: 1\n          },\n          transition: {\n            duration: 0.5\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: \"https://images.unsplash.com/photo-1504674900247-0877df9cc836?ixlib=rb-1.2.1&auto=format&fit=crop&w=1200&q=80\",\n            alt: \"Produits frais\",\n            className: \"rounded-lg shadow-xl\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute -bottom-6 -right-6 bg-white p-4 rounded-lg shadow-lg\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-green-100 p-2 rounded-full\",\n                children: /*#__PURE__*/_jsxDEV(FaUtensils, {\n                  className: \"text-primary-600 text-xl\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 204,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"font-semibold text-gray-900\",\n                  children: \"Livraison rapide\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 207,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600\",\n                  children: \"En 24h chez vous\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 208,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 154,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 153,\n    columnNumber: 5\n  }, this);\n\n  // Composant Category List\n  const CategoryList = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mt-8\",\n    children: categories.map(category => /*#__PURE__*/_jsxDEV(motion.div, {\n      className: `card p-6 border ${category.color} hover:shadow-lg transition-shadow`,\n      whileHover: {\n        y: -5\n      },\n      transition: {\n        duration: 0.3\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col items-center text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: `p-4 rounded-full ${category.color} mb-4`,\n          children: category.icon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: `text-xl font-semibold mb-2 ${category.textColor}`,\n          children: category.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mb-4\",\n          children: category.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: category.path,\n          className: `flex items-center ${category.textColor} font-medium hover:underline`,\n          children: [\"D\\xE9couvrir \", /*#__PURE__*/_jsxDEV(FaArrowRight, {\n            className: \"ml-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 11\n      }, this)\n    }, category.id, false, {\n      fileName: _jsxFileName,\n      lineNumber: 222,\n      columnNumber: 9\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 220,\n    columnNumber: 5\n  }, this);\n\n  // Composant Featured Products\n  const FeaturedProducts = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mt-8\",\n    children: isLoading ? Array(4).fill(0).map((_, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card animate-pulse\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-300 aspect-square w-full\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 space-y-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-4 bg-gray-300 rounded w-1/4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-6 bg-gray-300 rounded w-3/4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-4 bg-gray-300 rounded w-1/2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 257,\n        columnNumber: 13\n      }, this)]\n    }, index, true, {\n      fileName: _jsxFileName,\n      lineNumber: 255,\n      columnNumber: 11\n    }, this)) : featuredProducts.map(product => /*#__PURE__*/_jsxDEV(ProductCard, {\n      product: product\n    }, product.id, false, {\n      fileName: _jsxFileName,\n      lineNumber: 266,\n      columnNumber: 11\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 252,\n    columnNumber: 5\n  }, this);\n\n  // Composant Promo Section\n  const PromoSection = () => /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"py-16 bg-gradient-to-r from-secondary-500 to-secondary-600 text-white\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 gap-8 items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl md:text-4xl font-bold mb-4\",\n            children: \"Offre sp\\xE9ciale du moment\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-lg mb-6\",\n            children: \"Profitez de 15% de r\\xE9duction sur tous nos produits \\xE0 griller pour vos barbecues d'\\xE9t\\xE9 !\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-wrap gap-4\",\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/produits-grilles\",\n              className: \"bg-white text-secondary-600 px-6 py-3 rounded-full font-medium hover:bg-gray-100 transition-colors\",\n              children: \"En profiter maintenant\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: \"https://images.unsplash.com/photo-1544025162-d76694265947?ixlib=rb-1.2.1&auto=format&fit=crop&w=1200&q=80\",\n            alt: \"Barbecue\",\n            className: \"rounded-lg shadow-xl\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute top-0 right-0 bg-accent-500 text-white text-xl font-bold p-4 rounded-bl-lg rounded-tr-lg\",\n            children: \"-15%\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 276,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 275,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 274,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gradient-to-r from-green-600 to-green-700 text-white p-3\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto flex justify-between items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"h-5 w-5\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            stroke: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium\",\n            children: \"Mode D\\xE9monstration - Projet YUMMY\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"/admin/dashboard\",\n            className: \"bg-white text-green-600 px-4 py-1 rounded text-sm font-medium hover:bg-gray-100 transition-colors\",\n            children: \"\\uD83D\\uDD27 Dashboard Admin\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm opacity-75\",\n            children: \"Toutes les fonctionnalit\\xE9s sont accessibles\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 308,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 307,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(HeroSection, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 327,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-16 bg-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col md:flex-row justify-between items-center mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl font-bold text-gray-900\",\n            children: \"Nos cat\\xE9gories\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/products\",\n            className: \"flex items-center text-primary-600 font-medium hover:text-primary-700\",\n            children: [\"Voir tous les produits \", /*#__PURE__*/_jsxDEV(FaArrowRight, {\n              className: \"ml-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 38\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 331,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(CategoryList, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 337,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 330,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 329,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-16 bg-gray-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-3xl font-bold text-gray-900 mb-8\",\n          children: \"Produits en vedette\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 343,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FeaturedProducts, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 342,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 341,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(PromoSection, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 348,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-16 bg-gray-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-3xl font-bold text-gray-900 mb-8\",\n          children: \"Promotions en cours\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ActivePromotions, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 353,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 351,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 350,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-16 bg-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-3xl font-bold text-gray-900 mb-8\",\n          children: \"Nouveaux produits\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 359,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6\",\n          children: isLoading ? Array(4).fill(0).map((_, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card animate-pulse\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-300 aspect-square w-full\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 364,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-4 space-y-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"h-4 bg-gray-300 rounded w-1/4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 366,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"h-6 bg-gray-300 rounded w-3/4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 367,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"h-4 bg-gray-300 rounded w-1/2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 368,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 365,\n              columnNumber: 19\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 363,\n            columnNumber: 17\n          }, this)) : newProducts.map(product => /*#__PURE__*/_jsxDEV(ProductCard, {\n            product: product\n          }, product.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 374,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 358,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 357,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 305,\n    columnNumber: 5\n  }, this);\n};\n_s(HomePage, \"fW8VOQ2JbkaSsB8AAYp6DToti6o=\", false, function () {\n  return [useData];\n});\n_c = HomePage;\nexport default HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "motion", "useData", "ProductCard", "ActivePromotions", "FaArrowRight", "FaUtensils", "FaPizzaSlice", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FaCocktail", "jsxDEV", "_jsxDEV", "HomePage", "_s", "state", "products", "allProducts", "categories", "allCategories", "loading", "dataLoading", "featuredProducts", "setFeaturedProducts", "newProducts", "setNewProducts", "isLoading", "setIsLoading", "isUsingTestData", "setIsUsingTestData", "fetchProducts", "length", "slice", "console", "log", "error", "testProducts", "id", "name", "description", "short_description", "price", "sale_price", "image", "category", "is_featured", "is_active", "stock_quantity", "icon", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "textColor", "path", "HeroSection", "children", "h1", "initial", "opacity", "y", "animate", "transition", "duration", "p", "delay", "div", "to", "scale", "src", "alt", "CategoryList", "map", "whileHover", "FeaturedProducts", "Array", "fill", "_", "index", "product", "PromoSection", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "href", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet-Synthese/frontend/src/pages/HomePage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { useData } from '../context/DataContext';\nimport ProductCard from '../components/products/ProductCard';\nimport ActivePromotions from '../components/promotions/ActivePromotions';\nimport { FaArrowRight, FaUtensils, FaPizzaSlice, FaCheese, FaCocktail } from 'react-icons/fa';\n\nconst HomePage = () => {\n  const { state } = useData();\n  const { products: allProducts, categories: allCategories, loading: dataLoading } = state;\n\n  const [featuredProducts, setFeaturedProducts] = useState([]);\n  const [newProducts, setNewProducts] = useState([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [isUsingTestData, setIsUsingTestData] = useState(false);\n\n  useEffect(() => {\n    const fetchProducts = async () => {\n      try {\n        // Utiliser les données du contexte global en priorité\n        if (allProducts && allProducts.length > 0) {\n          setFeaturedProducts(allProducts.slice(0, 4));\n          setNewProducts(allProducts.slice(-4));\n          setIsLoading(false);\n          return;\n        }\n\n        // Fallback: essayer l'API (mais sans axios direct)\n        console.log('Tentative de chargement depuis l\\'API...');\n\n        // Cette section ne sera plus utilisée car on utilise le contexte\n        setIsLoading(false);\n      } catch (error) {\n        console.error('Erreur lors du chargement des produits:', error);\n\n        // Données de test en cas d'erreur de connexion\n        const testProducts = [\n          {\n            id: 1,\n            name: 'Poulet Grillé Entier',\n            description: 'Poulet entier grillé aux herbes de Provence, tendre et savoureux',\n            short_description: 'Poulet entier grillé aux herbes',\n            price: 25.99,\n            sale_price: null,\n            image: 'https://images.unsplash.com/photo-1598103442097-8b74394b95c6?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',\n            category: { name: 'Produits Grillés' },\n            is_featured: true,\n            is_active: true,\n            stock_quantity: 50\n          },\n          {\n            id: 2,\n            name: 'Brochettes de Bœuf',\n            description: 'Brochettes de bœuf marinées et grillées, accompagnées de légumes',\n            short_description: 'Brochettes de bœuf marinées',\n            price: 18.50,\n            sale_price: 16.99,\n            image: 'https://images.unsplash.com/photo-1529692236671-f1f6cf9683ba?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',\n            category: { name: 'Produits Grillés' },\n            is_featured: true,\n            is_active: true,\n            stock_quantity: 30\n          },\n          {\n            id: 3,\n            name: 'Salade César',\n            description: 'Salade César classique avec croûtons, parmesan et sauce maison',\n            short_description: 'Salade César classique',\n            price: 12.50,\n            sale_price: null,\n            image: 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',\n            category: { name: 'Salades' },\n            is_featured: true,\n            is_active: true,\n            stock_quantity: 40\n          },\n          {\n            id: 4,\n            name: 'Plateau de Fromages',\n            description: 'Sélection de fromages artisanaux avec confiture et noix',\n            short_description: 'Plateau de fromages artisanaux',\n            price: 19.90,\n            sale_price: null,\n            image: 'https://images.unsplash.com/photo-1486297678162-eb2a19b0a32d?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',\n            category: { name: 'Fromages' },\n            is_featured: true,\n            is_active: true,\n            stock_quantity: 15\n          }\n        ];\n\n        setFeaturedProducts(testProducts);\n        setNewProducts(testProducts);\n        setIsUsingTestData(false); // Masquer le bandeau pour la démonstration\n        console.log('Utilisation des données de test - Mode démonstration');\n        setIsLoading(false);\n      }\n    };\n\n    // Vérifier d'abord si on a des données du contexte\n    if (allProducts && allProducts.length > 0) {\n      setFeaturedProducts(allProducts.slice(0, 4));\n      setNewProducts(allProducts.slice(-4));\n      setIsLoading(false);\n      console.log('Utilisation des données du contexte global');\n    } else {\n      fetchProducts();\n    }\n  }, [allProducts]);\n\n  const categories = [\n    {\n      id: 1,\n      name: \"Produits à Griller\",\n      description: \"Viandes, saucisses et brochettes prêtes à griller\",\n      icon: <FaUtensils className=\"text-4xl text-yummy-grilled\" />,\n      color: \"bg-yummy-grilled/10 border-yummy-grilled/20\",\n      textColor: \"text-yummy-grilled\",\n      path: \"/produits-grilles\"\n    },\n    {\n      id: 2,\n      name: \"Produits Non-Grillés\",\n      description: \"Salades, sandwichs et plats préparés\",\n      icon: <FaPizzaSlice className=\"text-4xl text-yummy-nongrilled\" />,\n      color: \"bg-yummy-nongrilled/10 border-yummy-nongrilled/20\",\n      textColor: \"text-yummy-nongrilled\",\n      path: \"/produits-non-grilles\"\n    },\n    {\n      id: 3,\n      name: \"Fromages\",\n      description: \"Fromages locaux et importés de qualité\",\n      icon: <FaCheese className=\"text-4xl text-yummy-cheese\" />,\n      color: \"bg-yummy-cheese/10 border-yummy-cheese/20\",\n      textColor: \"text-yummy-cheese\",\n      path: \"/fromages\"\n    },\n    {\n      id: 4,\n      name: \"Boissons\",\n      description: \"Boissons fraîches, chaudes et alcoolisées\",\n      icon: <FaCocktail className=\"text-4xl text-yummy-drinks\" />,\n      color: \"bg-yummy-drinks/10 border-yummy-drinks/20\",\n      textColor: \"text-yummy-drinks\",\n      path: \"/boissons\"\n    }\n  ];\n\n  // Composant Hero Section\n  const HeroSection = () => (\n    <section className=\"relative bg-gradient-to-r from-green-50 to-green-100 py-16 md:py-24\">\n      <div className=\"container\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8 items-center\">\n          <div className=\"space-y-6\">\n            <motion.h1\n              className=\"text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.5 }}\n            >\n              Des produits frais pour <span className=\"text-primary-600\">tous vos repas</span>\n            </motion.h1>\n\n            <motion.p\n              className=\"text-lg text-gray-700\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.5, delay: 0.1 }}\n            >\n              Découvrez notre sélection de produits à griller, non-grillés, fromages et boissons de qualité pour satisfaire toutes vos envies gourmandes.\n            </motion.p>\n\n            <motion.div\n              className=\"flex flex-wrap gap-4\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.5, delay: 0.2 }}\n            >\n              <Link to=\"/products\" className=\"btn-primary\">\n                Découvrir nos produits\n              </Link>\n              <Link to=\"/about\" className=\"btn-outline\">\n                En savoir plus\n              </Link>\n            </motion.div>\n          </div>\n\n          <motion.div\n            className=\"relative\"\n            initial={{ opacity: 0, scale: 0.9 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ duration: 0.5 }}\n          >\n            <img\n              src=\"https://images.unsplash.com/photo-1504674900247-0877df9cc836?ixlib=rb-1.2.1&auto=format&fit=crop&w=1200&q=80\"\n              alt=\"Produits frais\"\n              className=\"rounded-lg shadow-xl\"\n            />\n            <div className=\"absolute -bottom-6 -right-6 bg-white p-4 rounded-lg shadow-lg\">\n              <div className=\"flex items-center gap-2\">\n                <div className=\"bg-green-100 p-2 rounded-full\">\n                  <FaUtensils className=\"text-primary-600 text-xl\" />\n                </div>\n                <div>\n                  <p className=\"font-semibold text-gray-900\">Livraison rapide</p>\n                  <p className=\"text-sm text-gray-600\">En 24h chez vous</p>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  );\n\n  // Composant Category List\n  const CategoryList = () => (\n    <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mt-8\">\n      {categories.map((category) => (\n        <motion.div\n          key={category.id}\n          className={`card p-6 border ${category.color} hover:shadow-lg transition-shadow`}\n          whileHover={{ y: -5 }}\n          transition={{ duration: 0.3 }}\n        >\n          <div className=\"flex flex-col items-center text-center\">\n            <div className={`p-4 rounded-full ${category.color} mb-4`}>\n              {category.icon}\n            </div>\n            <h3 className={`text-xl font-semibold mb-2 ${category.textColor}`}>\n              {category.name}\n            </h3>\n            <p className=\"text-gray-600 mb-4\">\n              {category.description}\n            </p>\n            <Link\n              to={category.path}\n              className={`flex items-center ${category.textColor} font-medium hover:underline`}\n            >\n              Découvrir <FaArrowRight className=\"ml-2\" />\n            </Link>\n          </div>\n        </motion.div>\n      ))}\n    </div>\n  );\n\n  // Composant Featured Products\n  const FeaturedProducts = () => (\n    <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mt-8\">\n      {isLoading ? (\n        Array(4).fill(0).map((_, index) => (\n          <div key={index} className=\"card animate-pulse\">\n            <div className=\"bg-gray-300 aspect-square w-full\"></div>\n            <div className=\"p-4 space-y-3\">\n              <div className=\"h-4 bg-gray-300 rounded w-1/4\"></div>\n              <div className=\"h-6 bg-gray-300 rounded w-3/4\"></div>\n              <div className=\"h-4 bg-gray-300 rounded w-1/2\"></div>\n            </div>\n          </div>\n        ))\n      ) : (\n        featuredProducts.map(product => (\n          <ProductCard key={product.id} product={product} />\n        ))\n      )}\n    </div>\n  );\n\n  // Composant Promo Section\n  const PromoSection = () => (\n    <section className=\"py-16 bg-gradient-to-r from-secondary-500 to-secondary-600 text-white\">\n      <div className=\"container\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8 items-center\">\n          <div>\n            <h2 className=\"text-3xl md:text-4xl font-bold mb-4\">Offre spéciale du moment</h2>\n            <p className=\"text-lg mb-6\">\n              Profitez de 15% de réduction sur tous nos produits à griller pour vos barbecues d'été !\n            </p>\n            <div className=\"flex flex-wrap gap-4\">\n              <Link to=\"/produits-grilles\" className=\"bg-white text-secondary-600 px-6 py-3 rounded-full font-medium hover:bg-gray-100 transition-colors\">\n                En profiter maintenant\n              </Link>\n            </div>\n          </div>\n\n          <div className=\"relative\">\n            <img\n              src=\"https://images.unsplash.com/photo-1544025162-d76694265947?ixlib=rb-1.2.1&auto=format&fit=crop&w=1200&q=80\"\n              alt=\"Barbecue\"\n              className=\"rounded-lg shadow-xl\"\n            />\n            <div className=\"absolute top-0 right-0 bg-accent-500 text-white text-xl font-bold p-4 rounded-bl-lg rounded-tr-lg\">\n              -15%\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n\n  return (\n    <div className=\"bg-white\">\n      {/* Bandeau d'accès rapide admin pour la démonstration */}\n      <div className=\"bg-gradient-to-r from-green-600 to-green-700 text-white p-3\">\n        <div className=\"container mx-auto flex justify-between items-center\">\n          <div className=\"flex items-center space-x-2\">\n            <svg className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n            </svg>\n            <span className=\"text-sm font-medium\">Mode Démonstration - Projet YUMMY</span>\n          </div>\n          <div className=\"flex items-center space-x-4\">\n            <a\n              href=\"/admin/dashboard\"\n              className=\"bg-white text-green-600 px-4 py-1 rounded text-sm font-medium hover:bg-gray-100 transition-colors\"\n            >\n              🔧 Dashboard Admin\n            </a>\n            <span className=\"text-sm opacity-75\">Toutes les fonctionnalités sont accessibles</span>\n          </div>\n        </div>\n      </div>\n\n      <HeroSection />\n\n      <section className=\"py-16 bg-white\">\n        <div className=\"container\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center mb-8\">\n            <h2 className=\"text-3xl font-bold text-gray-900\">Nos catégories</h2>\n            <Link to=\"/products\" className=\"flex items-center text-primary-600 font-medium hover:text-primary-700\">\n              Voir tous les produits <FaArrowRight className=\"ml-2\" />\n            </Link>\n          </div>\n          <CategoryList />\n        </div>\n      </section>\n\n      <section className=\"py-16 bg-gray-50\">\n        <div className=\"container\">\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-8\">Produits en vedette</h2>\n          <FeaturedProducts />\n        </div>\n      </section>\n\n      <PromoSection />\n\n      <section className=\"py-16 bg-gray-50\">\n        <div className=\"container\">\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-8\">Promotions en cours</h2>\n          <ActivePromotions />\n        </div>\n      </section>\n\n      <section className=\"py-16 bg-white\">\n        <div className=\"container\">\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-8\">Nouveaux produits</h2>\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6\">\n            {isLoading ? (\n              Array(4).fill(0).map((_, index) => (\n                <div key={index} className=\"card animate-pulse\">\n                  <div className=\"bg-gray-300 aspect-square w-full\"></div>\n                  <div className=\"p-4 space-y-3\">\n                    <div className=\"h-4 bg-gray-300 rounded w-1/4\"></div>\n                    <div className=\"h-6 bg-gray-300 rounded w-3/4\"></div>\n                    <div className=\"h-4 bg-gray-300 rounded w-1/2\"></div>\n                  </div>\n                </div>\n              ))\n            ) : (\n              newProducts.map(product => (\n                <ProductCard key={product.id} product={product} />\n              ))\n            )}\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n};\n\nexport default HomePage;\n\n\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,WAAW,MAAM,oCAAoC;AAC5D,OAAOC,gBAAgB,MAAM,2CAA2C;AACxE,SAASC,YAAY,EAAEC,UAAU,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,UAAU,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9F,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM;IAAEC;EAAM,CAAC,GAAGZ,OAAO,CAAC,CAAC;EAC3B,MAAM;IAAEa,QAAQ,EAAEC,WAAW;IAAEC,UAAU,EAAEC,aAAa;IAAEC,OAAO,EAAEC;EAAY,CAAC,GAAGN,KAAK;EAExF,MAAM,CAACO,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACyB,WAAW,EAAEC,cAAc,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC2B,SAAS,EAAEC,YAAY,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC6B,eAAe,EAAEC,kBAAkB,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAE7DC,SAAS,CAAC,MAAM;IACd,MAAM8B,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAI;QACF;QACA,IAAIb,WAAW,IAAIA,WAAW,CAACc,MAAM,GAAG,CAAC,EAAE;UACzCR,mBAAmB,CAACN,WAAW,CAACe,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UAC5CP,cAAc,CAACR,WAAW,CAACe,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;UACrCL,YAAY,CAAC,KAAK,CAAC;UACnB;QACF;;QAEA;QACAM,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;;QAEvD;QACAP,YAAY,CAAC,KAAK,CAAC;MACrB,CAAC,CAAC,OAAOQ,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;;QAE/D;QACA,MAAMC,YAAY,GAAG,CACnB;UACEC,EAAE,EAAE,CAAC;UACLC,IAAI,EAAE,sBAAsB;UAC5BC,WAAW,EAAE,kEAAkE;UAC/EC,iBAAiB,EAAE,iCAAiC;UACpDC,KAAK,EAAE,KAAK;UACZC,UAAU,EAAE,IAAI;UAChBC,KAAK,EAAE,6GAA6G;UACpHC,QAAQ,EAAE;YAAEN,IAAI,EAAE;UAAmB,CAAC;UACtCO,WAAW,EAAE,IAAI;UACjBC,SAAS,EAAE,IAAI;UACfC,cAAc,EAAE;QAClB,CAAC,EACD;UACEV,EAAE,EAAE,CAAC;UACLC,IAAI,EAAE,oBAAoB;UAC1BC,WAAW,EAAE,kEAAkE;UAC/EC,iBAAiB,EAAE,6BAA6B;UAChDC,KAAK,EAAE,KAAK;UACZC,UAAU,EAAE,KAAK;UACjBC,KAAK,EAAE,6GAA6G;UACpHC,QAAQ,EAAE;YAAEN,IAAI,EAAE;UAAmB,CAAC;UACtCO,WAAW,EAAE,IAAI;UACjBC,SAAS,EAAE,IAAI;UACfC,cAAc,EAAE;QAClB,CAAC,EACD;UACEV,EAAE,EAAE,CAAC;UACLC,IAAI,EAAE,cAAc;UACpBC,WAAW,EAAE,gEAAgE;UAC7EC,iBAAiB,EAAE,wBAAwB;UAC3CC,KAAK,EAAE,KAAK;UACZC,UAAU,EAAE,IAAI;UAChBC,KAAK,EAAE,6GAA6G;UACpHC,QAAQ,EAAE;YAAEN,IAAI,EAAE;UAAU,CAAC;UAC7BO,WAAW,EAAE,IAAI;UACjBC,SAAS,EAAE,IAAI;UACfC,cAAc,EAAE;QAClB,CAAC,EACD;UACEV,EAAE,EAAE,CAAC;UACLC,IAAI,EAAE,qBAAqB;UAC3BC,WAAW,EAAE,yDAAyD;UACtEC,iBAAiB,EAAE,gCAAgC;UACnDC,KAAK,EAAE,KAAK;UACZC,UAAU,EAAE,IAAI;UAChBC,KAAK,EAAE,6GAA6G;UACpHC,QAAQ,EAAE;YAAEN,IAAI,EAAE;UAAW,CAAC;UAC9BO,WAAW,EAAE,IAAI;UACjBC,SAAS,EAAE,IAAI;UACfC,cAAc,EAAE;QAClB,CAAC,CACF;QAEDxB,mBAAmB,CAACa,YAAY,CAAC;QACjCX,cAAc,CAACW,YAAY,CAAC;QAC5BP,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC;QAC3BI,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;QACnEP,YAAY,CAAC,KAAK,CAAC;MACrB;IACF,CAAC;;IAED;IACA,IAAIV,WAAW,IAAIA,WAAW,CAACc,MAAM,GAAG,CAAC,EAAE;MACzCR,mBAAmB,CAACN,WAAW,CAACe,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAC5CP,cAAc,CAACR,WAAW,CAACe,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;MACrCL,YAAY,CAAC,KAAK,CAAC;MACnBM,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;IAC3D,CAAC,MAAM;MACLJ,aAAa,CAAC,CAAC;IACjB;EACF,CAAC,EAAE,CAACb,WAAW,CAAC,CAAC;EAEjB,MAAMC,UAAU,GAAG,CACjB;IACEmB,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,oBAAoB;IAC1BC,WAAW,EAAE,mDAAmD;IAChES,IAAI,eAAEpC,OAAA,CAACL,UAAU;MAAC0C,SAAS,EAAC;IAA6B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC5DC,KAAK,EAAE,6CAA6C;IACpDC,SAAS,EAAE,oBAAoB;IAC/BC,IAAI,EAAE;EACR,CAAC,EACD;IACEnB,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,sBAAsB;IAC5BC,WAAW,EAAE,sCAAsC;IACnDS,IAAI,eAAEpC,OAAA,CAACJ,YAAY;MAACyC,SAAS,EAAC;IAAgC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACjEC,KAAK,EAAE,mDAAmD;IAC1DC,SAAS,EAAE,uBAAuB;IAClCC,IAAI,EAAE;EACR,CAAC,EACD;IACEnB,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,UAAU;IAChBC,WAAW,EAAE,wCAAwC;IACrDS,IAAI,eAAEpC,OAAA,CAACH,QAAQ;MAACwC,SAAS,EAAC;IAA4B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzDC,KAAK,EAAE,2CAA2C;IAClDC,SAAS,EAAE,mBAAmB;IAC9BC,IAAI,EAAE;EACR,CAAC,EACD;IACEnB,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,UAAU;IAChBC,WAAW,EAAE,2CAA2C;IACxDS,IAAI,eAAEpC,OAAA,CAACF,UAAU;MAACuC,SAAS,EAAC;IAA4B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC3DC,KAAK,EAAE,2CAA2C;IAClDC,SAAS,EAAE,mBAAmB;IAC9BC,IAAI,EAAE;EACR,CAAC,CACF;;EAED;EACA,MAAMC,WAAW,GAAGA,CAAA,kBAClB7C,OAAA;IAASqC,SAAS,EAAC,qEAAqE;IAAAS,QAAA,eACtF9C,OAAA;MAAKqC,SAAS,EAAC,WAAW;MAAAS,QAAA,eACxB9C,OAAA;QAAKqC,SAAS,EAAC,oDAAoD;QAAAS,QAAA,gBACjE9C,OAAA;UAAKqC,SAAS,EAAC,WAAW;UAAAS,QAAA,gBACxB9C,OAAA,CAACV,MAAM,CAACyD,EAAE;YACRV,SAAS,EAAC,0DAA0D;YACpEW,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAAAP,QAAA,GAC/B,0BACyB,eAAA9C,OAAA;cAAMqC,SAAS,EAAC,kBAAkB;cAAAS,QAAA,EAAC;YAAc;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC,eAEZzC,OAAA,CAACV,MAAM,CAACgE,CAAC;YACPjB,SAAS,EAAC,uBAAuB;YACjCW,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEE,KAAK,EAAE;YAAI,CAAE;YAAAT,QAAA,EAC3C;UAED;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eAEXzC,OAAA,CAACV,MAAM,CAACkE,GAAG;YACTnB,SAAS,EAAC,sBAAsB;YAChCW,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEE,KAAK,EAAE;YAAI,CAAE;YAAAT,QAAA,gBAE1C9C,OAAA,CAACX,IAAI;cAACoE,EAAE,EAAC,WAAW;cAACpB,SAAS,EAAC,aAAa;cAAAS,QAAA,EAAC;YAE7C;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPzC,OAAA,CAACX,IAAI;cAACoE,EAAE,EAAC,QAAQ;cAACpB,SAAS,EAAC,aAAa;cAAAS,QAAA,EAAC;YAE1C;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAENzC,OAAA,CAACV,MAAM,CAACkE,GAAG;UACTnB,SAAS,EAAC,UAAU;UACpBW,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAES,KAAK,EAAE;UAAI,CAAE;UACpCP,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAES,KAAK,EAAE;UAAE,CAAE;UAClCN,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAAAP,QAAA,gBAE9B9C,OAAA;YACE2D,GAAG,EAAC,8GAA8G;YAClHC,GAAG,EAAC,gBAAgB;YACpBvB,SAAS,EAAC;UAAsB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eACFzC,OAAA;YAAKqC,SAAS,EAAC,+DAA+D;YAAAS,QAAA,eAC5E9C,OAAA;cAAKqC,SAAS,EAAC,yBAAyB;cAAAS,QAAA,gBACtC9C,OAAA;gBAAKqC,SAAS,EAAC,+BAA+B;gBAAAS,QAAA,eAC5C9C,OAAA,CAACL,UAAU;kBAAC0C,SAAS,EAAC;gBAA0B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,eACNzC,OAAA;gBAAA8C,QAAA,gBACE9C,OAAA;kBAAGqC,SAAS,EAAC,6BAA6B;kBAAAS,QAAA,EAAC;gBAAgB;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAC/DzC,OAAA;kBAAGqC,SAAS,EAAC,uBAAuB;kBAAAS,QAAA,EAAC;gBAAgB;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CACV;;EAED;EACA,MAAMoB,YAAY,GAAGA,CAAA,kBACnB7D,OAAA;IAAKqC,SAAS,EAAC,2DAA2D;IAAAS,QAAA,EACvExC,UAAU,CAACwD,GAAG,CAAE9B,QAAQ,iBACvBhC,OAAA,CAACV,MAAM,CAACkE,GAAG;MAETnB,SAAS,EAAE,mBAAmBL,QAAQ,CAACU,KAAK,oCAAqC;MACjFqB,UAAU,EAAE;QAAEb,CAAC,EAAE,CAAC;MAAE,CAAE;MACtBE,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE;MAAAP,QAAA,eAE9B9C,OAAA;QAAKqC,SAAS,EAAC,wCAAwC;QAAAS,QAAA,gBACrD9C,OAAA;UAAKqC,SAAS,EAAE,oBAAoBL,QAAQ,CAACU,KAAK,OAAQ;UAAAI,QAAA,EACvDd,QAAQ,CAACI;QAAI;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eACNzC,OAAA;UAAIqC,SAAS,EAAE,8BAA8BL,QAAQ,CAACW,SAAS,EAAG;UAAAG,QAAA,EAC/Dd,QAAQ,CAACN;QAAI;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eACLzC,OAAA;UAAGqC,SAAS,EAAC,oBAAoB;UAAAS,QAAA,EAC9Bd,QAAQ,CAACL;QAAW;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eACJzC,OAAA,CAACX,IAAI;UACHoE,EAAE,EAAEzB,QAAQ,CAACY,IAAK;UAClBP,SAAS,EAAE,qBAAqBL,QAAQ,CAACW,SAAS,8BAA+B;UAAAG,QAAA,GAClF,eACW,eAAA9C,OAAA,CAACN,YAAY;YAAC2C,SAAS,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC,GArBDT,QAAQ,CAACP,EAAE;MAAAa,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAsBN,CACb;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CACN;;EAED;EACA,MAAMuB,gBAAgB,GAAGA,CAAA,kBACvBhE,OAAA;IAAKqC,SAAS,EAAC,2DAA2D;IAAAS,QAAA,EACvEhC,SAAS,GACRmD,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAACJ,GAAG,CAAC,CAACK,CAAC,EAAEC,KAAK,kBAC5BpE,OAAA;MAAiBqC,SAAS,EAAC,oBAAoB;MAAAS,QAAA,gBAC7C9C,OAAA;QAAKqC,SAAS,EAAC;MAAkC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACxDzC,OAAA;QAAKqC,SAAS,EAAC,eAAe;QAAAS,QAAA,gBAC5B9C,OAAA;UAAKqC,SAAS,EAAC;QAA+B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACrDzC,OAAA;UAAKqC,SAAS,EAAC;QAA+B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACrDzC,OAAA;UAAKqC,SAAS,EAAC;QAA+B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CAAC;IAAA,GANE2B,KAAK;MAAA9B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAOV,CACN,CAAC,GAEF/B,gBAAgB,CAACoD,GAAG,CAACO,OAAO,iBAC1BrE,OAAA,CAACR,WAAW;MAAkB6E,OAAO,EAAEA;IAAQ,GAA7BA,OAAO,CAAC5C,EAAE;MAAAa,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAqB,CAClD;EACF;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CACN;;EAED;EACA,MAAM6B,YAAY,GAAGA,CAAA,kBACnBtE,OAAA;IAASqC,SAAS,EAAC,uEAAuE;IAAAS,QAAA,eACxF9C,OAAA;MAAKqC,SAAS,EAAC,WAAW;MAAAS,QAAA,eACxB9C,OAAA;QAAKqC,SAAS,EAAC,oDAAoD;QAAAS,QAAA,gBACjE9C,OAAA;UAAA8C,QAAA,gBACE9C,OAAA;YAAIqC,SAAS,EAAC,qCAAqC;YAAAS,QAAA,EAAC;UAAwB;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjFzC,OAAA;YAAGqC,SAAS,EAAC,cAAc;YAAAS,QAAA,EAAC;UAE5B;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJzC,OAAA;YAAKqC,SAAS,EAAC,sBAAsB;YAAAS,QAAA,eACnC9C,OAAA,CAACX,IAAI;cAACoE,EAAE,EAAC,mBAAmB;cAACpB,SAAS,EAAC,oGAAoG;cAAAS,QAAA,EAAC;YAE5I;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENzC,OAAA;UAAKqC,SAAS,EAAC,UAAU;UAAAS,QAAA,gBACvB9C,OAAA;YACE2D,GAAG,EAAC,2GAA2G;YAC/GC,GAAG,EAAC,UAAU;YACdvB,SAAS,EAAC;UAAsB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eACFzC,OAAA;YAAKqC,SAAS,EAAC,mGAAmG;YAAAS,QAAA,EAAC;UAEnH;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CACV;EAED,oBACEzC,OAAA;IAAKqC,SAAS,EAAC,UAAU;IAAAS,QAAA,gBAEvB9C,OAAA;MAAKqC,SAAS,EAAC,6DAA6D;MAAAS,QAAA,eAC1E9C,OAAA;QAAKqC,SAAS,EAAC,qDAAqD;QAAAS,QAAA,gBAClE9C,OAAA;UAAKqC,SAAS,EAAC,6BAA6B;UAAAS,QAAA,gBAC1C9C,OAAA;YAAKqC,SAAS,EAAC,SAAS;YAAC6B,IAAI,EAAC,MAAM;YAACK,OAAO,EAAC,WAAW;YAACC,MAAM,EAAC,cAAc;YAAA1B,QAAA,eAC5E9C,OAAA;cAAMyE,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAA+C;cAAAtC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpH,CAAC,eACNzC,OAAA;YAAMqC,SAAS,EAAC,qBAAqB;YAAAS,QAAA,EAAC;UAAiC;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3E,CAAC,eACNzC,OAAA;UAAKqC,SAAS,EAAC,6BAA6B;UAAAS,QAAA,gBAC1C9C,OAAA;YACE6E,IAAI,EAAC,kBAAkB;YACvBxC,SAAS,EAAC,mGAAmG;YAAAS,QAAA,EAC9G;UAED;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJzC,OAAA;YAAMqC,SAAS,EAAC,oBAAoB;YAAAS,QAAA,EAAC;UAA2C;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENzC,OAAA,CAAC6C,WAAW;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEfzC,OAAA;MAASqC,SAAS,EAAC,gBAAgB;MAAAS,QAAA,eACjC9C,OAAA;QAAKqC,SAAS,EAAC,WAAW;QAAAS,QAAA,gBACxB9C,OAAA;UAAKqC,SAAS,EAAC,6DAA6D;UAAAS,QAAA,gBAC1E9C,OAAA;YAAIqC,SAAS,EAAC,kCAAkC;YAAAS,QAAA,EAAC;UAAc;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpEzC,OAAA,CAACX,IAAI;YAACoE,EAAE,EAAC,WAAW;YAACpB,SAAS,EAAC,uEAAuE;YAAAS,QAAA,GAAC,yBAC9E,eAAA9C,OAAA,CAACN,YAAY;cAAC2C,SAAS,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNzC,OAAA,CAAC6D,YAAY;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEVzC,OAAA;MAASqC,SAAS,EAAC,kBAAkB;MAAAS,QAAA,eACnC9C,OAAA;QAAKqC,SAAS,EAAC,WAAW;QAAAS,QAAA,gBACxB9C,OAAA;UAAIqC,SAAS,EAAC,uCAAuC;UAAAS,QAAA,EAAC;QAAmB;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9EzC,OAAA,CAACgE,gBAAgB;UAAA1B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEVzC,OAAA,CAACsE,YAAY;MAAAhC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEhBzC,OAAA;MAASqC,SAAS,EAAC,kBAAkB;MAAAS,QAAA,eACnC9C,OAAA;QAAKqC,SAAS,EAAC,WAAW;QAAAS,QAAA,gBACxB9C,OAAA;UAAIqC,SAAS,EAAC,uCAAuC;UAAAS,QAAA,EAAC;QAAmB;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9EzC,OAAA,CAACP,gBAAgB;UAAA6C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEVzC,OAAA;MAASqC,SAAS,EAAC,gBAAgB;MAAAS,QAAA,eACjC9C,OAAA;QAAKqC,SAAS,EAAC,WAAW;QAAAS,QAAA,gBACxB9C,OAAA;UAAIqC,SAAS,EAAC,uCAAuC;UAAAS,QAAA,EAAC;QAAiB;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5EzC,OAAA;UAAKqC,SAAS,EAAC,sDAAsD;UAAAS,QAAA,EAClEhC,SAAS,GACRmD,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAACJ,GAAG,CAAC,CAACK,CAAC,EAAEC,KAAK,kBAC5BpE,OAAA;YAAiBqC,SAAS,EAAC,oBAAoB;YAAAS,QAAA,gBAC7C9C,OAAA;cAAKqC,SAAS,EAAC;YAAkC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxDzC,OAAA;cAAKqC,SAAS,EAAC,eAAe;cAAAS,QAAA,gBAC5B9C,OAAA;gBAAKqC,SAAS,EAAC;cAA+B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACrDzC,OAAA;gBAAKqC,SAAS,EAAC;cAA+B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACrDzC,OAAA;gBAAKqC,SAAS,EAAC;cAA+B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC;UAAA,GANE2B,KAAK;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAOV,CACN,CAAC,GAEF7B,WAAW,CAACkD,GAAG,CAACO,OAAO,iBACrBrE,OAAA,CAACR,WAAW;YAAkB6E,OAAO,EAAEA;UAAQ,GAA7BA,OAAO,CAAC5C,EAAE;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAqB,CAClD;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACvC,EAAA,CArXID,QAAQ;EAAA,QACMV,OAAO;AAAA;AAAAuF,EAAA,GADrB7E,QAAQ;AAuXd,eAAeA,QAAQ;AAAC,IAAA6E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}