<?php

use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\UserController;
use App\Http\Controllers\Api\AddressController;
use App\Http\Controllers\Api\OrderController;
use App\Http\Controllers\Api\ReviewController;
use App\Http\Controllers\Api\ProductController;
use App\Http\Controllers\Api\CategoryController;
use App\Http\Controllers\Api\PromotionController;
use App\Http\Controllers\Api\DashboardController;

use Illuminate\Support\Facades\Route;

// Routes d'authentification
Route::post('/register', [AuthController::class, 'register']);
Route::post('/login', [AuthController::class, 'login']);
Route::middleware('auth:sanctum')->group(function () {
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::get('/me', [AuthController::class, 'me']);
});

// Routes de gestion des utilisateurs (admin seulement)
Route::middleware(['auth:sanctum', 'admin'])->group(function () {
    Route::apiResource('users', UserController::class);
});

// Products - routes publiques
Route::get('products', [ProductController::class, 'index']);
Route::get('products/{product}', [ProductController::class, 'show']);

// Products - routes protégées (admin)
Route::middleware(['auth:sanctum', 'admin'])->group(function () {
    Route::post('products', [ProductController::class, 'store']);
    Route::put('products/{product}', [ProductController::class, 'update']);
    Route::delete('products/{product}', [ProductController::class, 'destroy']);
});

// Categories
Route::get('categories', [CategoryController::class, 'index']);
Route::get('categories/{category}', [CategoryController::class, 'show']);

// Addresses
Route::middleware('auth:sanctum')->group(function () {
    Route::apiResource('addresses', AddressController::class);
});

// Orders
Route::middleware('auth:sanctum')->group(function () {
    Route::apiResource('orders', OrderController::class)->except(['update', 'destroy']);
    Route::post('orders/{order}/cancel', [OrderController::class, 'cancel']);
});

// Reviews
Route::get('products/{product}/reviews', [ReviewController::class, 'index']);
Route::middleware('auth:sanctum')->group(function () {
    Route::post('products/{product}/reviews', [ReviewController::class, 'store']);
    Route::put('reviews/{review}', [ReviewController::class, 'update']);
    Route::delete('reviews/{review}', [ReviewController::class, 'destroy']);
});

// Promotions - routes publiques
Route::get('promotions', [PromotionController::class, 'index']);
Route::post('promotions/validate', [PromotionController::class, 'validateCode']);

// Promotions - routes protégées (admin)
Route::middleware(['auth:sanctum', 'admin'])->group(function () {
    Route::apiResource('promotions', PromotionController::class)->except(['index']);
});

// Dashboard - routes protégées (admin)
Route::middleware(['auth:sanctum', 'admin'])->group(function () {
    Route::get('dashboard/stats', [DashboardController::class, 'getStats']);
    Route::get('dashboard/activity', [DashboardController::class, 'getRecentActivity']);
});

// Dashboard - routes publiques pour démonstration (à supprimer en production)
Route::prefix('admin/dashboard')->group(function () {
    Route::get('/stats', [DashboardController::class, 'getStats']);
    Route::get('/activity', [DashboardController::class, 'getRecentActivity']);
    Route::get('/charts', [DashboardController::class, 'getStats']); // Réutilise getStats
    Route::get('/low-stock', function () {
        return response()->json([
            'success' => true,
            'data' => [
                [
                    'id' => 1,
                    'name' => 'Poulet Grillé Entier',
                    'stock' => 3,
                    'min_stock' => 10,
                    'category' => 'Produits Grillés',
                    'price' => 89.99
                ],
                [
                    'id' => 2,
                    'name' => 'Brochettes de Bœuf',
                    'stock' => 2,
                    'min_stock' => 8,
                    'category' => 'Produits Grillés',
                    'price' => 65.50
                ],
                [
                    'id' => 3,
                    'name' => 'Fromage de Chèvre',
                    'stock' => 1,
                    'min_stock' => 5,
                    'category' => 'Fromages',
                    'price' => 45.00
                ]
            ]
        ]);
    });
    Route::get('/recent-orders', function () {
        return response()->json([
            'success' => true,
            'data' => [
                [
                    'id' => 1234,
                    'user' => [
                        'name' => 'Marie Dubois',
                        'email' => '<EMAIL>'
                    ],
                    'total' => 89.50,
                    'status' => 'pending',
                    'created_at' => '2024-01-15T10:30:00Z',
                    'items_count' => 3
                ],
                [
                    'id' => 1233,
                    'user' => [
                        'name' => 'Ahmed Benali',
                        'email' => '<EMAIL>'
                    ],
                    'total' => 156.75,
                    'status' => 'completed',
                    'created_at' => '2024-01-15T09:15:00Z',
                    'items_count' => 5
                ]
            ]
        ]);
    });
    Route::get('/health', function () {
        return response()->json([
            'success' => true,
            'message' => 'API Dashboard opérationnelle',
            'timestamp' => now()->toISOString()
        ]);
    });
});