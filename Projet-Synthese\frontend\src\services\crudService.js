import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

// Service générique CRUD
class CrudService {
  constructor(endpoint) {
    this.endpoint = endpoint;
    this.baseURL = `${API_BASE_URL}/api/${endpoint}`;
  }

  // GET - Récupérer tous les éléments
  async getAll(params = {}) {
    try {
      const response = await axios.get(this.baseURL, { params });
      return {
        success: true,
        data: response.data.data || response.data,
        meta: response.data.meta || null
      };
    } catch (error) {
      console.error(`Erreur lors de la récupération des ${this.endpoint}:`, error);
      return {
        success: false,
        error: error.response?.data?.message || 'Erreur de connexion'
      };
    }
  }

  // GET - Récupérer un élément par ID
  async getById(id) {
    try {
      const response = await axios.get(`${this.baseURL}/${id}`);
      return {
        success: true,
        data: response.data.data || response.data
      };
    } catch (error) {
      console.error(`Erreur lors de la récupération de ${this.endpoint}/${id}:`, error);
      return {
        success: false,
        error: error.response?.data?.message || 'Erreur de connexion'
      };
    }
  }

  // POST - Créer un nouvel élément
  async create(data) {
    try {
      const response = await axios.post(this.baseURL, data, {
        headers: {
          'Content-Type': data instanceof FormData ? 'multipart/form-data' : 'application/json'
        }
      });
      return {
        success: true,
        data: response.data.data || response.data
      };
    } catch (error) {
      console.error(`Erreur lors de la création de ${this.endpoint}:`, error);
      return {
        success: false,
        error: error.response?.data?.message || 'Erreur de connexion'
      };
    }
  }

  // PUT/PATCH - Mettre à jour un élément
  async update(id, data) {
    try {
      const response = await axios.put(`${this.baseURL}/${id}`, data, {
        headers: {
          'Content-Type': data instanceof FormData ? 'multipart/form-data' : 'application/json'
        }
      });
      return {
        success: true,
        data: response.data.data || response.data
      };
    } catch (error) {
      console.error(`Erreur lors de la mise à jour de ${this.endpoint}/${id}:`, error);
      return {
        success: false,
        error: error.response?.data?.message || 'Erreur de connexion'
      };
    }
  }

  // DELETE - Supprimer un élément
  async delete(id) {
    try {
      await axios.delete(`${this.baseURL}/${id}`);
      return {
        success: true,
        message: 'Élément supprimé avec succès'
      };
    } catch (error) {
      console.error(`Erreur lors de la suppression de ${this.endpoint}/${id}:`, error);
      return {
        success: false,
        error: error.response?.data?.message || 'Erreur de connexion'
      };
    }
  }
}

// Services spécifiques pour chaque entité
export const productService = new CrudService('admin/products');
export const categoryService = new CrudService('admin/categories');
export const orderService = new CrudService('admin/orders');
export const userService = new CrudService('admin/users');
export const promotionService = new CrudService('admin/promotions');

// Service spécialisé pour les produits avec fonctionnalités supplémentaires
export class ProductService extends CrudService {
  constructor() {
    super('admin/products');
  }

  // Rechercher des produits
  async search(query, filters = {}) {
    try {
      const params = {
        search: query,
        ...filters
      };
      return await this.getAll(params);
    } catch (error) {
      return {
        success: false,
        error: 'Erreur lors de la recherche'
      };
    }
  }

  // Mettre à jour le stock
  async updateStock(id, stock) {
    try {
      const response = await axios.patch(`${this.baseURL}/${id}/stock`, { stock });
      return {
        success: true,
        data: response.data.data || response.data
      };
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.message || 'Erreur de connexion'
      };
    }
  }

  // Activer/désactiver un produit
  async toggleStatus(id, status) {
    try {
      const response = await axios.patch(`${this.baseURL}/${id}/status`, { status });
      return {
        success: true,
        data: response.data.data || response.data
      };
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.message || 'Erreur de connexion'
      };
    }
  }
}

// Service spécialisé pour les commandes
export class OrderService extends CrudService {
  constructor() {
    super('admin/orders');
  }

  // Mettre à jour le statut d'une commande
  async updateStatus(id, status) {
    try {
      const response = await axios.patch(`${this.baseURL}/${id}/status`, { status });
      return {
        success: true,
        data: response.data.data || response.data
      };
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.message || 'Erreur de connexion'
      };
    }
  }

  // Récupérer les commandes par statut
  async getByStatus(status) {
    try {
      return await this.getAll({ status });
    } catch (error) {
      return {
        success: false,
        error: 'Erreur lors de la récupération des commandes'
      };
    }
  }
}

// Service spécialisé pour les promotions
export class PromotionService extends CrudService {
  constructor() {
    super('admin/promotions');
  }

  // Activer/désactiver une promotion
  async toggleStatus(id, status) {
    try {
      const response = await axios.patch(`${this.baseURL}/${id}/status`, { status });
      return {
        success: true,
        data: response.data.data || response.data
      };
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.message || 'Erreur de connexion'
      };
    }
  }

  // Récupérer les promotions actives
  async getActive() {
    try {
      return await this.getAll({ status: 'active' });
    } catch (error) {
      return {
        success: false,
        error: 'Erreur lors de la récupération des promotions actives'
      };
    }
  }
}

// Instances des services spécialisés
export const productServiceAdvanced = new ProductService();
export const orderServiceAdvanced = new OrderService();
export const promotionServiceAdvanced = new PromotionService();

export default CrudService;
