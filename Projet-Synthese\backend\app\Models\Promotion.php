<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class Promotion extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'code',
        'description',
        'type',
        'value',
        'minimum_amount',
        'maximum_discount',
        'usage_limit',
        'used_count',
        'start_date',
        'end_date',
        'is_active'
    ];

    protected $casts = [
        'value' => 'decimal:2',
        'minimum_amount' => 'decimal:2',
        'maximum_discount' => 'decimal:2',
        'usage_limit' => 'integer',
        'used_count' => 'integer',
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'is_active' => 'boolean'
    ];

    // Types de promotions
    const TYPE_PERCENTAGE = 'percentage';
    const TYPE_FIXED = 'fixed';
    const TYPE_FREE_SHIPPING = 'free_shipping';

    // Relations
    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeValid($query)
    {
        $now = Carbon::now();
        return $query->where('is_active', true)
                    ->where('start_date', '<=', $now)
                    ->where('end_date', '>=', $now)
                    ->whereRaw('used_count < usage_limit OR usage_limit IS NULL');
    }

    public function scopeByCode($query, $code)
    {
        return $query->where('code', strtoupper($code));
    }

    // Accessors
    public function getIsValidAttribute(): bool
    {
        $now = Carbon::now();
        
        return $this->is_active &&
               $this->start_date <= $now &&
               $this->end_date >= $now &&
               ($this->usage_limit === null || $this->used_count < $this->usage_limit);
    }

    public function getFormattedValueAttribute(): string
    {
        switch ($this->type) {
            case self::TYPE_PERCENTAGE:
                return $this->value . '%';
            case self::TYPE_FIXED:
                return number_format($this->value, 2) . ' $';
            case self::TYPE_FREE_SHIPPING:
                return 'Livraison gratuite';
            default:
                return $this->value;
        }
    }

    public function getRemainingUsesAttribute(): ?int
    {
        if ($this->usage_limit === null) {
            return null;
        }
        
        return max(0, $this->usage_limit - $this->used_count);
    }

    // Méthodes
    public function canBeUsed(float $orderAmount = 0): bool
    {
        if (!$this->is_valid) {
            return false;
        }

        if ($this->minimum_amount && $orderAmount < $this->minimum_amount) {
            return false;
        }

        return true;
    }

    public function calculateDiscount(float $orderAmount): float
    {
        if (!$this->canBeUsed($orderAmount)) {
            return 0;
        }

        switch ($this->type) {
            case self::TYPE_PERCENTAGE:
                $discount = ($orderAmount * $this->value) / 100;
                if ($this->maximum_discount && $discount > $this->maximum_discount) {
                    return $this->maximum_discount;
                }
                return $discount;

            case self::TYPE_FIXED:
                return min($this->value, $orderAmount);

            case self::TYPE_FREE_SHIPPING:
                // La logique de livraison gratuite sera gérée dans le contrôleur de commande
                return 0;

            default:
                return 0;
        }
    }

    public function incrementUsage(): void
    {
        $this->increment('used_count');
    }

    public function decrementUsage(): void
    {
        if ($this->used_count > 0) {
            $this->decrement('used_count');
        }
    }
}
