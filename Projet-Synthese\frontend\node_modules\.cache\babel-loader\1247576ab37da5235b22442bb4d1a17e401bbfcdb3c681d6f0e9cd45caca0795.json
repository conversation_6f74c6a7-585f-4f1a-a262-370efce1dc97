{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Projet-Synthese\\\\frontend\\\\src\\\\pages\\\\HomePage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { useData } from '../context/DataContext';\nimport ProductCard from '../components/products/ProductCard';\nimport ActivePromotions from '../components/promotions/ActivePromotions';\nimport { FaArrowRight, FaUtensils, FaPizzaSlice, FaCheese, FaCocktail } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HomePage = () => {\n  _s();\n  const [featuredProducts, setFeaturedProducts] = useState([]);\n  const [newProducts, setNewProducts] = useState([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [isUsingTestData, setIsUsingTestData] = useState(false);\n  useEffect(() => {\n    const fetchProducts = async () => {\n      try {\n        // Appel à l'API pour récupérer les produits en vedette\n        const featuredResponse = await axios.get(`${process.env.REACT_APP_API_URL}/api/products`, {\n          params: {\n            featured: true,\n            per_page: 4\n          }\n        });\n\n        // Appel à l'API pour récupérer les nouveaux produits\n        // Vous pouvez ajouter un paramètre dans votre API pour filtrer les nouveaux produits\n        // ou utiliser un tri par date de création\n        const newResponse = await axios.get(`${process.env.REACT_APP_API_URL}/api/products`, {\n          params: {\n            sort_by: 'created_at',\n            sort_direction: 'desc',\n            per_page: 4\n          }\n        });\n        setFeaturedProducts(featuredResponse.data.data.data);\n        setNewProducts(newResponse.data.data.data);\n        setIsLoading(false);\n      } catch (error) {\n        console.error('Erreur lors du chargement des produits:', error);\n\n        // Données de test en cas d'erreur de connexion\n        const testProducts = [{\n          id: 1,\n          name: 'Poulet Grillé Entier',\n          description: 'Poulet entier grillé aux herbes de Provence, tendre et savoureux',\n          short_description: 'Poulet entier grillé aux herbes',\n          price: 25.99,\n          sale_price: null,\n          image: 'https://images.unsplash.com/photo-1598103442097-8b74394b95c6?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',\n          category: {\n            name: 'Produits Grillés'\n          },\n          is_featured: true,\n          is_active: true,\n          stock_quantity: 50\n        }, {\n          id: 2,\n          name: 'Brochettes de Bœuf',\n          description: 'Brochettes de bœuf marinées et grillées, accompagnées de légumes',\n          short_description: 'Brochettes de bœuf marinées',\n          price: 18.50,\n          sale_price: 16.99,\n          image: 'https://images.unsplash.com/photo-1529692236671-f1f6cf9683ba?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',\n          category: {\n            name: 'Produits Grillés'\n          },\n          is_featured: true,\n          is_active: true,\n          stock_quantity: 30\n        }, {\n          id: 3,\n          name: 'Salade César',\n          description: 'Salade César classique avec croûtons, parmesan et sauce maison',\n          short_description: 'Salade César classique',\n          price: 12.50,\n          sale_price: null,\n          image: 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',\n          category: {\n            name: 'Salades'\n          },\n          is_featured: true,\n          is_active: true,\n          stock_quantity: 40\n        }, {\n          id: 4,\n          name: 'Plateau de Fromages',\n          description: 'Sélection de fromages artisanaux avec confiture et noix',\n          short_description: 'Plateau de fromages artisanaux',\n          price: 19.90,\n          sale_price: null,\n          image: 'https://images.unsplash.com/photo-1486297678162-eb2a19b0a32d?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',\n          category: {\n            name: 'Fromages'\n          },\n          is_featured: true,\n          is_active: true,\n          stock_quantity: 15\n        }];\n        setFeaturedProducts(testProducts);\n        setNewProducts(testProducts);\n        setIsUsingTestData(false); // Masquer le bandeau pour la démonstration\n        console.log('Utilisation des données de test - Mode démonstration');\n        setIsLoading(false);\n      }\n    };\n    fetchProducts();\n  }, []);\n  const categories = [{\n    id: 1,\n    name: \"Produits à Griller\",\n    description: \"Viandes, saucisses et brochettes prêtes à griller\",\n    icon: /*#__PURE__*/_jsxDEV(FaUtensils, {\n      className: \"text-4xl text-yummy-grilled\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 13\n    }, this),\n    color: \"bg-yummy-grilled/10 border-yummy-grilled/20\",\n    textColor: \"text-yummy-grilled\",\n    path: \"/produits-grilles\"\n  }, {\n    id: 2,\n    name: \"Produits Non-Grillés\",\n    description: \"Salades, sandwichs et plats préparés\",\n    icon: /*#__PURE__*/_jsxDEV(FaPizzaSlice, {\n      className: \"text-4xl text-yummy-nongrilled\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 13\n    }, this),\n    color: \"bg-yummy-nongrilled/10 border-yummy-nongrilled/20\",\n    textColor: \"text-yummy-nongrilled\",\n    path: \"/produits-non-grilles\"\n  }, {\n    id: 3,\n    name: \"Fromages\",\n    description: \"Fromages locaux et importés de qualité\",\n    icon: /*#__PURE__*/_jsxDEV(FaCheese, {\n      className: \"text-4xl text-yummy-cheese\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 13\n    }, this),\n    color: \"bg-yummy-cheese/10 border-yummy-cheese/20\",\n    textColor: \"text-yummy-cheese\",\n    path: \"/fromages\"\n  }, {\n    id: 4,\n    name: \"Boissons\",\n    description: \"Boissons fraîches, chaudes et alcoolisées\",\n    icon: /*#__PURE__*/_jsxDEV(FaCocktail, {\n      className: \"text-4xl text-yummy-drinks\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 13\n    }, this),\n    color: \"bg-yummy-drinks/10 border-yummy-drinks/20\",\n    textColor: \"text-yummy-drinks\",\n    path: \"/boissons\"\n  }];\n\n  // Composant Hero Section\n  const HeroSection = () => /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"relative bg-gradient-to-r from-green-50 to-green-100 py-16 md:py-24\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 gap-8 items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(motion.h1, {\n            className: \"text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900\",\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.5\n            },\n            children: [\"Des produits frais pour \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-primary-600\",\n              children: \"tous vos repas\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 39\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n            className: \"text-lg text-gray-700\",\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.5,\n              delay: 0.1\n            },\n            children: \"D\\xE9couvrez notre s\\xE9lection de produits \\xE0 griller, non-grill\\xE9s, fromages et boissons de qualit\\xE9 pour satisfaire toutes vos envies gourmandes.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"flex flex-wrap gap-4\",\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.5,\n              delay: 0.2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/products\",\n              className: \"btn-primary\",\n              children: \"D\\xE9couvrir nos produits\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/about\",\n              className: \"btn-outline\",\n              children: \"En savoir plus\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"relative\",\n          initial: {\n            opacity: 0,\n            scale: 0.9\n          },\n          animate: {\n            opacity: 1,\n            scale: 1\n          },\n          transition: {\n            duration: 0.5\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: \"https://images.unsplash.com/photo-1504674900247-0877df9cc836?ixlib=rb-1.2.1&auto=format&fit=crop&w=1200&q=80\",\n            alt: \"Produits frais\",\n            className: \"rounded-lg shadow-xl\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute -bottom-6 -right-6 bg-white p-4 rounded-lg shadow-lg\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-green-100 p-2 rounded-full\",\n                children: /*#__PURE__*/_jsxDEV(FaUtensils, {\n                  className: \"text-primary-600 text-xl\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"font-semibold text-gray-900\",\n                  children: \"Livraison rapide\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 205,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600\",\n                  children: \"En 24h chez vous\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 206,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 151,\n    columnNumber: 5\n  }, this);\n\n  // Composant Category List\n  const CategoryList = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mt-8\",\n    children: categories.map(category => /*#__PURE__*/_jsxDEV(motion.div, {\n      className: `card p-6 border ${category.color} hover:shadow-lg transition-shadow`,\n      whileHover: {\n        y: -5\n      },\n      transition: {\n        duration: 0.3\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col items-center text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: `p-4 rounded-full ${category.color} mb-4`,\n          children: category.icon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: `text-xl font-semibold mb-2 ${category.textColor}`,\n          children: category.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mb-4\",\n          children: category.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: category.path,\n          className: `flex items-center ${category.textColor} font-medium hover:underline`,\n          children: [\"D\\xE9couvrir \", /*#__PURE__*/_jsxDEV(FaArrowRight, {\n            className: \"ml-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 226,\n        columnNumber: 11\n      }, this)\n    }, category.id, false, {\n      fileName: _jsxFileName,\n      lineNumber: 220,\n      columnNumber: 9\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 218,\n    columnNumber: 5\n  }, this);\n\n  // Composant Featured Products\n  const FeaturedProducts = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mt-8\",\n    children: isLoading ? Array(4).fill(0).map((_, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card animate-pulse\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-300 aspect-square w-full\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 space-y-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-4 bg-gray-300 rounded w-1/4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-6 bg-gray-300 rounded w-3/4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-4 bg-gray-300 rounded w-1/2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 13\n      }, this)]\n    }, index, true, {\n      fileName: _jsxFileName,\n      lineNumber: 253,\n      columnNumber: 11\n    }, this)) : featuredProducts.map(product => /*#__PURE__*/_jsxDEV(ProductCard, {\n      product: product\n    }, product.id, false, {\n      fileName: _jsxFileName,\n      lineNumber: 264,\n      columnNumber: 11\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 250,\n    columnNumber: 5\n  }, this);\n\n  // Composant Promo Section\n  const PromoSection = () => /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"py-16 bg-gradient-to-r from-secondary-500 to-secondary-600 text-white\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 gap-8 items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl md:text-4xl font-bold mb-4\",\n            children: \"Offre sp\\xE9ciale du moment\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-lg mb-6\",\n            children: \"Profitez de 15% de r\\xE9duction sur tous nos produits \\xE0 griller pour vos barbecues d'\\xE9t\\xE9 !\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-wrap gap-4\",\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/produits-grilles\",\n              className: \"bg-white text-secondary-600 px-6 py-3 rounded-full font-medium hover:bg-gray-100 transition-colors\",\n              children: \"En profiter maintenant\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: \"https://images.unsplash.com/photo-1544025162-d76694265947?ixlib=rb-1.2.1&auto=format&fit=crop&w=1200&q=80\",\n            alt: \"Barbecue\",\n            className: \"rounded-lg shadow-xl\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute top-0 right-0 bg-accent-500 text-white text-xl font-bold p-4 rounded-bl-lg rounded-tr-lg\",\n            children: \"-15%\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 274,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 273,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 272,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gradient-to-r from-green-600 to-green-700 text-white p-3\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto flex justify-between items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"h-5 w-5\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            stroke: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium\",\n            children: \"Mode D\\xE9monstration - Projet YUMMY\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"/admin/dashboard\",\n            className: \"bg-white text-green-600 px-4 py-1 rounded text-sm font-medium hover:bg-gray-100 transition-colors\",\n            children: \"\\uD83D\\uDD27 Dashboard Admin\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 314,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm opacity-75\",\n            children: \"Toutes les fonctionnalit\\xE9s sont accessibles\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 313,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 306,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 305,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(HeroSection, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 325,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-16 bg-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col md:flex-row justify-between items-center mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl font-bold text-gray-900\",\n            children: \"Nos cat\\xE9gories\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/products\",\n            className: \"flex items-center text-primary-600 font-medium hover:text-primary-700\",\n            children: [\"Voir tous les produits \", /*#__PURE__*/_jsxDEV(FaArrowRight, {\n              className: \"ml-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 38\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(CategoryList, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 335,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 328,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 327,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-16 bg-gray-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-3xl font-bold text-gray-900 mb-8\",\n          children: \"Produits en vedette\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 341,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FeaturedProducts, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 340,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 339,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(PromoSection, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 346,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-16 bg-gray-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-3xl font-bold text-gray-900 mb-8\",\n          children: \"Promotions en cours\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ActivePromotions, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 349,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 348,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-16 bg-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-3xl font-bold text-gray-900 mb-8\",\n          children: \"Nouveaux produits\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 357,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6\",\n          children: isLoading ? Array(4).fill(0).map((_, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card animate-pulse\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-300 aspect-square w-full\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 362,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-4 space-y-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"h-4 bg-gray-300 rounded w-1/4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 364,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"h-6 bg-gray-300 rounded w-3/4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 365,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"h-4 bg-gray-300 rounded w-1/2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 366,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 19\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 17\n          }, this)) : newProducts.map(product => /*#__PURE__*/_jsxDEV(ProductCard, {\n            product: product\n          }, product.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 358,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 356,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 355,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 303,\n    columnNumber: 5\n  }, this);\n};\n_s(HomePage, \"TUJIbqAaCersGWAo0q4xXMUdGVI=\");\n_c = HomePage;\nexport default HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "motion", "useData", "ProductCard", "ActivePromotions", "FaArrowRight", "FaUtensils", "FaPizzaSlice", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FaCocktail", "jsxDEV", "_jsxDEV", "HomePage", "_s", "featuredProducts", "setFeaturedProducts", "newProducts", "setNewProducts", "isLoading", "setIsLoading", "isUsingTestData", "setIsUsingTestData", "fetchProducts", "featuredResponse", "axios", "get", "process", "env", "REACT_APP_API_URL", "params", "featured", "per_page", "newResponse", "sort_by", "sort_direction", "data", "error", "console", "testProducts", "id", "name", "description", "short_description", "price", "sale_price", "image", "category", "is_featured", "is_active", "stock_quantity", "log", "categories", "icon", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "textColor", "path", "HeroSection", "children", "h1", "initial", "opacity", "y", "animate", "transition", "duration", "p", "delay", "div", "to", "scale", "src", "alt", "CategoryList", "map", "whileHover", "FeaturedProducts", "Array", "fill", "_", "index", "product", "PromoSection", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "href", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet-Synthese/frontend/src/pages/HomePage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { useData } from '../context/DataContext';\nimport ProductCard from '../components/products/ProductCard';\nimport ActivePromotions from '../components/promotions/ActivePromotions';\nimport { FaArrowRight, FaUtensils, FaPizzaSlice, FaCheese, FaCocktail } from 'react-icons/fa';\n\nconst HomePage = () => {\n  const [featuredProducts, setFeaturedProducts] = useState([]);\n  const [newProducts, setNewProducts] = useState([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [isUsingTestData, setIsUsingTestData] = useState(false);\n\n  useEffect(() => {\n    const fetchProducts = async () => {\n      try {\n        // Appel à l'API pour récupérer les produits en vedette\n        const featuredResponse = await axios.get(`${process.env.REACT_APP_API_URL}/api/products`, {\n          params: {\n            featured: true,\n            per_page: 4\n          }\n        });\n\n        // Appel à l'API pour récupérer les nouveaux produits\n        // Vous pouvez ajouter un paramètre dans votre API pour filtrer les nouveaux produits\n        // ou utiliser un tri par date de création\n        const newResponse = await axios.get(`${process.env.REACT_APP_API_URL}/api/products`, {\n          params: {\n            sort_by: 'created_at',\n            sort_direction: 'desc',\n            per_page: 4\n          }\n        });\n\n        setFeaturedProducts(featuredResponse.data.data.data);\n        setNewProducts(newResponse.data.data.data);\n        setIsLoading(false);\n      } catch (error) {\n        console.error('Erreur lors du chargement des produits:', error);\n\n        // Données de test en cas d'erreur de connexion\n        const testProducts = [\n          {\n            id: 1,\n            name: 'Poulet Grillé Entier',\n            description: 'Poulet entier grillé aux herbes de Provence, tendre et savoureux',\n            short_description: 'Poulet entier grillé aux herbes',\n            price: 25.99,\n            sale_price: null,\n            image: 'https://images.unsplash.com/photo-1598103442097-8b74394b95c6?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',\n            category: { name: 'Produits Grillés' },\n            is_featured: true,\n            is_active: true,\n            stock_quantity: 50\n          },\n          {\n            id: 2,\n            name: 'Brochettes de Bœuf',\n            description: 'Brochettes de bœuf marinées et grillées, accompagnées de légumes',\n            short_description: 'Brochettes de bœuf marinées',\n            price: 18.50,\n            sale_price: 16.99,\n            image: 'https://images.unsplash.com/photo-1529692236671-f1f6cf9683ba?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',\n            category: { name: 'Produits Grillés' },\n            is_featured: true,\n            is_active: true,\n            stock_quantity: 30\n          },\n          {\n            id: 3,\n            name: 'Salade César',\n            description: 'Salade César classique avec croûtons, parmesan et sauce maison',\n            short_description: 'Salade César classique',\n            price: 12.50,\n            sale_price: null,\n            image: 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',\n            category: { name: 'Salades' },\n            is_featured: true,\n            is_active: true,\n            stock_quantity: 40\n          },\n          {\n            id: 4,\n            name: 'Plateau de Fromages',\n            description: 'Sélection de fromages artisanaux avec confiture et noix',\n            short_description: 'Plateau de fromages artisanaux',\n            price: 19.90,\n            sale_price: null,\n            image: 'https://images.unsplash.com/photo-1486297678162-eb2a19b0a32d?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',\n            category: { name: 'Fromages' },\n            is_featured: true,\n            is_active: true,\n            stock_quantity: 15\n          }\n        ];\n\n        setFeaturedProducts(testProducts);\n        setNewProducts(testProducts);\n        setIsUsingTestData(false); // Masquer le bandeau pour la démonstration\n        console.log('Utilisation des données de test - Mode démonstration');\n        setIsLoading(false);\n      }\n    };\n\n    fetchProducts();\n  }, []);\n\n  const categories = [\n    {\n      id: 1,\n      name: \"Produits à Griller\",\n      description: \"Viandes, saucisses et brochettes prêtes à griller\",\n      icon: <FaUtensils className=\"text-4xl text-yummy-grilled\" />,\n      color: \"bg-yummy-grilled/10 border-yummy-grilled/20\",\n      textColor: \"text-yummy-grilled\",\n      path: \"/produits-grilles\"\n    },\n    {\n      id: 2,\n      name: \"Produits Non-Grillés\",\n      description: \"Salades, sandwichs et plats préparés\",\n      icon: <FaPizzaSlice className=\"text-4xl text-yummy-nongrilled\" />,\n      color: \"bg-yummy-nongrilled/10 border-yummy-nongrilled/20\",\n      textColor: \"text-yummy-nongrilled\",\n      path: \"/produits-non-grilles\"\n    },\n    {\n      id: 3,\n      name: \"Fromages\",\n      description: \"Fromages locaux et importés de qualité\",\n      icon: <FaCheese className=\"text-4xl text-yummy-cheese\" />,\n      color: \"bg-yummy-cheese/10 border-yummy-cheese/20\",\n      textColor: \"text-yummy-cheese\",\n      path: \"/fromages\"\n    },\n    {\n      id: 4,\n      name: \"Boissons\",\n      description: \"Boissons fraîches, chaudes et alcoolisées\",\n      icon: <FaCocktail className=\"text-4xl text-yummy-drinks\" />,\n      color: \"bg-yummy-drinks/10 border-yummy-drinks/20\",\n      textColor: \"text-yummy-drinks\",\n      path: \"/boissons\"\n    }\n  ];\n\n  // Composant Hero Section\n  const HeroSection = () => (\n    <section className=\"relative bg-gradient-to-r from-green-50 to-green-100 py-16 md:py-24\">\n      <div className=\"container\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8 items-center\">\n          <div className=\"space-y-6\">\n            <motion.h1\n              className=\"text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.5 }}\n            >\n              Des produits frais pour <span className=\"text-primary-600\">tous vos repas</span>\n            </motion.h1>\n\n            <motion.p\n              className=\"text-lg text-gray-700\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.5, delay: 0.1 }}\n            >\n              Découvrez notre sélection de produits à griller, non-grillés, fromages et boissons de qualité pour satisfaire toutes vos envies gourmandes.\n            </motion.p>\n\n            <motion.div\n              className=\"flex flex-wrap gap-4\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.5, delay: 0.2 }}\n            >\n              <Link to=\"/products\" className=\"btn-primary\">\n                Découvrir nos produits\n              </Link>\n              <Link to=\"/about\" className=\"btn-outline\">\n                En savoir plus\n              </Link>\n            </motion.div>\n          </div>\n\n          <motion.div\n            className=\"relative\"\n            initial={{ opacity: 0, scale: 0.9 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ duration: 0.5 }}\n          >\n            <img\n              src=\"https://images.unsplash.com/photo-1504674900247-0877df9cc836?ixlib=rb-1.2.1&auto=format&fit=crop&w=1200&q=80\"\n              alt=\"Produits frais\"\n              className=\"rounded-lg shadow-xl\"\n            />\n            <div className=\"absolute -bottom-6 -right-6 bg-white p-4 rounded-lg shadow-lg\">\n              <div className=\"flex items-center gap-2\">\n                <div className=\"bg-green-100 p-2 rounded-full\">\n                  <FaUtensils className=\"text-primary-600 text-xl\" />\n                </div>\n                <div>\n                  <p className=\"font-semibold text-gray-900\">Livraison rapide</p>\n                  <p className=\"text-sm text-gray-600\">En 24h chez vous</p>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  );\n\n  // Composant Category List\n  const CategoryList = () => (\n    <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mt-8\">\n      {categories.map((category) => (\n        <motion.div\n          key={category.id}\n          className={`card p-6 border ${category.color} hover:shadow-lg transition-shadow`}\n          whileHover={{ y: -5 }}\n          transition={{ duration: 0.3 }}\n        >\n          <div className=\"flex flex-col items-center text-center\">\n            <div className={`p-4 rounded-full ${category.color} mb-4`}>\n              {category.icon}\n            </div>\n            <h3 className={`text-xl font-semibold mb-2 ${category.textColor}`}>\n              {category.name}\n            </h3>\n            <p className=\"text-gray-600 mb-4\">\n              {category.description}\n            </p>\n            <Link\n              to={category.path}\n              className={`flex items-center ${category.textColor} font-medium hover:underline`}\n            >\n              Découvrir <FaArrowRight className=\"ml-2\" />\n            </Link>\n          </div>\n        </motion.div>\n      ))}\n    </div>\n  );\n\n  // Composant Featured Products\n  const FeaturedProducts = () => (\n    <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mt-8\">\n      {isLoading ? (\n        Array(4).fill(0).map((_, index) => (\n          <div key={index} className=\"card animate-pulse\">\n            <div className=\"bg-gray-300 aspect-square w-full\"></div>\n            <div className=\"p-4 space-y-3\">\n              <div className=\"h-4 bg-gray-300 rounded w-1/4\"></div>\n              <div className=\"h-6 bg-gray-300 rounded w-3/4\"></div>\n              <div className=\"h-4 bg-gray-300 rounded w-1/2\"></div>\n            </div>\n          </div>\n        ))\n      ) : (\n        featuredProducts.map(product => (\n          <ProductCard key={product.id} product={product} />\n        ))\n      )}\n    </div>\n  );\n\n  // Composant Promo Section\n  const PromoSection = () => (\n    <section className=\"py-16 bg-gradient-to-r from-secondary-500 to-secondary-600 text-white\">\n      <div className=\"container\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8 items-center\">\n          <div>\n            <h2 className=\"text-3xl md:text-4xl font-bold mb-4\">Offre spéciale du moment</h2>\n            <p className=\"text-lg mb-6\">\n              Profitez de 15% de réduction sur tous nos produits à griller pour vos barbecues d'été !\n            </p>\n            <div className=\"flex flex-wrap gap-4\">\n              <Link to=\"/produits-grilles\" className=\"bg-white text-secondary-600 px-6 py-3 rounded-full font-medium hover:bg-gray-100 transition-colors\">\n                En profiter maintenant\n              </Link>\n            </div>\n          </div>\n\n          <div className=\"relative\">\n            <img\n              src=\"https://images.unsplash.com/photo-1544025162-d76694265947?ixlib=rb-1.2.1&auto=format&fit=crop&w=1200&q=80\"\n              alt=\"Barbecue\"\n              className=\"rounded-lg shadow-xl\"\n            />\n            <div className=\"absolute top-0 right-0 bg-accent-500 text-white text-xl font-bold p-4 rounded-bl-lg rounded-tr-lg\">\n              -15%\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n\n  return (\n    <div className=\"bg-white\">\n      {/* Bandeau d'accès rapide admin pour la démonstration */}\n      <div className=\"bg-gradient-to-r from-green-600 to-green-700 text-white p-3\">\n        <div className=\"container mx-auto flex justify-between items-center\">\n          <div className=\"flex items-center space-x-2\">\n            <svg className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n            </svg>\n            <span className=\"text-sm font-medium\">Mode Démonstration - Projet YUMMY</span>\n          </div>\n          <div className=\"flex items-center space-x-4\">\n            <a\n              href=\"/admin/dashboard\"\n              className=\"bg-white text-green-600 px-4 py-1 rounded text-sm font-medium hover:bg-gray-100 transition-colors\"\n            >\n              🔧 Dashboard Admin\n            </a>\n            <span className=\"text-sm opacity-75\">Toutes les fonctionnalités sont accessibles</span>\n          </div>\n        </div>\n      </div>\n\n      <HeroSection />\n\n      <section className=\"py-16 bg-white\">\n        <div className=\"container\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center mb-8\">\n            <h2 className=\"text-3xl font-bold text-gray-900\">Nos catégories</h2>\n            <Link to=\"/products\" className=\"flex items-center text-primary-600 font-medium hover:text-primary-700\">\n              Voir tous les produits <FaArrowRight className=\"ml-2\" />\n            </Link>\n          </div>\n          <CategoryList />\n        </div>\n      </section>\n\n      <section className=\"py-16 bg-gray-50\">\n        <div className=\"container\">\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-8\">Produits en vedette</h2>\n          <FeaturedProducts />\n        </div>\n      </section>\n\n      <PromoSection />\n\n      <section className=\"py-16 bg-gray-50\">\n        <div className=\"container\">\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-8\">Promotions en cours</h2>\n          <ActivePromotions />\n        </div>\n      </section>\n\n      <section className=\"py-16 bg-white\">\n        <div className=\"container\">\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-8\">Nouveaux produits</h2>\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6\">\n            {isLoading ? (\n              Array(4).fill(0).map((_, index) => (\n                <div key={index} className=\"card animate-pulse\">\n                  <div className=\"bg-gray-300 aspect-square w-full\"></div>\n                  <div className=\"p-4 space-y-3\">\n                    <div className=\"h-4 bg-gray-300 rounded w-1/4\"></div>\n                    <div className=\"h-6 bg-gray-300 rounded w-3/4\"></div>\n                    <div className=\"h-4 bg-gray-300 rounded w-1/2\"></div>\n                  </div>\n                </div>\n              ))\n            ) : (\n              newProducts.map(product => (\n                <ProductCard key={product.id} product={product} />\n              ))\n            )}\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n};\n\nexport default HomePage;\n\n\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,WAAW,MAAM,oCAAoC;AAC5D,OAAOC,gBAAgB,MAAM,2CAA2C;AACxE,SAASC,YAAY,EAAEC,UAAU,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,UAAU,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9F,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACkB,WAAW,EAAEC,cAAc,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACoB,SAAS,EAAEC,YAAY,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACsB,eAAe,EAAEC,kBAAkB,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EAE7DC,SAAS,CAAC,MAAM;IACd,MAAMuB,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAI;QACF;QACA,MAAMC,gBAAgB,GAAG,MAAMC,KAAK,CAACC,GAAG,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,eAAe,EAAE;UACxFC,MAAM,EAAE;YACNC,QAAQ,EAAE,IAAI;YACdC,QAAQ,EAAE;UACZ;QACF,CAAC,CAAC;;QAEF;QACA;QACA;QACA,MAAMC,WAAW,GAAG,MAAMR,KAAK,CAACC,GAAG,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,eAAe,EAAE;UACnFC,MAAM,EAAE;YACNI,OAAO,EAAE,YAAY;YACrBC,cAAc,EAAE,MAAM;YACtBH,QAAQ,EAAE;UACZ;QACF,CAAC,CAAC;QAEFhB,mBAAmB,CAACQ,gBAAgB,CAACY,IAAI,CAACA,IAAI,CAACA,IAAI,CAAC;QACpDlB,cAAc,CAACe,WAAW,CAACG,IAAI,CAACA,IAAI,CAACA,IAAI,CAAC;QAC1ChB,YAAY,CAAC,KAAK,CAAC;MACrB,CAAC,CAAC,OAAOiB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;;QAE/D;QACA,MAAME,YAAY,GAAG,CACnB;UACEC,EAAE,EAAE,CAAC;UACLC,IAAI,EAAE,sBAAsB;UAC5BC,WAAW,EAAE,kEAAkE;UAC/EC,iBAAiB,EAAE,iCAAiC;UACpDC,KAAK,EAAE,KAAK;UACZC,UAAU,EAAE,IAAI;UAChBC,KAAK,EAAE,6GAA6G;UACpHC,QAAQ,EAAE;YAAEN,IAAI,EAAE;UAAmB,CAAC;UACtCO,WAAW,EAAE,IAAI;UACjBC,SAAS,EAAE,IAAI;UACfC,cAAc,EAAE;QAClB,CAAC,EACD;UACEV,EAAE,EAAE,CAAC;UACLC,IAAI,EAAE,oBAAoB;UAC1BC,WAAW,EAAE,kEAAkE;UAC/EC,iBAAiB,EAAE,6BAA6B;UAChDC,KAAK,EAAE,KAAK;UACZC,UAAU,EAAE,KAAK;UACjBC,KAAK,EAAE,6GAA6G;UACpHC,QAAQ,EAAE;YAAEN,IAAI,EAAE;UAAmB,CAAC;UACtCO,WAAW,EAAE,IAAI;UACjBC,SAAS,EAAE,IAAI;UACfC,cAAc,EAAE;QAClB,CAAC,EACD;UACEV,EAAE,EAAE,CAAC;UACLC,IAAI,EAAE,cAAc;UACpBC,WAAW,EAAE,gEAAgE;UAC7EC,iBAAiB,EAAE,wBAAwB;UAC3CC,KAAK,EAAE,KAAK;UACZC,UAAU,EAAE,IAAI;UAChBC,KAAK,EAAE,6GAA6G;UACpHC,QAAQ,EAAE;YAAEN,IAAI,EAAE;UAAU,CAAC;UAC7BO,WAAW,EAAE,IAAI;UACjBC,SAAS,EAAE,IAAI;UACfC,cAAc,EAAE;QAClB,CAAC,EACD;UACEV,EAAE,EAAE,CAAC;UACLC,IAAI,EAAE,qBAAqB;UAC3BC,WAAW,EAAE,yDAAyD;UACtEC,iBAAiB,EAAE,gCAAgC;UACnDC,KAAK,EAAE,KAAK;UACZC,UAAU,EAAE,IAAI;UAChBC,KAAK,EAAE,6GAA6G;UACpHC,QAAQ,EAAE;YAAEN,IAAI,EAAE;UAAW,CAAC;UAC9BO,WAAW,EAAE,IAAI;UACjBC,SAAS,EAAE,IAAI;UACfC,cAAc,EAAE;QAClB,CAAC,CACF;QAEDlC,mBAAmB,CAACuB,YAAY,CAAC;QACjCrB,cAAc,CAACqB,YAAY,CAAC;QAC5BjB,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC;QAC3BgB,OAAO,CAACa,GAAG,CAAC,sDAAsD,CAAC;QACnE/B,YAAY,CAAC,KAAK,CAAC;MACrB;IACF,CAAC;IAEDG,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM6B,UAAU,GAAG,CACjB;IACEZ,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,oBAAoB;IAC1BC,WAAW,EAAE,mDAAmD;IAChEW,IAAI,eAAEzC,OAAA,CAACL,UAAU;MAAC+C,SAAS,EAAC;IAA6B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC5DC,KAAK,EAAE,6CAA6C;IACpDC,SAAS,EAAE,oBAAoB;IAC/BC,IAAI,EAAE;EACR,CAAC,EACD;IACErB,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,sBAAsB;IAC5BC,WAAW,EAAE,sCAAsC;IACnDW,IAAI,eAAEzC,OAAA,CAACJ,YAAY;MAAC8C,SAAS,EAAC;IAAgC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACjEC,KAAK,EAAE,mDAAmD;IAC1DC,SAAS,EAAE,uBAAuB;IAClCC,IAAI,EAAE;EACR,CAAC,EACD;IACErB,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,UAAU;IAChBC,WAAW,EAAE,wCAAwC;IACrDW,IAAI,eAAEzC,OAAA,CAACH,QAAQ;MAAC6C,SAAS,EAAC;IAA4B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzDC,KAAK,EAAE,2CAA2C;IAClDC,SAAS,EAAE,mBAAmB;IAC9BC,IAAI,EAAE;EACR,CAAC,EACD;IACErB,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,UAAU;IAChBC,WAAW,EAAE,2CAA2C;IACxDW,IAAI,eAAEzC,OAAA,CAACF,UAAU;MAAC4C,SAAS,EAAC;IAA4B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC3DC,KAAK,EAAE,2CAA2C;IAClDC,SAAS,EAAE,mBAAmB;IAC9BC,IAAI,EAAE;EACR,CAAC,CACF;;EAED;EACA,MAAMC,WAAW,GAAGA,CAAA,kBAClBlD,OAAA;IAAS0C,SAAS,EAAC,qEAAqE;IAAAS,QAAA,eACtFnD,OAAA;MAAK0C,SAAS,EAAC,WAAW;MAAAS,QAAA,eACxBnD,OAAA;QAAK0C,SAAS,EAAC,oDAAoD;QAAAS,QAAA,gBACjEnD,OAAA;UAAK0C,SAAS,EAAC,WAAW;UAAAS,QAAA,gBACxBnD,OAAA,CAACV,MAAM,CAAC8D,EAAE;YACRV,SAAS,EAAC,0DAA0D;YACpEW,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAAAP,QAAA,GAC/B,0BACyB,eAAAnD,OAAA;cAAM0C,SAAS,EAAC,kBAAkB;cAAAS,QAAA,EAAC;YAAc;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC,eAEZ9C,OAAA,CAACV,MAAM,CAACqE,CAAC;YACPjB,SAAS,EAAC,uBAAuB;YACjCW,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEE,KAAK,EAAE;YAAI,CAAE;YAAAT,QAAA,EAC3C;UAED;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eAEX9C,OAAA,CAACV,MAAM,CAACuE,GAAG;YACTnB,SAAS,EAAC,sBAAsB;YAChCW,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEE,KAAK,EAAE;YAAI,CAAE;YAAAT,QAAA,gBAE1CnD,OAAA,CAACX,IAAI;cAACyE,EAAE,EAAC,WAAW;cAACpB,SAAS,EAAC,aAAa;cAAAS,QAAA,EAAC;YAE7C;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACP9C,OAAA,CAACX,IAAI;cAACyE,EAAE,EAAC,QAAQ;cAACpB,SAAS,EAAC,aAAa;cAAAS,QAAA,EAAC;YAE1C;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAEN9C,OAAA,CAACV,MAAM,CAACuE,GAAG;UACTnB,SAAS,EAAC,UAAU;UACpBW,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAES,KAAK,EAAE;UAAI,CAAE;UACpCP,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAES,KAAK,EAAE;UAAE,CAAE;UAClCN,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAAAP,QAAA,gBAE9BnD,OAAA;YACEgE,GAAG,EAAC,8GAA8G;YAClHC,GAAG,EAAC,gBAAgB;YACpBvB,SAAS,EAAC;UAAsB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eACF9C,OAAA;YAAK0C,SAAS,EAAC,+DAA+D;YAAAS,QAAA,eAC5EnD,OAAA;cAAK0C,SAAS,EAAC,yBAAyB;cAAAS,QAAA,gBACtCnD,OAAA;gBAAK0C,SAAS,EAAC,+BAA+B;gBAAAS,QAAA,eAC5CnD,OAAA,CAACL,UAAU;kBAAC+C,SAAS,EAAC;gBAA0B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,eACN9C,OAAA;gBAAAmD,QAAA,gBACEnD,OAAA;kBAAG0C,SAAS,EAAC,6BAA6B;kBAAAS,QAAA,EAAC;gBAAgB;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAC/D9C,OAAA;kBAAG0C,SAAS,EAAC,uBAAuB;kBAAAS,QAAA,EAAC;gBAAgB;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CACV;;EAED;EACA,MAAMoB,YAAY,GAAGA,CAAA,kBACnBlE,OAAA;IAAK0C,SAAS,EAAC,2DAA2D;IAAAS,QAAA,EACvEX,UAAU,CAAC2B,GAAG,CAAEhC,QAAQ,iBACvBnC,OAAA,CAACV,MAAM,CAACuE,GAAG;MAETnB,SAAS,EAAE,mBAAmBP,QAAQ,CAACY,KAAK,oCAAqC;MACjFqB,UAAU,EAAE;QAAEb,CAAC,EAAE,CAAC;MAAE,CAAE;MACtBE,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE;MAAAP,QAAA,eAE9BnD,OAAA;QAAK0C,SAAS,EAAC,wCAAwC;QAAAS,QAAA,gBACrDnD,OAAA;UAAK0C,SAAS,EAAE,oBAAoBP,QAAQ,CAACY,KAAK,OAAQ;UAAAI,QAAA,EACvDhB,QAAQ,CAACM;QAAI;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eACN9C,OAAA;UAAI0C,SAAS,EAAE,8BAA8BP,QAAQ,CAACa,SAAS,EAAG;UAAAG,QAAA,EAC/DhB,QAAQ,CAACN;QAAI;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eACL9C,OAAA;UAAG0C,SAAS,EAAC,oBAAoB;UAAAS,QAAA,EAC9BhB,QAAQ,CAACL;QAAW;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eACJ9C,OAAA,CAACX,IAAI;UACHyE,EAAE,EAAE3B,QAAQ,CAACc,IAAK;UAClBP,SAAS,EAAE,qBAAqBP,QAAQ,CAACa,SAAS,8BAA+B;UAAAG,QAAA,GAClF,eACW,eAAAnD,OAAA,CAACN,YAAY;YAACgD,SAAS,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC,GArBDX,QAAQ,CAACP,EAAE;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAsBN,CACb;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CACN;;EAED;EACA,MAAMuB,gBAAgB,GAAGA,CAAA,kBACvBrE,OAAA;IAAK0C,SAAS,EAAC,2DAA2D;IAAAS,QAAA,EACvE5C,SAAS,GACR+D,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAACJ,GAAG,CAAC,CAACK,CAAC,EAAEC,KAAK,kBAC5BzE,OAAA;MAAiB0C,SAAS,EAAC,oBAAoB;MAAAS,QAAA,gBAC7CnD,OAAA;QAAK0C,SAAS,EAAC;MAAkC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACxD9C,OAAA;QAAK0C,SAAS,EAAC,eAAe;QAAAS,QAAA,gBAC5BnD,OAAA;UAAK0C,SAAS,EAAC;QAA+B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACrD9C,OAAA;UAAK0C,SAAS,EAAC;QAA+B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACrD9C,OAAA;UAAK0C,SAAS,EAAC;QAA+B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CAAC;IAAA,GANE2B,KAAK;MAAA9B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAOV,CACN,CAAC,GAEF3C,gBAAgB,CAACgE,GAAG,CAACO,OAAO,iBAC1B1E,OAAA,CAACR,WAAW;MAAkBkF,OAAO,EAAEA;IAAQ,GAA7BA,OAAO,CAAC9C,EAAE;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAqB,CAClD;EACF;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CACN;;EAED;EACA,MAAM6B,YAAY,GAAGA,CAAA,kBACnB3E,OAAA;IAAS0C,SAAS,EAAC,uEAAuE;IAAAS,QAAA,eACxFnD,OAAA;MAAK0C,SAAS,EAAC,WAAW;MAAAS,QAAA,eACxBnD,OAAA;QAAK0C,SAAS,EAAC,oDAAoD;QAAAS,QAAA,gBACjEnD,OAAA;UAAAmD,QAAA,gBACEnD,OAAA;YAAI0C,SAAS,EAAC,qCAAqC;YAAAS,QAAA,EAAC;UAAwB;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjF9C,OAAA;YAAG0C,SAAS,EAAC,cAAc;YAAAS,QAAA,EAAC;UAE5B;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJ9C,OAAA;YAAK0C,SAAS,EAAC,sBAAsB;YAAAS,QAAA,eACnCnD,OAAA,CAACX,IAAI;cAACyE,EAAE,EAAC,mBAAmB;cAACpB,SAAS,EAAC,oGAAoG;cAAAS,QAAA,EAAC;YAE5I;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN9C,OAAA;UAAK0C,SAAS,EAAC,UAAU;UAAAS,QAAA,gBACvBnD,OAAA;YACEgE,GAAG,EAAC,2GAA2G;YAC/GC,GAAG,EAAC,UAAU;YACdvB,SAAS,EAAC;UAAsB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eACF9C,OAAA;YAAK0C,SAAS,EAAC,mGAAmG;YAAAS,QAAA,EAAC;UAEnH;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CACV;EAED,oBACE9C,OAAA;IAAK0C,SAAS,EAAC,UAAU;IAAAS,QAAA,gBAEvBnD,OAAA;MAAK0C,SAAS,EAAC,6DAA6D;MAAAS,QAAA,eAC1EnD,OAAA;QAAK0C,SAAS,EAAC,qDAAqD;QAAAS,QAAA,gBAClEnD,OAAA;UAAK0C,SAAS,EAAC,6BAA6B;UAAAS,QAAA,gBAC1CnD,OAAA;YAAK0C,SAAS,EAAC,SAAS;YAAC6B,IAAI,EAAC,MAAM;YAACK,OAAO,EAAC,WAAW;YAACC,MAAM,EAAC,cAAc;YAAA1B,QAAA,eAC5EnD,OAAA;cAAM8E,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAA+C;cAAAtC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpH,CAAC,eACN9C,OAAA;YAAM0C,SAAS,EAAC,qBAAqB;YAAAS,QAAA,EAAC;UAAiC;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3E,CAAC,eACN9C,OAAA;UAAK0C,SAAS,EAAC,6BAA6B;UAAAS,QAAA,gBAC1CnD,OAAA;YACEkF,IAAI,EAAC,kBAAkB;YACvBxC,SAAS,EAAC,mGAAmG;YAAAS,QAAA,EAC9G;UAED;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJ9C,OAAA;YAAM0C,SAAS,EAAC,oBAAoB;YAAAS,QAAA,EAAC;UAA2C;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN9C,OAAA,CAACkD,WAAW;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEf9C,OAAA;MAAS0C,SAAS,EAAC,gBAAgB;MAAAS,QAAA,eACjCnD,OAAA;QAAK0C,SAAS,EAAC,WAAW;QAAAS,QAAA,gBACxBnD,OAAA;UAAK0C,SAAS,EAAC,6DAA6D;UAAAS,QAAA,gBAC1EnD,OAAA;YAAI0C,SAAS,EAAC,kCAAkC;YAAAS,QAAA,EAAC;UAAc;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpE9C,OAAA,CAACX,IAAI;YAACyE,EAAE,EAAC,WAAW;YAACpB,SAAS,EAAC,uEAAuE;YAAAS,QAAA,GAAC,yBAC9E,eAAAnD,OAAA,CAACN,YAAY;cAACgD,SAAS,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACN9C,OAAA,CAACkE,YAAY;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEV9C,OAAA;MAAS0C,SAAS,EAAC,kBAAkB;MAAAS,QAAA,eACnCnD,OAAA;QAAK0C,SAAS,EAAC,WAAW;QAAAS,QAAA,gBACxBnD,OAAA;UAAI0C,SAAS,EAAC,uCAAuC;UAAAS,QAAA,EAAC;QAAmB;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9E9C,OAAA,CAACqE,gBAAgB;UAAA1B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEV9C,OAAA,CAAC2E,YAAY;MAAAhC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEhB9C,OAAA;MAAS0C,SAAS,EAAC,kBAAkB;MAAAS,QAAA,eACnCnD,OAAA;QAAK0C,SAAS,EAAC,WAAW;QAAAS,QAAA,gBACxBnD,OAAA;UAAI0C,SAAS,EAAC,uCAAuC;UAAAS,QAAA,EAAC;QAAmB;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9E9C,OAAA,CAACP,gBAAgB;UAAAkD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEV9C,OAAA;MAAS0C,SAAS,EAAC,gBAAgB;MAAAS,QAAA,eACjCnD,OAAA;QAAK0C,SAAS,EAAC,WAAW;QAAAS,QAAA,gBACxBnD,OAAA;UAAI0C,SAAS,EAAC,uCAAuC;UAAAS,QAAA,EAAC;QAAiB;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5E9C,OAAA;UAAK0C,SAAS,EAAC,sDAAsD;UAAAS,QAAA,EAClE5C,SAAS,GACR+D,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAACJ,GAAG,CAAC,CAACK,CAAC,EAAEC,KAAK,kBAC5BzE,OAAA;YAAiB0C,SAAS,EAAC,oBAAoB;YAAAS,QAAA,gBAC7CnD,OAAA;cAAK0C,SAAS,EAAC;YAAkC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxD9C,OAAA;cAAK0C,SAAS,EAAC,eAAe;cAAAS,QAAA,gBAC5BnD,OAAA;gBAAK0C,SAAS,EAAC;cAA+B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACrD9C,OAAA;gBAAK0C,SAAS,EAAC;cAA+B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACrD9C,OAAA;gBAAK0C,SAAS,EAAC;cAA+B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC;UAAA,GANE2B,KAAK;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAOV,CACN,CAAC,GAEFzC,WAAW,CAAC8D,GAAG,CAACO,OAAO,iBACrB1E,OAAA,CAACR,WAAW;YAAkBkF,OAAO,EAAEA;UAAQ,GAA7BA,OAAO,CAAC9C,EAAE;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAqB,CAClD;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAAC5C,EAAA,CAnXID,QAAQ;AAAAkF,EAAA,GAARlF,QAAQ;AAqXd,eAAeA,QAAQ;AAAC,IAAAkF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}