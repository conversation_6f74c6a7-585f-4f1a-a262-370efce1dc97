<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Category;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Produits Grillés',
                'slug' => 'produits-grilles',
                'description' => 'Délicieux produits grillés à la perfection',
                'image' => '/images/categories/grilles.jpg',
                'is_active' => true,
                'sort_order' => 1
            ],
            [
                'name' => 'Produits Non Grillés',
                'slug' => 'produits-non-grilles',
                'description' => 'Produits frais et savoureux non grillés',
                'image' => '/images/categories/non-grilles.jpg',
                'is_active' => true,
                'sort_order' => 2
            ],
            [
                'name' => 'Fromages',
                'slug' => 'fromages',
                'description' => 'Sélection de fromages artisanaux',
                'image' => '/images/categories/fromages.jpg',
                'is_active' => true,
                'sort_order' => 3
            ],
            [
                'name' => 'Boissons',
                'slug' => 'boissons',
                'description' => 'Boissons fraîches et rafraîchissantes',
                'image' => '/images/categories/boissons.jpg',
                'is_active' => true,
                'sort_order' => 4
            ],
            [
                'name' => 'Desserts',
                'slug' => 'desserts',
                'description' => 'Desserts maison délicieux',
                'image' => '/images/categories/desserts.jpg',
                'is_active' => true,
                'sort_order' => 5
            ],
            [
                'name' => 'Salades',
                'slug' => 'salades',
                'description' => 'Salades fraîches et équilibrées',
                'image' => '/images/categories/salades.jpg',
                'is_active' => true,
                'sort_order' => 6
            ]
        ];

        foreach ($categories as $category) {
            Category::create($category);
        }
    }
}
