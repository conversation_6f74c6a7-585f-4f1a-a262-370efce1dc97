const express = require('express');
const cors = require('cors');
const app = express();
const PORT = 8000;

// Middleware
app.use(cors());
app.use(express.json());

// Données de test
const categories = [
  { id: 1, name: 'Produits Grillés', slug: 'produits-grilles', description: 'Délicieux produits grillés à la perfection' },
  { id: 2, name: 'Produits Non Grillés', slug: 'produits-non-grilles', description: 'Produits frais et savoureux non grillés' },
  { id: 3, name: 'Fromages', slug: 'fromages', description: 'Sélection de fromages artisanaux' },
  { id: 4, name: '<PERSON><PERSON>', slug: 'boissons', description: 'Boissons fraîches et rafraîchissantes' },
  { id: 5, name: 'Desser<PERSON>', slug: 'desserts', description: 'Desserts maison délicieux' },
  { id: 6, name: 'Salades', slug: 'salades', description: 'Salades fraîches et équilibrées' }
];

const products = [
  {
    id: 1,
    name: 'Poulet Grillé Entier',
    description: 'Poulet entier grillé aux herbes de Provence, tendre et savoureux',
    short_description: 'Poulet entier grillé aux herbes',
    price: 25.99,
    sale_price: null,
    image: 'https://images.unsplash.com/photo-1598103442097-8b74394b95c6?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
    category: categories[0],
    category_id: 1,
    is_featured: true,
    is_active: true,
    stock_quantity: 50,
    sku: 'PGE001'
  },
  {
    id: 2,
    name: 'Brochettes de Bœuf',
    description: 'Brochettes de bœuf marinées et grillées, accompagnées de légumes',
    short_description: 'Brochettes de bœuf marinées',
    price: 18.50,
    sale_price: 16.99,
    image: 'https://images.unsplash.com/photo-1529692236671-f1f6cf9683ba?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
    category: categories[0],
    category_id: 1,
    is_featured: true,
    is_active: true,
    stock_quantity: 30,
    sku: 'BBG001'
  },
  {
    id: 3,
    name: 'Salade César',
    description: 'Salade César classique avec croûtons, parmesan et sauce maison',
    short_description: 'Salade César classique',
    price: 12.50,
    sale_price: null,
    image: 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
    category: categories[5],
    category_id: 6,
    is_featured: true,
    is_active: true,
    stock_quantity: 40,
    sku: 'SC001'
  },
  {
    id: 4,
    name: 'Plateau de Fromages',
    description: 'Sélection de fromages artisanaux avec confiture et noix',
    short_description: 'Plateau de fromages artisanaux',
    price: 19.90,
    sale_price: null,
    image: 'https://images.unsplash.com/photo-1486297678162-eb2a19b0a32d?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
    category: categories[2],
    category_id: 3,
    is_featured: true,
    is_active: true,
    stock_quantity: 15,
    sku: 'PF001'
  },
  {
    id: 5,
    name: 'Jus d\'Orange Frais',
    description: 'Jus d\'orange fraîchement pressé, 100% naturel',
    short_description: 'Jus d\'orange fraîchement pressé',
    price: 4.50,
    sale_price: null,
    image: 'https://images.unsplash.com/photo-1613478223719-2ab802602423?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
    category: categories[3],
    category_id: 4,
    is_featured: false,
    is_active: true,
    stock_quantity: 100,
    sku: 'JOF001'
  },
  {
    id: 6,
    name: 'Tiramisu Maison',
    description: 'Tiramisu traditionnel fait maison avec mascarpone et café',
    short_description: 'Tiramisu traditionnel maison',
    price: 8.50,
    sale_price: null,
    image: 'https://images.unsplash.com/photo-1571877227200-a0d98ea607e9?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
    category: categories[4],
    category_id: 5,
    is_featured: true,
    is_active: true,
    stock_quantity: 25,
    sku: 'TM001'
  },
  {
    id: 7,
    name: 'Saumon Grillé',
    description: 'Filet de saumon grillé avec une marinade au citron et aneth',
    short_description: 'Filet de saumon grillé au citron',
    price: 22.00,
    sale_price: null,
    image: 'https://images.unsplash.com/photo-1467003909585-2f8a72700288?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
    category: categories[0],
    category_id: 1,
    is_featured: false,
    is_active: true,
    stock_quantity: 25,
    sku: 'SG001'
  },
  {
    id: 8,
    name: 'Carpaccio de Bœuf',
    description: 'Fines tranches de bœuf cru avec roquette, parmesan et huile d\'olive',
    short_description: 'Carpaccio de bœuf à la roquette',
    price: 16.00,
    sale_price: null,
    image: 'https://images.unsplash.com/photo-1546833999-b9f581a1996d?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
    category: categories[1],
    category_id: 2,
    is_featured: false,
    is_active: true,
    stock_quantity: 20,
    sku: 'CB001'
  }
];

const promotions = [
  {
    id: 1,
    name: 'Bienvenue 10%',
    code: 'BIENVENUE10',
    description: 'Réduction de 10% pour les nouveaux clients',
    type: 'percentage',
    value: 10,
    minimum_amount: 50,
    usage_limit: 100,
    used_count: 15,
    start_date: new Date().toISOString(),
    end_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
    is_active: true
  },
  {
    id: 2,
    name: 'Livraison Gratuite',
    code: 'LIVRAISON',
    description: 'Livraison gratuite pour toute commande',
    type: 'free_shipping',
    value: 0,
    minimum_amount: 30,
    usage_limit: 50,
    used_count: 8,
    start_date: new Date().toISOString(),
    end_date: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString(),
    is_active: true
  },
  {
    id: 3,
    name: 'Réduction 5€',
    code: 'REDUCTION5',
    description: '5€ de réduction immédiate',
    type: 'fixed',
    value: 5,
    minimum_amount: 25,
    usage_limit: 30,
    used_count: 12,
    start_date: new Date().toISOString(),
    end_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
    is_active: true
  }
];

// Routes API
app.get('/api/products', (req, res) => {
  res.json({
    status: 'success',
    data: products,
    message: 'Produits récupérés avec succès'
  });
});

app.get('/api/products/featured', (req, res) => {
  const featured = products.filter(p => p.is_featured);
  res.json({
    status: 'success',
    data: featured,
    message: 'Produits en vedette récupérés avec succès'
  });
});

app.get('/api/categories', (req, res) => {
  res.json({
    status: 'success',
    data: categories,
    message: 'Catégories récupérées avec succès'
  });
});

app.get('/api/promotions', (req, res) => {
  const activePromotions = promotions.filter(p => p.is_active);
  res.json({
    status: 'success',
    data: activePromotions,
    message: 'Promotions récupérées avec succès'
  });
});

app.get('/api/dashboard/stats', (req, res) => {
  res.json({
    status: 'success',
    data: {
      overview: {
        total_orders: 125,
        total_revenue: 15420.50,
        total_users: 89,
        total_products: products.length,
        orders_last_30_days: 32,
        revenue_last_30_days: 4250.00,
        new_users_last_30_days: 15,
        active_promotions: promotions.filter(p => p.is_active).length,
        low_stock_products: products.filter(p => p.stock_quantity <= 10).length
      },
      charts: {
        monthly_revenue: [
          { month: 'Jan', revenue: 2100 },
          { month: 'Fév', revenue: 2800 },
          { month: 'Mar', revenue: 3200 },
          { month: 'Avr', revenue: 2900 },
          { month: 'Mai', revenue: 3800 },
          { month: 'Jun', revenue: 4250 }
        ],
        popular_categories: [
          { name: 'Produits Grillés', total_sold: 145 },
          { name: 'Non Grillés', total_sold: 98 },
          { name: 'Fromages', total_sold: 67 },
          { name: 'Boissons', total_sold: 89 }
        ]
      },
      top_products: [
        { name: 'Poulet Grillé Entier', total_sold: 45 },
        { name: 'Salade César', total_sold: 32 },
        { name: 'Plateau de Fromages', total_sold: 28 },
        { name: 'Brochettes de Bœuf', total_sold: 25 },
        { name: 'Jus d\'Orange Frais', total_sold: 22 }
      ]
    }
  });
});

// Route de test
app.get('/api/test', (req, res) => {
  res.json({
    status: 'success',
    message: 'API Server is running!',
    timestamp: new Date().toISOString()
  });
});

// Démarrage du serveur
app.listen(PORT, () => {
  console.log(`🚀 API Server running on http://localhost:${PORT}`);
  console.log(`📊 Dashboard stats: http://localhost:${PORT}/api/dashboard/stats`);
  console.log(`🛍️ Products: http://localhost:${PORT}/api/products`);
  console.log(`🎯 Promotions: http://localhost:${PORT}/api/promotions`);
});

module.exports = app;
