<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Créer un utilisateur admin
        User::create([
            'name' => 'Admin YUMMY',
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'password' => Hash::make('password123'),
            'role' => 'admin',
        ]);

        // Créer un utilisateur manager
        User::create([
            'name' => 'Manager YUMMY',
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'password' => Hash::make('password123'),
            'role' => 'manager',
        ]);

        // Créer des clients de test
        User::create([
            'name' => '<PERSON>',
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'password' => Hash::make('password123'),
            'role' => 'client',
        ]);

        User::create([
            'name' => '<PERSON>',
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'password' => Hash::make('password123'),
            'role' => 'client',
        ]);

        User::create([
            'name' => 'Marie Dubois',
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'password' => Hash::make('password123'),
            'role' => 'client',
        ]);

        User::create([
            'name' => 'Ahmed Hassan',
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'password' => Hash::make('password123'),
            'role' => 'client',
        ]);
    }
}
