import React, { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';

const CategoryRedirect = () => {
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    const path = location.pathname.replace('/', '');
    
    // Rediriger vers la page de catégorie avec le bon format
    navigate(`/categories/${path}`, { replace: true });
  }, [navigate, location]);

  return (
    <div className="flex justify-center items-center h-64">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Redirection en cours...</p>
      </div>
    </div>
  );
};

export default CategoryRedirect;
