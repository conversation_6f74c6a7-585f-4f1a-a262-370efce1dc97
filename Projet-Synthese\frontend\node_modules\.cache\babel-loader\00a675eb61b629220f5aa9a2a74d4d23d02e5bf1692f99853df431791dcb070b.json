{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Projet-Synthese\\\\frontend\\\\src\\\\components\\\\products\\\\ProductCard.js\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ProductCard = ({\n  product\n}) => {\n  // Adapter les noms de propriétés selon votre API\n  const {\n    id,\n    name,\n    slug,\n    description,\n    price,\n    sale_price,\n    image,\n    category\n  } = product;\n\n  // Calculer le pourcentage de réduction si un prix de vente est défini\n  const discountPercentage = sale_price && price > sale_price ? Math.round((price - sale_price) / price * 100) : null;\n\n  // Construire l'URL de l'image complète\n  const imageUrl = image ? `${process.env.REACT_APP_API_URL}/storage/${image}` : 'https://via.placeholder.com/300';\n  return /*#__PURE__*/_jsxDEV(motion.div, {\n    className: \"card overflow-hidden hover:shadow-lg transition-shadow\",\n    whileHover: {\n      y: -5\n    },\n    transition: {\n      duration: 0.3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Link, {\n      to: `/products/${slug}`,\n      className: \"block relative\",\n      children: [discountPercentage && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-0 right-0 bg-accent-500 text-white text-sm font-bold p-2 rounded-bl-lg\",\n        children: [\"-\", discountPercentage, \"%\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n        src: imageUrl,\n        alt: name,\n        className: \"w-full aspect-square object-cover\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-sm text-gray-500 mb-1\",\n        children: (category === null || category === void 0 ? void 0 : category.name) || 'Catégorie'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: `/products/${slug}`,\n        className: \"block\",\n        children: /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900 hover:text-primary-600 transition-colors\",\n          children: name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600 text-sm mt-1 line-clamp-2\",\n        children: description\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center mt-3\",\n        children: sale_price ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-lg font-bold text-primary-600\",\n            children: [sale_price.toFixed(2), \" DH\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm text-gray-500 line-through ml-2\",\n            children: [price.toFixed(2), \" DH\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true) : /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-lg font-bold text-gray-900\",\n          children: [price.toFixed(2), \" DH\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-4\",\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: `/products/${slug}`,\n          className: \"btn-primary w-full text-center\",\n          children: \"Voir le produit\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 29,\n    columnNumber: 5\n  }, this);\n};\n_c = ProductCard;\nexport default ProductCard;\nvar _c;\n$RefreshReg$(_c, \"ProductCard\");", "map": {"version": 3, "names": ["React", "Link", "motion", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProductCard", "product", "id", "name", "slug", "description", "price", "sale_price", "image", "category", "discountPercentage", "Math", "round", "imageUrl", "process", "env", "REACT_APP_API_URL", "div", "className", "whileHover", "y", "transition", "duration", "children", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "src", "alt", "toFixed", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet-Synthese/frontend/src/components/products/ProductCard.js"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\nimport { motion } from 'framer-motion';\n\nconst ProductCard = ({ product }) => {\n  // Adapter les noms de propriétés selon votre API\n  const {\n    id,\n    name,\n    slug,\n    description,\n    price,\n    sale_price,\n    image,\n    category\n  } = product;\n\n  // Calculer le pourcentage de réduction si un prix de vente est défini\n  const discountPercentage = sale_price && price > sale_price\n    ? Math.round(((price - sale_price) / price) * 100)\n    : null;\n\n  // Construire l'URL de l'image complète\n  const imageUrl = image \n    ? `${process.env.REACT_APP_API_URL}/storage/${image}`\n    : 'https://via.placeholder.com/300';\n\n  return (\n    <motion.div\n      className=\"card overflow-hidden hover:shadow-lg transition-shadow\"\n      whileHover={{ y: -5 }}\n      transition={{ duration: 0.3 }}\n    >\n      <Link to={`/products/${slug}`} className=\"block relative\">\n        {discountPercentage && (\n          <div className=\"absolute top-0 right-0 bg-accent-500 text-white text-sm font-bold p-2 rounded-bl-lg\">\n            -{discountPercentage}%\n          </div>\n        )}\n        <img \n          src={imageUrl} \n          alt={name} \n          className=\"w-full aspect-square object-cover\"\n        />\n      </Link>\n      \n      <div className=\"p-4\">\n        <div className=\"text-sm text-gray-500 mb-1\">\n          {category?.name || 'Catégorie'}\n        </div>\n        \n        <Link to={`/products/${slug}`} className=\"block\">\n          <h3 className=\"text-lg font-semibold text-gray-900 hover:text-primary-600 transition-colors\">\n            {name}\n          </h3>\n        </Link>\n        \n        <p className=\"text-gray-600 text-sm mt-1 line-clamp-2\">\n          {description}\n        </p>\n        \n        <div className=\"flex items-center mt-3\">\n          {sale_price ? (\n            <>\n              <span className=\"text-lg font-bold text-primary-600\">\n                {sale_price.toFixed(2)} DH\n              </span>\n              <span className=\"text-sm text-gray-500 line-through ml-2\">\n                {price.toFixed(2)} DH\n              </span>\n            </>\n          ) : (\n            <span className=\"text-lg font-bold text-gray-900\">\n              {price.toFixed(2)} DH\n            </span>\n          )}\n        </div>\n        \n        <div className=\"mt-4\">\n          <Link \n            to={`/products/${slug}`} \n            className=\"btn-primary w-full text-center\"\n          >\n            Voir le produit\n          </Link>\n        </div>\n      </div>\n    </motion.div>\n  );\n};\n\nexport default ProductCard;\n\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,MAAM,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEvC,MAAMC,WAAW,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EACnC;EACA,MAAM;IACJC,EAAE;IACFC,IAAI;IACJC,IAAI;IACJC,WAAW;IACXC,KAAK;IACLC,UAAU;IACVC,KAAK;IACLC;EACF,CAAC,GAAGR,OAAO;;EAEX;EACA,MAAMS,kBAAkB,GAAGH,UAAU,IAAID,KAAK,GAAGC,UAAU,GACvDI,IAAI,CAACC,KAAK,CAAE,CAACN,KAAK,GAAGC,UAAU,IAAID,KAAK,GAAI,GAAG,CAAC,GAChD,IAAI;;EAER;EACA,MAAMO,QAAQ,GAAGL,KAAK,GAClB,GAAGM,OAAO,CAACC,GAAG,CAACC,iBAAiB,YAAYR,KAAK,EAAE,GACnD,iCAAiC;EAErC,oBACEX,OAAA,CAACF,MAAM,CAACsB,GAAG;IACTC,SAAS,EAAC,wDAAwD;IAClEC,UAAU,EAAE;MAAEC,CAAC,EAAE,CAAC;IAAE,CAAE;IACtBC,UAAU,EAAE;MAAEC,QAAQ,EAAE;IAAI,CAAE;IAAAC,QAAA,gBAE9B1B,OAAA,CAACH,IAAI;MAAC8B,EAAE,EAAE,aAAapB,IAAI,EAAG;MAACc,SAAS,EAAC,gBAAgB;MAAAK,QAAA,GACtDb,kBAAkB,iBACjBb,OAAA;QAAKqB,SAAS,EAAC,qFAAqF;QAAAK,QAAA,GAAC,GAClG,EAACb,kBAAkB,EAAC,GACvB;MAAA;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CACN,eACD/B,OAAA;QACEgC,GAAG,EAAEhB,QAAS;QACdiB,GAAG,EAAE3B,IAAK;QACVe,SAAS,EAAC;MAAmC;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAEP/B,OAAA;MAAKqB,SAAS,EAAC,KAAK;MAAAK,QAAA,gBAClB1B,OAAA;QAAKqB,SAAS,EAAC,4BAA4B;QAAAK,QAAA,EACxC,CAAAd,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEN,IAAI,KAAI;MAAW;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B,CAAC,eAEN/B,OAAA,CAACH,IAAI;QAAC8B,EAAE,EAAE,aAAapB,IAAI,EAAG;QAACc,SAAS,EAAC,OAAO;QAAAK,QAAA,eAC9C1B,OAAA;UAAIqB,SAAS,EAAC,8EAA8E;UAAAK,QAAA,EACzFpB;QAAI;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAEP/B,OAAA;QAAGqB,SAAS,EAAC,yCAAyC;QAAAK,QAAA,EACnDlB;MAAW;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC,eAEJ/B,OAAA;QAAKqB,SAAS,EAAC,wBAAwB;QAAAK,QAAA,EACpChB,UAAU,gBACTV,OAAA,CAAAE,SAAA;UAAAwB,QAAA,gBACE1B,OAAA;YAAMqB,SAAS,EAAC,oCAAoC;YAAAK,QAAA,GACjDhB,UAAU,CAACwB,OAAO,CAAC,CAAC,CAAC,EAAC,KACzB;UAAA;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACP/B,OAAA;YAAMqB,SAAS,EAAC,yCAAyC;YAAAK,QAAA,GACtDjB,KAAK,CAACyB,OAAO,CAAC,CAAC,CAAC,EAAC,KACpB;UAAA;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA,eACP,CAAC,gBAEH/B,OAAA;UAAMqB,SAAS,EAAC,iCAAiC;UAAAK,QAAA,GAC9CjB,KAAK,CAACyB,OAAO,CAAC,CAAC,CAAC,EAAC,KACpB;QAAA;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MACP;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEN/B,OAAA;QAAKqB,SAAS,EAAC,MAAM;QAAAK,QAAA,eACnB1B,OAAA,CAACH,IAAI;UACH8B,EAAE,EAAE,aAAapB,IAAI,EAAG;UACxBc,SAAS,EAAC,gCAAgC;UAAAK,QAAA,EAC3C;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEjB,CAAC;AAACI,EAAA,GArFIhC,WAAW;AAuFjB,eAAeA,WAAW;AAAC,IAAAgC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}