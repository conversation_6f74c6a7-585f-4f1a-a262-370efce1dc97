{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Projet-Synthese\\\\frontend\\\\src\\\\pages\\\\Admin\\\\Dashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { toast } from 'react-toastify';\nimport { FaUsers, FaShoppingCart, FaDollarSign, FaBox, FaExclamationTriangle, FaSync, FaChartLine, FaClock } from 'react-icons/fa';\nimport { dashboardService } from '../../services/dashboardService';\nimport { Line, Doughnut, Bar } from 'react-chartjs-2';\nimport { Chart as ChartJS, CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend, ArcElement, BarElement } from 'chart.js';\n\n// Enregistrer les composants Chart.js\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend, ArcElement, BarElement);\nconst Dashboard = () => {\n  _s();\n  var _stats$overview, _stats$overview2, _stats$overview3, _stats$overview4, _stats$overview5, _stats$overview6, _stats$overview7, _stats$overview8, _stats$overview9, _stats$overview10, _stats$charts, _stats$charts$monthly, _stats$charts2, _stats$charts2$monthl, _stats$charts3, _stats$charts3$popula, _stats$charts4, _stats$charts4$popula, _stats$overview11, _stats$overview12, _stats$overview13;\n  const [stats, setStats] = useState(null);\n  const [chartData, setChartData] = useState(null);\n  const [recentActivity, setRecentActivity] = useState([]);\n  const [lowStockProducts, setLowStockProducts] = useState([]);\n  const [recentOrders, setRecentOrders] = useState([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [lastUpdated, setLastUpdated] = useState(new Date());\n  useEffect(() => {\n    fetchDashboardData();\n  }, []);\n  const fetchDashboardData = async () => {\n    try {\n      setIsLoading(true);\n\n      // Charger toutes les données en parallèle depuis les APIs\n      const [statsResult, chartResult, activityResult, lowStockResult, ordersResult] = await Promise.all([dashboardService.getStats(), dashboardService.getChartData(), dashboardService.getRecentActivity(), dashboardService.getLowStockProducts(), dashboardService.getRecentOrders()]);\n\n      // Traiter les statistiques\n      if (statsResult.success) {\n        console.log('✅ Données stats chargées depuis l\\'API');\n        setStats({\n          overview: {\n            total_orders: statsResult.data.totalOrders,\n            total_revenue: statsResult.data.totalRevenue,\n            total_users: statsResult.data.totalUsers,\n            total_products: statsResult.data.totalProducts,\n            orders_last_30_days: statsResult.data.weeklyOrders,\n            revenue_last_30_days: statsResult.data.monthlyRevenue,\n            new_users_last_30_days: 15,\n            active_promotions: 3,\n            low_stock_products: statsResult.data.lowStockProducts\n          }\n        });\n      } else {\n        console.log('⚠️ Mode démonstration - Stats');\n        setStats({\n          overview: {\n            total_orders: statsResult.data.totalOrders,\n            total_revenue: statsResult.data.totalRevenue,\n            total_users: statsResult.data.totalUsers,\n            total_products: statsResult.data.totalProducts,\n            orders_last_30_days: statsResult.data.weeklyOrders,\n            revenue_last_30_days: statsResult.data.monthlyRevenue,\n            new_users_last_30_days: 15,\n            active_promotions: 3,\n            low_stock_products: statsResult.data.lowStockProducts\n          }\n        });\n      }\n\n      // Traiter les données de graphiques\n      if (chartResult.success) {\n        console.log('✅ Données charts chargées depuis l\\'API');\n        setChartData(chartResult.data);\n      } else {\n        console.log('⚠️ Mode démonstration - Charts');\n        setChartData(chartResult.data);\n      }\n\n      // Traiter les activités récentes\n      if (activityResult.success) {\n        console.log('✅ Activités chargées depuis l\\'API');\n        setRecentActivity(activityResult.data);\n      } else {\n        console.log('⚠️ Mode démonstration - Activity');\n        setRecentActivity(activityResult.data);\n      }\n\n      // Traiter les produits en stock faible\n      if (lowStockResult.success) {\n        console.log('✅ Stock faible chargé depuis l\\'API');\n        setLowStockProducts(lowStockResult.data);\n      } else {\n        console.log('⚠️ Mode démonstration - Low Stock');\n        setLowStockProducts(lowStockResult.data);\n      }\n\n      // Traiter les commandes récentes\n      if (ordersResult.success) {\n        console.log('✅ Commandes récentes chargées depuis l\\'API');\n        setRecentOrders(ordersResult.data);\n      } else {\n        console.log('⚠️ Mode démonstration - Orders');\n        setRecentOrders(ordersResult.data);\n      }\n      setLastUpdated(new Date());\n    } catch (error) {\n      toast.error('Erreur lors du chargement des données du dashboard');\n      console.error('Erreur dashboard:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('fr-MA', {\n      style: 'currency',\n      currency: 'MAD'\n    }).format(amount);\n  };\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-center items-center h-64\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 161,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-gray-900\",\n          children: \"Tableau de bord\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-500\",\n          children: [\"Derni\\xE8re mise \\xE0 jour: \", lastUpdated.toLocaleString('fr-FR')]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: fetchDashboardData,\n        className: \"flex items-center space-x-2 px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors\",\n        disabled: isLoading,\n        children: [/*#__PURE__*/_jsxDEV(FaSync, {\n          className: `${isLoading ? 'animate-spin' : ''}`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Actualiser\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 170,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(StatsCard, {\n        title: \"Total Commandes\",\n        value: (stats === null || stats === void 0 ? void 0 : (_stats$overview = stats.overview) === null || _stats$overview === void 0 ? void 0 : _stats$overview.total_orders) || 0,\n        change: `+${(stats === null || stats === void 0 ? void 0 : (_stats$overview2 = stats.overview) === null || _stats$overview2 === void 0 ? void 0 : _stats$overview2.orders_last_30_days) || 0} ce mois`,\n        icon: /*#__PURE__*/_jsxDEV(FaShoppingCart, {\n          className: \"text-xl\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 17\n        }, this),\n        color: \"blue\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatsCard, {\n        title: \"Revenus Total\",\n        value: formatCurrency((stats === null || stats === void 0 ? void 0 : (_stats$overview3 = stats.overview) === null || _stats$overview3 === void 0 ? void 0 : _stats$overview3.total_revenue) || 0),\n        change: `+${formatCurrency((stats === null || stats === void 0 ? void 0 : (_stats$overview4 = stats.overview) === null || _stats$overview4 === void 0 ? void 0 : _stats$overview4.revenue_last_30_days) || 0)} ce mois`,\n        icon: /*#__PURE__*/_jsxDEV(FaDollarSign, {\n          className: \"text-xl\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 17\n        }, this),\n        color: \"green\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatsCard, {\n        title: \"Clients\",\n        value: (stats === null || stats === void 0 ? void 0 : (_stats$overview5 = stats.overview) === null || _stats$overview5 === void 0 ? void 0 : _stats$overview5.total_users) || 0,\n        change: `+${(stats === null || stats === void 0 ? void 0 : (_stats$overview6 = stats.overview) === null || _stats$overview6 === void 0 ? void 0 : _stats$overview6.new_users_last_30_days) || 0} nouveaux`,\n        icon: /*#__PURE__*/_jsxDEV(FaUsers, {\n          className: \"text-xl\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 17\n        }, this),\n        color: \"purple\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatsCard, {\n        title: \"Produits\",\n        value: (stats === null || stats === void 0 ? void 0 : (_stats$overview7 = stats.overview) === null || _stats$overview7 === void 0 ? void 0 : _stats$overview7.total_products) || 0,\n        change: (stats === null || stats === void 0 ? void 0 : (_stats$overview8 = stats.overview) === null || _stats$overview8 === void 0 ? void 0 : _stats$overview8.low_stock_products) > 0 ? `${stats.overview.low_stock_products} en rupture` : 'Stock suffisant',\n        icon: /*#__PURE__*/_jsxDEV(FaBox, {\n          className: \"text-xl\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 17\n        }, this),\n        color: (stats === null || stats === void 0 ? void 0 : (_stats$overview9 = stats.overview) === null || _stats$overview9 === void 0 ? void 0 : _stats$overview9.low_stock_products) > 0 ? 'red' : 'orange'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 188,\n      columnNumber: 7\n    }, this), (stats === null || stats === void 0 ? void 0 : (_stats$overview10 = stats.overview) === null || _stats$overview10 === void 0 ? void 0 : _stats$overview10.low_stock_products) > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(FaExclamationTriangle, {\n          className: \"text-red-500 mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-red-800\",\n          children: [\"Attention: \", stats.overview.low_stock_products, \" produit(s) en rupture de stock\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 227,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(DashboardCharts, {\n      salesData: {\n        labels: (stats === null || stats === void 0 ? void 0 : (_stats$charts = stats.charts) === null || _stats$charts === void 0 ? void 0 : (_stats$charts$monthly = _stats$charts.monthly_revenue) === null || _stats$charts$monthly === void 0 ? void 0 : _stats$charts$monthly.map(item => item.month)) || [],\n        data: (stats === null || stats === void 0 ? void 0 : (_stats$charts2 = stats.charts) === null || _stats$charts2 === void 0 ? void 0 : (_stats$charts2$monthl = _stats$charts2.monthly_revenue) === null || _stats$charts2$monthl === void 0 ? void 0 : _stats$charts2$monthl.map(item => item.revenue)) || []\n      },\n      categoryData: {\n        labels: (stats === null || stats === void 0 ? void 0 : (_stats$charts3 = stats.charts) === null || _stats$charts3 === void 0 ? void 0 : (_stats$charts3$popula = _stats$charts3.popular_categories) === null || _stats$charts3$popula === void 0 ? void 0 : _stats$charts3$popula.map(item => item.name)) || [],\n        data: (stats === null || stats === void 0 ? void 0 : (_stats$charts4 = stats.charts) === null || _stats$charts4 === void 0 ? void 0 : (_stats$charts4$popula = _stats$charts4.popular_categories) === null || _stats$charts4$popula === void 0 ? void 0 : _stats$charts4$popula.map(item => item.total_sold)) || []\n      },\n      topProducts: (stats === null || stats === void 0 ? void 0 : stats.top_products) || []\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 238,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 xl:grid-cols-3 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"xl:col-span-2\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-sm p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-900 mb-4\",\n            children: \"Activit\\xE9s R\\xE9centes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: recentActivity.map(activity => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-2 h-2 rounded-full ${activity.status === 'success' ? 'bg-green-500' : activity.status === 'warning' ? 'bg-yellow-500' : activity.status === 'pending' ? 'bg-blue-500' : activity.status === 'completed' ? 'bg-green-600' : 'bg-gray-500'}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-900\",\n                  children: activity.message\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 267,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-500\",\n                  children: [activity.user, \" \\u2022 \", activity.time, activity.amount && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-2 font-medium text-green-600\",\n                    children: formatCurrency(activity.amount)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 271,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 268,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 19\n              }, this)]\n            }, activity.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white p-4 lg:p-6 rounded-lg shadow-sm border\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-900 mb-4\",\n            children: \"Promotions actives\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-3xl font-bold text-primary-600\",\n              children: (stats === null || stats === void 0 ? void 0 : (_stats$overview11 = stats.overview) === null || _stats$overview11 === void 0 ? void 0 : _stats$overview11.active_promotions) || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-500\",\n              children: \"promotions en cours\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white p-4 lg:p-6 rounded-lg shadow-sm border\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-900 mb-4\",\n            children: \"Performance\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Taux de conversion\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium\",\n                children: \"12.5%\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Panier moyen\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium\",\n                children: formatCurrency(((stats === null || stats === void 0 ? void 0 : (_stats$overview12 = stats.overview) === null || _stats$overview12 === void 0 ? void 0 : _stats$overview12.total_revenue) || 0) / ((stats === null || stats === void 0 ? void 0 : (_stats$overview13 = stats.overview) === null || _stats$overview13 === void 0 ? void 0 : _stats$overview13.total_orders) || 1))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 302,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Satisfaction client\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium\",\n                children: \"4.2/5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 310,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 251,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 168,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"18t/b74kzNMz6xcaDL3P7WHfDG0=\");\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "toast", "FaUsers", "FaShoppingCart", "FaDollarSign", "FaBox", "FaExclamationTriangle", "FaSync", "FaChartLine", "FaClock", "dashboardService", "Line", "Doughnut", "Bar", "Chart", "ChartJS", "CategoryScale", "LinearScale", "PointElement", "LineElement", "Title", "<PERSON><PERSON><PERSON>", "Legend", "ArcElement", "BarElement", "jsxDEV", "_jsxDEV", "register", "Dashboard", "_s", "_stats$overview", "_stats$overview2", "_stats$overview3", "_stats$overview4", "_stats$overview5", "_stats$overview6", "_stats$overview7", "_stats$overview8", "_stats$overview9", "_stats$overview10", "_stats$charts", "_stats$charts$monthly", "_stats$charts2", "_stats$charts2$monthl", "_stats$charts3", "_stats$charts3$popula", "_stats$charts4", "_stats$charts4$popula", "_stats$overview11", "_stats$overview12", "_stats$overview13", "stats", "setStats", "chartData", "setChartData", "recentActivity", "setRecentActivity", "lowStockProducts", "setLowStockProducts", "recentOrders", "setRecentOrders", "isLoading", "setIsLoading", "lastUpdated", "setLastUpdated", "Date", "fetchDashboardData", "statsResult", "chartResult", "activityResult", "lowStockResult", "ordersResult", "Promise", "all", "getStats", "getChartData", "getRecentActivity", "getLowStockProducts", "getRecentOrders", "success", "console", "log", "overview", "total_orders", "data", "totalOrders", "total_revenue", "totalRevenue", "total_users", "totalUsers", "total_products", "totalProducts", "orders_last_30_days", "weeklyOrders", "revenue_last_30_days", "monthlyRevenue", "new_users_last_30_days", "active_promotions", "low_stock_products", "error", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "format", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "toLocaleString", "onClick", "disabled", "StatsCard", "title", "value", "change", "icon", "color", "DashboardCharts", "salesData", "labels", "charts", "monthly_revenue", "map", "item", "month", "revenue", "categoryData", "popular_categories", "name", "total_sold", "topProducts", "top_products", "activity", "status", "message", "user", "time", "id", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet-Synthese/frontend/src/pages/Admin/Dashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { toast } from 'react-toastify';\nimport {\n  FaUsers,\n  FaShoppingCart,\n  FaDollarSign,\n  FaBox,\n  FaExclamationTriangle,\n  FaSync,\n  FaChartLine,\n  FaClock\n} from 'react-icons/fa';\nimport { dashboardService } from '../../services/dashboardService';\nimport { Line, Doughnut, Bar } from 'react-chartjs-2';\nimport {\n  Chart as ChartJS,\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  Title,\n  Tooltip,\n  Legend,\n  ArcElement,\n  BarElement,\n} from 'chart.js';\n\n\n// Enregistrer les composants Chart.js\nChartJS.register(\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  Title,\n  Tooltip,\n  Legend,\n  ArcElement,\n  BarElement\n);\n\nconst Dashboard = () => {\n  const [stats, setStats] = useState(null);\n  const [chartData, setChartData] = useState(null);\n  const [recentActivity, setRecentActivity] = useState([]);\n  const [lowStockProducts, setLowStockProducts] = useState([]);\n  const [recentOrders, setRecentOrders] = useState([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [lastUpdated, setLastUpdated] = useState(new Date());\n\n  useEffect(() => {\n    fetchDashboardData();\n  }, []);\n\n  const fetchDashboardData = async () => {\n    try {\n      setIsLoading(true);\n\n      // Charger toutes les données en parallèle depuis les APIs\n      const [\n        statsResult,\n        chartResult,\n        activityResult,\n        lowStockResult,\n        ordersResult\n      ] = await Promise.all([\n        dashboardService.getStats(),\n        dashboardService.getChartData(),\n        dashboardService.getRecentActivity(),\n        dashboardService.getLowStockProducts(),\n        dashboardService.getRecentOrders()\n      ]);\n\n      // Traiter les statistiques\n      if (statsResult.success) {\n        console.log('✅ Données stats chargées depuis l\\'API');\n        setStats({\n          overview: {\n            total_orders: statsResult.data.totalOrders,\n            total_revenue: statsResult.data.totalRevenue,\n            total_users: statsResult.data.totalUsers,\n            total_products: statsResult.data.totalProducts,\n            orders_last_30_days: statsResult.data.weeklyOrders,\n            revenue_last_30_days: statsResult.data.monthlyRevenue,\n            new_users_last_30_days: 15,\n            active_promotions: 3,\n            low_stock_products: statsResult.data.lowStockProducts\n          }\n        });\n      } else {\n        console.log('⚠️ Mode démonstration - Stats');\n        setStats({\n          overview: {\n            total_orders: statsResult.data.totalOrders,\n            total_revenue: statsResult.data.totalRevenue,\n            total_users: statsResult.data.totalUsers,\n            total_products: statsResult.data.totalProducts,\n            orders_last_30_days: statsResult.data.weeklyOrders,\n            revenue_last_30_days: statsResult.data.monthlyRevenue,\n            new_users_last_30_days: 15,\n            active_promotions: 3,\n            low_stock_products: statsResult.data.lowStockProducts\n          }\n        });\n      }\n\n      // Traiter les données de graphiques\n      if (chartResult.success) {\n        console.log('✅ Données charts chargées depuis l\\'API');\n        setChartData(chartResult.data);\n      } else {\n        console.log('⚠️ Mode démonstration - Charts');\n        setChartData(chartResult.data);\n      }\n\n      // Traiter les activités récentes\n      if (activityResult.success) {\n        console.log('✅ Activités chargées depuis l\\'API');\n        setRecentActivity(activityResult.data);\n      } else {\n        console.log('⚠️ Mode démonstration - Activity');\n        setRecentActivity(activityResult.data);\n      }\n\n      // Traiter les produits en stock faible\n      if (lowStockResult.success) {\n        console.log('✅ Stock faible chargé depuis l\\'API');\n        setLowStockProducts(lowStockResult.data);\n      } else {\n        console.log('⚠️ Mode démonstration - Low Stock');\n        setLowStockProducts(lowStockResult.data);\n      }\n\n      // Traiter les commandes récentes\n      if (ordersResult.success) {\n        console.log('✅ Commandes récentes chargées depuis l\\'API');\n        setRecentOrders(ordersResult.data);\n      } else {\n        console.log('⚠️ Mode démonstration - Orders');\n        setRecentOrders(ordersResult.data);\n      }\n\n      setLastUpdated(new Date());\n    } catch (error) {\n      toast.error('Erreur lors du chargement des données du dashboard');\n      console.error('Erreur dashboard:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const formatCurrency = (amount) => {\n    return new Intl.NumberFormat('fr-MA', {\n      style: 'currency',\n      currency: 'MAD'\n    }).format(amount);\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"flex justify-center items-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* En-tête */}\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">Tableau de bord</h1>\n          <p className=\"text-sm text-gray-500\">\n            Dernière mise à jour: {lastUpdated.toLocaleString('fr-FR')}\n          </p>\n        </div>\n        <button\n          onClick={fetchDashboardData}\n          className=\"flex items-center space-x-2 px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors\"\n          disabled={isLoading}\n        >\n          <FaSync className={`${isLoading ? 'animate-spin' : ''}`} />\n          <span>Actualiser</span>\n        </button>\n      </div>\n\n      {/* Statistiques principales */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        <StatsCard\n          title=\"Total Commandes\"\n          value={stats?.overview?.total_orders || 0}\n          change={`+${stats?.overview?.orders_last_30_days || 0} ce mois`}\n          icon={<FaShoppingCart className=\"text-xl\" />}\n          color=\"blue\"\n        />\n\n        <StatsCard\n          title=\"Revenus Total\"\n          value={formatCurrency(stats?.overview?.total_revenue || 0)}\n          change={`+${formatCurrency(stats?.overview?.revenue_last_30_days || 0)} ce mois`}\n          icon={<FaDollarSign className=\"text-xl\" />}\n          color=\"green\"\n        />\n\n        <StatsCard\n          title=\"Clients\"\n          value={stats?.overview?.total_users || 0}\n          change={`+${stats?.overview?.new_users_last_30_days || 0} nouveaux`}\n          icon={<FaUsers className=\"text-xl\" />}\n          color=\"purple\"\n        />\n\n        <StatsCard\n          title=\"Produits\"\n          value={stats?.overview?.total_products || 0}\n          change={stats?.overview?.low_stock_products > 0 ?\n            `${stats.overview.low_stock_products} en rupture` :\n            'Stock suffisant'\n          }\n          icon={<FaBox className=\"text-xl\" />}\n          color={stats?.overview?.low_stock_products > 0 ? 'red' : 'orange'}\n        />\n      </div>\n\n      {/* Alertes */}\n      {stats?.overview?.low_stock_products > 0 && (\n        <div className=\"bg-red-50 border border-red-200 rounded-lg p-4\">\n          <div className=\"flex items-center\">\n            <FaExclamationTriangle className=\"text-red-500 mr-2\" />\n            <span className=\"text-red-800\">\n              Attention: {stats.overview.low_stock_products} produit(s) en rupture de stock\n            </span>\n          </div>\n        </div>\n      )}\n\n      {/* Graphiques */}\n      <DashboardCharts\n        salesData={{\n          labels: stats?.charts?.monthly_revenue?.map(item => item.month) || [],\n          data: stats?.charts?.monthly_revenue?.map(item => item.revenue) || []\n        }}\n        categoryData={{\n          labels: stats?.charts?.popular_categories?.map(item => item.name) || [],\n          data: stats?.charts?.popular_categories?.map(item => item.total_sold) || []\n        }}\n        topProducts={stats?.top_products || []}\n      />\n\n      {/* Section inférieure */}\n      <div className=\"grid grid-cols-1 xl:grid-cols-3 gap-6\">\n        {/* Activités récentes */}\n        <div className=\"xl:col-span-2\">\n          <div className=\"bg-white rounded-lg shadow-sm p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Activités Récentes</h3>\n            <div className=\"space-y-4\">\n              {recentActivity.map((activity) => (\n                <div key={activity.id} className=\"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg\">\n                  <div className={`w-2 h-2 rounded-full ${\n                    activity.status === 'success' ? 'bg-green-500' :\n                    activity.status === 'warning' ? 'bg-yellow-500' :\n                    activity.status === 'pending' ? 'bg-blue-500' :\n                    activity.status === 'completed' ? 'bg-green-600' :\n                    'bg-gray-500'\n                  }`}></div>\n                  <div className=\"flex-1\">\n                    <p className=\"text-sm font-medium text-gray-900\">{activity.message}</p>\n                    <p className=\"text-xs text-gray-500\">\n                      {activity.user} • {activity.time}\n                      {activity.amount && (\n                        <span className=\"ml-2 font-medium text-green-600\">\n                          {formatCurrency(activity.amount)}\n                        </span>\n                      )}\n                    </p>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n\n        {/* Statistiques supplémentaires */}\n        <div className=\"space-y-6\">\n          <div className=\"bg-white p-4 lg:p-6 rounded-lg shadow-sm border\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Promotions actives</h3>\n            <div className=\"text-center\">\n              <p className=\"text-3xl font-bold text-primary-600\">\n                {stats?.overview?.active_promotions || 0}\n              </p>\n              <p className=\"text-sm text-gray-500\">promotions en cours</p>\n            </div>\n          </div>\n\n          <div className=\"bg-white p-4 lg:p-6 rounded-lg shadow-sm border\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Performance</h3>\n            <div className=\"space-y-3\">\n              <div className=\"flex justify-between\">\n                <span className=\"text-sm text-gray-600\">Taux de conversion</span>\n                <span className=\"text-sm font-medium\">12.5%</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-sm text-gray-600\">Panier moyen</span>\n                <span className=\"text-sm font-medium\">\n                  {formatCurrency((stats?.overview?.total_revenue || 0) / (stats?.overview?.total_orders || 1))}\n                </span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-sm text-gray-600\">Satisfaction client</span>\n                <span className=\"text-sm font-medium\">4.2/5</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Dashboard;\n\n\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SACEC,OAAO,EACPC,cAAc,EACdC,YAAY,EACZC,KAAK,EACLC,qBAAqB,EACrBC,MAAM,EACNC,WAAW,EACXC,OAAO,QACF,gBAAgB;AACvB,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,IAAI,EAAEC,QAAQ,EAAEC,GAAG,QAAQ,iBAAiB;AACrD,SACEC,KAAK,IAAIC,OAAO,EAChBC,aAAa,EACbC,WAAW,EACXC,YAAY,EACZC,WAAW,EACXC,KAAK,EACLC,OAAO,EACPC,MAAM,EACNC,UAAU,EACVC,UAAU,QACL,UAAU;;AAGjB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACAX,OAAO,CAACY,QAAQ,CACdX,aAAa,EACbC,WAAW,EACXC,YAAY,EACZC,WAAW,EACXC,KAAK,EACLC,OAAO,EACPC,MAAM,EACNC,UAAU,EACVC,UACF,CAAC;AAED,MAAMI,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,eAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,iBAAA,EAAAC,aAAA,EAAAC,qBAAA,EAAAC,cAAA,EAAAC,qBAAA,EAAAC,cAAA,EAAAC,qBAAA,EAAAC,cAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,iBAAA,EAAAC,iBAAA;EACtB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGrD,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACsD,SAAS,EAAEC,YAAY,CAAC,GAAGvD,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACwD,cAAc,EAAEC,iBAAiB,CAAC,GAAGzD,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC0D,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3D,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAAC4D,YAAY,EAAEC,eAAe,CAAC,GAAG7D,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC8D,SAAS,EAAEC,YAAY,CAAC,GAAG/D,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACgE,WAAW,EAAEC,cAAc,CAAC,GAAGjE,QAAQ,CAAC,IAAIkE,IAAI,CAAC,CAAC,CAAC;EAE1DjE,SAAS,CAAC,MAAM;IACdkE,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACFJ,YAAY,CAAC,IAAI,CAAC;;MAElB;MACA,MAAM,CACJK,WAAW,EACXC,WAAW,EACXC,cAAc,EACdC,cAAc,EACdC,YAAY,CACb,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACpB/D,gBAAgB,CAACgE,QAAQ,CAAC,CAAC,EAC3BhE,gBAAgB,CAACiE,YAAY,CAAC,CAAC,EAC/BjE,gBAAgB,CAACkE,iBAAiB,CAAC,CAAC,EACpClE,gBAAgB,CAACmE,mBAAmB,CAAC,CAAC,EACtCnE,gBAAgB,CAACoE,eAAe,CAAC,CAAC,CACnC,CAAC;;MAEF;MACA,IAAIX,WAAW,CAACY,OAAO,EAAE;QACvBC,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;QACrD7B,QAAQ,CAAC;UACP8B,QAAQ,EAAE;YACRC,YAAY,EAAEhB,WAAW,CAACiB,IAAI,CAACC,WAAW;YAC1CC,aAAa,EAAEnB,WAAW,CAACiB,IAAI,CAACG,YAAY;YAC5CC,WAAW,EAAErB,WAAW,CAACiB,IAAI,CAACK,UAAU;YACxCC,cAAc,EAAEvB,WAAW,CAACiB,IAAI,CAACO,aAAa;YAC9CC,mBAAmB,EAAEzB,WAAW,CAACiB,IAAI,CAACS,YAAY;YAClDC,oBAAoB,EAAE3B,WAAW,CAACiB,IAAI,CAACW,cAAc;YACrDC,sBAAsB,EAAE,EAAE;YAC1BC,iBAAiB,EAAE,CAAC;YACpBC,kBAAkB,EAAE/B,WAAW,CAACiB,IAAI,CAAC3B;UACvC;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACLuB,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;QAC5C7B,QAAQ,CAAC;UACP8B,QAAQ,EAAE;YACRC,YAAY,EAAEhB,WAAW,CAACiB,IAAI,CAACC,WAAW;YAC1CC,aAAa,EAAEnB,WAAW,CAACiB,IAAI,CAACG,YAAY;YAC5CC,WAAW,EAAErB,WAAW,CAACiB,IAAI,CAACK,UAAU;YACxCC,cAAc,EAAEvB,WAAW,CAACiB,IAAI,CAACO,aAAa;YAC9CC,mBAAmB,EAAEzB,WAAW,CAACiB,IAAI,CAACS,YAAY;YAClDC,oBAAoB,EAAE3B,WAAW,CAACiB,IAAI,CAACW,cAAc;YACrDC,sBAAsB,EAAE,EAAE;YAC1BC,iBAAiB,EAAE,CAAC;YACpBC,kBAAkB,EAAE/B,WAAW,CAACiB,IAAI,CAAC3B;UACvC;QACF,CAAC,CAAC;MACJ;;MAEA;MACA,IAAIW,WAAW,CAACW,OAAO,EAAE;QACvBC,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;QACtD3B,YAAY,CAACc,WAAW,CAACgB,IAAI,CAAC;MAChC,CAAC,MAAM;QACLJ,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;QAC7C3B,YAAY,CAACc,WAAW,CAACgB,IAAI,CAAC;MAChC;;MAEA;MACA,IAAIf,cAAc,CAACU,OAAO,EAAE;QAC1BC,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;QACjDzB,iBAAiB,CAACa,cAAc,CAACe,IAAI,CAAC;MACxC,CAAC,MAAM;QACLJ,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;QAC/CzB,iBAAiB,CAACa,cAAc,CAACe,IAAI,CAAC;MACxC;;MAEA;MACA,IAAId,cAAc,CAACS,OAAO,EAAE;QAC1BC,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;QAClDvB,mBAAmB,CAACY,cAAc,CAACc,IAAI,CAAC;MAC1C,CAAC,MAAM;QACLJ,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;QAChDvB,mBAAmB,CAACY,cAAc,CAACc,IAAI,CAAC;MAC1C;;MAEA;MACA,IAAIb,YAAY,CAACQ,OAAO,EAAE;QACxBC,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;QAC1DrB,eAAe,CAACW,YAAY,CAACa,IAAI,CAAC;MACpC,CAAC,MAAM;QACLJ,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;QAC7CrB,eAAe,CAACW,YAAY,CAACa,IAAI,CAAC;MACpC;MAEApB,cAAc,CAAC,IAAIC,IAAI,CAAC,CAAC,CAAC;IAC5B,CAAC,CAAC,OAAOkC,KAAK,EAAE;MACdlG,KAAK,CAACkG,KAAK,CAAC,oDAAoD,CAAC;MACjEnB,OAAO,CAACmB,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;IAC3C,CAAC,SAAS;MACRrC,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMsC,cAAc,GAAIC,MAAM,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAACC,MAAM,CAACL,MAAM,CAAC;EACnB,CAAC;EAED,IAAIxC,SAAS,EAAE;IACb,oBACEnC,OAAA;MAAKiF,SAAS,EAAC,uCAAuC;MAAAC,QAAA,eACpDlF,OAAA;QAAKiF,SAAS,EAAC;MAAmE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtF,CAAC;EAEV;EAEA,oBACEtF,OAAA;IAAKiF,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBlF,OAAA;MAAKiF,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChDlF,OAAA;QAAAkF,QAAA,gBACElF,OAAA;UAAIiF,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrEtF,OAAA;UAAGiF,SAAS,EAAC,uBAAuB;UAAAC,QAAA,GAAC,8BACb,EAAC7C,WAAW,CAACkD,cAAc,CAAC,OAAO,CAAC;QAAA;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACNtF,OAAA;QACEwF,OAAO,EAAEhD,kBAAmB;QAC5ByC,SAAS,EAAC,mHAAmH;QAC7HQ,QAAQ,EAAEtD,SAAU;QAAA+C,QAAA,gBAEpBlF,OAAA,CAACnB,MAAM;UAACoG,SAAS,EAAE,GAAG9C,SAAS,GAAG,cAAc,GAAG,EAAE;QAAG;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3DtF,OAAA;UAAAkF,QAAA,EAAM;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNtF,OAAA;MAAKiF,SAAS,EAAC,sDAAsD;MAAAC,QAAA,gBACnElF,OAAA,CAAC0F,SAAS;QACRC,KAAK,EAAC,iBAAiB;QACvBC,KAAK,EAAE,CAAAnE,KAAK,aAALA,KAAK,wBAAArB,eAAA,GAALqB,KAAK,CAAE+B,QAAQ,cAAApD,eAAA,uBAAfA,eAAA,CAAiBqD,YAAY,KAAI,CAAE;QAC1CoC,MAAM,EAAE,IAAI,CAAApE,KAAK,aAALA,KAAK,wBAAApB,gBAAA,GAALoB,KAAK,CAAE+B,QAAQ,cAAAnD,gBAAA,uBAAfA,gBAAA,CAAiB6D,mBAAmB,KAAI,CAAC,UAAW;QAChE4B,IAAI,eAAE9F,OAAA,CAACvB,cAAc;UAACwG,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC7CS,KAAK,EAAC;MAAM;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC,eAEFtF,OAAA,CAAC0F,SAAS;QACRC,KAAK,EAAC,eAAe;QACrBC,KAAK,EAAElB,cAAc,CAAC,CAAAjD,KAAK,aAALA,KAAK,wBAAAnB,gBAAA,GAALmB,KAAK,CAAE+B,QAAQ,cAAAlD,gBAAA,uBAAfA,gBAAA,CAAiBsD,aAAa,KAAI,CAAC,CAAE;QAC3DiC,MAAM,EAAE,IAAInB,cAAc,CAAC,CAAAjD,KAAK,aAALA,KAAK,wBAAAlB,gBAAA,GAALkB,KAAK,CAAE+B,QAAQ,cAAAjD,gBAAA,uBAAfA,gBAAA,CAAiB6D,oBAAoB,KAAI,CAAC,CAAC,UAAW;QACjF0B,IAAI,eAAE9F,OAAA,CAACtB,YAAY;UAACuG,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC3CS,KAAK,EAAC;MAAO;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC,eAEFtF,OAAA,CAAC0F,SAAS;QACRC,KAAK,EAAC,SAAS;QACfC,KAAK,EAAE,CAAAnE,KAAK,aAALA,KAAK,wBAAAjB,gBAAA,GAALiB,KAAK,CAAE+B,QAAQ,cAAAhD,gBAAA,uBAAfA,gBAAA,CAAiBsD,WAAW,KAAI,CAAE;QACzC+B,MAAM,EAAE,IAAI,CAAApE,KAAK,aAALA,KAAK,wBAAAhB,gBAAA,GAALgB,KAAK,CAAE+B,QAAQ,cAAA/C,gBAAA,uBAAfA,gBAAA,CAAiB6D,sBAAsB,KAAI,CAAC,WAAY;QACpEwB,IAAI,eAAE9F,OAAA,CAACxB,OAAO;UAACyG,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACtCS,KAAK,EAAC;MAAQ;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC,eAEFtF,OAAA,CAAC0F,SAAS;QACRC,KAAK,EAAC,UAAU;QAChBC,KAAK,EAAE,CAAAnE,KAAK,aAALA,KAAK,wBAAAf,gBAAA,GAALe,KAAK,CAAE+B,QAAQ,cAAA9C,gBAAA,uBAAfA,gBAAA,CAAiBsD,cAAc,KAAI,CAAE;QAC5C6B,MAAM,EAAE,CAAApE,KAAK,aAALA,KAAK,wBAAAd,gBAAA,GAALc,KAAK,CAAE+B,QAAQ,cAAA7C,gBAAA,uBAAfA,gBAAA,CAAiB6D,kBAAkB,IAAG,CAAC,GAC7C,GAAG/C,KAAK,CAAC+B,QAAQ,CAACgB,kBAAkB,aAAa,GACjD,iBACD;QACDsB,IAAI,eAAE9F,OAAA,CAACrB,KAAK;UAACsG,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACpCS,KAAK,EAAE,CAAAtE,KAAK,aAALA,KAAK,wBAAAb,gBAAA,GAALa,KAAK,CAAE+B,QAAQ,cAAA5C,gBAAA,uBAAfA,gBAAA,CAAiB4D,kBAAkB,IAAG,CAAC,GAAG,KAAK,GAAG;MAAS;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAGL,CAAA7D,KAAK,aAALA,KAAK,wBAAAZ,iBAAA,GAALY,KAAK,CAAE+B,QAAQ,cAAA3C,iBAAA,uBAAfA,iBAAA,CAAiB2D,kBAAkB,IAAG,CAAC,iBACtCxE,OAAA;MAAKiF,SAAS,EAAC,gDAAgD;MAAAC,QAAA,eAC7DlF,OAAA;QAAKiF,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChClF,OAAA,CAACpB,qBAAqB;UAACqG,SAAS,EAAC;QAAmB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACvDtF,OAAA;UAAMiF,SAAS,EAAC,cAAc;UAAAC,QAAA,GAAC,aAClB,EAACzD,KAAK,CAAC+B,QAAQ,CAACgB,kBAAkB,EAAC,iCAChD;QAAA;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDtF,OAAA,CAACgG,eAAe;MACdC,SAAS,EAAE;QACTC,MAAM,EAAE,CAAAzE,KAAK,aAALA,KAAK,wBAAAX,aAAA,GAALW,KAAK,CAAE0E,MAAM,cAAArF,aAAA,wBAAAC,qBAAA,GAAbD,aAAA,CAAesF,eAAe,cAAArF,qBAAA,uBAA9BA,qBAAA,CAAgCsF,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACC,KAAK,CAAC,KAAI,EAAE;QACrE7C,IAAI,EAAE,CAAAjC,KAAK,aAALA,KAAK,wBAAAT,cAAA,GAALS,KAAK,CAAE0E,MAAM,cAAAnF,cAAA,wBAAAC,qBAAA,GAAbD,cAAA,CAAeoF,eAAe,cAAAnF,qBAAA,uBAA9BA,qBAAA,CAAgCoF,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACE,OAAO,CAAC,KAAI;MACrE,CAAE;MACFC,YAAY,EAAE;QACZP,MAAM,EAAE,CAAAzE,KAAK,aAALA,KAAK,wBAAAP,cAAA,GAALO,KAAK,CAAE0E,MAAM,cAAAjF,cAAA,wBAAAC,qBAAA,GAAbD,cAAA,CAAewF,kBAAkB,cAAAvF,qBAAA,uBAAjCA,qBAAA,CAAmCkF,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACK,IAAI,CAAC,KAAI,EAAE;QACvEjD,IAAI,EAAE,CAAAjC,KAAK,aAALA,KAAK,wBAAAL,cAAA,GAALK,KAAK,CAAE0E,MAAM,cAAA/E,cAAA,wBAAAC,qBAAA,GAAbD,cAAA,CAAesF,kBAAkB,cAAArF,qBAAA,uBAAjCA,qBAAA,CAAmCgF,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACM,UAAU,CAAC,KAAI;MAC3E,CAAE;MACFC,WAAW,EAAE,CAAApF,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEqF,YAAY,KAAI;IAAG;MAAA3B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxC,CAAC,eAGFtF,OAAA;MAAKiF,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBAEpDlF,OAAA;QAAKiF,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC5BlF,OAAA;UAAKiF,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDlF,OAAA;YAAIiF,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChFtF,OAAA;YAAKiF,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvBrD,cAAc,CAACwE,GAAG,CAAEU,QAAQ,iBAC3B/G,OAAA;cAAuBiF,SAAS,EAAC,uDAAuD;cAAAC,QAAA,gBACtFlF,OAAA;gBAAKiF,SAAS,EAAE,wBACd8B,QAAQ,CAACC,MAAM,KAAK,SAAS,GAAG,cAAc,GAC9CD,QAAQ,CAACC,MAAM,KAAK,SAAS,GAAG,eAAe,GAC/CD,QAAQ,CAACC,MAAM,KAAK,SAAS,GAAG,aAAa,GAC7CD,QAAQ,CAACC,MAAM,KAAK,WAAW,GAAG,cAAc,GAChD,aAAa;cACZ;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACVtF,OAAA;gBAAKiF,SAAS,EAAC,QAAQ;gBAAAC,QAAA,gBACrBlF,OAAA;kBAAGiF,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAE6B,QAAQ,CAACE;gBAAO;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACvEtF,OAAA;kBAAGiF,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,GACjC6B,QAAQ,CAACG,IAAI,EAAC,UAAG,EAACH,QAAQ,CAACI,IAAI,EAC/BJ,QAAQ,CAACpC,MAAM,iBACd3E,OAAA;oBAAMiF,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAC9CR,cAAc,CAACqC,QAAQ,CAACpC,MAAM;kBAAC;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CACP;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA,GAlBEyB,QAAQ,CAACK,EAAE;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAmBhB,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNtF,OAAA;QAAKiF,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBlF,OAAA;UAAKiF,SAAS,EAAC,iDAAiD;UAAAC,QAAA,gBAC9DlF,OAAA;YAAIiF,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChFtF,OAAA;YAAKiF,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BlF,OAAA;cAAGiF,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAC/C,CAAAzD,KAAK,aAALA,KAAK,wBAAAH,iBAAA,GAALG,KAAK,CAAE+B,QAAQ,cAAAlC,iBAAA,uBAAfA,iBAAA,CAAiBiD,iBAAiB,KAAI;YAAC;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC,eACJtF,OAAA;cAAGiF,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENtF,OAAA;UAAKiF,SAAS,EAAC,iDAAiD;UAAAC,QAAA,gBAC9DlF,OAAA;YAAIiF,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzEtF,OAAA;YAAKiF,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBlF,OAAA;cAAKiF,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACnClF,OAAA;gBAAMiF,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACjEtF,OAAA;gBAAMiF,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC,eACNtF,OAAA;cAAKiF,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACnClF,OAAA;gBAAMiF,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC3DtF,OAAA;gBAAMiF,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAClCR,cAAc,CAAC,CAAC,CAAAjD,KAAK,aAALA,KAAK,wBAAAF,iBAAA,GAALE,KAAK,CAAE+B,QAAQ,cAAAjC,iBAAA,uBAAfA,iBAAA,CAAiBqC,aAAa,KAAI,CAAC,KAAK,CAAAnC,KAAK,aAALA,KAAK,wBAAAD,iBAAA,GAALC,KAAK,CAAE+B,QAAQ,cAAAhC,iBAAA,uBAAfA,iBAAA,CAAiBiC,YAAY,KAAI,CAAC,CAAC;cAAC;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNtF,OAAA;cAAKiF,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACnClF,OAAA;gBAAMiF,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAClEtF,OAAA;gBAAMiF,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACnF,EAAA,CApRID,SAAS;AAAAmH,EAAA,GAATnH,SAAS;AAsRf,eAAeA,SAAS;AAAC,IAAAmH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}