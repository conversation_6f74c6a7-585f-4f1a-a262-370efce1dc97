<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Promotion;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class PromotionController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:sanctum')->except(['index', 'validateCode']);
    }

    public function index()
    {
        $promotions = Promotion::active()
            ->valid()
            ->select(['id', 'name', 'description', 'type', 'value', 'end_date'])
            ->get();

        return response()->json($promotions);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:50|unique:promotions',
            'description' => 'nullable|string',
            'type' => ['required', Rule::in(['percentage', 'fixed', 'free_shipping'])],
            'value' => 'required|numeric|min:0',
            'minimum_amount' => 'nullable|numeric|min:0',
            'maximum_discount' => 'nullable|numeric|min:0',
            'usage_limit' => 'nullable|integer|min:1',
            'start_date' => 'required|date|after_or_equal:today',
            'end_date' => 'required|date|after:start_date',
            'is_active' => 'nullable|boolean'
        ]);

        // Validation spécifique selon le type
        if ($validated['type'] === 'percentage' && $validated['value'] > 100) {
            return response()->json([
                'message' => 'Le pourcentage ne peut pas dépasser 100%'
            ], 422);
        }

        // Convertir le code en majuscules
        $validated['code'] = strtoupper($validated['code']);

        $promotion = Promotion::create($validated);

        return response()->json([
            'status' => 'success',
            'message' => 'Promotion créée avec succès',
            'data' => $promotion
        ], 201);
    }

    public function show(Promotion $promotion)
    {
        return response()->json($promotion);
    }

    public function update(Request $request, Promotion $promotion)
    {
        $validated = $request->validate([
            'name' => 'sometimes|required|string|max:255',
            'code' => ['sometimes', 'required', 'string', 'max:50', Rule::unique('promotions')->ignore($promotion->id)],
            'description' => 'nullable|string',
            'type' => ['sometimes', 'required', Rule::in(['percentage', 'fixed', 'free_shipping'])],
            'value' => 'sometimes|required|numeric|min:0',
            'minimum_amount' => 'nullable|numeric|min:0',
            'maximum_discount' => 'nullable|numeric|min:0',
            'usage_limit' => 'nullable|integer|min:1',
            'start_date' => 'sometimes|required|date',
            'end_date' => 'sometimes|required|date|after:start_date',
            'is_active' => 'nullable|boolean'
        ]);

        // Validation spécifique selon le type
        if (isset($validated['type']) && $validated['type'] === 'percentage' && 
            isset($validated['value']) && $validated['value'] > 100) {
            return response()->json([
                'message' => 'Le pourcentage ne peut pas dépasser 100%'
            ], 422);
        }

        // Convertir le code en majuscules si fourni
        if (isset($validated['code'])) {
            $validated['code'] = strtoupper($validated['code']);
        }

        $promotion->update($validated);

        return response()->json([
            'status' => 'success',
            'message' => 'Promotion mise à jour avec succès',
            'data' => $promotion
        ]);
    }

    public function destroy(Promotion $promotion)
    {
        // Vérifier si la promotion est utilisée dans des commandes
        if ($promotion->orders()->exists()) {
            return response()->json([
                'message' => 'Impossible de supprimer une promotion utilisée dans des commandes'
            ], 422);
        }

        $promotion->delete();

        return response()->json([
            'status' => 'success',
            'message' => 'Promotion supprimée avec succès'
        ]);
    }

    public function validateCode(Request $request)
    {
        $request->validate([
            'code' => 'required|string',
            'order_amount' => 'nullable|numeric|min:0'
        ]);

        $code = strtoupper($request->code);
        $orderAmount = $request->order_amount ?? 0;

        $promotion = Promotion::byCode($code)->first();

        if (!$promotion) {
            return response()->json([
                'valid' => false,
                'message' => 'Code promo invalide'
            ], 404);
        }

        if (!$promotion->canBeUsed($orderAmount)) {
            $message = 'Code promo non valide';
            
            if (!$promotion->is_valid) {
                $message = 'Ce code promo a expiré ou n\'est plus actif';
            } elseif ($promotion->minimum_amount && $orderAmount < $promotion->minimum_amount) {
                $message = "Montant minimum requis : " . number_format($promotion->minimum_amount, 2) . " $";
            }

            return response()->json([
                'valid' => false,
                'message' => $message
            ], 422);
        }

        $discount = $promotion->calculateDiscount($orderAmount);

        return response()->json([
            'valid' => true,
            'promotion' => [
                'id' => $promotion->id,
                'name' => $promotion->name,
                'code' => $promotion->code,
                'type' => $promotion->type,
                'value' => $promotion->value,
                'formatted_value' => $promotion->formatted_value,
                'discount_amount' => $discount,
                'minimum_amount' => $promotion->minimum_amount,
                'remaining_uses' => $promotion->remaining_uses
            ],
            'discount_amount' => $discount,
            'message' => 'Code promo appliqué avec succès'
        ]);
    }
}
