{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Projet-Synthese\\\\frontend\\\\src\\\\components\\\\admin\\\\AdminHeader.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminHeader = () => {\n  _s();\n  var _state$user;\n  const {\n    state,\n    logout\n  } = useAuth();\n\n  // Gestion sécurisée de l'état d'authentification\n  const userName = (state === null || state === void 0 ? void 0 : (_state$user = state.user) === null || _state$user === void 0 ? void 0 : _state$user.name) || 'Admin';\n  const handleLogout = () => {\n    if (logout) {\n      logout();\n    }\n    // En mode démonstration, on peut simplement recharger la page\n    window.location.href = '/';\n  };\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    className: \"bg-white shadow-sm h-16 flex items-center px-6 sticky top-0 z-10\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex justify-between items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-gray-800 font-medium\",\n          children: [\"Bonjour, \", userName]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-4\",\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          className: \"text-gray-600 hover:text-green-600 transition\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            className: \"h-5 w-5\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            stroke: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 31,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 30,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: logout,\n          className: \"text-gray-600 hover:text-red-600 transition\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            className: \"h-5 w-5\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            stroke: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 40,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 20,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminHeader, \"Yj5iRxhOag00j7J/1RZAg0mOulQ=\", false, function () {\n  return [useAuth];\n});\n_c = AdminHeader;\nexport default AdminHeader;\nvar _c;\n$RefreshReg$(_c, \"AdminHeader\");", "map": {"version": 3, "names": ["React", "Link", "useAuth", "jsxDEV", "_jsxDEV", "Ad<PERSON><PERSON><PERSON><PERSON>", "_s", "_state$user", "state", "logout", "userName", "user", "name", "handleLogout", "window", "location", "href", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Projet-Synthese/frontend/src/components/admin/AdminHeader.js"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\n\nconst AdminHeader = () => {\n  const { state, logout } = useAuth();\n\n  // Gestion sécurisée de l'état d'authentification\n  const userName = state?.user?.name || 'Admin';\n\n  const handleLogout = () => {\n    if (logout) {\n      logout();\n    }\n    // En mode démonstration, on peut simplement recharger la page\n    window.location.href = '/';\n  };\n\n  return (\n    <header className=\"bg-white shadow-sm h-16 flex items-center px-6 sticky top-0 z-10\">\n      <div className=\"flex-1 flex justify-between items-center\">\n        <div className=\"flex items-center\">\n          <span className=\"text-gray-800 font-medium\">\n            Bonjour, {userName}\n          </span>\n        </div>\n        \n        <div className=\"flex items-center space-x-4\">\n          <Link to=\"/\" className=\"text-gray-600 hover:text-green-600 transition\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\" />\n            </svg>\n          </Link>\n          \n          <button \n            onClick={logout}\n            className=\"text-gray-600 hover:text-red-600 transition\"\n          >\n            <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\n            </svg>\n          </button>\n        </div>\n      </div>\n    </header>\n  );\n};\n\nexport default AdminHeader;"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,OAAO,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,WAAA;EACxB,MAAM;IAAEC,KAAK;IAAEC;EAAO,CAAC,GAAGP,OAAO,CAAC,CAAC;;EAEnC;EACA,MAAMQ,QAAQ,GAAG,CAAAF,KAAK,aAALA,KAAK,wBAAAD,WAAA,GAALC,KAAK,CAAEG,IAAI,cAAAJ,WAAA,uBAAXA,WAAA,CAAaK,IAAI,KAAI,OAAO;EAE7C,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIJ,MAAM,EAAE;MACVA,MAAM,CAAC,CAAC;IACV;IACA;IACAK,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;EAC5B,CAAC;EAED,oBACEZ,OAAA;IAAQa,SAAS,EAAC,kEAAkE;IAAAC,QAAA,eAClFd,OAAA;MAAKa,SAAS,EAAC,0CAA0C;MAAAC,QAAA,gBACvDd,OAAA;QAAKa,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAChCd,OAAA;UAAMa,SAAS,EAAC,2BAA2B;UAAAC,QAAA,GAAC,WACjC,EAACR,QAAQ;QAAA;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENlB,OAAA;QAAKa,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1Cd,OAAA,CAACH,IAAI;UAACsB,EAAE,EAAC,GAAG;UAACN,SAAS,EAAC,+CAA+C;UAAAC,QAAA,eACpEd,OAAA;YAAKoB,KAAK,EAAC,4BAA4B;YAACP,SAAS,EAAC,SAAS;YAACQ,IAAI,EAAC,MAAM;YAACC,OAAO,EAAC,WAAW;YAACC,MAAM,EAAC,cAAc;YAAAT,QAAA,eAC/Gd,OAAA;cAAMwB,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAAkJ;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEPlB,OAAA;UACE4B,OAAO,EAAEvB,MAAO;UAChBQ,SAAS,EAAC,6CAA6C;UAAAC,QAAA,eAEvDd,OAAA;YAAKoB,KAAK,EAAC,4BAA4B;YAACP,SAAS,EAAC,SAAS;YAACQ,IAAI,EAAC,MAAM;YAACC,OAAO,EAAC,WAAW;YAACC,MAAM,EAAC,cAAc;YAAAT,QAAA,eAC/Gd,OAAA;cAAMwB,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAA2F;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAAChB,EAAA,CA1CID,WAAW;EAAA,QACWH,OAAO;AAAA;AAAA+B,EAAA,GAD7B5B,WAAW;AA4CjB,eAAeA,WAAW;AAAC,IAAA4B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}